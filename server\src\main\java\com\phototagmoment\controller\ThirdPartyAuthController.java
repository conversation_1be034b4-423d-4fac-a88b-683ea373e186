package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.AuthLoginDTO;
import com.phototagmoment.dto.AuthUserInfoDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.security.JwtTokenProvider;
import com.phototagmoment.service.AuthService;
import com.phototagmoment.vo.TokenVO;
import com.phototagmoment.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 第三方登录控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth/third-party")
@Tag(name = "第三方登录接口", description = "第三方登录相关接口")
public class ThirdPartyAuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserAuthMapper userAuthMapper;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    /**
     * 获取授权URL
     */
    @GetMapping("/{source}/auth-url")
    @Operation(summary = "获取授权URL", description = "获取第三方平台授权URL")
    public ApiResponse<Map<String, String>> getAuthUrl(
            @Parameter(description = "第三方平台来源") @PathVariable String source,
            @Parameter(description = "状态参数") @RequestParam(required = false) String state) {
        // 如果没有提供状态参数，生成一个随机的状态参数
        if (state == null || state.isEmpty()) {
            state = UUID.randomUUID().toString();
        }

        // 获取授权URL
        AuthLoginDTO authLoginDTO = authService.getAuthUrl(source, state);
        if (!authLoginDTO.isSuccess()) {
            return ApiResponse.failed(authLoginDTO.getErrorMsg());
        }

        Map<String, String> result = new HashMap<>();
        result.put("authUrl", authLoginDTO.getAuthUrl());
        result.put("state", authLoginDTO.getState());

        return ApiResponse.success(result);
    }

    /**
     * 处理授权回调
     */
    @PostMapping("/{source}/login")
    @Operation(summary = "第三方登录", description = "处理第三方平台授权回调")
    public ApiResponse<Map<String, Object>> login(
            @Parameter(description = "第三方平台来源") @PathVariable String source,
            @Parameter(description = "授权码") @RequestParam String code,
            @Parameter(description = "状态参数") @RequestParam(required = false) String state) {
        // 创建授权回调对象
        AuthCallback callback = new AuthCallback();
        callback.setCode(code);
        callback.setState(state);

        // 处理登录
        AuthLoginDTO loginResult = authService.login(source, callback);
        if (!loginResult.isSuccess()) {
            return ApiResponse.failed(loginResult.getErrorMsg());
        }

        // 查询用户是否已存在
        UserAuth userAuth = userAuthMapper.selectByTypeAndIdentifier(source, loginResult.getOpenId());
        User user;

        if (userAuth == null) {
            // 用户不存在，创建新用户
            user = createUser(loginResult);
            // 创建用户认证信息
            createUserAuth(user.getId(), loginResult);
        } else {
            // 用户已存在，更新用户信息
            user = userMapper.selectById(userAuth.getUserId());
            if (user == null) {
                return ApiResponse.failed("用户不存在");
            }
            // 更新用户认证信息
            updateUserAuth(userAuth, loginResult);
        }

        // 生成JWT
        String token = jwtTokenProvider.generateToken(user.getUsername(), user.getId());

        // 更新用户最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 构建返回对象
        UserVO userVO = convertToUserVO(user);
        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(token);
        tokenVO.setTokenType(tokenPrefix);
        tokenVO.setExpiresIn(jwtExpiration / 1000);
        tokenVO.setUser(userVO);

        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", userVO);

        return ApiResponse.success(result);
    }

    /**
     * 创建用户
     */
    private User createUser(AuthLoginDTO loginResult) {
        // 获取用户信息
        AuthUserInfoDTO userInfo = authService.getUserInfo(loginResult.getSource(), loginResult.getAccessToken(), loginResult.getOpenId());

        User user = new User();
        // 生成随机用户名
        String username = loginResult.getSource() + "_" + loginResult.getOpenId().substring(0, 8);
        user.setUsername(username);

        if (userInfo != null) {
            // 设置昵称
            if (StringUtils.hasText(userInfo.getNickname())) {
                user.setNickname(userInfo.getNickname());
            } else {
                user.setNickname(username);
            }

            // 设置头像
            if (StringUtils.hasText(userInfo.getAvatarLarge())) {
                user.setAvatar(userInfo.getAvatarLarge());
            } else if (StringUtils.hasText(userInfo.getAvatarMedium())) {
                user.setAvatar(userInfo.getAvatarMedium());
            } else if (StringUtils.hasText(userInfo.getAvatarSmall())) {
                user.setAvatar(userInfo.getAvatarSmall());
            }

            // 设置性别
            if (StringUtils.hasText(userInfo.getGender())) {
                user.setGender("男".equals(userInfo.getGender()) ? 1 : ("女".equals(userInfo.getGender()) ? 2 : 0));
            } else {
                user.setGender(0);
            }
        } else {
            user.setNickname(username);
            user.setGender(0);
        }

        user.setStatus(1);
        user.setIsVerified(0);
        user.setIsAdmin(0);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        user.setLastLoginTime(LocalDateTime.now());

        // 设置一个随机密码
        String randomPassword = UUID.randomUUID().toString().substring(0, 8);
        user.setPassword(passwordEncoder.encode(randomPassword));

        userMapper.insert(user);
        log.info("创建用户成功: userId={}, username={}", user.getId(), user.getUsername());

        return user;
    }

    /**
     * 创建用户认证信息
     */
    private void createUserAuth(Long userId, AuthLoginDTO loginResult) {
        UserAuth userAuth = new UserAuth();
        userAuth.setUserId(userId);
        userAuth.setIdentityType(loginResult.getSource());
        userAuth.setIdentifier(loginResult.getOpenId());
        userAuth.setCredential(loginResult.getAccessToken());
        userAuth.setVerified(1);
        userAuth.setCreatedAt(LocalDateTime.now());
        userAuth.setUpdatedAt(LocalDateTime.now());

        userAuthMapper.insert(userAuth);
        log.info("创建用户认证信息成功: userId={}, source={}, openId={}",
                userId, loginResult.getSource(), loginResult.getOpenId());
    }

    /**
     * 更新用户认证信息
     */
    private void updateUserAuth(UserAuth userAuth, AuthLoginDTO loginResult) {
        userAuth.setCredential(loginResult.getAccessToken());
        userAuth.setUpdatedAt(LocalDateTime.now());

        userAuthMapper.updateById(userAuth);
        log.info("更新用户认证信息成功: userId={}, source={}, openId={}",
                userAuth.getUserId(), loginResult.getSource(), loginResult.getOpenId());
    }

    /**
     * 转换为用户VO
     */
    private UserVO convertToUserVO(User user) {
        UserVO userVO = new UserVO();
        userVO.setId(user.getId());
        userVO.setUsername(user.getUsername());
        userVO.setNickname(user.getNickname());
        userVO.setAvatar(user.getAvatar());
        userVO.setEmail(user.getEmail());
        userVO.setPhone(user.getPhone());
        userVO.setGender(user.getGender());
        userVO.setBirthday(user.getBirthday());
        userVO.setBio(user.getBio());
        userVO.setIsVerified(user.getIsVerified());

        return userVO;
    }
}
