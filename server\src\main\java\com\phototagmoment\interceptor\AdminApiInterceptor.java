package com.phototagmoment.interceptor;

import com.phototagmoment.entity.Admin;
import com.phototagmoment.mapper.AdminMapper;
import com.phototagmoment.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 管理员API拦截器
 * 专门用于处理后台管理接口的认证
 */
@Slf4j
@Component
public class AdminApiInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private AdminMapper adminMapper;

    @Value("${jwt.header:Authorization}")
    private String tokenHeader;

    @Value("${jwt.prefix:Bearer }")
    private String tokenPrefix;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (request == null) {
            log.warn("请求对象为null");
            return true;
        }

        String requestURI = request.getRequestURI();
        if (requestURI == null) {
            log.warn("请求URI为null");
            return true;
        }

        // 只处理后台管理接口
        if (requestURI.startsWith("/api/admin/system/") || requestURI.startsWith("/admin/system/")) {
            log.info("AdminApiInterceptor处理后台管理接口: {}", requestURI);

            try {
                // 获取JWT
                String jwt = getJwtFromRequest(request);

                if (jwt != null && StringUtils.hasText(jwt)) {
                    log.info("从请求中获取到JWT，开始验证");

                    try {
                        // 验证token
                        if (jwtTokenProvider != null && jwtTokenProvider.validateToken(jwt)) {
                            // 从token中获取用户名
                            String username = jwtTokenProvider.getUsernameFromToken(jwt);
                            if (username == null || !StringUtils.hasText(username)) {
                                log.warn("从JWT中获取的用户名为空");
                                return true;
                            }

                            log.info("JWT验证成功，用户名: {}", username);

                            // 查询管理员信息
                            if (adminMapper == null) {
                                log.warn("adminMapper未注入");
                                return true;
                            }

                            Admin admin = adminMapper.selectByUsername(username);
                            if (admin != null) {
                                log.info("查询到管理员信息: {}", admin.getUsername());

                                // 创建认证对象
                                List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));

                                UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                                        admin.getUsername(),
                                        "", // 密码为空，因为我们使用JWT认证
                                        admin.getStatus() == 1, // 启用状态
                                        true, // 账号未过期
                                        true, // 凭证未过期
                                        true, // 账号未锁定
                                        authorities
                                );

                                // 创建认证对象
                                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                        userDetails, null, userDetails.getAuthorities());

                                // 设置请求详情
                                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                                // 设置认证信息到SecurityContext
                                SecurityContextHolder.getContext().setAuthentication(authentication);
                                log.info("成功设置认证信息到SecurityContext: {}", username);
                            } else {
                                log.warn("未找到管理员信息: {}", username);
                            }
                        } else {
                            log.warn("JWT验证失败或jwtTokenProvider为null");
                        }
                    } catch (Exception e) {
                        log.error("JWT验证错误: {}", e.getMessage());
                    }
                } else {
                    log.info("请求中没有找到JWT，继续处理");
                }
            } catch (Exception e) {
                log.error("处理请求时发生异常: {}", e.getMessage());
            }
        }

        // 始终放行，让Spring Security的过滤器链来决定是否允许访问
        return true;
    }

    /**
     * 从请求中获取JWT
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        try {
            // 从Authorization头获取token
            String bearerToken = request.getHeader(tokenHeader);

            if (bearerToken != null && !bearerToken.isEmpty()) {
                try {
                    // 检查tokenPrefix是否为空
                    if (tokenPrefix != null && !tokenPrefix.isEmpty() && bearerToken.startsWith(tokenPrefix)) {
                        // 提取token，去掉前缀和空格
                        return bearerToken.substring(tokenPrefix.length()).trim();
                    } else if (bearerToken.startsWith("Bearer ")) {
                        // 如果以"Bearer "开头，但tokenPrefix不是"Bearer "
                        return bearerToken.substring(7).trim();
                    } else {
                        // 没有Bearer前缀，直接使用
                        return bearerToken;
                    }
                } catch (Exception e) {
                    log.error("处理Authorization头时发生异常: {}", e.getMessage());
                    return null;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("从请求中获取JWT失败: {}", e.getMessage());
            return null;
        }
    }
}
