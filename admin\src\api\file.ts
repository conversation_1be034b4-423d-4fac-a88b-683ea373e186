import request from '@/utils/request'

// 文件上传参数接口
interface FileUploadParams {
  type?: string;
  [key: string]: any;
}

// 文件信息接口
interface FileInfo {
  id: number;
  originalName: string;
  fileName: string;
  filePath: string;
  fileUrl: string;
  fileSize: number;
  fileSizeFormatted: string;
  fileType: string;
  mimeType: string;
  extension: string;
  category: string;
  uploaderId: number;
  uploaderName: string;
  uploaderType: string;
  storageType: string;
  inTrash: boolean;
  status: number;
  tags: string;
  description: string;
  accessCount: number;
  lastAccessTime: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  [key: string]: any;
}

// 文件列表查询参数接口
interface FileListParams {
  page?: number;
  size?: number;
  fileType?: string;
  uploaderId?: number;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  [key: string]: any;
}

// 文件搜索参数接口
interface FileSearchParams {
  page?: number;
  size?: number;
  keyword: string;
  fileType?: string;
  minSize?: number;
  maxSize?: number;
  [key: string]: any;
}

/**
 * 上传单个文件
 * @param file 文件对象
 * @param params 上传参数
 */
export function uploadFile(file: File, params: FileUploadParams = {}) {
  const formData = new FormData()
  formData.append('file', file)
  if (params.type) {
    formData.append('type', params.type)
  }
  
  return request({
    url: '/admin/file/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量上传文件
 * @param files 文件列表
 * @param params 上传参数
 */
export function batchUploadFiles(files: File[], params: FileUploadParams = {}) {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  if (params.type) {
    formData.append('type', params.type)
  }
  
  return request({
    url: '/admin/file/batch-upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取上传凭证（七牛云）
 * @param type 文件类型
 */
export function getUploadToken(type: string = 'general') {
  return request({
    url: '/admin/file/upload-token',
    method: 'get',
    params: { type }
  })
}

/**
 * 删除文件
 * @param filePath 文件路径
 */
export function deleteFile(filePath: string) {
  return request({
    url: '/admin/file/delete',
    method: 'delete',
    params: { filePath }
  })
}

/**
 * 获取文件列表
 * @param params 查询参数
 */
export function getFileList(params: FileListParams) {
  return request({
    url: '/admin/file-manage/list',
    method: 'get',
    params
  })
}

/**
 * 获取文件详情
 * @param fileId 文件ID
 */
export function getFileDetail(fileId: number) {
  return request({
    url: `/admin/file-manage/${fileId}`,
    method: 'get'
  })
}

/**
 * 批量删除文件
 * @param fileIds 文件ID列表
 */
export function batchDeleteFiles(fileIds: number[]) {
  return request({
    url: '/admin/file-manage/batch-delete',
    method: 'delete',
    data: fileIds
  })
}

/**
 * 重命名文件
 * @param fileId 文件ID
 * @param newName 新文件名
 */
export function renameFile(fileId: number, newName: string) {
  return request({
    url: `/admin/file-manage/${fileId}/rename`,
    method: 'put',
    params: { newName }
  })
}

/**
 * 移动文件到回收站
 * @param fileId 文件ID
 */
export function moveToTrash(fileId: number) {
  return request({
    url: `/admin/file-manage/${fileId}/trash`,
    method: 'put'
  })
}

/**
 * 从回收站恢复文件
 * @param fileId 文件ID
 */
export function restoreFile(fileId: number) {
  return request({
    url: `/admin/file-manage/${fileId}/restore`,
    method: 'put'
  })
}

/**
 * 获取回收站文件列表
 * @param page 页码
 * @param size 每页大小
 */
export function getTrashFiles(page: number = 1, size: number = 20) {
  return request({
    url: '/admin/file-manage/trash',
    method: 'get',
    params: { page, size }
  })
}

/**
 * 清空回收站
 */
export function clearTrash() {
  return request({
    url: '/admin/file-manage/trash/clear',
    method: 'delete'
  })
}

/**
 * 获取文件统计信息
 */
export function getFileStatistics() {
  return request({
    url: '/admin/file-manage/statistics',
    method: 'get'
  })
}

/**
 * 获取存储空间使用情况
 */
export function getStorageUsage() {
  return request({
    url: '/admin/file-manage/storage-usage',
    method: 'get'
  })
}

/**
 * 获取文件类型分布统计
 */
export function getFileTypeDistribution() {
  return request({
    url: '/admin/file-manage/type-distribution',
    method: 'get'
  })
}

/**
 * 检查文件完整性
 * @param fileIds 文件ID列表（可选）
 */
export function checkFileIntegrity(fileIds?: number[]) {
  return request({
    url: '/admin/file-manage/check-integrity',
    method: 'post',
    data: fileIds || []
  })
}

/**
 * 生成文件访问链接
 * @param fileId 文件ID
 * @param expireSeconds 有效期（秒）
 */
export function generateAccessUrl(fileId: number, expireSeconds: number = 3600) {
  return request({
    url: `/admin/file-manage/${fileId}/access-url`,
    method: 'post',
    params: { expireSeconds }
  })
}

/**
 * 批量移动文件
 * @param fileIds 文件ID列表
 * @param targetPath 目标路径
 */
export function batchMoveFiles(fileIds: number[], targetPath: string) {
  return request({
    url: '/admin/file-manage/batch-move',
    method: 'put',
    params: { fileIds: fileIds.join(','), targetPath }
  })
}

/**
 * 搜索文件
 * @param params 搜索参数
 */
export function searchFiles(params: FileSearchParams) {
  return request({
    url: '/admin/file-manage/search',
    method: 'get',
    params
  })
}

// 导出类型定义
export type {
  FileInfo,
  FileListParams,
  FileSearchParams,
  FileUploadParams
}
