-- 添加照片草稿表
-- 版本号 1.3.1，避免与 V1.4__Create_Admin_Tables.sql 冲突
CREATE TABLE IF NOT EXISTS `ptm_photo_draft` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '草稿ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `title` varchar(100) DEFAULT NULL COMMENT '草稿标题',
  `description` text DEFAULT NULL COMMENT '草稿描述',
  `location` varchar(100) DEFAULT NULL COMMENT '拍摄地点',
  `tags` text DEFAULT NULL COMMENT '标签，JSON格式',
  `mentions` text DEFAULT NULL COMMENT '提及用户，JSON格式',
  `visibility` tinyint(1) DEFAULT 1 COMMENT '可见性: 0-私密, 1-公开, 2-好友可见',
  `allow_comment` tinyint(1) DEFAULT 1 COMMENT '是否允许评论: 0-不允许, 1-允许',
  `allow_download` tinyint(1) DEFAULT 1 COMMENT '是否允许下载: 0-不允许, 1-允许',
  `temp_file_paths` text DEFAULT NULL COMMENT '临时文件路径，JSON格式',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除, 1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片草稿表';

-- 修改通知表，添加@用户通知类型
ALTER TABLE `ptm_notification`
MODIFY COLUMN `type` tinyint(1) NOT NULL COMMENT '通知类型：1关注，2点赞，3评论，4回复，5系统，6@用户';
