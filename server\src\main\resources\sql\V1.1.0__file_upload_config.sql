-- =====================================================
-- PhotoTagMoment 文件上传配置管理功能数据库脚本
-- 版本: V1.1.0
-- 创建时间: 2025-05-23
-- 描述: 新增文件上传配置管理相关表和数据
-- =====================================================

-- 1. 创建文件上传配置表
CREATE TABLE IF NOT EXISTS `ptm_file_upload_config` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_name` VARCHAR(100) NOT NULL COMMENT '配置名称',
    `storage_type` VARCHAR(50) NOT NULL COMMENT '存储类型（LOCAL/QINIU/ALIYUN_OSS/TENCENT_COS/AWS_S3/MINIO）',
    `config_params` TEXT COMMENT '配置参数（JSON格式，敏感信息已加密）',
    `upload_limits` TEXT COMMENT '上传限制配置（JSON格式）',
    `path_config` TEXT COMMENT '路径配置（JSON格式）',
    `enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用（0禁用 1启用）',
    `is_default` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为默认配置（0否 1是）',
    `status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '配置状态（0正常 1禁用 2异常）',
    `sort_order` INT(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    `last_test_time` DATETIME DEFAULT NULL COMMENT '最后测试时间',
    `last_test_result` TEXT DEFAULT NULL COMMENT '最后测试结果（JSON格式）',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` DATETIME DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_name` (`config_name`, `is_deleted`),
    KEY `idx_storage_type` (`storage_type`),
    KEY `idx_enabled` (`enabled`),
    KEY `idx_is_default` (`is_default`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传配置表';

-- 2. 创建配置变更日志表
CREATE TABLE IF NOT EXISTS `ptm_file_upload_config_log` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` BIGINT(20) NOT NULL COMMENT '配置ID',
    `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型（CREATE/UPDATE/DELETE/TEST/ENABLE/DISABLE）',
    `old_value` TEXT COMMENT '变更前的值（JSON格式）',
    `new_value` TEXT COMMENT '变更后的值（JSON格式）',
    `operator_id` BIGINT(20) DEFAULT NULL COMMENT '操作者ID',
    `operator_name` VARCHAR(100) DEFAULT NULL COMMENT '操作者名称',
    `operator_ip` VARCHAR(50) DEFAULT NULL COMMENT '操作者IP',
    `operation_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传配置变更日志表';

-- 3. 创建配置统计表
CREATE TABLE IF NOT EXISTS `ptm_file_upload_config_stats` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_id` BIGINT(20) NOT NULL COMMENT '配置ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `upload_count` INT(11) NOT NULL DEFAULT 0 COMMENT '上传次数',
    `upload_size` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '上传总大小（字节）',
    `success_count` INT(11) NOT NULL DEFAULT 0 COMMENT '成功次数',
    `fail_count` INT(11) NOT NULL DEFAULT 0 COMMENT '失败次数',
    `avg_response_time` INT(11) NOT NULL DEFAULT 0 COMMENT '平均响应时间（毫秒）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_date` (`config_id`, `stat_date`),
    KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传配置统计表';

-- 4. 添加外键约束
ALTER TABLE `ptm_file_upload_config_log`
ADD CONSTRAINT `fk_config_log_config_id`
FOREIGN KEY (`config_id`) REFERENCES `ptm_file_upload_config` (`id`) ON DELETE CASCADE;

ALTER TABLE `ptm_file_upload_config_stats`
ADD CONSTRAINT `fk_config_stats_config_id`
FOREIGN KEY (`config_id`) REFERENCES `ptm_file_upload_config` (`id`) ON DELETE CASCADE;

-- 5. 插入默认本地存储配置
INSERT INTO `ptm_file_upload_config` (
    `config_name`,
    `storage_type`,
    `config_params`,
    `upload_limits`,
    `path_config`,
    `enabled`,
    `is_default`,
    `status`,
    `sort_order`,
    `description`
) VALUES (
    '默认本地存储',
    'LOCAL',
    '{"localPath":"uploads","domain":"http://localhost:8081","useHttps":false,"connectTimeout":30,"readTimeout":60}',
    '{"maxFileSize":50,"maxFileCount":10,"allowedFileTypes":["jpg","jpeg","png","gif","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","zip","rar"],"forbiddenFileTypes":["exe","bat","sh","cmd"],"enableFileTypeCheck":true,"enableContentCheck":false,"enableVirusScan":false,"imageMaxDimensions":{"maxWidth":4096,"maxHeight":4096}}',
    '{"rootPath":"uploads","fileNamingRule":"UUID","directoryStructure":"DATE_USER_TYPE","enableDateDirectory":true,"enableUserDirectory":true,"enableTypeDirectory":true,"customPrefix":"","thumbnailDirectory":"thumbnails","tempDirectory":"temp"}',
    1,
    1,
    0,
    1,
    '系统默认的本地文件存储配置'
);

-- 6. 插入示例七牛云配置（需要用户自行配置真实的密钥）
INSERT INTO `ptm_file_upload_config` (
    `config_name`,
    `storage_type`,
    `config_params`,
    `upload_limits`,
    `path_config`,
    `enabled`,
    `is_default`,
    `status`,
    `sort_order`,
    `description`
) VALUES (
    '七牛云存储示例',
    'QINIU',
    '{"qiniuAccessKey":"your-access-key","qiniuSecretKey":"your-secret-key","qiniuBucket":"your-bucket","qiniuRegion":"z0","domain":"https://your-domain.com","useHttps":true,"connectTimeout":30,"readTimeout":60}',
    '{"maxFileSize":100,"maxFileCount":20,"allowedFileTypes":["jpg","jpeg","png","gif","webp","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","zip","rar","mp4","avi","mov"],"forbiddenFileTypes":["exe","bat","sh","cmd","scr"],"enableFileTypeCheck":true,"enableContentCheck":true,"enableVirusScan":false,"imageMaxDimensions":{"maxWidth":8192,"maxHeight":8192}}',
    '{"rootPath":"phototagmoment","fileNamingRule":"UUID","directoryStructure":"DATE_TYPE","enableDateDirectory":true,"enableUserDirectory":false,"enableTypeDirectory":true,"customPrefix":"ptm","thumbnailDirectory":"thumbnails","tempDirectory":"temp"}',
    0,
    0,
    1,
    2,
    '七牛云对象存储配置示例，需要配置真实的AccessKey和SecretKey'
);

-- 7. 插入示例阿里云OSS配置（需要用户自行配置真实的密钥）
INSERT INTO `ptm_file_upload_config` (
    `config_name`,
    `storage_type`,
    `config_params`,
    `upload_limits`,
    `path_config`,
    `enabled`,
    `is_default`,
    `status`,
    `sort_order`,
    `description`
) VALUES (
    '阿里云OSS示例',
    'ALIYUN_OSS',
    '{"aliyunAccessKeyId":"your-access-key-id","aliyunAccessKeySecret":"your-access-key-secret","aliyunBucket":"your-bucket","aliyunEndpoint":"oss-cn-hangzhou.aliyuncs.com","domain":"https://your-bucket.oss-cn-hangzhou.aliyuncs.com","useHttps":true,"connectTimeout":30,"readTimeout":60}',
    '{"maxFileSize":200,"maxFileCount":50,"allowedFileTypes":["jpg","jpeg","png","gif","webp","bmp","svg","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","csv","zip","rar","7z","mp4","avi","mov","wmv","flv","mp3","wav","aac"],"forbiddenFileTypes":["exe","bat","sh","cmd","scr","msi"],"enableFileTypeCheck":true,"enableContentCheck":true,"enableVirusScan":true,"imageMaxDimensions":{"maxWidth":10240,"maxHeight":10240}}',
    '{"rootPath":"phototagmoment","fileNamingRule":"UUID_ORIGINAL","directoryStructure":"DATE_USER_TYPE","enableDateDirectory":true,"enableUserDirectory":true,"enableTypeDirectory":true,"customPrefix":"ptm","thumbnailDirectory":"thumbnails","tempDirectory":"temp"}',
    0,
    0,
    1,
    3,
    '阿里云对象存储服务配置示例，需要配置真实的AccessKey'
);

-- 8. 创建配置概览视图
CREATE OR REPLACE VIEW `v_file_upload_config_overview` AS
SELECT
    c.id,
    c.config_name,
    c.storage_type,
    c.enabled,
    c.is_default,
    c.status,
    c.description,
    c.last_test_time,
    c.created_at,
    c.updated_at,
    COALESCE(s.upload_count, 0) as total_upload_count,
    COALESCE(s.upload_size, 0) as total_upload_size,
    COALESCE(s.success_count, 0) as total_success_count,
    COALESCE(s.fail_count, 0) as total_fail_count,
    CASE
        WHEN COALESCE(s.upload_count, 0) = 0 THEN 0
        ELSE ROUND(COALESCE(s.success_count, 0) * 100.0 / COALESCE(s.upload_count, 0), 2)
    END as success_rate
FROM `ptm_file_upload_config` c
LEFT JOIN (
    SELECT
        config_id,
        SUM(upload_count) as upload_count,
        SUM(upload_size) as upload_size,
        SUM(success_count) as success_count,
        SUM(fail_count) as fail_count
    FROM `ptm_file_upload_config_stats`
    GROUP BY config_id
) s ON c.id = s.config_id
WHERE c.is_deleted = 0;

-- 9. 插入文件上传配置相关的系统配置
INSERT IGNORE INTO `ptm_system_config` (`config_key`, `config_value`, `config_name`, `config_type`) VALUES
('file.upload.default.config', '1', '默认文件上传配置ID', 'SYSTEM'),
('file.upload.max.concurrent', '10', '最大并发上传数', 'SYSTEM'),
('file.upload.chunk.size', '2097152', '分片上传大小（字节）', 'SYSTEM'),
('file.upload.temp.expire.hours', '24', '临时文件过期时间（小时）', 'SYSTEM'),
('file.upload.auto.cleanup.enabled', 'true', '是否启用自动清理', 'SYSTEM'),
('file.upload.thumbnail.enabled', 'true', '是否启用缩略图生成', 'SYSTEM'),
('file.upload.thumbnail.quality', '0.8', '缩略图质量（0.1-1.0）', 'SYSTEM'),
('file.upload.thumbnail.max.width', '300', '缩略图最大宽度', 'SYSTEM'),
('file.upload.thumbnail.max.height', '300', '缩略图最大高度', 'SYSTEM');

-- 10. 创建触发器：确保只有一个默认配置
DELIMITER $$
CREATE TRIGGER `tr_file_upload_config_default_check`
BEFORE UPDATE ON `ptm_file_upload_config`
FOR EACH ROW
BEGIN
    -- 如果设置为默认配置，则将其他配置的默认标志设为0
    IF NEW.is_default = 1 AND OLD.is_default = 0 THEN
        UPDATE `ptm_file_upload_config`
        SET `is_default` = 0
        WHERE `id` != NEW.id AND `is_deleted` = 0;
    END IF;
END$$
DELIMITER ;

-- 11. 创建触发器：插入时确保只有一个默认配置
DELIMITER $$
CREATE TRIGGER `tr_file_upload_config_default_insert`
BEFORE INSERT ON `ptm_file_upload_config`
FOR EACH ROW
BEGIN
    -- 如果插入的是默认配置，则将其他配置的默认标志设为0
    IF NEW.is_default = 1 THEN
        UPDATE `ptm_file_upload_config`
        SET `is_default` = 0
        WHERE `is_deleted` = 0;
    END IF;
END$$
DELIMITER ;

-- 12. 创建存储过程：清理过期的临时文件配置统计
DELIMITER $$
CREATE PROCEDURE `sp_cleanup_file_upload_stats`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_config_id BIGINT;
    DECLARE cur CURSOR FOR
        SELECT DISTINCT config_id
        FROM ptm_file_upload_config_stats
        WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;

    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO v_config_id;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 删除90天前的统计数据
        DELETE FROM ptm_file_upload_config_stats
        WHERE config_id = v_config_id
        AND stat_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);

    END LOOP;
    CLOSE cur;

    COMMIT;
END$$
DELIMITER ;

-- 13. 创建事件：定期清理过期统计数据
CREATE EVENT IF NOT EXISTS `ev_cleanup_file_upload_stats`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
CALL sp_cleanup_file_upload_stats();

-- 脚本执行完成
SELECT '文件上传配置管理功能数据库脚本执行完成' AS message;
