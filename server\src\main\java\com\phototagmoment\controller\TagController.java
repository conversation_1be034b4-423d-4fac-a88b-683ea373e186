package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.TagDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.TagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签控制器
 */
@Slf4j
@RestController
@RequestMapping("/tag")
@Tag(name = "标签接口", description = "标签相关接口")
public class TagController {

    @Autowired
    private TagService tagService;

    /**
     * 获取标签详情
     */
    @GetMapping("/{tagName}")
    @Operation(summary = "获取标签详情", description = "获取标签详情")
    public ApiResponse<TagDTO> getTagDetail(
            @Parameter(description = "标签名称") @PathVariable String tagName) {
        TagDTO tagDTO = tagService.getTagDetail(tagName);
        return ApiResponse.success(tagDTO);
    }

    /**
     * 获取标签相关内容
     */
    @GetMapping("/{tagName}/content")
    @Operation(summary = "获取标签相关内容", description = "获取标签相关的照片")
    public ApiResponse<IPage<PhotoDTO>> getTagContent(
            @Parameter(description = "标签名称") @PathVariable String tagName,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        IPage<PhotoDTO> photoPage = tagService.getTagContent(tagName, page, size, currentUserId);
        return ApiResponse.success(photoPage);
    }

    /**
     * 获取热门标签
     */
    @GetMapping("/hot")
    @Operation(summary = "获取热门标签", description = "获取热门标签列表")
    public ApiResponse<List<TagDTO>> getHotTags(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        List<TagDTO> tags = tagService.getHotTags(limit);
        return ApiResponse.success(tags);
    }
}
