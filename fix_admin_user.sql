-- 修复管理员用户和认证信息
-- 解决照片笔记管理功能的登录问题

USE phototag_moment;

-- 1. 检查并创建管理员用户
INSERT IGNORE INTO ptm_user (
    username, nickname, password, email, phone, 
    avatar, gender, birthday, bio, location, 
    status, is_admin, is_verified, 
    created_at, updated_at
) VALUES (
    'admin', '系统管理员', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', 
    '<EMAIL>', '13800138000',
    '/default/admin-avatar.jpg', 0, '1990-01-01', '系统管理员账号', '系统',
    1, 1, 1,
    NOW(), NOW()
);

-- 2. 获取管理员用户ID
SET @admin_user_id = (SELECT id FROM ptm_user WHERE username = 'admin' LIMIT 1);

-- 3. 创建管理员认证信息（用户名密码方式）
INSERT IGNORE INTO ptm_user_auth (
    user_id, identity_type, identifier, credential, verified, created_at, updated_at
) VALUES (
    @admin_user_id, 'username', 'admin', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', 1, NOW(), NOW()
);

-- 4. 检查并修复现有管理员用户的认证信息
INSERT IGNORE INTO ptm_user_auth (
    user_id, identity_type, identifier, credential, verified, created_at, updated_at
)
SELECT 
    u.id, 'username', u.username, u.password, 1, NOW(), NOW()
FROM ptm_user u 
WHERE u.is_admin = 1 
AND NOT EXISTS (
    SELECT 1 FROM ptm_user_auth ua 
    WHERE ua.user_id = u.id AND ua.identity_type = 'username'
);

-- 5. 创建测试用户（用于测试照片笔记发布功能）
INSERT IGNORE INTO ptm_user (
    username, nickname, password, email, phone, 
    avatar, gender, birthday, bio, location, 
    status, is_admin, is_verified, 
    created_at, updated_at
) VALUES (
    'testuser', '测试用户', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', 
    '<EMAIL>', '13800138001',
    '/default/user-avatar.jpg', 1, '1995-01-01', '测试用户账号', '测试城市',
    1, 0, 1,
    NOW(), NOW()
);

-- 6. 为测试用户创建认证信息
SET @test_user_id = (SELECT id FROM ptm_user WHERE username = 'testuser' LIMIT 1);

INSERT IGNORE INTO ptm_user_auth (
    user_id, identity_type, identifier, credential, verified, created_at, updated_at
) VALUES (
    @test_user_id, 'username', 'testuser', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', 1, NOW(), NOW()
);

-- 7. 创建一些测试照片笔记数据
INSERT IGNORE INTO ptm_photo_note (
    user_id, title, content, photo_count, 
    status, visibility, allow_comment, 
    view_count, like_count, comment_count, share_count,
    location, longitude, latitude,
    created_at, updated_at
) VALUES 
(
    @test_user_id, '测试照片笔记1', '这是第一条测试照片笔记内容 #测试标签# #风景# @admin', 1,
    1, 1, 1,
    10, 5, 2, 1,
    '测试地点1', 116.397128, 39.916527,
    NOW() - INTERVAL 1 DAY, NOW() - INTERVAL 1 DAY
),
(
    @test_user_id, '测试照片笔记2', '这是第二条测试照片笔记内容 #生活# #美食#', 2,
    0, 1, 1,
    5, 2, 1, 0,
    '测试地点2', 116.407128, 39.926527,
    NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR
),
(
    @test_user_id, '测试照片笔记3', '这是第三条测试照片笔记内容 #旅行# #摄影#', 1,
    1, 2, 1,
    15, 8, 3, 2,
    '测试地点3', 116.387128, 39.906527,
    NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR
);

-- 8. 为照片笔记添加标签
SET @note1_id = (SELECT id FROM ptm_photo_note WHERE title = '测试照片笔记1' LIMIT 1);
SET @note2_id = (SELECT id FROM ptm_photo_note WHERE title = '测试照片笔记2' LIMIT 1);
SET @note3_id = (SELECT id FROM ptm_photo_note WHERE title = '测试照片笔记3' LIMIT 1);

INSERT IGNORE INTO ptm_photo_note_tag (note_id, tag_name, created_at) VALUES
(@note1_id, '测试标签', NOW() - INTERVAL 1 DAY),
(@note1_id, '风景', NOW() - INTERVAL 1 DAY),
(@note2_id, '生活', NOW() - INTERVAL 2 HOUR),
(@note2_id, '美食', NOW() - INTERVAL 2 HOUR),
(@note3_id, '旅行', NOW() - INTERVAL 3 HOUR),
(@note3_id, '摄影', NOW() - INTERVAL 3 HOUR);

-- 9. 为照片笔记添加@用户
INSERT IGNORE INTO ptm_photo_note_mention (note_id, mentioned_user_id, created_at) VALUES
(@note1_id, @admin_user_id, NOW() - INTERVAL 1 DAY);

-- 10. 更新标签统计
INSERT IGNORE INTO ptm_tag_stats (tag_name, usage_count, hot_score, created_at, updated_at) VALUES
('测试标签', 1, 1.0, NOW(), NOW()),
('风景', 1, 1.0, NOW(), NOW()),
('生活', 1, 1.0, NOW(), NOW()),
('美食', 1, 1.0, NOW(), NOW()),
('旅行', 1, 1.0, NOW(), NOW()),
('摄影', 1, 1.0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    usage_count = usage_count + 1,
    hot_score = hot_score + 0.1,
    updated_at = NOW();

-- 11. 显示创建结果
SELECT '管理员用户信息' as info;
SELECT id, username, nickname, is_admin, status FROM ptm_user WHERE username IN ('admin', 'testuser');

SELECT '用户认证信息' as info;
SELECT ua.user_id, u.username, ua.identity_type, ua.identifier, ua.verified 
FROM ptm_user_auth ua 
JOIN ptm_user u ON ua.user_id = u.id 
WHERE u.username IN ('admin', 'testuser');

SELECT '照片笔记数据' as info;
SELECT id, user_id, title, status, visibility, created_at FROM ptm_photo_note ORDER BY created_at DESC LIMIT 5;

SELECT '标签数据' as info;
SELECT pnt.note_id, pnt.tag_name FROM ptm_photo_note_tag pnt ORDER BY pnt.created_at DESC LIMIT 10;

-- 注意：密码是 "123456" 的BCrypt加密结果
