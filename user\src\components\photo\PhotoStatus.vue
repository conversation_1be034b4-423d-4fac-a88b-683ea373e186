<template>
  <div class="photo-status" :class="statusClass">
    <van-icon :name="statusIcon" />
    <span class="status-text">{{ statusText }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: Number,
    required: true
  }
});

// 状态类名
const statusClass = computed(() => {
  switch (props.status) {
    case 0: return 'status-pending';
    case 1: return 'status-normal';
    case 2: return 'status-rejected';
    case 3: return 'status-deleted';
    default: return '';
  }
});

// 状态图标
const statusIcon = computed(() => {
  switch (props.status) {
    case 0: return 'clock-o';
    case 1: return 'passed';
    case 2: return 'close';
    case 3: return 'delete';
    default: return 'question-o';
  }
});

// 状态文本
const statusText = computed(() => {
  switch (props.status) {
    case 0: return '审核中';
    case 1: return '已通过';
    case 2: return '未通过';
    case 3: return '已删除';
    default: return '未知状态';
  }
});
</script>

<style lang="scss" scoped>
.photo-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  
  .van-icon {
    margin-right: 4px;
  }
  
  &.status-pending {
    background-color: #e6f7ff;
    color: #1890ff;
  }
  
  &.status-normal {
    background-color: #f6ffed;
    color: #52c41a;
  }
  
  &.status-rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
  }
  
  &.status-deleted {
    background-color: #f5f5f5;
    color: #999999;
  }
}
</style>
