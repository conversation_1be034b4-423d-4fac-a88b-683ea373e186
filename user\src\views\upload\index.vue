<template>
  <div class="upload-container">
    <van-nav-bar
      title="发布照片笔记"
      left-arrow
      @click-left="goBack"
      fixed
    ></van-nav-bar>

    <div class="upload-content">
      <!-- 内容编辑区域 - 现代化设计 -->
      <div class="edit-card">
        <van-form>
          <!-- 照片上传区域 -->
          <div class="upload-section">
            <div class="section-header">
              <h2 class="section-title">照片笔记</h2>
              <div class="action-buttons">
                <van-button
                  plain
                  size="small"
                  class="action-button draft-button"
                  @click="saveDraft"
                >
                  保存草稿
                </van-button>
                <van-button
                  type="primary"
                  size="small"
                  class="action-button publish-button"
                  :disabled="isUploading || (selectedFiles.length > 0 && !canUpload)"
                  :loading="isUploading"
                  @click="uploadFiles"
                >
                  {{ isUploading ? '发布中...' : '发布笔记' }}
                </van-button>
              </div>
            </div>

            <!-- 使用Vant的Uploader组件 -->
            <van-uploader
              v-model="fileList"
              :max-count="9"
              multiple
              :max-size="10 * 1024 * 1024"
              accept="image/*"
              :before-read="beforeUploadCheck"
              :after-read="afterUpload"
              @delete="onDeleteFile"
              @click-preview="onPreviewFile"
              :preview-full-image="false"
              :image-fit="'cover'"
              class="custom-uploader"
            >
              <template #preview-cover="{ file }">
                <div class="preview-cover">
                  <div class="file-info">
                    <span class="file-name">{{ truncateFileName(file.name || file.file?.name) }}</span>
                    <span class="file-size">{{ formatFileSize(file.file?.size || 0) }}</span>
                  </div>
                </div>
              </template>
              <div class="upload-area">
                <div class="upload-placeholder">
                  <van-icon name="photograph-o" class="upload-icon" />
                  <p class="upload-text" v-if="isMobile">点击选择照片</p>
                  <p class="upload-text" v-else>点击上传照片</p>
                  <p class="upload-hint">支持JPG、PNG等</p>
                </div>
              </div>
            </van-uploader>

            <!-- 照片预览弹出层 -->
            <van-popup
              v-model="showPreview"
              class="preview-popup"
              closeable
              close-icon="clear"
              @click-overlay="showPreview = false"
            >
              <div class="preview-container">
                <van-image
                  :src="currentFile?.preview"
                  :alt="currentFile?.name"
                  class="preview-image"
                  fit="contain"
                />
              </div>
            </van-popup>
          </div>

          <div class="content-section">
            <h2 class="section-title">内容信息</h2>

            <!-- 标题 -->
            <div class="form-item">
              <label class="form-label">标题</label>
              <van-field
                v-model="formData.title"
                placeholder="给照片笔记添加一个标题"
                :rules="[{ required: true, message: '请输入照片笔记标题' }]"
                class="modern-field"
              />
            </div>

            <!-- 正文描述 - 支持@用户和#话题标签 -->
            <div class="form-item">
              <label class="form-label">正文描述</label>
              <div class="description-field">
                <div
                  class="description-editor"
                  contenteditable="true"
                  ref="descriptionEditor"
                  @input="handleDescriptionInput"
                  @keyup="handleDescriptionKeyup"
                  @blur="hideDropdown"
                  placeholder="分享你的照片笔记，可以使用#添加话题，@提及好友..."
                ></div>

                <!-- @用户下拉框 -->
                <div class="mention-dropdown" v-if="showUserDropdown">
                  <div class="dropdown-title">选择用户</div>
                  <div class="dropdown-loading" v-if="loadingUsers">加载中...</div>
                  <div class="dropdown-empty" v-else-if="!filteredUsers.length">未找到相关用户</div>
                  <div
                    v-else
                    class="dropdown-item"
                    v-for="user in filteredUsers"
                    :key="user.id"
                    @click="selectUser(user)"
                  >
                    <img :src="user.avatar || '/default-avatar.png'" class="dropdown-avatar" />
                    <span>{{ user.nickname || user.username }}</span>
                  </div>
                </div>

                <!-- #话题标签下拉框 -->
                <div class="tag-dropdown" v-if="showTagDropdown">
                  <div class="dropdown-title">选择话题</div>
                  <div class="dropdown-loading" v-if="loadingTags">加载中...</div>
                  <div class="dropdown-empty" v-else-if="!filteredTags.length">未找到相关话题</div>
                  <div
                    v-else
                    class="dropdown-item"
                    v-for="tag in filteredTags"
                    :key="tag.name"
                    @click="selectTag(tag)"
                  >
                    <span class="tag-icon">#</span>
                    <span>{{ tag.name }}</span>
                    <span class="tag-count">{{ tag.count || 0 }}人讨论</span>
                  </div>
                  <div class="dropdown-create" v-if="tagQuery && !tagExists" @click="createNewTag">
                    <span>创建话题 #{{ tagQuery }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 位置信息 -->
            <div class="form-item">
              <label class="form-label">位置</label>
              <van-field
                v-model="formData.location"
                placeholder="添加位置信息"
                class="modern-field"
                left-icon="location-o"
              />
            </div>
          </div>

          <div class="settings-section">
            <h2 class="section-title">发布设置</h2>

            <!-- 可见性设置 -->
            <div class="form-item">
              <label class="form-label">可见性</label>
              <div class="visibility-options">
                <div
                  class="visibility-option"
                  :class="{ 'active': formData.visibility === 1 }"
                  @click="formData.visibility = 1"
                >
                  <van-icon name="eye-o" />
                  <span>公开</span>
                </div>
                <div
                  class="visibility-option"
                  :class="{ 'active': formData.visibility === 2 }"
                  @click="formData.visibility = 2"
                >
                  <van-icon name="friends-o" />
                  <span>好友可见</span>
                </div>
                <div
                  class="visibility-option"
                  :class="{ 'active': formData.visibility === 0 }"
                  @click="formData.visibility = 0"
                >
                  <van-icon name="lock" />
                  <span>私密</span>
                </div>
              </div>
            </div>

            <!-- 权限设置 -->
            <div class="form-item">
              <label class="form-label">权限设置</label>
              <div class="permission-options">
                <van-checkbox v-model="formData.allowComment" icon-size="20px" checked-color="#3498db">
                  <span class="permission-label">允许评论</span>
                </van-checkbox>
                <van-checkbox v-model="formData.allowDownload" icon-size="20px" checked-color="#3498db">
                  <span class="permission-label">允许下载</span>
                </van-checkbox>
              </div>
            </div>
          </div>
        </van-form>
      </div>
      <!-- 上传进度 -->
      <div class="upload-progress" v-if="isUploading">
        <van-progress
          :percentage="uploadProgress"
          :show-pivot="true"
          color="#3498db"
        />
        <div class="progress-text">
          正在发布照片笔记... {{ uploadedCount }}/{{ fileList.length }} 张照片
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showSuccessToast } from 'vant';
import { uploadPhotoForNote, publishPhotoNote } from '@/api/photo';
import type { AxiosProgressEvent } from 'axios';

interface User {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
}

interface Tag {
  name: string;
  count?: number;
}

interface MentionInfo {
  startPos: number;
  endPos: number;
  query: string;
}

const router = useRouter();

// 基本引用
const descriptionEditor = ref<HTMLDivElement | null>(null);
const selectedFiles = ref<any[]>([]);
const fileList = ref<any[]>([]);
const currentFileIndex = ref(0);
const isUploading = ref(false);
const uploadProgress = ref(0);
const uploadedCount = ref(0);
const showPreview = ref(false);

// 检测是否为移动设备
const isMobile = ref(window.innerWidth <= 768);
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth <= 768;
});

// 表单数据（无论是否上传照片都可用）
const formData = ref({
  title: '',
  description: '',
  location: '',
  tags: [] as string[],
  mentions: [] as any[],
  visibility: 1, // 默认公开
  allowComment: true,
  allowDownload: true
});

// @用户相关
const showUserDropdown = ref(false);
const loadingUsers = ref(false);
const filteredUsers = ref<User[]>([]);
const userQuery = ref('');
const currentMention = ref<MentionInfo | null>(null);

// #话题标签相关
const showTagDropdown = ref(false);
const loadingTags = ref(false);
const filteredTags = ref<Tag[]>([]);
const tagQuery = ref('');
const tagExists = ref(false);
const currentTag = ref<MentionInfo | null>(null);

// 返回上一页
const goBack = () => {
  // 释放所有预览URL
  selectedFiles.value.forEach(file => {
    URL.revokeObjectURL(file.preview);
  });

  router.back();
};

// 当前选中的文件
const currentFile = computed(() => {
  if (selectedFiles.value.length === 0) {
    return null;
  }
  return selectedFiles.value[currentFileIndex.value];
});

// 是否可以上传
const canUpload = computed(() => {
  if (fileList.value.length === 0) {
    return false;
  }

  // 检查是否有标题
  return !!formData.value.title;
});



// Vant Uploader 相关方法
// 上传前检查
const beforeUploadCheck = (file: File | File[]) => {
  // 如果是数组，检查每个文件
  if (Array.isArray(file)) {
    for (const f of file) {
      const result = checkSingleFile(f);
      if (!result) return false;
    }
    return true;
  } else {
    // 单个文件
    return checkSingleFile(file);
  }
};

// 检查单个文件
const checkSingleFile = (file: File) => {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    showToast('请上传图片文件');
    return false;
  }

  // 检查文件大小
  if (file.size > 10 * 1024 * 1024) {
    showToast('图片大小不能超过10MB');
    return false;
  }

  return true;
};

// 上传后处理
const afterUpload = (file: any) => {
  // 如果是多文件上传，file是数组
  const fileArray = Array.isArray(file) ? file : [file];

  // 处理每个文件，添加额外信息
  fileArray.forEach(item => {
    item.title = '';
    item.description = '';
    item.location = '';
    item.tags = [];
    item.mentions = [];
    item.visibility = 1; // 默认公开
    item.allowComment = true;
    item.allowDownload = true;
  });

  // 同步到selectedFiles以保持兼容性
  selectedFiles.value = fileList.value.map(item => ({
    ...item,
    name: item.file.name,
    size: item.file.size,
    type: item.file.type,
    preview: item.url || item.content,
    file: item.file
  }));
};

// 删除文件
const onDeleteFile = () => {
  // 同步到selectedFiles以保持兼容性
  selectedFiles.value = fileList.value.map(item => ({
    ...item,
    name: item.file.name,
    size: item.file.size,
    type: item.file.type,
    preview: item.url || item.content,
    file: item.file
  }));
};

// 预览文件
const onPreviewFile = (file: any) => {
  // 找到对应的索引
  const index = fileList.value.findIndex(item => item === file);
  if (index !== -1) {
    currentFileIndex.value = index;
    showPreview.value = true;
  }
};



// 处理描述输入
const handleDescriptionInput = (event: Event) => {
  const target = event.target as HTMLDivElement;
  if (!target) return;

  // 保存描述内容到表单数据
  formData.value.description = target.innerHTML;

  // 如果有选中的文件，也保存到当前文件
  if (currentFile.value) {
    currentFile.value.description = target.innerHTML;
  }
};

// 处理描述框键盘事件
const handleDescriptionKeyup = (event: KeyboardEvent) => {
  const target = event.target as HTMLDivElement;
  if (!target) return;

  const selection = window.getSelection();
  if (!selection || !selection.rangeCount) return;

  const range = selection.getRangeAt(0);
  const preCaretRange = range.cloneRange();
  preCaretRange.selectNodeContents(target);
  preCaretRange.setEnd(range.endContainer, range.endOffset);
  const caretOffset = preCaretRange.toString().length;

  // 获取光标前的文本
  const text = target.textContent || '';
  const textBeforeCaret = text.substring(0, caretOffset);

  // 检查@用户
  const atMatch = textBeforeCaret.match(/@([^@#\s]*)$/);
  if (atMatch) {
    const query = atMatch[1];
    const startPos = caretOffset - query.length - 1;
    userQuery.value = query;
    currentMention.value = { startPos, endPos: caretOffset, query };
    searchForUsers(query);
    showUserDropdown.value = true;
    showTagDropdown.value = false;
    return;
  }

  // 检查#话题标签
  const hashMatch = textBeforeCaret.match(/#([^@#\s]*)$/);
  if (hashMatch) {
    const query = hashMatch[1];
    const startPos = caretOffset - query.length - 1;
    tagQuery.value = query;
    currentTag.value = { startPos, endPos: caretOffset, query };
    searchForTags(query);
    showTagDropdown.value = true;
    showUserDropdown.value = false;
    return;
  }

  // 如果没有匹配，隐藏下拉框
  hideDropdown();
};

// 隐藏下拉框
const hideDropdown = () => {
  setTimeout(() => {
    showUserDropdown.value = false;
    showTagDropdown.value = false;
  }, 200);
};

// 搜索用户
const searchForUsers = async (query: string) => {
  if (!query.trim()) {
    filteredUsers.value = [];
    return;
  }

  loadingUsers.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));

    // 模拟数据
    const mockUsers: User[] = [
      { id: 1, username: 'user1', nickname: '用户1', avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
      { id: 2, username: 'user2', nickname: '用户2', avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
      { id: 3, username: 'user3', nickname: '用户3', avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
      { id: 4, username: 'user4', nickname: '用户4', avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
      { id: 5, username: 'user5', nickname: '用户5', avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
    ];

    // 过滤用户
    filteredUsers.value = mockUsers.filter(user =>
      user.username.includes(query) ||
      (user.nickname && user.nickname.includes(query))
    ).slice(0, 5);
  } catch (error) {
    console.error('搜索用户失败', error);
    filteredUsers.value = [];
  } finally {
    loadingUsers.value = false;
  }
};

// 搜索标签
const searchForTags = async (query: string) => {
  if (!query.trim()) {
    filteredTags.value = [];
    return;
  }

  loadingTags.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));

    // 模拟数据
    const mockTags: Tag[] = [
      { name: '风景摄影', count: 9876 },
      { name: '人像写真', count: 8765 },
      { name: '美食探店', count: 7654 },
      { name: '旅行日记', count: 6543 },
      { name: '城市建筑', count: 5432 },
      { name: '宠物萌宠', count: 4321 },
      { name: '时尚穿搭', count: 3210 },
      { name: '生活方式', count: 2109 },
      { name: '创意设计', count: 1987 },
      { name: '自然风光', count: 1876 },
    ];

    // 过滤标签
    filteredTags.value = mockTags.filter(tag =>
      tag.name.includes(query)
    ).slice(0, 5);

    // 检查标签是否存在
    tagExists.value = filteredTags.value.some(tag =>
      tag.name.toLowerCase() === query.toLowerCase()
    );
  } catch (error) {
    console.error('搜索标签失败', error);
    filteredTags.value = [];
    tagExists.value = false;
  } finally {
    loadingTags.value = false;
  }
};

// 选择用户
const selectUser = (user: User) => {
  if (!descriptionEditor.value || !currentMention.value) return;

  const editor = descriptionEditor.value;
  const html = editor.innerHTML;
  const mention = currentMention.value;

  // 创建用户提及标签
  const userTag = `<span class="mention-tag" data-user-id="${user.id}">@${user.nickname || user.username}</span>`;

  // 替换文本
  const beforeMention = html.substring(0, mention.startPos);
  const afterMention = html.substring(mention.endPos);
  editor.innerHTML = beforeMention + userTag + afterMention;

  // 添加到表单数据的提及列表
  const mentionData = {
    userId: user.id,
    username: user.username,
    nickname: user.nickname
  };

  formData.value.mentions.push(mentionData);

  // 如果有选中的文件，也添加到当前文件
  if (currentFile.value) {
    if (!currentFile.value.mentions) {
      currentFile.value.mentions = [];
    }
    currentFile.value.mentions.push(mentionData);
  }

  // 隐藏下拉框
  showUserDropdown.value = false;

  // 将光标移动到标签后面
  nextTick(() => {
    placeCursorAfterNode(editor, userTag);
  });
};

// 选择标签
const selectTag = (tag: Tag) => {
  if (!descriptionEditor.value || !currentTag.value) return;

  const editor = descriptionEditor.value;
  const html = editor.innerHTML;
  const tagInfo = currentTag.value;

  // 创建话题标签
  const topicTag = `<span class="topic-tag" data-tag-name="${tag.name}">#${tag.name}</span>`;

  // 替换文本
  const beforeTag = html.substring(0, tagInfo.startPos);
  const afterTag = html.substring(tagInfo.endPos);
  editor.innerHTML = beforeTag + topicTag + afterTag;

  // 添加到表单数据的标签列表
  if (!formData.value.tags.includes(tag.name)) {
    formData.value.tags.push(tag.name);
  }

  // 如果有选中的文件，也添加到当前文件
  if (currentFile.value && !currentFile.value.tags.includes(tag.name)) {
    currentFile.value.tags.push(tag.name);
  }

  // 隐藏下拉框
  showTagDropdown.value = false;

  // 将光标移动到标签后面
  nextTick(() => {
    placeCursorAfterNode(editor, topicTag);
  });
};

// 创建新标签
const createNewTag = () => {
  if (!tagQuery.value.trim() || !currentTag.value) return;

  const newTag: Tag = {
    name: tagQuery.value.trim(),
    count: 0
  };

  selectTag(newTag);
};

// 将光标放在节点后面
const placeCursorAfterNode = (editor: HTMLElement, html: string) => {
  // 创建临时元素来查找插入的节点
  const temp = document.createElement('div');
  temp.innerHTML = html;
  const insertedNode = temp.firstChild;

  // 查找编辑器中对应的节点
  const nodes = editor.querySelectorAll(insertedNode?.nodeName || '');
  if (nodes.length === 0) return;

  const lastInsertedNode = nodes[nodes.length - 1];

  // 设置光标位置
  const selection = window.getSelection();
  const range = document.createRange();
  range.setStartAfter(lastInsertedNode);
  range.collapse(true);

  selection?.removeAllRanges();
  selection?.addRange(range);
};



// 保存草稿
const saveDraft = async () => {
  try {
    // 使用表单数据保存草稿
    const draftData = {
      title: formData.value.title || '未命名草稿',
      description: formData.value.description || '',
      location: formData.value.location || '',
      tags: formData.value.tags || [],
      mentions: formData.value.mentions || [],
      visibility: formData.value.visibility,
      allowComment: formData.value.allowComment ? 1 : 0,
      allowDownload: formData.value.allowDownload ? 1 : 0,
      savedAt: new Date().toISOString()
    };

    // 如果有上传的照片，添加照片信息
    const fileData = fileList.value.map(file => ({
      fileName: file.file.name,
      fileSize: file.file.size,
      fileType: file.file.type
    }));

    // 保存到本地存储
    try {
      const drafts = JSON.parse(localStorage.getItem('photo_drafts') || '[]');

      // 添加到草稿列表
      drafts.unshift({
        id: Date.now(),
        data: draftData,
        files: fileData,
        savedAt: new Date().toISOString()
      });

      // 限制草稿数量
      if (drafts.length > 10) {
        drafts.pop();
      }

      // 保存到本地存储
      localStorage.setItem('photo_drafts', JSON.stringify(drafts));

      showSuccessToast('草稿保存成功');
    } catch (error) {
      console.error('本地保存草稿失败', error);
      showToast('保存草稿失败');
    }
  } catch (error) {
    console.error('保存草稿失败', error);
    showToast('保存草稿失败');
  }
};

// 获取图片宽度
const getImageWidth = (file: File): Promise<number> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve(img.width);
      URL.revokeObjectURL(img.src);
    };
    img.src = URL.createObjectURL(file);
  });
};

// 获取图片高度
const getImageHeight = (file: File): Promise<number> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve(img.height);
      URL.revokeObjectURL(img.src);
    };
    img.src = URL.createObjectURL(file);
  });
};

// 发布照片笔记
const uploadFiles = async () => {
  // 如果没有上传照片，提示用户
  if (fileList.value.length === 0) {
    showToast('请先上传照片');
    return;
  }

  // 如果有照片但标题为空，提示用户
  if (!canUpload.value) {
    showToast('请填写照片笔记标题');
    return;
  }

  isUploading.value = true;
  uploadProgress.value = 0;
  uploadedCount.value = 0;

  try {
    const totalFiles = fileList.value.length;
    const uploadedPhotos: any[] = [];

    // 提取描述中的@用户和#话题标签
    const description = formData.value.description || '';
    const mentionRegex = /<span class="mention-tag" data-user-id="(\d+)">@([^<]+)<\/span>/g;
    const topicRegex = /<span class="topic-tag" data-tag-name="([^"]+)">#([^<]+)<\/span>/g;

    let mentionMatch: RegExpExecArray | null;
    const mentions = [];
    while ((mentionMatch = mentionRegex.exec(description)) !== null) {
      mentions.push({
        userId: parseInt(mentionMatch[1]),
        username: mentionMatch[2]
      });
    }

    let topicMatch: RegExpExecArray | null;
    const topics = [];
    while ((topicMatch = topicRegex.exec(description)) !== null) {
      topics.push(topicMatch[1]);
    }

    // 确保标签列表包含描述中的所有话题
    const tags = formData.value.tags || [];
    topics.forEach(topic => {
      if (!tags.includes(topic)) {
        tags.push(topic);
      }
    });

    // 逐个上传照片到七牛云
    for (let i = 0; i < totalFiles; i++) {
      const file = fileList.value[i].file;

      try {
        console.log(`正在上传第${i+1}张照片...`);

        // 上传照片到七牛云（仅获取URL，不保存到数据库）
        const uploadResult = await uploadPhotoForNote(file, (event: AxiosProgressEvent) => {
          if (event.total) {
            // 计算总体进度
            const singleFileProgress = Math.round((event.loaded / event.total) * 100);
            const overallProgress = Math.round(((i + singleFileProgress / 100) / totalFiles) * 100);
            uploadProgress.value = overallProgress;
          }
        });

        uploadedPhotos.push({
          url: uploadResult.url,
          thumbnailUrl: uploadResult.thumbnailUrl,
          width: uploadResult.width,
          height: uploadResult.height,
          sortOrder: i + 1,
          storagePath: uploadResult.key,
          originalFilename: uploadResult.originalFilename,
          fileSize: null, // 七牛云上传无法直接获取文件大小
          fileType: file.type
        });

        uploadedCount.value++;
        console.log(`第${i+1}张照片上传成功`);

      } catch (error) {
        console.error(`第${i+1}张照片上传失败:`, error);
        throw error;
      }
    }

    // 所有照片上传完成，发布照片笔记
    console.log('所有照片上传完成，准备发布照片笔记...');

    const publishData = {
      title: formData.value.title,
      content: description,
      photos: uploadedPhotos,
      visibility: formData.value.visibility,
      allowComment: formData.value.allowComment ? 1 : 0,
      allowDownload: formData.value.allowDownload ? 1 : 0,
      location: formData.value.location,
      longitude: null,
      latitude: null
    };

    try {
      const result = await publishPhotoNote(publishData);

      showSuccessToast('照片笔记发布成功！');

      // 跳转到照片笔记详情页
      setTimeout(() => {
        if (result && result.data) {
          console.log('跳转到照片笔记详情页，ID:', result.data);
          router.push(`/photo-note/${result.data}`);
        } else {
          console.warn('发布成功但未获取到照片笔记ID');
          router.push('/user/profile');
        }
      }, 1500);

    } catch (error) {
      console.error('发布照片笔记失败:', error);
      showToast('发布失败，请重试');
      isUploading.value = false;
      return;
    }

    // 上传完成
    // 注意：跳转逻辑已经在各自的上传成功处理中实现
  } catch (error) {
    console.error('发布失败', error);
    showToast('发布失败，请重试');
  } finally {
    isUploading.value = false;
  }
};

// 截断文件名
const truncateFileName = (name: string, maxLength = 20) => {
  if (name.length <= maxLength) return name;

  const extension = name.split('.').pop() || '';
  const nameWithoutExt = name.substring(0, name.length - extension.length - 1);

  return nameWithoutExt.substring(0, maxLength - extension.length - 3) + '...' + extension;
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B';
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
  return (size / (1024 * 1024)).toFixed(1) + ' MB';
};
</script>

<style lang="scss" scoped>
.upload-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20px;

  .header {
    display: none; // 隐藏旧的头部
  }

  .upload-content {
    max-width: 1000px;
    margin: 0 auto;
  }

  .edit-card {
    background-color: white;
    margin-bottom: 24px;
    overflow: hidden;

    @media (min-width: 769px) {
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }

    .upload-section, .content-section, .settings-section {
      padding: 24px;

      @media (min-width: 769px) {
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }
      }

      @media (max-width: 768px) {
        padding: 16px;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 0 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;

      @media (max-width: 768px) {
        margin: 0 0 16px;
        padding-bottom: 8px;
      }

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;

        @media (max-width: 768px) {
          font-size: 16px;
        }
      }

      .action-buttons {
        display: flex;
        gap: 10px;

        .action-button {
          height: 36px;
          font-size: 14px;

          &.draft-button {
            color: #3498db;
            border-color: #3498db;
          }

          &.publish-button {
            background-color: #3498db;
            border-color: #3498db;
          }
        }
      }
    }

    .subsection-title {
      font-size: 16px;
      font-weight: 500;
      color: #555;
      margin: 16px 0 12px;

      @media (max-width: 768px) {
        font-size: 15px;
        margin: 12px 0 8px;
      }
    }

    .custom-uploader {
      :deep(.van-uploader__wrapper) {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        @media (max-width: 768px) {
          gap: 10px;
        }
      }

      :deep(.van-uploader__preview) {
        margin: 0;
        width: 140px;
        height: 140px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        @media (max-width: 768px) {
          width: 110px;
          height: 110px;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
      }

      :deep(.van-uploader__preview-image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      :deep(.van-uploader__upload) {
        width: 140px !important;
        height: 140px !important;
        margin: 0;

        @media (max-width: 768px) {
          width: 110px !important;
          height: 110px !important;
        }
      }

      :deep(.van-uploader__preview-delete) {
        top: 8px;
        right: 8px;
        width: 24px;
        height: 24px;
        background-color: rgba(0, 0, 0, 0.6);

        &:hover {
          background-color: rgba(255, 0, 0, 0.7);
        }

        .van-uploader__preview-delete-icon {
          font-size: 14px;
        }
      }
    }

    .upload-area {
      border: 2px dashed #e0e0e0;
      border-radius: 8px;
      padding: 0;
      margin: 0;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #fafafa;
      width: 140px;
      height: 140px;
      display: flex;
      align-items: center;
      justify-content: center;

      @media (max-width: 768px) {
        width: 110px;
        height: 110px;
      }

      &:hover {
        border-color: #3498db;
        background-color: #f0f7fc;
      }

      .file-input {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }

      .upload-placeholder {
        text-align: center;
        transition: all 0.3s;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &.is-dragover {
          background-color: rgba(52, 152, 219, 0.1);
        }

        .upload-icon {
          margin-bottom: 8px;
          color: #3498db;
          font-size: 32px !important;

          @media (max-width: 768px) {
            margin-bottom: 6px;
            font-size: 28px !important;
          }
        }

        .upload-text {
          font-size: 14px;
          margin: 0 0 6px;
          color: #333;
          font-weight: 500;
          padding: 0 8px;
          text-align: center;

          @media (max-width: 768px) {
            font-size: 12px;
            margin: 0 0 4px;
          }
        }

        .upload-hint {
          font-size: 12px;
          color: #888;
          padding: 0 8px;
          text-align: center;

          @media (max-width: 768px) {
            font-size: 10px;
          }
        }
      }
    }

    .selected-files-container {
      margin-bottom: 24px;

      @media (max-width: 768px) {
        margin-bottom: 16px;
      }
    }

    .selected-files {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 16px;

      @media (max-width: 768px) {
        gap: 10px;
        margin-bottom: 12px;
      }

      .file-item {
        position: relative;
        width: 140px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        @media (max-width: 768px) {
          width: 110px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        &.active {
          box-shadow: 0 0 0 3px #3498db, 0 4px 12px rgba(52, 152, 219, 0.2);
          transform: translateY(-2px);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .file-preview {
          width: 100%;
          height: 100px;
          object-fit: cover;

          @media (max-width: 768px) {
            height: 80px;
          }
        }

        .file-info {
          padding: 8px 10px;
          background-color: white;

          .file-name {
            display: block;
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #333;
          }

          .file-size {
            display: block;
            font-size: 12px;
            color: #888;
            margin-top: 2px;
          }
        }

        .remove-btn {
          position: absolute;
          top: 8px;
          right: 8px;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          opacity: 0;
          transition: opacity 0.3s, background-color 0.2s;

          &:hover {
            background-color: rgba(255, 0, 0, 0.7);
          }
        }

        &:hover .remove-btn {
          opacity: 1;
        }
      }

      .add-more {
        width: 140px;
        height: 140px;
        border: 2px dashed #e0e0e0;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background-color: #fafafa;
        transition: all 0.3s ease;

        @media (max-width: 768px) {
          width: 110px;
          height: 110px;
        }

        .add-icon {
          margin-bottom: 8px;
          color: #3498db;
        }

        span {
          font-size: 14px;
          color: #666;
        }

        &:hover {
          border-color: #3498db;
          background-color: #f0f7fc;

          .add-icon, span {
            color: #3498db;
          }
        }
      }
    }

    .preview-popup {
      width: 90%;
      height: 90%;
      background-color: rgba(0, 0, 0, 0.9);
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 0;

      .preview-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .preview-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      :deep(.van-popup__close-icon) {
        color: white;
        font-size: 24px;
        top: 16px;
        right: 16px;
      }
    }

    .form-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      @media (max-width: 768px) {
        margin-bottom: 16px;
      }

      .form-label {
        display: block;
        font-size: 15px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;

        @media (max-width: 768px) {
          font-size: 14px;
          margin-bottom: 6px;
        }
      }
    }

    .modern-field {
      background-color: #f9f9f9;
      border-radius: 8px;

      &:focus-within {
        background-color: #f0f7fc;
      }
    }

    .visibility-options {
      display: flex;
      gap: 16px;

      @media (max-width: 768px) {
        gap: 10px;
      }

      .visibility-option {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 16px;
        border-radius: 8px;
        background-color: #f9f9f9;
        cursor: pointer;
        transition: all 0.3s ease;

        @media (max-width: 768px) {
          padding: 12px 8px;
        }

        .van-icon {
          font-size: 24px;
          margin-bottom: 8px;
          color: #666;

          @media (max-width: 768px) {
            font-size: 20px;
            margin-bottom: 6px;
          }
        }

        span {
          font-size: 14px;
          color: #666;

          @media (max-width: 768px) {
            font-size: 12px;
          }
        }

        &:hover {
          background-color: #f0f7fc;

          .van-icon, span {
            color: #3498db;
          }
        }

        &.active {
          background-color: #e6f7ff;
          border: 1px solid #3498db;

          .van-icon, span {
            color: #3498db;
            font-weight: 500;
          }
        }
      }
    }

    .permission-options {
      display: flex;
      gap: 24px;

      @media (max-width: 768px) {
        gap: 16px;
      }

      .permission-label {
        font-size: 14px;
        color: #333;
        margin-left: 4px;

        @media (max-width: 768px) {
          font-size: 13px;
        }
      }
    }
  }

  .description-field {
    position: relative;
    width: 100%;

    .description-editor {
      min-height: 120px;
      padding: 12px 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      outline: none;
      font-size: 15px;
      line-height: 1.6;
      color: #333;
      background-color: #f9f9f9;
      transition: all 0.3s ease;
      overflow-y: auto;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);

      @media (max-width: 768px) {
        min-height: 100px;
        padding: 10px 12px;
        font-size: 14px;
        border-radius: 6px;
        box-shadow: none;
      }

      &:focus {
        border-color: #3498db;
        background-color: #f0f7fc;
        box-shadow: inset 0 1px 3px rgba(52, 152, 219, 0.1);
      }

      &:empty:before {
        content: attr(placeholder);
        color: #aaa;
        font-style: italic;
      }

      .mention-tag, .topic-tag {
        display: inline-block;
        padding: 2px 6px;
        margin: 0 2px;
        border-radius: 4px;
        white-space: nowrap;
        cursor: pointer;
        font-weight: 500;
      }

      .mention-tag {
        background-color: rgba(64, 158, 255, 0.15);
        color: #1989fa;
      }

      .topic-tag {
        background-color: rgba(255, 87, 34, 0.15);
        color: #ff5722;
      }
    }

    .mention-dropdown, .tag-dropdown {
      position: absolute;
      z-index: 100;
      width: 280px;
      max-height: 320px;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      margin-top: 8px;
      border: 1px solid #eee;

      .dropdown-title {
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 600;
        color: #666;
        background-color: #f9f9f9;
        border-bottom: 1px solid #eee;
      }

      .dropdown-loading, .dropdown-empty {
        padding: 16px;
        text-align: center;
        color: #888;
        font-size: 14px;
      }

      .dropdown-item {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f0f7fc;
        }

        .dropdown-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 12px;
          object-fit: cover;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tag-icon {
          font-size: 18px;
          color: #ff5722;
          margin-right: 12px;
          font-weight: bold;
        }

        .tag-count {
          margin-left: auto;
          font-size: 13px;
          color: #888;
          background-color: #f5f5f5;
          padding: 2px 8px;
          border-radius: 12px;
        }
      }

      .dropdown-create {
        padding: 12px 16px;
        border-top: 1px solid #eee;
        color: #3498db;
        cursor: pointer;
        text-align: center;
        font-weight: 500;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f0f7fc;
        }
      }
    }
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 16px;

    .tag-item {
      background-color: #f0f7fc;
      color: #3498db;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 14px;
      font-weight: 500;
      display: inline-flex;
      align-items: center;

      .van-icon {
        margin-left: 6px;
        font-size: 16px;
      }

      &:hover {
        background-color: #e6f7ff;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .header-action-button {
      height: 32px;
      font-size: 14px;

      &.draft-button {
        color: #3498db;
        border-color: #3498db;
      }

      &.publish-button {
        background-color: #3498db;
        border-color: #3498db;
      }
    }
  }

  .upload-actions {
    display: flex;
    justify-content: center;
    margin: 0 0 24px;
    gap: 20px;

    @media (max-width: 768px) {
      margin: 0 0 16px;
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 10;
    }

    .action-button {
      flex: 0;
      height: 48px;
      border-radius: 50px;
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;

      @media (max-width: 768px) {
        height: 56px;
        width: 56px;
        padding: 0;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
      }

      .van-icon {
        margin-right: 8px;
        font-size: 18px;

        @media (max-width: 768px) {
          font-size: 24px;
          margin-right: 0;
        }
      }

      span {
        @media (max-width: 768px) {
          display: none;
        }
      }
    }

    .cancel-button {
      border-color: #e0e0e0;
      color: #666;

      &:hover {
        border-color: #d0d0d0;
        color: #333;
        background-color: #f9f9f9;
      }
    }

    .draft-button {
      border-color: #3498db;
      color: #3498db;

      &:hover {
        background-color: #f0f7fc;
      }
    }

    .publish-button {
      background-color: #3498db;
      border-color: #3498db;

      &:hover:not(:disabled) {
        background-color: #2980b9;
        border-color: #2980b9;
      }

      &:disabled {
        opacity: 0.6;
      }
    }
  }

  .upload-progress {
    margin: 0 0 24px;
    background-color: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

    @media (max-width: 768px) {
      margin: 0 0 16px;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .progress-text {
      margin-top: 12px;
      text-align: center;
      font-size: 15px;
      color: #666;
      font-weight: 500;

      @media (max-width: 768px) {
        margin-top: 8px;
        font-size: 14px;
      }
    }
  }
}
</style>
