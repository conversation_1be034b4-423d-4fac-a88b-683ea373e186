package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.common.Result;
import com.phototagmoment.entity.IdentityVerification;
import com.phototagmoment.service.ExtendedIdentityVerificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员实名认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/identity-verification")
@Tag(name = "管理员实名认证", description = "管理员实名认证相关接口")
public class AdminIdentityVerificationController {

    @Autowired
    @Qualifier("extendedIdentityVerificationServiceImpl")
    private ExtendedIdentityVerificationService identityVerificationService;

    /**
     * 获取待审核的实名认证列表
     *
     * @param page 页码
     * @param size 每页大小
     * @return 实名认证列表
     */
    @GetMapping("/pending")
    @Operation(summary = "获取待审核的实名认证列表", description = "分页获取待审核的实名认证列表")
    public Result<IPage<IdentityVerification>> getPendingVerifications(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Page<IdentityVerification> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<IdentityVerification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdentityVerification::getStatus, 0)  // 0表示待审核
                .orderByDesc(IdentityVerification::getCreatedAt);
        IPage<IdentityVerification> verificationPage = identityVerificationService.page(pageParam, queryWrapper);
        return Result.success(verificationPage);
    }

    /**
     * 获取已审核的实名认证列表
     *
     * @param page 页码
     * @param size 每页大小
     * @return 实名认证列表
     */
    @GetMapping("/reviewed")
    @Operation(summary = "获取已审核的实名认证列表", description = "分页获取已审核的实名认证列表")
    public Result<IPage<IdentityVerification>> getReviewedVerifications(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Page<IdentityVerification> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<IdentityVerification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(IdentityVerification::getStatus, 0)  // 非待审核
                .orderByDesc(IdentityVerification::getUpdatedAt);
        IPage<IdentityVerification> verificationPage = identityVerificationService.page(pageParam, queryWrapper);
        return Result.success(verificationPage);
    }

    /**
     * 获取实名认证详情
     *
     * @param id 认证记录ID
     * @return 实名认证详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取实名认证详情", description = "根据ID获取实名认证详情")
    public Result<IdentityVerification> getVerificationDetail(@Parameter(description = "认证记录ID") @PathVariable Long id) {
        IdentityVerification verification = identityVerificationService.getById(id);
        if (verification == null) {
            return Result.fail("未找到实名认证记录");
        }
        return Result.success(verification);
    }

    /**
     * 审核实名认证
     *
     * @param id     认证记录ID
     * @param status 审核状态（1：通过，2：拒绝）
     * @param reason 拒绝原因
     * @return 审核结果
     */
    @PostMapping("/review")
    @Operation(summary = "审核实名认证", description = "管理员审核实名认证")
    public Result<Boolean> reviewVerification(
            @Parameter(description = "认证记录ID") @RequestParam Long id,
            @Parameter(description = "审核状态（1：通过，2：拒绝）") @RequestParam Integer status,
            @Parameter(description = "拒绝原因") @RequestParam(required = false) String reason) {
        if (status == null || (status != 1 && status != 2)) {
            return Result.fail("审核状态不正确");
        }

        if (status == 2 && (reason == null || reason.isEmpty())) {
            return Result.fail("拒绝原因不能为空");
        }

        boolean result = identityVerificationService.reviewVerification(id, status, reason);
        return Result.success(result, "审核成功");
    }
}
