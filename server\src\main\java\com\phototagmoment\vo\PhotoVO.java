package com.phototagmoment.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 照片视图对象
 */
@Data
public class PhotoVO {

    /**
     * 照片ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 照片标题
     */
    private String title;

    /**
     * 照片描述
     */
    private String description;

    /**
     * 照片URL
     */
    private String url;

    /**
     * 照片缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 照片位置
     */
    private String location;

    /**
     * 照片标签
     */
    private List<String> tags;

    /**
     * 照片状态（0：待审核，1：已发布，2：已拒绝）
     */
    private Integer status;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 是否点赞
     */
    private Boolean isLiked;

    /**
     * 是否收藏
     */
    private Boolean isCollected;

    /**
     * 是否关注了作者
     */
    private Boolean isFollowed;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 是否是分组照片
     */
    private Boolean isGrouped = false;

    /**
     * 同组照片列表（最多4张）
     * 使用 @JsonIgnore 防止循环引用导致的无限递归
     */
    @JsonIgnore
    private List<PhotoVO> groupPhotos = new ArrayList<>();

    /**
     * 同组照片数量
     */
    private Integer groupPhotoCount = 0;

    /**
     * 同组照片ID列表
     */
    private List<Long> groupPhotoIds = new ArrayList<>();
}
