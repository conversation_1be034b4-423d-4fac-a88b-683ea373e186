package com.phototagmoment.config;

import com.phototagmoment.service.StorageService;
import com.phototagmoment.service.impl.LocalStorageServiceImpl;
import com.phototagmoment.service.impl.QiniuStorageServiceAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.PostConstruct;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 存储配置类
 */
@Slf4j
@Configuration
public class StorageConfig {

    @Value("${storage.local.path}")
    private String storagePath;

    @Value("${storage.local.enabled:true}")
    private boolean localStorageEnabled;

    @Autowired
    private QiniuConfig qiniuConfig;

    /**
     * 初始化存储目录
     */
    @PostConstruct
    public void init() {
        if (!localStorageEnabled) {
            log.info("本地存储未启用，跳过目录初始化");
            return;
        }

        log.info("初始化存储目录: {}", storagePath);

        try {
            // 创建基本目录
            Path basePath = Paths.get(storagePath);
            if (!Files.exists(basePath)) {
                Files.createDirectories(basePath);
                log.info("创建基本存储目录: {}", basePath);
            }

            // 创建上传目录
            Path uploadsPath = Paths.get(storagePath, "uploads");
            if (!Files.exists(uploadsPath)) {
                Files.createDirectories(uploadsPath);
                log.info("创建上传目录: {}", uploadsPath);
            }

            // 创建照片目录
            Path photosPath = Paths.get(storagePath, "photos");
            if (!Files.exists(photosPath)) {
                Files.createDirectories(photosPath);
                log.info("创建照片目录: {}", photosPath);
            }

            // 创建临时目录
            Path tempPath = Paths.get(storagePath, "temp");
            if (!Files.exists(tempPath)) {
                Files.createDirectories(tempPath);
                log.info("创建临时目录: {}", tempPath);
            }

            log.info("存储目录初始化完成");
        } catch (Exception e) {
            log.error("初始化存储目录失败", e);
            throw new RuntimeException("初始化存储目录失败", e);
        }
    }

    /**
     * 本地存储服务
     * 当本地存储启用时使用本地存储
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "storage.local.enabled", havingValue = "true", matchIfMissing = true)
    public StorageService localStorageService() {
        return new LocalStorageServiceImpl();
    }

    /**
     * 七牛云存储服务
     * 当七牛云存储启用时使用七牛云存储
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "storage.qiniu.enabled", havingValue = "true")
    public StorageService qiniuStorageService() {
        return new QiniuStorageServiceAdapter();
    }
}
