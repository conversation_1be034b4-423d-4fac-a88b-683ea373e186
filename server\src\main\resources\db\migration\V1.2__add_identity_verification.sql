-- 添加用户表实名认证字段
-- 检查列是否存在，如果不存在则添加
SET @exist_is_verified = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_user' AND column_name = 'is_verified');
SET @exist_real_name = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_user' AND column_name = 'real_name');

SET @add_is_verified = IF(@exist_is_verified = 0, 'ALTER TABLE ptm_user ADD COLUMN `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT "是否实名认证：0-未认证，1-已认证" AFTER `status`', 'SELECT "Column is_verified already exists"');
SET @add_real_name = IF(@exist_real_name = 0, 'ALTER TABLE ptm_user ADD COLUMN `real_name` varchar(50) DEFAULT NULL COMMENT "真实姓名" AFTER `is_verified`', 'SELECT "Column real_name already exists"');

PREPARE stmt1 FROM @add_is_verified;
EXECUTE stmt1;
DEALLOCATE PREPARE stmt1;

PREPARE stmt2 FROM @add_real_name;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 创建实名认证表
CREATE TABLE IF NOT EXISTS `ptm_identity_verification` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '认证ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
    `id_card` varchar(18) NOT NULL COMMENT '身份证号',
    `id_card_front_url` varchar(255) DEFAULT NULL COMMENT '身份证正面照片URL',
    `id_card_back_url` varchar(255) DEFAULT NULL COMMENT '身份证背面照片URL',
    `id_card_holding_url` varchar(255) DEFAULT NULL COMMENT '手持身份证照片URL',
    `face_image_url` varchar(255) DEFAULT NULL COMMENT '人脸照片URL',
    `verify_method` tinyint(1) NOT NULL DEFAULT 4 COMMENT '认证方式：1-支付宝，2-微信，3-人脸识别，4-人工审核',
    `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '认证状态：0-待审核，1-已认证，2-认证失败',
    `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
    `verified_at` datetime DEFAULT NULL COMMENT '认证时间',
    `reviewer_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实名认证表';
