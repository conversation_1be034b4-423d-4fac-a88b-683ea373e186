package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 照片笔记实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ptm_photo_note")
public class PhotoNote implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 照片笔记ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 标题（可选，最多100字符）
     */
    private String title;

    /**
     * 正文内容（必填，最多2000字符）
     */
    private String content;

    /**
     * 照片数量（1-9张）
     */
    private Integer photoCount;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 分享数
     */
    private Integer shareCount;

    /**
     * 可见性: 0-私密, 1-公开, 2-好友可见
     */
    private Integer visibility;

    /**
     * 是否允许评论: 0-不允许, 1-允许
     */
    private Integer allowComment;

    /**
     * 状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除
     */
    private Integer status;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 地理位置
     */
    private String location;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDeleted;
}
