package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.config.QiniuConfig;
import com.phototagmoment.dto.FileUploadConfigDTO;
import com.phototagmoment.entity.FileUploadConfig;
import com.phototagmoment.mapper.FileUploadConfigMapper;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.FileUploadConfigService;
import com.phototagmoment.utils.EncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件上传配置服务实现类
 */
@Slf4j
@Service
public class FileUploadConfigServiceImpl implements FileUploadConfigService {

    @Autowired
    private FileUploadConfigMapper configMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private EncryptionUtil encryptionUtil;

    @Autowired(required = false)
    private QiniuConfig qiniuConfig;

    @Override
    public IPage<FileUploadConfigDTO> getConfigList(Page<FileUploadConfigDTO> page, String keyword,
                                                   String storageType, Boolean enabled, Integer status) {
        // 查询配置列表
        List<FileUploadConfig> configs = configMapper.searchConfigs(keyword, storageType, enabled, status);

        // 转换为DTO
        List<FileUploadConfigDTO> configDTOs = configs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 手动分页（简化实现）
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), configDTOs.size());
        List<FileUploadConfigDTO> pageData = configDTOs.subList(start, end);

        // 构建分页结果
        Page<FileUploadConfigDTO> result = new Page<>(page.getCurrent(), page.getSize(), configDTOs.size());
        result.setRecords(pageData);

        return result;
    }

    @Override
    public FileUploadConfigDTO getConfigDetail(Long configId) {
        FileUploadConfig config = configMapper.selectById(configId);
        if (config == null) {
            return null;
        }
        return convertToDTO(config);
    }

    @Override
    @Transactional
    @CacheEvict(value = "fileUploadConfig", allEntries = true)
    public Long createConfig(FileUploadConfigDTO configDTO) {
        // 检查配置名称是否重复
        if (!isConfigNameAvailable(configDTO.getConfigName(), null)) {
            throw new RuntimeException("配置名称已存在");
        }

        FileUploadConfig config = new FileUploadConfig();
        BeanUtils.copyProperties(configDTO, config);

        // 设置JSON字段
        try {
            if (configDTO.getConfigParams() != null) {
                config.setConfigParams(encryptSensitiveData(objectMapper.writeValueAsString(configDTO.getConfigParams())));
            }
            if (configDTO.getUploadLimits() != null) {
                config.setUploadLimits(objectMapper.writeValueAsString(configDTO.getUploadLimits()));
            }
            if (configDTO.getPathConfig() != null) {
                config.setPathConfig(objectMapper.writeValueAsString(configDTO.getPathConfig()));
            }
        } catch (JsonProcessingException e) {
            log.error("序列化配置参数失败", e);
            throw new RuntimeException("配置参数格式错误");
        }

        // 设置创建信息
        Long currentUserId = SecurityUtil.getCurrentUserId();
        config.setCreatedBy(currentUserId);
        config.setUpdatedBy(currentUserId);
        config.setCreatedAt(LocalDateTime.now());
        config.setUpdatedAt(LocalDateTime.now());

        // 如果设置为默认配置，先清除其他默认配置
        if (Boolean.TRUE.equals(configDTO.getIsDefault())) {
            List<Long> defaultConfigIds = configMapper.selectDefaultConfigIds();
            for (Long defaultConfigId : defaultConfigIds) {
                configMapper.clearDefaultConfigById(defaultConfigId);
            }
        }

        configMapper.insert(config);

        log.info("创建文件上传配置成功: configId={}, configName={}", config.getId(), config.getConfigName());
        return config.getId();
    }

    @Override
    @Transactional
    @CacheEvict(value = "fileUploadConfig", allEntries = true)
    public boolean updateConfig(FileUploadConfigDTO configDTO) {
        if (configDTO.getId() == null) {
            return false;
        }

        // 检查配置名称是否重复
        if (!isConfigNameAvailable(configDTO.getConfigName(), configDTO.getId())) {
            throw new RuntimeException("配置名称已存在");
        }

        FileUploadConfig config = configMapper.selectById(configDTO.getId());
        if (config == null) {
            return false;
        }

        // 更新基本信息
        BeanUtils.copyProperties(configDTO, config, "id", "createdBy", "createdAt");

        // 更新JSON字段
        try {
            if (configDTO.getConfigParams() != null) {
                config.setConfigParams(encryptSensitiveData(objectMapper.writeValueAsString(configDTO.getConfigParams())));
            }
            if (configDTO.getUploadLimits() != null) {
                config.setUploadLimits(objectMapper.writeValueAsString(configDTO.getUploadLimits()));
            }
            if (configDTO.getPathConfig() != null) {
                config.setPathConfig(objectMapper.writeValueAsString(configDTO.getPathConfig()));
            }
        } catch (JsonProcessingException e) {
            log.error("序列化配置参数失败", e);
            throw new RuntimeException("配置参数格式错误");
        }

        // 设置更新信息
        config.setUpdatedBy(SecurityUtil.getCurrentUserId());
        config.setUpdatedAt(LocalDateTime.now());

        // 检查是否需要更新默认配置状态
        boolean needUpdateDefault = Boolean.TRUE.equals(configDTO.getIsDefault()) && !Boolean.TRUE.equals(config.getIsDefault());

        // 如果需要设置为默认配置，先清除其他默认配置
        if (needUpdateDefault) {
            List<Long> defaultConfigIds = configMapper.selectDefaultConfigIds();
            for (Long defaultConfigId : defaultConfigIds) {
                configMapper.clearDefaultConfigById(defaultConfigId);
            }
        }

        int updated;
        if (needUpdateDefault) {
            // 如果需要更新默认状态，使用专门的方法
            config.setIsDefault(true);
            updated = configMapper.updateById(config);
        } else {
            // 如果不涉及默认状态变更，使用选择性更新避免触发器
            updated = updateConfigSelectively(config, configDTO);
        }

        if (updated > 0) {
            log.info("更新文件上传配置成功: configId={}, configName={}", config.getId(), config.getConfigName());

            // 如果更新的是七牛云配置，刷新QiniuConfig
            if ("QINIU".equals(config.getStorageType()) && qiniuConfig != null) {
                try {
                    qiniuConfig.refreshConfig();
                    log.info("七牛云配置更新后自动刷新QiniuConfig成功");
                } catch (Exception e) {
                    log.error("七牛云配置更新后自动刷新QiniuConfig失败: {}", e.getMessage(), e);
                }
            }
        }

        return updated > 0;
    }

    @Override
    @Transactional
    @CacheEvict(value = "fileUploadConfig", allEntries = true)
    public boolean deleteConfig(Long configId) {
        FileUploadConfig config = configMapper.selectById(configId);
        if (config == null) {
            return false;
        }

        // 不能删除默认配置
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            throw new RuntimeException("不能删除默认配置");
        }

        int deleted = configMapper.deleteById(configId);

        if (deleted > 0) {
            log.info("删除文件上传配置成功: configId={}, configName={}", configId, config.getConfigName());
        }

        return deleted > 0;
    }

    @Override
    @Transactional
    @CacheEvict(value = "fileUploadConfig", allEntries = true)
    public Map<String, Object> batchDeleteConfigs(List<Long> configIds) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (Long configId : configIds) {
            try {
                if (deleteConfig(configId)) {
                    successCount++;
                } else {
                    failCount++;
                    errors.add("配置ID " + configId + " 删除失败");
                }
            } catch (Exception e) {
                failCount++;
                errors.add("配置ID " + configId + " 删除失败: " + e.getMessage());
                log.error("删除配置失败: configId={}", configId, e);
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);

        return result;
    }

    @Override
    @CacheEvict(value = "fileUploadConfig", allEntries = true)
    public boolean toggleConfig(Long configId, Boolean enabled) {
        FileUploadConfig config = configMapper.selectById(configId);
        if (config == null) {
            return false;
        }

        // 使用直接的SQL更新，避免触发器冲突
        // 只更新enabled、updated_by和updated_at字段，不触及is_default字段
        Long currentUserId = SecurityUtil.getCurrentUserId();
        LocalDateTime updateTime = LocalDateTime.now();

        int updated = configMapper.updateEnabledStatus(configId, enabled, currentUserId, updateTime);

        if (updated > 0) {
            log.info("切换配置启用状态成功: configId={}, enabled={}", configId, enabled);

            // 如果切换的是七牛云配置，刷新QiniuConfig
            if ("QINIU".equals(config.getStorageType()) && qiniuConfig != null) {
                try {
                    qiniuConfig.refreshConfig();
                    log.info("七牛云配置启用状态切换后自动刷新QiniuConfig成功");
                } catch (Exception e) {
                    log.error("七牛云配置启用状态切换后自动刷新QiniuConfig失败: {}", e.getMessage(), e);
                }
            }
        }

        return updated > 0;
    }

    @Override
    @Transactional
    @CacheEvict(value = "fileUploadConfig", allEntries = true)
    public boolean setDefaultConfig(Long configId) {
        FileUploadConfig config = configMapper.selectById(configId);
        if (config == null) {
            log.warn("设置默认配置失败: 配置不存在, configId={}", configId);
            return false;
        }

        // 记录配置状态信息
        log.info("准备设置默认配置: configId={}, configName={}, enabled={}, currentDefault={}",
                configId, config.getConfigName(), config.getEnabled(), config.getIsDefault());

        try {
            Long currentUserId = SecurityUtil.getCurrentUserId();
            LocalDateTime updateTime = LocalDateTime.now();

            // 分步操作避免MyBatis Plus的BlockAttackInnerInterceptor限制
            // 第一步：查询当前默认配置并清除
            List<Long> defaultConfigIds = configMapper.selectDefaultConfigIds();
            int cleared = 0;
            for (Long defaultConfigId : defaultConfigIds) {
                cleared += configMapper.clearDefaultConfigById(defaultConfigId);
            }
            log.info("清除其他默认配置: 影响行数={}, 清除的配置IDs={}", cleared, defaultConfigIds);

            // 第二步：设置指定配置为默认
            int updated = configMapper.setDefaultConfig(configId, currentUserId, updateTime);

            if (updated > 0) {
                log.info("设置默认配置成功: configId={}, configName={}, updatedBy={}, 影响行数={}",
                        configId, config.getConfigName(), currentUserId, updated);

                // 验证设置结果
                FileUploadConfig updatedConfig = configMapper.selectById(configId);
                log.info("设置后验证: configId={}, isDefault={}", configId, updatedConfig.getIsDefault());

                return true;
            } else {
                log.warn("设置默认配置失败: 更新行数为0, configId={}, configName={}", configId, config.getConfigName());
                return false;
            }
        } catch (Exception e) {
            log.error("设置默认配置异常: configId={}, error={}", configId, e.getMessage(), e);
            throw new RuntimeException("设置默认配置失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Cacheable(value = "fileUploadConfig", key = "'default'")
    public FileUploadConfigDTO getDefaultConfig() {
        FileUploadConfig config = configMapper.selectDefaultConfig();
        if (config == null) {
            return null;
        }
        return convertToDTO(config);
    }

    @Override
    @Cacheable(value = "fileUploadConfig", key = "'enabled'")
    public List<FileUploadConfigDTO> getEnabledConfigs() {
        List<FileUploadConfig> configs = configMapper.selectEnabledConfigs();
        return configs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "fileUploadConfig", key = "'storageType:' + #storageType")
    public FileUploadConfigDTO getConfigByStorageType(String storageType) {
        FileUploadConfig config = configMapper.selectByStorageType(storageType);
        if (config == null) {
            return null;
        }
        return convertToDTO(config);
    }

    @Override
    public FileUploadConfigDTO.TestResult testConfig(Long configId) {
        FileUploadConfig config = configMapper.selectById(configId);
        if (config == null) {
            FileUploadConfigDTO.TestResult result = new FileUploadConfigDTO.TestResult();
            result.setSuccess(false);
            result.setMessage("配置不存在");
            result.setTestTime(LocalDateTime.now());
            return result;
        }

        FileUploadConfigDTO configDTO = convertToDTO(config);
        FileUploadConfigDTO.TestResult result = testConfigWithoutSave(configDTO);

        // 保存测试结果
        try {
            String testResultJson = objectMapper.writeValueAsString(result);
            configMapper.updateTestResult(configId, result.getTestTime(), testResultJson, LocalDateTime.now());

            // 更新配置状态
            Integer newStatus = result.getSuccess() ? FileUploadConfig.Status.NORMAL : FileUploadConfig.Status.ERROR;
            configMapper.updateConfigStatus(configId, newStatus, LocalDateTime.now());
        } catch (JsonProcessingException e) {
            log.error("保存测试结果失败", e);
        }

        return result;
    }

    @Override
    public FileUploadConfigDTO.TestResult testConfigWithoutSave(FileUploadConfigDTO configDTO) {
        FileUploadConfigDTO.TestResult result = new FileUploadConfigDTO.TestResult();
        result.setTestTime(LocalDateTime.now());

        long startTime = System.currentTimeMillis();

        try {
            // 根据存储类型进行不同的测试
            boolean testSuccess = performStorageTest(configDTO);

            long endTime = System.currentTimeMillis();
            result.setResponseTime(endTime - startTime);
            result.setSuccess(testSuccess);
            result.setMessage(testSuccess ? "连接测试成功" : "连接测试失败");

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            result.setResponseTime(endTime - startTime);
            result.setSuccess(false);
            result.setMessage("连接测试失败: " + e.getMessage());
            result.setErrorDetails(e.toString());
            log.error("配置测试失败", e);
        }

        return result;
    }

    @Override
    public Map<String, Object> batchTestConfigs(List<Long> configIds) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> testResults = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        for (Long configId : configIds) {
            try {
                FileUploadConfigDTO.TestResult testResult = testConfig(configId);

                Map<String, Object> configResult = new HashMap<>();
                configResult.put("configId", configId);
                configResult.put("success", testResult.getSuccess());
                configResult.put("message", testResult.getMessage());
                configResult.put("responseTime", testResult.getResponseTime());
                testResults.add(configResult);

                if (testResult.getSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                Map<String, Object> configResult = new HashMap<>();
                configResult.put("configId", configId);
                configResult.put("success", false);
                configResult.put("message", "测试失败: " + e.getMessage());
                testResults.add(configResult);
                failCount++;
                log.error("批量测试配置失败: configId={}", configId, e);
            }
        }

        result.put("testResults", testResults);
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("testTime", LocalDateTime.now());

        return result;
    }

    @Override
    public Long copyConfig(Long configId, String newConfigName) {
        FileUploadConfig sourceConfig = configMapper.selectById(configId);
        if (sourceConfig == null) {
            return null;
        }

        // 检查新配置名称是否可用
        if (!isConfigNameAvailable(newConfigName, null)) {
            throw new RuntimeException("配置名称已存在");
        }

        FileUploadConfig newConfig = new FileUploadConfig();
        BeanUtils.copyProperties(sourceConfig, newConfig, "id", "configName", "isDefault", "createdBy", "createdAt", "updatedBy", "updatedAt");

        newConfig.setConfigName(newConfigName);
        newConfig.setIsDefault(false);
        newConfig.setCreatedBy(SecurityUtil.getCurrentUserId());
        newConfig.setUpdatedBy(SecurityUtil.getCurrentUserId());
        newConfig.setCreatedAt(LocalDateTime.now());
        newConfig.setUpdatedAt(LocalDateTime.now());

        configMapper.insert(newConfig);

        log.info("复制配置成功: sourceConfigId={}, newConfigId={}, newConfigName={}",
                configId, newConfig.getId(), newConfigName);

        return newConfig.getId();
    }

    @Override
    public Map<String, Object> getConfigStatistics() {
        return configMapper.selectConfigStatistics();
    }

    @Override
    public List<Map<String, Object>> getStorageTypeStatistics() {
        return configMapper.selectStorageTypeStatistics();
    }

    @Override
    public Map<String, Object> getConfigHealthStatus() {
        Map<String, Object> healthStatus = new HashMap<>();

        // 获取基本统计
        Map<String, Object> statistics = getConfigStatistics();
        healthStatus.put("statistics", statistics);

        // 获取最近失败的配置
        List<FileUploadConfig> failedConfigs = configMapper.selectRecentFailedConfigs(5);
        healthStatus.put("recentFailedConfigs", failedConfigs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList()));

        // 获取长时间未测试的配置
        LocalDateTime beforeTime = LocalDateTime.now().minusDays(7);
        List<FileUploadConfig> untestedConfigs = configMapper.selectUntestedConfigs(beforeTime, 5);
        healthStatus.put("untestedConfigs", untestedConfigs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList()));

        // 计算健康分数
        int totalConfigs = ((Number) statistics.get("totalCount")).intValue();
        int normalConfigs = ((Number) statistics.get("normalCount")).intValue();
        int healthScore = totalConfigs > 0 ? (normalConfigs * 100 / totalConfigs) : 100;
        healthStatus.put("healthScore", healthScore);

        return healthStatus;
    }

    @Override
    @CacheEvict(value = "fileUploadConfig", allEntries = true)
    public boolean refreshConfigCache() {
        log.info("刷新文件上传配置缓存");
        return true;
    }

    @Override
    public boolean isConfigNameAvailable(String configName, Long excludeId) {
        if (!StringUtils.hasText(configName)) {
            return false;
        }

        Long excludeIdValue = excludeId != null ? excludeId : -1L;
        int count = configMapper.countByConfigName(configName, excludeIdValue);
        return count == 0;
    }

    @Override
    public List<FileUploadConfigDTO> getRecentFailedConfigs(Integer limit) {
        List<FileUploadConfig> configs = configMapper.selectRecentFailedConfigs(limit);
        return configs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<FileUploadConfigDTO> getUntestedConfigs(Integer days, Integer limit) {
        LocalDateTime beforeTime = LocalDateTime.now().minusDays(days);
        List<FileUploadConfig> configs = configMapper.selectUntestedConfigs(beforeTime, limit);
        return configs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    // 其他方法的简化实现...
    @Override
    public Map<String, Object> importConfigs(String configData) {
        // TODO: 实现配置导入逻辑
        return new HashMap<>();
    }

    @Override
    public String exportConfigs(List<Long> configIds) {
        // TODO: 实现配置导出逻辑
        return "";
    }

    @Override
    public Map<String, Object> validateConfig(FileUploadConfigDTO configDTO) {
        // TODO: 实现配置验证逻辑
        return new HashMap<>();
    }

    @Override
    public List<Map<String, Object>> getSupportedStorageTypes() {
        // TODO: 实现获取支持的存储类型
        return new ArrayList<>();
    }

    @Override
    public FileUploadConfigDTO getConfigTemplate(String storageType) {
        // TODO: 实现获取配置模板
        return new FileUploadConfigDTO();
    }

    @Override
    public Map<String, Object> autoRepairConfig(Long configId) {
        // TODO: 实现自动修复配置
        return new HashMap<>();
    }

    /**
     * 选择性更新配置（避免触发器冲突）
     */
    private int updateConfigSelectively(FileUploadConfig config, FileUploadConfigDTO configDTO) {
        // 使用专门的SQL更新，避免更新is_default字段
        return configMapper.updateConfigSelectively(
            config.getId(),
            config.getConfigName(),
            config.getStorageType(),
            config.getEnabled(),
            config.getConfigParams(),
            config.getUploadLimits(),
            config.getPathConfig(),
            config.getDescription(),
            config.getSortOrder(),
            config.getStatus(),
            config.getUpdatedBy(),
            config.getUpdatedAt()
        );
    }

    /**
     * 转换实体为DTO
     */
    private FileUploadConfigDTO convertToDTO(FileUploadConfig config) {
        FileUploadConfigDTO dto = new FileUploadConfigDTO();
        BeanUtils.copyProperties(config, dto);

        // 解析JSON字段
        try {
            if (StringUtils.hasText(config.getConfigParams())) {
                String decryptedParams = decryptSensitiveData(config.getConfigParams());
                dto.setConfigParams(objectMapper.readValue(decryptedParams, FileUploadConfigDTO.StorageConfigParams.class));
            }
            if (StringUtils.hasText(config.getUploadLimits())) {
                dto.setUploadLimits(objectMapper.readValue(config.getUploadLimits(), FileUploadConfigDTO.UploadLimitsConfig.class));
            }
            if (StringUtils.hasText(config.getPathConfig())) {
                dto.setPathConfig(objectMapper.readValue(config.getPathConfig(), FileUploadConfigDTO.PathConfig.class));
            }
        } catch (JsonProcessingException e) {
            log.error("解析配置参数失败: configId={}", config.getId(), e);
        }

        return dto;
    }

    /**
     * 执行存储测试
     */
    private boolean performStorageTest(FileUploadConfigDTO configDTO) {
        // 简化实现，实际应该根据不同存储类型进行具体测试
        String storageType = configDTO.getStorageType();

        switch (storageType) {
            case FileUploadConfig.StorageType.LOCAL:
                return testLocalStorage(configDTO);
            case FileUploadConfig.StorageType.QINIU:
                return testQiniuStorage(configDTO);
            case FileUploadConfig.StorageType.ALIYUN_OSS:
                return testAliyunOssStorage(configDTO);
            case FileUploadConfig.StorageType.TENCENT_COS:
                return testTencentCosStorage(configDTO);
            default:
                return false;
        }
    }

    private boolean testLocalStorage(FileUploadConfigDTO configDTO) {
        // TODO: 实现本地存储测试
        return true;
    }

    private boolean testQiniuStorage(FileUploadConfigDTO configDTO) {
        // TODO: 实现七牛云存储测试
        return true;
    }

    private boolean testAliyunOssStorage(FileUploadConfigDTO configDTO) {
        // TODO: 实现阿里云OSS存储测试
        return true;
    }

    private boolean testTencentCosStorage(FileUploadConfigDTO configDTO) {
        // TODO: 实现腾讯云COS存储测试
        return true;
    }

    /**
     * 加密敏感数据
     */
    private String encryptSensitiveData(String data) {
        if (data == null || data.isEmpty()) {
            return data;
        }
        return encryptionUtil.encrypt(data);
    }

    /**
     * 解密敏感数据
     * 支持向后兼容：如果数据不是Base64格式，则认为是明文数据直接返回
     */
    private String decryptSensitiveData(String encryptedData) {
        if (encryptedData == null || encryptedData.isEmpty()) {
            return encryptedData;
        }

        // 检查是否为JSON格式（明文数据）
        if (encryptedData.trim().startsWith("{") && encryptedData.trim().endsWith("}")) {
            log.debug("检测到明文JSON数据，直接返回: configParams");
            return encryptedData;
        }

        // 检查是否为有效的Base64格式
        if (!isValidBase64(encryptedData)) {
            log.warn("数据既不是JSON格式也不是有效的Base64格式，直接返回原数据");
            return encryptedData;
        }

        try {
            return encryptionUtil.decrypt(encryptedData);
        } catch (Exception e) {
            log.error("解密失败，返回原数据: {}", e.getMessage());
            return encryptedData;
        }
    }

    /**
     * 检查字符串是否为有效的Base64格式
     */
    private boolean isValidBase64(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        // Base64字符串只能包含A-Z, a-z, 0-9, +, /, = 字符
        String base64Pattern = "^[A-Za-z0-9+/]*={0,2}$";
        if (!str.matches(base64Pattern)) {
            return false;
        }

        // Base64字符串长度必须是4的倍数
        return str.length() % 4 == 0;
    }
}
