package com.phototagmoment.config;

import com.phototagmoment.interceptor.AdminApiInterceptor;
import com.phototagmoment.interceptor.IdentityVerificationInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private IdentityVerificationInterceptor identityVerificationInterceptor;

    @Autowired
    private AdminApiInterceptor adminApiInterceptor;

    /**
     * 跨域配置
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    /**
     * 添加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("添加拦截器");

        // 管理员API拦截器，优先级高于其他拦截器
        registry.addInterceptor(adminApiInterceptor)
                .addPathPatterns("/api/admin/system/**", "/admin/system/**", "/admin/system/system-role/**")
                .order(1);

        log.info("添加管理员API拦截器完成");

        // 实名认证拦截器
        registry.addInterceptor(identityVerificationInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                        "/auth/**",
                        "/login",
                        "/wechat/**",
                        "/file/**",
                        "/identity-verification/**",
                        "/swagger-ui/**",
                        "/swagger-resources/**",
                        "/v3/api-docs/**",
                        "/doc.html",
                        "/webjars/**",
                        "/search/**",
                        "/recommendation/**",
                        "/photo/list/**",
                        "/photo/detail/**",
                        "/photo/comments/**", // 允许匿名访问照片评论
                        "/photo-notes/list/**", // 允许匿名访问照片笔记列表
                        "/photo-notes/detail/**", // 允许匿名访问照片笔记详情
                        "/photo-notes/*/comments/**", // 允许匿名访问照片笔记评论
                        "/tag/**",
                        "/user/profile/**",
                        "/user/*/photos/**", // 添加用户照片路径到白名单
                        "/user/*/collections/**", // 添加用户收藏路径到白名单
                        "/dict/**",
                        "/notification/**", // 添加通知路径到白名单
                        "/api/admin/system/**", // 排除后台管理接口
                        "/admin/system/**", // 排除后台管理接口
                        "/error"
                )
                .order(2);

        log.info("拦截器添加完成");
    }

    /**
     * 资源处理器
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");

        // 配置Knife4j和Swagger UI资源映射
        registry.addResourceHandler("/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/swagger-resources/**")
                .addResourceLocations("classpath:/META-INF/resources/swagger-resources/");
        registry.addResourceHandler("/v3/api-docs/**")
                .addResourceLocations("classpath:/META-INF/resources/v3/api-docs/");

        // 添加Knife4j的静态资源映射
        registry.addResourceHandler("/favicon.ico")
                .addResourceLocations("classpath:/META-INF/resources/");
    }
}
