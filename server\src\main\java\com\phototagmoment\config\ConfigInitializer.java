package com.phototagmoment.config;

import com.phototagmoment.entity.SystemConfig;
import com.phototagmoment.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置初始化器
 * 用于在系统启动时将YAML配置迁移到数据库
 */
@Slf4j
@Component
public class ConfigInitializer implements CommandLineRunner {

    // 标记配置是否已初始化
    private static boolean initialized = false;

    /**
     * 获取配置初始化状态
     * @return 配置是否已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }

    @Autowired
    private SystemConfigService configService;

    @Autowired
    private Environment environment;

    @Value("${content-moderation.enabled:false}")
    private boolean contentModerationEnabled;

    @Value("${content-moderation.provider:local}")
    private String contentModerationProvider;

    @Value("${content-moderation.auto-approve:false}")
    private boolean contentModerationAutoApprove;

    @Value("${content-moderation.mode:mixed}")
    private String contentModerationMode;

    @Value("${content-moderation.sensitivity:80}")
    private int contentModerationSensitivity;

    @Value("${content-moderation.baidu.app-id:}")
    private String contentModerationBaiduAppId;

    @Value("${content-moderation.baidu.api-key:}")
    private String contentModerationBaiduApiKey;

    @Value("${content-moderation.baidu.secret-key:}")
    private String contentModerationBaiduSecretKey;

    @Value("${qiniu.enabled:false}")
    private boolean qiniuEnabled;

    @Value("${qiniu.access-key:}")
    private String qiniuAccessKey;

    @Value("${qiniu.secret-key:}")
    private String qiniuSecretKey;

    @Value("${qiniu.bucket:}")
    private String qiniuBucket;

    @Value("${qiniu.region:}")
    private String qiniuRegion;

    @Value("${qiniu.domain:}")
    private String qiniuDomain;

    @Value("${qiniu.upload-dir:phototagmoment}")
    private String qiniuUploadDir;

    @Value("${qiniu.is-private:false}")
    private boolean qiniuIsPrivate;

    @Value("${qiniu.download-expires:3600}")
    private long qiniuDownloadExpires;

    // 第三方登录配置
    @Value("${auth.enabled:true}")
    private boolean authEnabled;

    @Value("${auth.qq.enabled:false}")
    private boolean authQqEnabled;

    @Value("${auth.qq.client-id:}")
    private String authQqClientId;

    @Value("${auth.qq.client-secret:}")
    private String authQqClientSecret;

    @Value("${auth.qq.redirect-uri:}")
    private String authQqRedirectUri;

    @Value("${auth.wechat.enabled:false}")
    private boolean authWechatEnabled;

    @Value("${auth.wechat.client-id:}")
    private String authWechatClientId;

    @Value("${auth.wechat.client-secret:}")
    private String authWechatClientSecret;

    @Value("${auth.wechat.redirect-uri:}")
    private String authWechatRedirectUri;

    // 微信公众号配置
    @Value("${wechat.enabled:false}")
    private boolean wechatEnabled;

    @Value("${wechat.mp.app-id:}")
    private String wechatMpAppId;

    @Value("${wechat.mp.app-secret:}")
    private String wechatMpAppSecret;

    @Value("${wechat.mp.token:}")
    private String wechatMpToken;

    @Value("${wechat.mp.aes-key:}")
    private String wechatMpAesKey;

    // 微信小程序配置
    @Value("${wechat.mini-app.app-id:}")
    private String wechatMiniAppId;

    @Value("${wechat.mini-app.app-secret:}")
    private String wechatMiniAppSecret;

    @Value("${wechat.mini-app.token:}")
    private String wechatMiniToken;

    @Value("${wechat.mini-app.aes-key:}")
    private String wechatMiniAesKey;

    // 实名认证配置
    @Value("${identity-verification.enabled:false}")
    private boolean identityVerificationEnabled;

    @Value("${identity-verification.provider:local}")
    private String identityVerificationProvider;

    @Value("${identity-verification.alipay.app-id:}")
    private String identityVerificationAlipayAppId;

    @Value("${identity-verification.alipay.private-key:}")
    private String identityVerificationAlipayPrivateKey;

    @Value("${identity-verification.alipay.public-key:}")
    private String identityVerificationAlipayPublicKey;

    @Value("${identity-verification.wechat.app-id:}")
    private String identityVerificationWechatAppId;

    @Value("${identity-verification.wechat.app-secret:}")
    private String identityVerificationWechatAppSecret;

    // 短信配置
    @Value("${sms.enabled:false}")
    private boolean smsEnabled;

    @Value("${sms.provider:aliyun}")
    private String smsProvider;

    @Value("${sms.aliyun.access-key-id:}")
    private String smsAliyunAccessKeyId;

    @Value("${sms.aliyun.access-key-secret:}")
    private String smsAliyunAccessKeySecret;

    @Value("${sms.aliyun.sign-name:}")
    private String smsAliyunSignName;

    @Value("${sms.aliyun.template-code:}")
    private String smsAliyunTemplateCode;

    @Value("${sms.verification-code.length:6}")
    private int smsVerificationCodeLength;

    @Value("${sms.verification-code.expiration:300}")
    private int smsVerificationCodeExpiration;

    @Value("${sms.verification-code.daily-limit:10}")
    private int smsVerificationCodeDailyLimit;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化系统配置...");

        // 初始化内容审核配置
        initContentModerationConfig();

        // 初始化存储配置
        initStorageConfig();

        // 初始化第三方登录配置
        initAuthConfig();

        // 初始化实名认证配置
        initIdentityVerificationConfig();

        // 初始化短信配置
        initSmsConfig();

        // 初始化微信配置
        initWechatConfig();

        // 设置初始化标志
        initialized = true;

        log.info("系统配置初始化完成");
    }

    /**
     * 初始化内容审核配置
     */
    private void initContentModerationConfig() {
        Map<String, String> configMap = new HashMap<>();

        // 内容审核基本配置
        configMap.put("content-moderation.enabled", String.valueOf(contentModerationEnabled));
        configMap.put("content-moderation.image.provider", contentModerationProvider);
        configMap.put("content-moderation.text.provider", "local");
        configMap.put("content-moderation.auto-approve", String.valueOf(contentModerationAutoApprove));
        configMap.put("content-moderation.mode", contentModerationMode);
        configMap.put("content-moderation.sensitivity", String.valueOf(contentModerationSensitivity));

        // 百度内容审核配置
        configMap.put("content-moderation.baidu.app-id", contentModerationBaiduAppId);
        configMap.put("content-moderation.baidu.api-key", contentModerationBaiduApiKey);
        configMap.put("content-moderation.baidu.secret-key", contentModerationBaiduSecretKey);

        // 保存配置
        saveConfigIfNotExists(configMap);
    }

    /**
     * 初始化存储配置
     */
    private void initStorageConfig() {
        Map<String, String> configMap = new HashMap<>();

        // 存储类型配置
        configMap.put("storage.type", qiniuEnabled ? "qiniu" : "local");

        // 七牛云存储配置
        configMap.put("storage.qiniu.access-key", qiniuAccessKey);
        configMap.put("storage.qiniu.secret-key", qiniuSecretKey);
        configMap.put("storage.qiniu.bucket", qiniuBucket);
        configMap.put("storage.qiniu.region", qiniuRegion);
        configMap.put("storage.qiniu.domain", qiniuDomain);
        configMap.put("storage.qiniu.upload-dir", qiniuUploadDir);
        configMap.put("storage.qiniu.is-private", String.valueOf(qiniuIsPrivate));
        configMap.put("storage.qiniu.download-expires", String.valueOf(qiniuDownloadExpires));

        // 保存配置
        saveConfigIfNotExists(configMap);
    }

    /**
     * 保存配置（如果不存在）
     *
     * @param configMap 配置映射
     */
    private void saveConfigIfNotExists(Map<String, String> configMap) {
        for (Map.Entry<String, String> entry : configMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            try {
                // 检查配置是否已存在
                String existingValue = configService.getConfigValue(key);
                if (existingValue != null) {
                    log.info("配置已存在，跳过: {}", key);
                    continue;
                }

                // 创建新配置
                SystemConfig config = new SystemConfig();
                config.setConfigKey(key);
                config.setConfigValue(value);
                config.setConfigName(getConfigName(key));
                config.setConfigType(getConfigType(value));
                config.setRemark(getConfigRemark(key));
                config.setDescription(getConfigDescription(key));
                config.setIsSystem(true);

                // 保存配置
                boolean success = configService.saveConfig(config);
                if (success) {
                    log.info("成功保存配置: {}", key);
                } else {
                    log.warn("保存配置失败: {}", key);
                }
            } catch (Exception e) {
                log.error("处理配置时发生错误: {}, 错误: {}", key, e.getMessage());
            }
        }
    }

    /**
     * 获取配置名称
     *
     * @param key 配置键
     * @return 配置名称
     */
    private String getConfigName(String key) {
        // 根据配置键生成配置名称
        String[] parts = key.split("\\.");
        StringBuilder name = new StringBuilder();
        for (String part : parts) {
            name.append(part.substring(0, 1).toUpperCase())
                .append(part.substring(1))
                .append(" ");
        }
        return name.toString().trim();
    }

    /**
     * 获取配置类型
     *
     * @param value 配置值
     * @return 配置类型
     */
    private String getConfigType(String value) {
        if (value == null || value.isEmpty()) {
            return "string";
        }

        if (value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")) {
            return "boolean";
        }

        try {
            Integer.parseInt(value);
            return "number";
        } catch (NumberFormatException e) {
            try {
                Double.parseDouble(value);
                return "number";
            } catch (NumberFormatException e2) {
                if (value.startsWith("{") && value.endsWith("}") || value.startsWith("[") && value.endsWith("]")) {
                    return "json";
                }
                return "string";
            }
        }
    }

    /**
     * 初始化第三方登录配置
     */
    private void initAuthConfig() {
        Map<String, String> configMap = new HashMap<>();

        // 第三方登录基本配置
        configMap.put("auth.enabled", String.valueOf(authEnabled));

        // QQ登录配置
        configMap.put("auth.qq.enabled", String.valueOf(authQqEnabled));
        configMap.put("auth.qq.client-id", authQqClientId);
        configMap.put("auth.qq.client-secret", authQqClientSecret);
        configMap.put("auth.qq.redirect-uri", authQqRedirectUri);

        // 微信登录配置
        configMap.put("auth.wechat.enabled", String.valueOf(authWechatEnabled));
        configMap.put("auth.wechat.client-id", authWechatClientId);
        configMap.put("auth.wechat.client-secret", authWechatClientSecret);
        configMap.put("auth.wechat.redirect-uri", authWechatRedirectUri);

        // 保存配置
        saveConfigIfNotExists(configMap);

        log.info("第三方登录配置初始化完成");
    }

    /**
     * 初始化实名认证配置
     */
    private void initIdentityVerificationConfig() {
        Map<String, String> configMap = new HashMap<>();

        // 实名认证基本配置
        configMap.put("identity-verification.enabled", String.valueOf(identityVerificationEnabled));
        configMap.put("identity-verification.provider", identityVerificationProvider);

        // 支付宝实名认证配置
        configMap.put("identity-verification.alipay.app-id", identityVerificationAlipayAppId);
        configMap.put("identity-verification.alipay.private-key", identityVerificationAlipayPrivateKey);
        configMap.put("identity-verification.alipay.public-key", identityVerificationAlipayPublicKey);

        // 微信实名认证配置
        configMap.put("identity-verification.wechat.app-id", identityVerificationWechatAppId);
        configMap.put("identity-verification.wechat.app-secret", identityVerificationWechatAppSecret);

        // 保存配置
        saveConfigIfNotExists(configMap);

        log.info("实名认证配置初始化完成");
    }

    /**
     * 初始化短信配置
     */
    private void initSmsConfig() {
        Map<String, String> configMap = new HashMap<>();

        // 短信基本配置
        configMap.put("sms.enabled", String.valueOf(smsEnabled));
        configMap.put("sms.provider", smsProvider);

        // 阿里云短信配置
        configMap.put("sms.aliyun.access-key-id", smsAliyunAccessKeyId);
        configMap.put("sms.aliyun.access-key-secret", smsAliyunAccessKeySecret);
        configMap.put("sms.aliyun.sign-name", smsAliyunSignName);
        configMap.put("sms.aliyun.template-code", smsAliyunTemplateCode);

        // 验证码配置
        configMap.put("sms.verification-code.length", String.valueOf(smsVerificationCodeLength));
        configMap.put("sms.verification-code.expiration", String.valueOf(smsVerificationCodeExpiration));
        configMap.put("sms.verification-code.daily-limit", String.valueOf(smsVerificationCodeDailyLimit));

        // 保存配置
        saveConfigIfNotExists(configMap);

        log.info("短信配置初始化完成");
    }

    /**
     * 初始化微信配置
     */
    private void initWechatConfig() {
        Map<String, String> configMap = new HashMap<>();

        // 微信基本配置
        configMap.put("wechat.enabled", String.valueOf(wechatEnabled));

        // 微信公众号配置
        configMap.put("wechat.mp.app-id", wechatMpAppId);
        configMap.put("wechat.mp.app-secret", wechatMpAppSecret);
        configMap.put("wechat.mp.token", wechatMpToken);
        configMap.put("wechat.mp.aes-key", wechatMpAesKey);

        // 微信小程序配置
        configMap.put("wechat.mini-app.app-id", wechatMiniAppId);
        configMap.put("wechat.mini-app.app-secret", wechatMiniAppSecret);
        configMap.put("wechat.mini-app.token", wechatMiniToken);
        configMap.put("wechat.mini-app.aes-key", wechatMiniAesKey);

        // 保存配置
        saveConfigIfNotExists(configMap);

        log.info("微信配置初始化完成");
    }

    /**
     * 获取配置备注
     *
     * @param key 配置键
     * @return 配置备注
     */
    private String getConfigRemark(String key) {
        // 根据配置键生成配置备注
        return "系统配置: " + key;
    }

    /**
     * 获取配置描述
     *
     * @param key 配置键
     * @return 配置描述
     */
    private String getConfigDescription(String key) {
        // 根据配置键生成配置描述
        return "系统配置项: " + key;
    }
}
