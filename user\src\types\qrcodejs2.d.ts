declare module 'qrcodejs2' {
  interface QRCodeOptions {
    text: string;
    width?: number;
    height?: number;
    colorDark?: string;
    colorLight?: string;
    correctLevel?: number;
  }

  class QRCode {
    constructor(element: HTMLElement, options: QRCodeOptions);
    static CorrectLevel: {
      L: number;
      M: number;
      Q: number;
      H: number;
    };
  }

  export default QRCode;
}
