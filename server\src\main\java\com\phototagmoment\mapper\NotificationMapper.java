package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.NotificationDTO;
import com.phototagmoment.entity.Notification;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * 通知Mapper接口
 */
@Repository
public interface NotificationMapper extends BaseMapper<Notification> {

    /**
     * 分页查询用户通知列表
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param type 通知类型，为null则查询所有类型
     * @return 通知列表
     */
    IPage<NotificationDTO> selectUserNotifications(Page<NotificationDTO> page, @Param("userId") Long userId, @Param("type") Integer type);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int countUnreadNotifications(@Param("userId") Long userId);

    /**
     * 标记用户所有通知为已读
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    @Update("UPDATE ptm_notification SET is_read = 1 WHERE user_id = #{userId} AND is_read = 0")
    int markAllAsRead(@Param("userId") Long userId);

    /**
     * 标记指定通知为已读
     *
     * @param notificationId 通知ID
     * @return 影响行数
     */
    @Update("UPDATE ptm_notification SET is_read = 1 WHERE id = #{notificationId}")
    int markAsRead(@Param("notificationId") Long notificationId);
}
