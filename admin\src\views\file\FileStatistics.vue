<template>
  <div class="file-statistics-container">
    <el-card class="page-header">
      <div class="header-content">
        <h2>文件统计</h2>
        <p>查看文件上传、存储和使用情况的详细统计信息</p>
      </div>
    </el-card>

    <!-- 时间范围选择 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="统计时间">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="统计维度">
          <el-select v-model="filterForm.dimension" @change="handleDimensionChange">
            <el-option label="按天统计" value="day" />
            <el-option label="按周统计" value="week" />
            <el-option label="按月统计" value="month" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadStatistics">
            <el-icon><refresh /></el-icon>
            刷新数据
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 总体统计 -->
    <el-row :gutter="16" class="overview-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#409eff"><document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overviewStats.totalFiles }}</div>
              <div class="stat-label">总文件数</div>
              <div class="stat-change" :class="overviewStats.filesChange >= 0 ? 'positive' : 'negative'">
                {{ overviewStats.filesChange >= 0 ? '+' : '' }}{{ overviewStats.filesChange }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#67c23a"><box /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatFileSize(overviewStats.totalSize) }}</div>
              <div class="stat-label">总存储大小</div>
              <div class="stat-change" :class="overviewStats.sizeChange >= 0 ? 'positive' : 'negative'">
                {{ overviewStats.sizeChange >= 0 ? '+' : '' }}{{ formatFileSize(overviewStats.sizeChange) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#e6a23c"><upload /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overviewStats.todayUploads }}</div>
              <div class="stat-label">今日上传</div>
              <div class="stat-change" :class="overviewStats.uploadsChange >= 0 ? 'positive' : 'negative'">
                {{ overviewStats.uploadsChange >= 0 ? '+' : '' }}{{ overviewStats.uploadsChange }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32" color="#f56c6c"><user /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overviewStats.activeUsers }}</div>
              <div class="stat-label">活跃用户</div>
              <div class="stat-change" :class="overviewStats.usersChange >= 0 ? 'positive' : 'negative'">
                {{ overviewStats.usersChange >= 0 ? '+' : '' }}{{ overviewStats.usersChange }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="16" class="chart-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>文件上传趋势</span>
          </template>
          <div ref="uploadTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>存储使用情况</span>
          </template>
          <div ref="storageUsageChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="16" class="chart-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>文件类型分布</span>
          </template>
          <div ref="fileTypeChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>存储服务使用率</span>
          </template>
          <div ref="storageServiceChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="detail-table">
      <template #header>
        <div class="card-header">
          <span>详细统计数据</span>
          <el-button size="small" @click="exportData">
            <el-icon><download /></el-icon>
            导出数据
          </el-button>
        </div>
      </template>

      <el-table :data="detailStats" v-loading="loading" style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="uploadCount" label="上传数量" width="100" />
        <el-table-column prop="uploadSize" label="上传大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.uploadSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="deleteCount" label="删除数量" width="100" />
        <el-table-column prop="deleteSize" label="删除大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.deleteSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="activeUsers" label="活跃用户" width="100" />
        <el-table-column prop="avgFileSize" label="平均文件大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.avgFileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="successRate" label="成功率" width="100">
          <template #default="{ row }">
            <el-tag :type="row.successRate >= 95 ? 'success' : row.successRate >= 90 ? 'warning' : 'danger'">
              {{ row.successRate }}%
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Box, Upload, User, Refresh, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getFileOverviewStats,
  getFileDetailStats,
  getFileTypeDistribution,
  getStorageServiceUsage,
  getUploadTrendData,
  exportFileStats
} from '@/api/fileStatistics'

// 响应式数据
const loading = ref(false)
const uploadTrendChart = ref()
const storageUsageChart = ref()
const fileTypeChart = ref()
const storageServiceChart = ref()

const filterForm = reactive({
  dateRange: [],
  dimension: 'day'
})

const overviewStats = reactive({
  totalFiles: 0,
  totalSize: 0,
  todayUploads: 0,
  activeUsers: 0,
  filesChange: 0,
  sizeChange: 0,
  uploadsChange: 0,
  usersChange: 0
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const detailStats = ref([])

// 图表实例
let uploadTrendChartInstance = null
let storageUsageChartInstance = null
let fileTypeChartInstance = null
let storageServiceChartInstance = null

// 方法
const loadStatistics = async () => {
  loading.value = true
  try {
    // 加载概览统计
    const overviewResponse = await getFileOverviewStats({
      startDate: filterForm.dateRange?.[0],
      endDate: filterForm.dateRange?.[1],
      dimension: filterForm.dimension as 'day' | 'week' | 'month'
    })
    Object.assign(overviewStats, overviewResponse.data)

    // 加载详细统计
    const detailResponse = await getFileDetailStats({
      startDate: filterForm.dateRange?.[0],
      endDate: filterForm.dateRange?.[1],
      dimension: filterForm.dimension as 'day' | 'week' | 'month',
      page: pagination.page,
      size: pagination.size
    })
    detailStats.value = detailResponse.data.records
    pagination.total = detailResponse.data.total

    // 更新图表
    await nextTick()
    updateCharts()

  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const updateCharts = async () => {
  try {
    // 加载图表数据
    const [trendData, typeDistribution, storageUsage] = await Promise.all([
      getUploadTrendData(7),
      getFileTypeDistribution(),
      getStorageServiceUsage()
    ])

    // 文件上传趋势图
    if (uploadTrendChartInstance && trendData.data.length) {
      const option = {
        title: {
          text: '文件上传趋势',
          left: 'center',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['上传数量', '上传大小(MB)'],
          bottom: 0
        },
        xAxis: {
          type: 'category',
          data: trendData.data.map(item => item.date.substring(5))
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '大小(MB)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '上传数量',
            type: 'line',
            data: trendData.data.map(item => item.uploadCount),
            smooth: true
          },
          {
            name: '上传大小(MB)',
            type: 'bar',
            yAxisIndex: 1,
            data: trendData.data.map(item => Math.round(item.uploadSize / 1024 / 1024))
          }
        ]
      }
      uploadTrendChartInstance.setOption(option)
    }

    // 存储使用情况图
    if (storageUsageChartInstance && storageUsage.data.length) {
      const option = {
        title: {
          text: '存储使用情况',
          left: 'center',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '存储使用',
            type: 'pie',
            radius: '50%',
            data: storageUsage.data.map(item => ({
              value: item.totalSize,
              name: item.serviceName
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      storageUsageChartInstance.setOption(option)
    }

    // 文件类型分布图
    if (fileTypeChartInstance && typeDistribution.data.length) {
      const option = {
        title: {
          text: '文件类型分布',
          left: 'center',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: typeDistribution.data.map(item => item.type)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: typeDistribution.data.map(item => item.count),
            type: 'bar',
            itemStyle: {
              color: function(params: any) {
                const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452']
                return colors[params.dataIndex % colors.length]
              }
            }
          }
        ]
      }
      fileTypeChartInstance.setOption(option)
    }
  } catch (error) {
    console.error('更新图表失败:', error)
  }
}



const initCharts = () => {
  uploadTrendChartInstance = echarts.init(uploadTrendChart.value)
  storageUsageChartInstance = echarts.init(storageUsageChart.value)
  fileTypeChartInstance = echarts.init(fileTypeChart.value)
  storageServiceChartInstance = echarts.init(storageServiceChart.value)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    uploadTrendChartInstance?.resize()
    storageUsageChartInstance?.resize()
    fileTypeChartInstance?.resize()
    storageServiceChartInstance?.resize()
  })
}

const handleDateChange = () => {
  loadStatistics()
}

const handleDimensionChange = () => {
  loadStatistics()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadStatistics()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadStatistics()
}

const exportData = async () => {
  try {
    const response = await exportFileStats({
      startDate: filterForm.dateRange?.[0],
      endDate: filterForm.dateRange?.[1],
      dimension: filterForm.dimension as 'day' | 'week' | 'month'
    })

    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `文件统计_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 辅助方法
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(async () => {
  // 设置默认时间范围（最近7天）
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 6)

  filterForm.dateRange = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]

  await nextTick()
  initCharts()
  loadStatistics()
})
</script>

<style scoped>
.file-statistics-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.stat-icon {
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: bold;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  width: 100%;
}

.detail-table {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
