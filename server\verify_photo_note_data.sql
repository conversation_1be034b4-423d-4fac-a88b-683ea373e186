-- PhotoTagMoment 照片笔记数据验证脚本
-- 用于诊断用户端发布和管理端查询的数据同步问题

-- ==========================================
-- 1. 数据库基础信息
-- ==========================================
SELECT 
    '=== PhotoTagMoment 照片笔记数据验证报告 ===' as report_title
UNION ALL
SELECT CONCAT('验证时间: ', NOW()) as report_info
UNION ALL
SELECT CONCAT('数据库: ', DATABASE()) as report_info
UNION ALL
SELECT '===========================================' as separator;

-- ==========================================
-- 2. 检查表是否存在
-- ==========================================
SELECT 
    '2. 表结构验证' as section_title
UNION ALL
SELECT 
    CONCAT('ptm_photo_note: ', 
        CASE WHEN COUNT(*) > 0 THEN '✅ 存在' ELSE '❌ 不存在' END
    ) as table_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo_note'
UNION ALL
SELECT 
    CONCAT('ptm_photo: ', 
        CASE WHEN COUNT(*) > 0 THEN '✅ 存在' ELSE '❌ 不存在' END
    ) as table_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo'
UNION ALL
SELECT 
    CONCAT('ptm_photo_note_image: ', 
        CASE WHEN COUNT(*) > 0 THEN '✅ 存在' ELSE '❌ 不存在' END
    ) as table_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo_note_image';

-- ==========================================
-- 3. 照片笔记数据统计
-- ==========================================
SELECT '3. 照片笔记数据统计' as section_title;

SELECT 
    COUNT(*) as total_notes,
    COUNT(CASE WHEN status = 0 THEN 1 END) as pending_notes,
    COUNT(CASE WHEN status = 1 THEN 1 END) as approved_notes,
    COUNT(CASE WHEN status = 2 THEN 1 END) as rejected_notes,
    COUNT(CASE WHEN status = 3 THEN 1 END) as deleted_notes,
    COUNT(CASE WHEN is_deleted = 1 THEN 1 END) as soft_deleted_notes,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as today_notes,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_notes
FROM ptm_photo_note;

-- ==========================================
-- 4. 照片数据统计
-- ==========================================
SELECT '4. 照片数据统计' as section_title;

SELECT 
    COUNT(*) as total_photos,
    COUNT(CASE WHEN user_id IS NULL THEN 1 END) as note_photos,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as user_photos,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as today_photos,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_photos
FROM ptm_photo;

-- ==========================================
-- 5. 照片笔记图片关联统计
-- ==========================================
SELECT '5. 照片笔记图片关联统计' as section_title;

SELECT 
    COUNT(*) as total_relations,
    COUNT(DISTINCT note_id) as notes_with_photos,
    COUNT(DISTINCT photo_id) as photos_in_notes,
    AVG(photo_count_per_note) as avg_photos_per_note
FROM (
    SELECT 
        note_id,
        COUNT(photo_id) as photo_count_per_note
    FROM ptm_photo_note_image
    GROUP BY note_id
) as note_photo_counts;

-- ==========================================
-- 6. 最近发布的照片笔记详情
-- ==========================================
SELECT '6. 最近发布的照片笔记详情' as section_title;

SELECT 
    pn.id as note_id,
    pn.title,
    LEFT(pn.content, 50) as content_preview,
    pn.status,
    CASE 
        WHEN pn.status = 0 THEN '待审核'
        WHEN pn.status = 1 THEN '已通过'
        WHEN pn.status = 2 THEN '已拒绝'
        WHEN pn.status = 3 THEN '已删除'
        ELSE '未知状态'
    END as status_text,
    pn.visibility,
    pn.photo_count,
    pn.created_at,
    u.nickname as user_nickname,
    COUNT(pni.photo_id) as actual_photo_count,
    pn.is_deleted
FROM ptm_photo_note pn
LEFT JOIN ptm_user u ON pn.user_id = u.id
LEFT JOIN ptm_photo_note_image pni ON pn.id = pni.note_id
GROUP BY pn.id
ORDER BY pn.created_at DESC
LIMIT 10;

-- ==========================================
-- 7. 检查数据一致性
-- ==========================================
SELECT '7. 数据一致性检查' as section_title;

-- 检查照片笔记的photo_count字段与实际关联的照片数量是否一致
SELECT 
    '照片数量一致性检查' as check_type,
    COUNT(*) as total_notes,
    COUNT(CASE WHEN pn.photo_count = actual_count.photo_count THEN 1 END) as consistent_notes,
    COUNT(CASE WHEN pn.photo_count != actual_count.photo_count THEN 1 END) as inconsistent_notes
FROM ptm_photo_note pn
LEFT JOIN (
    SELECT 
        note_id,
        COUNT(photo_id) as photo_count
    FROM ptm_photo_note_image
    GROUP BY note_id
) actual_count ON pn.id = actual_count.note_id
WHERE pn.is_deleted = 0;

-- ==========================================
-- 8. 检查孤立数据
-- ==========================================
SELECT '8. 孤立数据检查' as section_title;

-- 检查没有关联照片的照片笔记
SELECT 
    '没有关联照片的照片笔记' as orphan_type,
    COUNT(*) as orphan_count
FROM ptm_photo_note pn
LEFT JOIN ptm_photo_note_image pni ON pn.id = pni.note_id
WHERE pni.note_id IS NULL AND pn.is_deleted = 0;

-- 检查关联了不存在照片的照片笔记图片关联记录
SELECT 
    '关联不存在照片的关联记录' as orphan_type,
    COUNT(*) as orphan_count
FROM ptm_photo_note_image pni
LEFT JOIN ptm_photo p ON pni.photo_id = p.id
WHERE p.id IS NULL;

-- 检查关联了不存在照片笔记的照片笔记图片关联记录
SELECT 
    '关联不存在照片笔记的关联记录' as orphan_type,
    COUNT(*) as orphan_count
FROM ptm_photo_note_image pni
LEFT JOIN ptm_photo_note pn ON pni.note_id = pn.id
WHERE pn.id IS NULL;

-- ==========================================
-- 9. 管理端查询模拟
-- ==========================================
SELECT '9. 管理端查询模拟' as section_title;

-- 模拟管理端的查询逻辑
SELECT 
    pn.id,
    pn.user_id,
    u.nickname,
    u.avatar,
    pn.title,
    LEFT(pn.content, 100) as content,
    pn.photo_count,
    pn.view_count,
    pn.like_count,
    pn.comment_count,
    pn.share_count,
    pn.visibility,
    pn.allow_comment,
    pn.status,
    pn.reject_reason,
    pn.location,
    pn.created_at,
    pn.updated_at,
    false as is_liked,
    false as is_collected
FROM ptm_photo_note pn
LEFT JOIN ptm_user u ON pn.user_id = u.id
WHERE pn.is_deleted = 0
ORDER BY pn.created_at DESC
LIMIT 5;

-- ==========================================
-- 10. 用户端查询模拟
-- ==========================================
SELECT '10. 用户端查询模拟' as section_title;

-- 模拟用户端的查询逻辑（只显示已通过审核的）
SELECT 
    pn.id,
    pn.user_id,
    u.nickname,
    u.avatar,
    pn.title,
    LEFT(pn.content, 100) as content,
    pn.photo_count,
    pn.view_count,
    pn.like_count,
    pn.comment_count,
    pn.share_count,
    pn.visibility,
    pn.allow_comment,
    pn.status,
    pn.created_at,
    pn.updated_at
FROM ptm_photo_note pn
LEFT JOIN ptm_user u ON pn.user_id = u.id
WHERE pn.is_deleted = 0 
    AND pn.status = 1  -- 只显示已通过审核的
    AND pn.visibility = 1  -- 只显示公开的
ORDER BY pn.created_at DESC
LIMIT 5;

-- ==========================================
-- 11. 照片笔记图片详情检查
-- ==========================================
SELECT '11. 照片笔记图片详情检查' as section_title;

SELECT 
    pni.note_id,
    pni.photo_id,
    pni.sort_order,
    p.url,
    p.thumbnail_url,
    p.width,
    p.height,
    p.user_id as photo_user_id,
    p.created_at as photo_created_at
FROM ptm_photo_note_image pni
LEFT JOIN ptm_photo p ON pni.photo_id = p.id
ORDER BY pni.note_id DESC, pni.sort_order ASC
LIMIT 10;

-- ==========================================
-- 12. 总结报告
-- ==========================================
SELECT '12. 验证总结' as section_title;

SELECT 
    '验证完成时间' as summary_item,
    NOW() as summary_value
UNION ALL
SELECT 
    '建议下一步',
    '根据以上数据分析问题原因并制定修复方案' as summary_value;
