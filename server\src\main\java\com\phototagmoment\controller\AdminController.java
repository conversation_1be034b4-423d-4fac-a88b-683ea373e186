package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.AdminService;
import com.phototagmoment.vo.AdminVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@Tag(name = "管理员接口", description = "管理员相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private AdminService adminService;

    /**
     * 获取当前管理员信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取当前管理员信息", description = "获取当前登录管理员的详细信息")
    public ApiResponse<AdminVO> getAdminInfo() {
        AdminVO adminVO = adminService.getCurrentAdmin();
        return ApiResponse.success(adminVO);
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/user/list")
    @Operation(summary = "获取用户列表", description = "分页获取用户列表")
    public ApiResponse<IPage<UserDTO>> getUserList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "手机号") @RequestParam(required = false) String phone,
            @Parameter(description = "邮箱") @RequestParam(required = false) String email,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        IPage<UserDTO> userPage = adminService.getUserList(page, size, username, phone, email, status);
        return ApiResponse.success(userPage);
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/user/{id}")
    @Operation(summary = "获取用户详情", description = "根据用户ID获取用户详情")
    public ApiResponse<UserDTO> getUserDetail(@Parameter(description = "用户ID") @PathVariable Long id) {
        UserDTO userDTO = adminService.getUserDetail(id);
        return ApiResponse.success(userDTO);
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/user/{id}/status")
    @Operation(summary = "更新用户状态", description = "更新用户状态（启用/禁用）")
    public ApiResponse<Boolean> updateUserStatus(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam Integer status) {
        boolean result = adminService.updateUserStatus(id, status);
        return ApiResponse.success(result, status == 1 ? "启用成功" : "禁用成功");
    }

    /**
     * 获取实名认证列表
     */
    @GetMapping("/user/verification/list")
    @Operation(summary = "获取实名认证列表", description = "分页获取实名认证列表")
    public ApiResponse<IPage<UserDTO>> getVerificationList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "状态：0-待审核，1-已通过，2-已拒绝") @RequestParam(required = false) Integer status) {
        IPage<UserDTO> verificationPage = adminService.getVerificationList(page, size, status);
        return ApiResponse.success(verificationPage);
    }

    /**
     * 审核实名认证
     */
    @PutMapping("/user/verification/{id}")
    @Operation(summary = "审核实名认证", description = "审核用户实名认证")
    public ApiResponse<Boolean> verifyUser(
            @Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "状态：1-通过，2-拒绝") @RequestParam Integer status,
            @Parameter(description = "拒绝理由") @RequestParam(required = false) String reason) {
        boolean result = adminService.verifyUser(id, status, reason);
        return ApiResponse.success(result, status == 1 ? "审核通过" : "审核拒绝");
    }
}
