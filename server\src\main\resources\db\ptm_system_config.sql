/*
 Navicat Premium Data Transfer

 Source Server         : local8.0
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : localhost:31160
 Source Schema         : phototag_moment

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 23/05/2025 19:00:52
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ptm_system_config
-- ----------------------------
DROP TABLE IF EXISTS `ptm_system_config`;
CREATE TABLE `ptm_system_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `config_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置值',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置名称',
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置类型（string, number, boolean, json）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `is_system` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否系统内置（0否 1是）',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_config_key`(`config_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 140 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_system_config
-- ----------------------------
INSERT INTO `ptm_system_config` VALUES (1, 'system.name', 'PhotoTagMoment', '系统名称', 'string', '系统名称', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (2, 'system.logo', '/logo.png', '系统Logo', 'string', '系统Logo路径', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (3, 'system.description', '照片社交分享平台', '系统描述', 'string', '系统描述', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (4, 'system.version', '1.0.0', '系统版本', 'string', '系统版本号', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (5, 'system.copyright', 'Copyright © 2023 PhotoTagMoment', '版权信息', 'string', '系统版权信息', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (6, 'system.record.number', '', '备案号', 'string', '网站备案号', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (7, 'upload.max.size', '10', '上传文件大小限制', 'number', '上传文件大小限制（MB）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (8, 'upload.allowed.types', 'jpg,jpeg,png,gif,webp', '允许上传的文件类型', 'string', '允许上传的文件类型，逗号分隔', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (9, 'user.default.avatar', '/default-avatar.png', '用户默认头像', 'string', '用户默认头像路径', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (10, 'user.register.verify', 'true', '注册是否需要验证', 'boolean', '注册是否需要验证（短信验证码）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (11, 'user.login.verify', 'true', '登录是否需要验证', 'boolean', '登录是否需要验证（短信验证码）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (12, 'user.identity.verify', 'true', '是否开启实名认证', 'boolean', '是否开启实名认证', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (13, 'content.audit.enabled', 'true', '是否开启内容审核', 'boolean', '是否开启内容审核', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (14, 'content.audit.provider', 'local', '内容审核服务提供商', 'string', '内容审核服务提供商（local, aliyun, tencent, baidu）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (15, 'sensitive.word.filter', 'true', '是否开启敏感词过滤', 'boolean', '是否开启敏感词过滤', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (16, 'notification.email.enabled', 'false', '是否开启邮件通知', 'boolean', '是否开启邮件通知', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (17, 'notification.sms.enabled', 'true', '是否开启短信通知', 'boolean', '是否开启短信通知', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (18, 'notification.websocket.enabled', 'true', '是否开启WebSocket通知', 'boolean', '是否开启WebSocket通知', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (19, 'content-moderation.enabled', 'true', '启用内容审核', 'boolean', '是否启用内容审核功能', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (20, 'content-moderation.image.provider', 'baidu', '图像审核服务提供商', 'string', '图像审核服务提供商：local, baidu', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (21, 'content-moderation.text.provider', 'local', '文本审核服务提供商', 'string', '文本审核服务提供商：local', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (22, 'content-moderation.auto-approve', 'false', '自动通过审核', 'boolean', '是否自动通过审核（用于测试）', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (23, 'content-moderation.mode', 'mixed', '审核模式', 'string', '审核模式：auto-自动审核, manual-人工审核, mixed-混合审核', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (24, 'content-moderation.sensitivity', '80', '自动审核敏感度', 'number', '自动审核敏感度：0-100，值越大越严格', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (25, 'content-moderation.contact-info.filter', 'true', '联系方式过滤', 'boolean', '是否过滤联系方式', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (26, 'content-moderation.baidu.app-id', '118929147', '百度AppID', 'string', '百度内容审核AppID', 1, NULL, '2025-05-19 18:24:54', '2025-05-20 20:39:20');
INSERT INTO `ptm_system_config` VALUES (27, 'content-moderation.baidu.api-key', 'xwHcCeFjXNwxk436aNc44bUh', '百度ApiKey', 'string', '百度内容审核ApiKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-20 20:39:24');
INSERT INTO `ptm_system_config` VALUES (28, 'content-moderation.baidu.secret-key', 'G6CJsS8fMoTcEe1aZ0dNGDLZcYudwTlY', '百度SecretKey', 'string', '百度内容审核SecretKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-20 20:39:27');
INSERT INTO `ptm_system_config` VALUES (29, 'storage.type', 'qiniu', '存储类型', 'string', '存储类型：local, qiniu, tencent, aliyun', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (30, 'storage.local.path', '/data/upload', '本地存储路径', 'string', '本地存储路径', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (31, 'storage.local.domain', 'http://localhost:8081/api/file', '本地存储域名', 'string', '本地存储域名', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (32, 'storage.qiniu.access-key', '', '七牛云AccessKey', 'string', '七牛云AccessKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (33, 'storage.qiniu.secret-key', '', '七牛云SecretKey', 'string', '七牛云SecretKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (34, 'storage.qiniu.bucket', 'phototagmoment', '七牛云存储空间', 'string', '七牛云存储空间', 1, NULL, '2025-05-19 18:24:54', '2025-05-23 17:22:32');
INSERT INTO `ptm_system_config` VALUES (35, 'storage.qiniu.domain', 'sw5eg63qc.hn-bkt.clouddn.com', '七牛云域名', 'string', '七牛云域名', 1, NULL, '2025-05-19 18:24:54', '2025-05-23 17:22:25');
INSERT INTO `ptm_system_config` VALUES (36, 'storage.qiniu.region', 'huanan', '七牛云区域', 'string', '七牛云区域', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (37, 'storage.qiniu.upload-dir', 'phototagmoment', '七牛云上传目录', 'string', '七牛云上传目录', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (38, 'storage.qiniu.is-private', 'true', '七牛云是否私有空间', 'boolean', '七牛云是否私有空间', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (39, 'storage.qiniu.download-expires', '3600', '七牛云下载凭证有效期', 'number', '七牛云下载凭证有效期（秒）', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (40, 'sensitive.word.replace.char', '*', '敏感词替换字符', 'string', '敏感词替换字符', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (106, 'auth.wechat.client-secret', '', 'Auth Wechat Client-secret', 'string', '系统配置: auth.wechat.client-secret', 1, '系统配置项: auth.wechat.client-secret', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (107, 'auth.qq.enabled', 'true', 'Auth Qq Enabled', 'boolean', '系统配置: auth.qq.enabled', 1, '系统配置项: auth.qq.enabled', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (108, 'auth.qq.client-secret', 'cc64e81ca78cb0633fef78ca495d79e9', 'Auth Qq Client-secret', 'string', '系统配置: auth.qq.client-secret', 1, '系统配置项: auth.qq.client-secret', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (109, 'auth.enabled', 'true', 'Auth Enabled', 'boolean', '系统配置: auth.enabled', 1, '系统配置项: auth.enabled', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (110, 'auth.wechat.client-id', '', 'Auth Wechat Client-id', 'string', '系统配置: auth.wechat.client-id', 1, '系统配置项: auth.wechat.client-id', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (111, 'auth.qq.redirect-uri', 'https://www.51oscode.com/connect.php', 'Auth Qq Redirect-uri', 'string', '系统配置: auth.qq.redirect-uri', 1, '系统配置项: auth.qq.redirect-uri', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (112, 'auth.wechat.enabled', 'true', 'Auth Wechat Enabled', 'boolean', '系统配置: auth.wechat.enabled', 1, '系统配置项: auth.wechat.enabled', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (113, 'auth.qq.client-id', '101757976', 'Auth Qq Client-id', 'number', '系统配置: auth.qq.client-id', 1, '系统配置项: auth.qq.client-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (114, 'auth.wechat.redirect-uri', '', 'Auth Wechat Redirect-uri', 'string', '系统配置: auth.wechat.redirect-uri', 1, '系统配置项: auth.wechat.redirect-uri', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (115, 'identity-verification.alipay.private-key', '', 'Identity-verification Alipay Private-key', 'string', '系统配置: identity-verification.alipay.private-key', 1, '系统配置项: identity-verification.alipay.private-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (116, 'identity-verification.wechat.app-id', '', 'Identity-verification Wechat App-id', 'string', '系统配置: identity-verification.wechat.app-id', 1, '系统配置项: identity-verification.wechat.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (117, 'identity-verification.enabled', 'false', 'Identity-verification Enabled', 'boolean', '系统配置: identity-verification.enabled', 1, '系统配置项: identity-verification.enabled', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (118, 'identity-verification.alipay.app-id', '', 'Identity-verification Alipay App-id', 'string', '系统配置: identity-verification.alipay.app-id', 1, '系统配置项: identity-verification.alipay.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (119, 'identity-verification.provider', 'local', 'Identity-verification Provider', 'string', '系统配置: identity-verification.provider', 1, '系统配置项: identity-verification.provider', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (120, 'identity-verification.wechat.app-secret', '', 'Identity-verification Wechat App-secret', 'string', '系统配置: identity-verification.wechat.app-secret', 1, '系统配置项: identity-verification.wechat.app-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (121, 'identity-verification.alipay.public-key', '', 'Identity-verification Alipay Public-key', 'string', '系统配置: identity-verification.alipay.public-key', 1, '系统配置项: identity-verification.alipay.public-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (122, 'sms.aliyun.access-key-id', '', 'Sms Aliyun Access-key-id', 'string', '系统配置: sms.aliyun.access-key-id', 1, '系统配置项: sms.aliyun.access-key-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (123, 'sms.verification-code.daily-limit', '10', 'Sms Verification-code Daily-limit', 'number', '系统配置: sms.verification-code.daily-limit', 1, '系统配置项: sms.verification-code.daily-limit', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (124, 'sms.aliyun.access-key-secret', '', 'Sms Aliyun Access-key-secret', 'string', '系统配置: sms.aliyun.access-key-secret', 1, '系统配置项: sms.aliyun.access-key-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (125, 'sms.verification-code.expiration', '300', 'Sms Verification-code Expiration', 'number', '系统配置: sms.verification-code.expiration', 1, '系统配置项: sms.verification-code.expiration', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (126, 'sms.aliyun.template-code', 'SMS_123456789', 'Sms Aliyun Template-code', 'string', '系统配置: sms.aliyun.template-code', 1, '系统配置项: sms.aliyun.template-code', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (127, 'sms.enabled', 'false', 'Sms Enabled', 'boolean', '系统配置: sms.enabled', 1, '系统配置项: sms.enabled', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (128, 'sms.aliyun.sign-name', 'PhotoTagMoment', 'Sms Aliyun Sign-name', 'string', '系统配置: sms.aliyun.sign-name', 1, '系统配置项: sms.aliyun.sign-name', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (129, 'sms.verification-code.length', '6', 'Sms Verification-code Length', 'number', '系统配置: sms.verification-code.length', 1, '系统配置项: sms.verification-code.length', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (130, 'sms.provider', 'aliyun', 'Sms Provider', 'string', '系统配置: sms.provider', 1, '系统配置项: sms.provider', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (131, 'wechat.mp.app-id', '', 'Wechat Mp App-id', 'string', '系统配置: wechat.mp.app-id', 1, '系统配置项: wechat.mp.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (132, 'wechat.mp.aes-key', '', 'Wechat Mp Aes-key', 'string', '系统配置: wechat.mp.aes-key', 1, '系统配置项: wechat.mp.aes-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (133, 'wechat.mini-app.app-id', '', 'Wechat Mini-app App-id', 'string', '系统配置: wechat.mini-app.app-id', 1, '系统配置项: wechat.mini-app.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (134, 'wechat.mini-app.aes-key', '', 'Wechat Mini-app Aes-key', 'string', '系统配置: wechat.mini-app.aes-key', 1, '系统配置项: wechat.mini-app.aes-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (135, 'wechat.mp.token', '', 'Wechat Mp Token', 'string', '系统配置: wechat.mp.token', 1, '系统配置项: wechat.mp.token', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (136, 'wechat.mini-app.app-secret', '', 'Wechat Mini-app App-secret', 'string', '系统配置: wechat.mini-app.app-secret', 1, '系统配置项: wechat.mini-app.app-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (137, 'wechat.enabled', 'false', 'Wechat Enabled', 'boolean', '系统配置: wechat.enabled', 1, '系统配置项: wechat.enabled', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (138, 'wechat.mini-app.token', '', 'Wechat Mini-app Token', 'string', '系统配置: wechat.mini-app.token', 1, '系统配置项: wechat.mini-app.token', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (139, 'wechat.mp.app-secret', '', 'Wechat Mp App-secret', 'string', '系统配置: wechat.mp.app-secret', 1, '系统配置项: wechat.mp.app-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');

SET FOREIGN_KEY_CHECKS = 1;
