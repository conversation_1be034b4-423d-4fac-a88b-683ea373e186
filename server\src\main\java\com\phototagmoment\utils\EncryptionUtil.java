package com.phototagmoment.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 加密工具类
 * 用于敏感数据的加密和解密
 */
@Slf4j
@Component
public class EncryptionUtil {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    
    @Value("${app.encryption.secret-key:PhotoTagMoment2024SecretKey}")
    private String secretKey;

    /**
     * 加密字符串
     * @param plainText 明文
     * @return 加密后的Base64字符串
     */
    public String encrypt(String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return plainText;
        }
        
        try {
            SecretKeySpec keySpec = new SecretKeySpec(getKeyBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("加密失败", e);
            return plainText; // 加密失败时返回原文
        }
    }

    /**
     * 解密字符串
     * @param encryptedText 加密的Base64字符串
     * @return 解密后的明文
     */
    public String decrypt(String encryptedText) {
        if (encryptedText == null || encryptedText.isEmpty()) {
            return encryptedText;
        }
        
        try {
            SecretKeySpec keySpec = new SecretKeySpec(getKeyBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密失败", e);
            return encryptedText; // 解密失败时返回原文
        }
    }

    /**
     * 生成随机密钥
     * @return Base64编码的密钥
     */
    public static String generateSecretKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(256, new SecureRandom());
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            log.error("生成密钥失败", e);
            return null;
        }
    }

    /**
     * 获取密钥字节数组
     * @return 密钥字节数组
     */
    private byte[] getKeyBytes() {
        // 确保密钥长度为32字节（256位）
        String key = secretKey;
        if (key.length() < 32) {
            // 如果密钥长度不足，用0填充
            key = String.format("%-32s", key).replace(' ', '0');
        } else if (key.length() > 32) {
            // 如果密钥长度超过，截取前32位
            key = key.substring(0, 32);
        }
        return key.getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 掩码敏感信息
     * @param sensitiveInfo 敏感信息
     * @return 掩码后的信息
     */
    public static String maskSensitiveInfo(String sensitiveInfo) {
        if (sensitiveInfo == null || sensitiveInfo.isEmpty()) {
            return sensitiveInfo;
        }
        
        int length = sensitiveInfo.length();
        if (length <= 4) {
            return "*".repeat(length);
        } else if (length <= 8) {
            return sensitiveInfo.substring(0, 2) + "*".repeat(length - 4) + sensitiveInfo.substring(length - 2);
        } else {
            return sensitiveInfo.substring(0, 4) + "*".repeat(length - 8) + sensitiveInfo.substring(length - 4);
        }
    }

    /**
     * 验证字符串是否为加密格式
     * @param text 待验证的字符串
     * @return 是否为加密格式
     */
    public static boolean isEncrypted(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        try {
            // 尝试Base64解码，如果成功且长度合理，可能是加密数据
            byte[] decoded = Base64.getDecoder().decode(text);
            return decoded.length > 0 && decoded.length % 16 == 0; // AES块大小为16字节
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 安全比较两个字符串
     * @param a 字符串a
     * @param b 字符串b
     * @return 是否相等
     */
    public static boolean secureEquals(String a, String b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        
        byte[] aBytes = a.getBytes(StandardCharsets.UTF_8);
        byte[] bBytes = b.getBytes(StandardCharsets.UTF_8);
        
        if (aBytes.length != bBytes.length) {
            return false;
        }
        
        int result = 0;
        for (int i = 0; i < aBytes.length; i++) {
            result |= aBytes[i] ^ bBytes[i];
        }
        
        return result == 0;
    }

    /**
     * 生成随机盐值
     * @param length 盐值长度
     * @return Base64编码的盐值
     */
    public static String generateSalt(int length) {
        try {
            SecureRandom random = new SecureRandom();
            byte[] salt = new byte[length];
            random.nextBytes(salt);
            return Base64.getEncoder().encodeToString(salt);
        } catch (Exception e) {
            log.error("生成盐值失败", e);
            return null;
        }
    }

    /**
     * 哈希密码
     * @param password 明文密码
     * @param salt 盐值
     * @return 哈希后的密码
     */
    public static String hashPassword(String password, String salt) {
        try {
            String combined = password + salt;
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(combined.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("密码哈希失败", e);
            return null;
        }
    }

    /**
     * 验证密码
     * @param password 明文密码
     * @param salt 盐值
     * @param hashedPassword 哈希后的密码
     * @return 是否匹配
     */
    public static boolean verifyPassword(String password, String salt, String hashedPassword) {
        String computedHash = hashPassword(password, salt);
        return secureEquals(computedHash, hashedPassword);
    }
}
