package com.phototagmoment.service.parser;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * 敏感词解析器工厂
 */
@Slf4j
@Component
public class SensitiveWordParserFactory {

    private final List<SensitiveWordFileParser> parsers;

    @Autowired
    public SensitiveWordParserFactory(List<SensitiveWordFileParser> parsers) {
        this.parsers = parsers;
    }

    /**
     * 获取适合的解析器
     *
     * @param file 文件
     * @return 解析器
     * @throws IllegalArgumentException 如果没有找到适合的解析器
     */
    public SensitiveWordFileParser getParser(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        int lastDotIndex = originalFilename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            throw new IllegalArgumentException("文件没有扩展名");
        }
        
        String fileExtension = originalFilename.substring(lastDotIndex + 1);
        
        Optional<SensitiveWordFileParser> parser = parsers.stream()
                .filter(p -> p.supports(fileExtension))
                .findFirst();
        
        return parser.orElseThrow(() -> 
                new IllegalArgumentException("不支持的文件类型: " + fileExtension));
    }
}
