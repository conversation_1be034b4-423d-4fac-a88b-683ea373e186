import request from '@/utils/request'

/**
 * 获取角色列表
 * @param params 查询参数
 */
export function getRoleList(params?: any) {
  return request({
    url: '/admin/system/role/list',
    method: 'get',
    params
  })
}

/**
 * 获取角色详情
 * @param id 角色ID
 */
export function getRoleDetail(id: number) {
  return request({
    url: `/admin/system/role/${id}`,
    method: 'get'
  })
}

/**
 * 创建角色
 * @param data 角色信息
 */
export function createRole(data: any) {
  return request({
    url: '/admin/system/role',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param id 角色ID
 * @param data 角色信息
 */
export function updateRole(id: number, data: any) {
  return request({
    url: `/admin/system/role/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param id 角色ID
 */
export function deleteRole(id: number) {
  return request({
    url: `/admin/system/role/${id}`,
    method: 'delete'
  })
}

/**
 * 更新角色状态
 * @param id 角色ID
 * @param status 状态：0-禁用，1-启用
 */
export function updateRoleStatus(id: number, status: number) {
  return request({
    url: `/admin/system/role/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 获取角色权限
 * @param id 角色ID
 */
export function getRolePermissions(id: number) {
  return request({
    url: `/admin/system/role/${id}/permissions`,
    method: 'get'
  })
}

/**
 * 分配角色权限
 * @param id 角色ID
 * @param permissionIds 权限ID列表
 */
export function assignRolePermissions(id: number, permissionIds: number[]) {
  return request({
    url: `/admin/system/role/${id}/permissions`,
    method: 'post',
    data: { permissionIds }
  })
}
