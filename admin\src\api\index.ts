// 统一导出所有API接口

// 照片笔记管理
export * from './photoNote'

// 标签管理
export * from './tag'

// 用户管理
export * from './userManagement'

// 统计数据
export * from './statistics'

// 照片管理
export * from './photo'

// 评论管理
export * from './comment'

// 文件管理
export * from './file'
export * from './fileStatistics'
export * from './fileUploadConfig'

// 用户端用户管理
export * from './user'

// 仪表板
export * from './dashboard'

// 系统管理
export * from './system/admin'
export * from './system/config'
export * from './system/log'
export * from './system/permission'
export * from './system/role'
export * from './system/sensitiveWord'

// 类型定义导出
export type {
  // 照片笔记相关类型
  PhotoNoteListParams,
  PhotoNoteData,
  AuditData as PhotoNoteAuditData,
  PhotoNoteStatsData
} from './photoNote'

export type {
  // 标签相关类型
  TagListParams,
  TagData,
  TagStatsData,
  TagMergeData
} from './tag'

export type {
  // 用户管理相关类型
  UserListParams,
  UserData,
  UserStatusData
} from './userManagement'

export type {
  // 统计相关类型
  StatsParams,
  SystemStatsData,
  ContentAuditStatsData,
  UserActivityStatsData,
  TrendData
} from './statistics'

export type {
  // 系统配置相关类型
  SystemConfigData,
  ConfigListParams
} from './system/config'
