package com.phototagmoment.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 照片审核VO
 */
@Data
public class PhotoAuditVO {

    /**
     * 照片ID
     */
    private Long photoId;

    /**
     * 照片标题
     */
    private String title;

    /**
     * 照片描述
     */
    private String description;

    /**
     * 照片URL
     */
    private String url;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 照片状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除
     */
    private Integer status;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 上传时间
     */
    private LocalDateTime createdAt;

    /**
     * 最近审核时间
     */
    private LocalDateTime lastAuditTime;

    /**
     * 最近审核类型: 0-自动审核, 1-人工审核
     */
    private Integer lastAuditType;

    /**
     * 最近审核结果: 0-待审核, 1-通过, 2-拒绝
     */
    private Integer lastAuditResult;
}
