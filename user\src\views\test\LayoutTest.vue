<template>
  <div class="photo-note-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="布局和标签测试"
      left-arrow
      @click-left="$router.back()"
      class="detail-navbar"
    />

    <!-- 照片笔记内容 -->
    <div class="note-content">
      <!-- 用户信息 -->
      <div class="user-info">
        <van-image
          src="https://via.placeholder.com/40x40/4CAF50/FFFFFF?text=U"
          round
          width="40"
          height="40"
          fit="cover"
          class="user-avatar"
        />
        <div class="user-details">
          <div class="user-name">测试用户</div>
          <div class="publish-time">2小时前</div>
        </div>
        <van-button
          type="primary"
          size="small"
          round
          class="follow-btn"
        >
          关注
        </van-button>
      </div>

      <!-- 标题和内容 - PC端在照片上方显示 -->
      <div class="content-section content-section-top">
        <h2 class="note-title">测试照片笔记标题</h2>
        <div class="note-content-text" v-html="processedContent" @click="handleContentClick"></div>
      </div>

      <!-- 照片展示 -->
      <div class="photo-section">
        <div class="photo-grid grid-2x2">
          <div class="photo-item">
            <van-image
              src="https://via.placeholder.com/300x300/4CAF50/FFFFFF?text=Photo1"
              fit="cover"
              width="100%"
              height="100%"
              alt="测试照片1"
            />
          </div>
          <div class="photo-item">
            <van-image
              src="https://via.placeholder.com/300x300/2196F3/FFFFFF?text=Photo2"
              fit="cover"
              width="100%"
              height="100%"
              alt="测试照片2"
            />
          </div>
          <div class="photo-item">
            <van-image
              src="https://via.placeholder.com/300x300/FF9800/FFFFFF?text=Photo3"
              fit="cover"
              width="100%"
              height="100%"
              alt="测试照片3"
            />
          </div>
          <div class="photo-item">
            <van-image
              src="https://via.placeholder.com/300x300/E91E63/FFFFFF?text=Photo4"
              fit="cover"
              width="100%"
              height="100%"
              alt="测试照片4"
            />
          </div>
        </div>
      </div>

      <!-- 标题和内容 - 移动端在照片下方显示 -->
      <div class="content-section content-section-bottom">
        <h2 class="note-title">测试照片笔记标题</h2>
        <div class="note-content-text" v-html="processedContent" @click="handleContentClick"></div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <div class="action-buttons">
          <div class="action-item">
            <van-icon name="like-o" size="20" />
            <span class="action-text">128</span>
          </div>
          <div class="action-item">
            <van-icon name="star-o" size="20" />
            <span class="action-text">56</span>
          </div>
          <div class="action-item">
            <van-icon name="chat-o" size="20" />
            <span class="action-text">23</span>
          </div>
          <div class="action-item">
            <van-icon name="share-o" size="20" />
            <span class="action-text">分享</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试信息面板 -->
    <div class="test-panel">
      <van-cell-group title="布局测试信息">
        <van-cell title="当前屏幕宽度" :value="screenWidth + 'px'" />
        <van-cell title="布局模式" :value="screenWidth >= 768 ? 'PC端' : '移动端'" />
        <van-cell title="期望布局顺序" :value="expectedLayout" />
      </van-cell-group>
      
      <van-cell-group title="标签高亮测试">
        <van-cell title="原始内容" :value="originalContent" />
        <van-cell title="处理后内容" :value="processedContent" />
        <van-cell title="测试说明" value="点击上方的蓝色标签和橙色用户名" />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 测试数据
const originalContent = ref('今天和 @小明 @小红 一起去了 #海边# 拍照，天气很好！#旅行# #摄影# #风景# 遇到了 @小李，大家一起度过了愉快的时光。')
const screenWidth = ref(window.innerWidth)

// 处理标签和@用户高亮显示
const processedContent = computed(() => {
  let content = originalContent.value

  // 处理标签 #标签名称# 格式，显示为蓝色可点击
  content = content.replace(/#([^#]+)#/g, '<span class="tag-highlight" data-tag="$1">#$1#</span>')

  // 处理@用户提及 @用户昵称 格式，显示为橙色可点击
  content = content.replace(/@([^\s@]+)/g, '<span class="mention-highlight" data-mention="$1">@$1</span>')

  return content
})

// 期望的布局顺序
const expectedLayout = computed(() => {
  if (screenWidth.value >= 768) {
    return 'PC端: 用户信息 → 标题内容 → 照片 → 操作按钮'
  } else {
    return '移动端: 用户信息 → 照片 → 标题内容 → 操作按钮'
  }
})

// 处理标签点击
const handleTagClick = (tagName) => {
  console.log('点击标签:', tagName)
  showToast(`点击了标签: ${tagName}`)
}

// 处理@用户提及点击
const handleMentionClick = (username) => {
  console.log('点击用户提及:', username)
  showToast(`点击了用户: ${username}`)
}

// 内容点击处理函数
const handleContentClick = (event) => {
  const target = event.target
  console.log('点击了内容元素:', target)

  // 处理标签点击
  if (target.classList.contains('tag-highlight')) {
    const tagName = target.getAttribute('data-tag')
    console.log('点击标签:', tagName)
    if (tagName) {
      handleTagClick(tagName)
    }
  }

  // 处理用户提及点击
  if (target.classList.contains('mention-highlight')) {
    const username = target.getAttribute('data-mention')
    console.log('点击用户提及:', username)
    if (username) {
      handleMentionClick(username)
    }
  }
}

// 监听窗口大小变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  console.log('=== 布局和标签测试页面 ===')
  console.log('原始内容:', originalContent.value)
  console.log('处理后内容:', processedContent.value)
  console.log('当前屏幕宽度:', screenWidth.value)
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateScreenWidth)
  
  // 调试DOM渲染
  nextTick(() => {
    setTimeout(() => {
      const contentElements = document.querySelectorAll('.note-content-text')
      console.log('找到内容元素数量:', contentElements.length)
      
      contentElements.forEach((element, index) => {
        console.log(`内容元素${index + 1} HTML:`, element.innerHTML)
        const highlights = element.querySelectorAll('.tag-highlight, .mention-highlight')
        console.log(`找到${highlights.length}个高亮元素`)
        highlights.forEach((highlight, hIndex) => {
          console.log(`高亮元素${hIndex + 1}:`, highlight.outerHTML)
        })
      })
    }, 100)
  })
})
</script>

<style scoped>
/* 复用原有样式 */
.photo-note-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.detail-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.note-content {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  margin-right: 12px;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  cursor: pointer;
}

.publish-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.follow-btn {
  margin-left: 12px;
}

.photo-section {
  padding: 0 16px;
}

.photo-grid {
  display: grid;
  gap: 4px;
  margin-bottom: 16px;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.content-section {
  padding: 0 16px 16px;
}

.note-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.note-content-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 标签和用户提及高亮样式 */
.note-content-text :deep(.tag-highlight) {
  color: #1989fa;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.note-content-text :deep(.tag-highlight:hover) {
  color: #0570d1;
  text-decoration: underline;
}

.note-content-text :deep(.mention-highlight) {
  color: #ff8c00;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.note-content-text :deep(.mention-highlight:hover) {
  color: #e67700;
  text-decoration: underline;
}

/* 操作按钮区域 */
.action-section {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.action-item:hover {
  background-color: #f5f5f5;
}

.action-item .van-icon {
  margin-bottom: 4px;
  color: #666;
  transition: color 0.2s;
}

.action-text {
  font-size: 12px;
  color: #666;
}

.test-panel {
  margin: 16px;
}

/* 内容区域布局控制 */
/* 移动端：标题内容在照片下方 */
.note-content .content-section-top {
  display: none !important; /* 移动端隐藏标题内容在照片上方 */
}

.note-content .content-section-bottom {
  display: block !important; /* 移动端显示标题内容在照片下方 */
}

/* PC端适配 */
@media (min-width: 768px) {
  .photo-note-detail {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
  }

  .note-content {
    margin: 0;
    border-radius: 0;
  }

  .action-buttons {
    max-width: 400px;
    margin: 0 auto;
  }

  .action-item {
    flex-direction: row;
    gap: 8px;
  }

  .action-item .van-icon {
    margin-bottom: 0;
  }

  /* PC端布局：标题和内容在照片上方 */
  .note-content .content-section-top {
    display: block !important; /* PC端显示标题内容在照片上方 */
  }

  .note-content .content-section-bottom {
    display: none !important; /* PC端隐藏标题内容在照片下方 */
  }

  /* PC端照片网格优化 */
  .photo-grid.grid-2x2 {
    max-width: 600px;
    margin: 0 auto;
  }

  .test-panel {
    margin: 0;
  }
}
</style>
