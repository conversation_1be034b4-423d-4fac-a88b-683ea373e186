<template>
  <el-card class="form-section" header="存储路径配置">
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="根目录">
          <el-input
            v-model="localConfig.rootPath"
            placeholder="请输入根目录路径"
            @blur="handleChange"
          />
          <div class="form-tip">
            文件存储的根目录，相对于存储服务的根路径
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="文件命名规则">
          <el-select
            v-model="localConfig.fileNamingRule"
            placeholder="请选择命名规则"
            style="width: 100%"
            @change="handleChange"
          >
            <el-option label="UUID" value="UUID" />
            <el-option label="时间戳" value="TIMESTAMP" />
            <el-option label="原文件名" value="ORIGINAL" />
            <el-option label="时间戳+原文件名" value="TIMESTAMP_ORIGINAL" />
            <el-option label="UUID+原文件名" value="UUID_ORIGINAL" />
          </el-select>
          <div class="form-tip">
            文件重命名规则，建议使用UUID避免文件名冲突
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="目录结构规则">
      <el-select
        v-model="localConfig.directoryStructure"
        placeholder="请选择目录结构"
        style="width: 100%"
        @change="handleChange"
      >
        <el-option label="扁平结构" value="FLAT" />
        <el-option label="按日期分类" value="DATE" />
        <el-option label="按用户分类" value="USER" />
        <el-option label="按文件类型分类" value="TYPE" />
        <el-option label="日期+用户" value="DATE_USER" />
        <el-option label="日期+类型" value="DATE_TYPE" />
        <el-option label="用户+类型" value="USER_TYPE" />
        <el-option label="日期+用户+类型" value="DATE_USER_TYPE" />
      </el-select>
      <div class="form-tip">
        目录组织结构，影响文件的存储路径
      </div>
    </el-form-item>

    <el-form-item label="目录选项">
      <div class="directory-options">
        <el-checkbox
          v-model="localConfig.enableDateDirectory"
          @change="handleChange"
        >
          启用日期目录
        </el-checkbox>
        <div class="option-description">
          按年/月/日创建目录结构，如：2024/01/15/
        </div>
      </div>
      <div class="directory-options">
        <el-checkbox
          v-model="localConfig.enableUserDirectory"
          @change="handleChange"
        >
          启用用户目录
        </el-checkbox>
        <div class="option-description">
          为每个用户创建独立目录，如：user_123/
        </div>
      </div>
      <div class="directory-options">
        <el-checkbox
          v-model="localConfig.enableTypeDirectory"
          @change="handleChange"
        >
          启用类型目录
        </el-checkbox>
        <div class="option-description">
          按文件类型创建目录，如：images/、documents/
        </div>
      </div>
    </el-form-item>

    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="自定义前缀">
          <el-input
            v-model="localConfig.customPrefix"
            placeholder="请输入自定义路径前缀"
            @blur="handleChange"
          />
          <div class="form-tip">
            可选的路径前缀，如：project_name/
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="缩略图目录">
          <el-input
            v-model="localConfig.thumbnailDirectory"
            placeholder="请输入缩略图目录名称"
            @blur="handleChange"
          />
          <div class="form-tip">
            存储缩略图的目录名称
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="临时文件目录">
      <el-input
        v-model="localConfig.tempDirectory"
        placeholder="请输入临时文件目录名称"
        @blur="handleChange"
      />
      <div class="form-tip">
        存储临时文件的目录名称，用于分片上传等场景
      </div>
    </el-form-item>

    <el-form-item label="路径预览">
      <div class="path-preview">
        <div class="preview-item">
          <span class="preview-label">完整路径示例：</span>
          <code class="preview-path">{{ generatePreviewPath() }}</code>
        </div>
        <div class="preview-item">
          <span class="preview-label">缩略图路径：</span>
          <code class="preview-path">{{ generateThumbnailPath() }}</code>
        </div>
        <div class="preview-item">
          <span class="preview-label">临时文件路径：</span>
          <code class="preview-path">{{ generateTempPath() }}</code>
        </div>
      </div>
    </el-form-item>

    <el-form-item label="快速配置">
      <el-button-group>
        <el-button size="small" @click="applyPreset('simple')">
          简单模式
        </el-button>
        <el-button size="small" @click="applyPreset('organized')">
          组织模式
        </el-button>
        <el-button size="small" @click="applyPreset('enterprise')">
          企业模式
        </el-button>
        <el-button size="small" @click="applyPreset('cdn')">
          CDN模式
        </el-button>
      </el-button-group>
    </el-form-item>
  </el-card>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import type { PathConfig } from '@/api/fileUploadConfig'

// Props
interface Props {
  modelValue?: PathConfig
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    rootPath: 'uploads',
    fileNamingRule: 'UUID',
    directoryStructure: 'DATE_USER_TYPE',
    enableDateDirectory: true,
    enableUserDirectory: true,
    enableTypeDirectory: true,
    customPrefix: '',
    thumbnailDirectory: 'thumbnails',
    tempDirectory: 'temp'
  })
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: PathConfig]
}>()

// 响应式数据
const localConfig = reactive<PathConfig>({
  rootPath: 'uploads',
  fileNamingRule: 'UUID',
  directoryStructure: 'DATE_USER_TYPE',
  enableDateDirectory: true,
  enableUserDirectory: true,
  enableTypeDirectory: true,
  customPrefix: '',
  thumbnailDirectory: 'thumbnails',
  tempDirectory: 'temp'
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(localConfig, newValue)
  }
}, { immediate: true, deep: true })

// 方法
const handleChange = () => {
  emit('update:modelValue', { ...localConfig })
}

const generatePreviewPath = () => {
  let path = localConfig.rootPath || 'uploads'
  
  // 添加自定义前缀
  if (localConfig.customPrefix) {
    path += '/' + localConfig.customPrefix.replace(/^\/+|\/+$/g, '')
  }
  
  // 根据目录结构规则添加路径
  const structure = localConfig.directoryStructure || 'DATE_USER_TYPE'
  
  if (structure.includes('DATE') || localConfig.enableDateDirectory) {
    path += '/2024/01/15'
  }
  
  if (structure.includes('USER') || localConfig.enableUserDirectory) {
    path += '/user_123'
  }
  
  if (structure.includes('TYPE') || localConfig.enableTypeDirectory) {
    path += '/images'
  }
  
  // 添加文件名
  const namingRule = localConfig.fileNamingRule || 'UUID'
  let fileName = ''
  
  switch (namingRule) {
    case 'UUID':
      fileName = 'a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg'
      break
    case 'TIMESTAMP':
      fileName = '1704067200000.jpg'
      break
    case 'ORIGINAL':
      fileName = 'example.jpg'
      break
    case 'TIMESTAMP_ORIGINAL':
      fileName = '1704067200000_example.jpg'
      break
    case 'UUID_ORIGINAL':
      fileName = 'a1b2c3d4_example.jpg'
      break
    default:
      fileName = 'file.jpg'
  }
  
  return path + '/' + fileName
}

const generateThumbnailPath = () => {
  const basePath = generatePreviewPath()
  const pathParts = basePath.split('/')
  const fileName = pathParts.pop()
  const thumbnailDir = localConfig.thumbnailDirectory || 'thumbnails'
  
  return pathParts.join('/') + '/' + thumbnailDir + '/' + fileName
}

const generateTempPath = () => {
  let path = localConfig.rootPath || 'uploads'
  
  if (localConfig.customPrefix) {
    path += '/' + localConfig.customPrefix.replace(/^\/+|\/+$/g, '')
  }
  
  const tempDir = localConfig.tempDirectory || 'temp'
  return path + '/' + tempDir + '/chunk_a1b2c3d4.tmp'
}

const applyPreset = (preset: string) => {
  switch (preset) {
    case 'simple':
      localConfig.rootPath = 'uploads'
      localConfig.fileNamingRule = 'UUID'
      localConfig.directoryStructure = 'FLAT'
      localConfig.enableDateDirectory = false
      localConfig.enableUserDirectory = false
      localConfig.enableTypeDirectory = false
      localConfig.customPrefix = ''
      break
    case 'organized':
      localConfig.rootPath = 'uploads'
      localConfig.fileNamingRule = 'UUID'
      localConfig.directoryStructure = 'DATE_TYPE'
      localConfig.enableDateDirectory = true
      localConfig.enableUserDirectory = false
      localConfig.enableTypeDirectory = true
      localConfig.customPrefix = ''
      break
    case 'enterprise':
      localConfig.rootPath = 'files'
      localConfig.fileNamingRule = 'UUID_ORIGINAL'
      localConfig.directoryStructure = 'DATE_USER_TYPE'
      localConfig.enableDateDirectory = true
      localConfig.enableUserDirectory = true
      localConfig.enableTypeDirectory = true
      localConfig.customPrefix = 'enterprise'
      break
    case 'cdn':
      localConfig.rootPath = 'static'
      localConfig.fileNamingRule = 'UUID'
      localConfig.directoryStructure = 'TYPE'
      localConfig.enableDateDirectory = false
      localConfig.enableUserDirectory = false
      localConfig.enableTypeDirectory = true
      localConfig.customPrefix = 'cdn'
      break
  }
  
  handleChange()
}
</script>

<style scoped>
.directory-options {
  margin-bottom: 12px;
}

.directory-options:last-child {
  margin-bottom: 0;
}

.option-description {
  margin-top: 4px;
  margin-left: 24px;
  color: #909399;
  font-size: 12px;
}

.form-tip {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.path-preview {
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.preview-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-label {
  min-width: 120px;
  color: #606266;
  font-size: 14px;
}

.preview-path {
  flex: 1;
  padding: 4px 8px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #409eff;
}

.el-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
