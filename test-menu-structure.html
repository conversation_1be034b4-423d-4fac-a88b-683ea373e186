<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotoTagMoment 菜单结构测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px;
        }
        .menu-section {
            margin-bottom: 40px;
        }
        .menu-title {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #409eff;
            display: flex;
            align-items: center;
        }
        .menu-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background-size: contain;
        }
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .menu-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #409eff;
        }
        .menu-item h3 {
            margin: 0 0 10px 0;
            color: #409eff;
            font-size: 16px;
        }
        .menu-item p {
            margin: 0;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
        }
        .menu-item .path {
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #909399;
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 3px;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.new {
            background: #e1f3d8;
            color: #67c23a;
        }
        .status.enhanced {
            background: #ecf5ff;
            color: #409eff;
        }
        .test-section {
            margin-top: 40px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 6px;
            border-left: 4px solid #409eff;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .test-link {
            display: block;
            padding: 10px 15px;
            background: white;
            color: #409eff;
            text-decoration: none;
            border-radius: 4px;
            border: 1px solid #409eff;
            text-align: center;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: #409eff;
            color: white;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        .comparison-item {
            padding: 20px;
            border-radius: 6px;
        }
        .comparison-item.before {
            background: #fef0f0;
            border-left: 4px solid #f56c6c;
        }
        .comparison-item.after {
            background: #f0f9ff;
            border-left: 4px solid #409eff;
        }
        .comparison-item h3 {
            margin: 0 0 15px 0;
            color: #303133;
        }
        .comparison-item ul {
            margin: 0;
            padding-left: 20px;
        }
        .comparison-item li {
            margin-bottom: 5px;
            color: #606266;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 PhotoTagMoment 菜单结构重构</h1>
            <p>新的三级菜单架构 - 提升管理效率和用户体验</p>
        </div>
        
        <div class="content">
            <!-- 内容管理 -->
            <div class="menu-section">
                <div class="menu-title">
                    📝 内容管理 <span class="status new">新增</span>
                </div>
                <div class="menu-grid">
                    <div class="menu-item">
                        <h3>敏感词管理</h3>
                        <p>管理内容过滤的敏感词库，支持词库导入导出和智能匹配</p>
                        <div class="path">/content/sensitive-word</div>
                    </div>
                    <div class="menu-item">
                        <h3>内容审核配置</h3>
                        <p>配置内容审核规则和第三方审核服务，设置审核策略</p>
                        <div class="path">/content/content-moderation</div>
                    </div>
                    <div class="menu-item">
                        <h3>内容审核 <span class="status new">新增</span></h3>
                        <p>审核用户发布的照片笔记内容，支持批量审核和详细预览</p>
                        <div class="path">/content/content-review</div>
                    </div>
                    <div class="menu-item">
                        <h3>举报管理 <span class="status new">新增</span></h3>
                        <p>处理用户举报的内容和行为，维护社区秩序和规范</p>
                        <div class="path">/content/report-management</div>
                    </div>
                </div>
            </div>

            <!-- 文件管理 -->
            <div class="menu-section">
                <div class="menu-title">
                    📁 文件管理 <span class="status new">新增</span>
                </div>
                <div class="menu-grid">
                    <div class="menu-item">
                        <h3>文件管理</h3>
                        <p>查看和管理已上传的文件，支持文件搜索、预览和批量操作</p>
                        <div class="path">/file/management</div>
                    </div>
                    <div class="menu-item">
                        <h3>上传配置</h3>
                        <p>配置文件上传存储服务和限制，支持多种存储方案</p>
                        <div class="path">/file/upload-config</div>
                    </div>
                    <div class="menu-item">
                        <h3>存储配置 <span class="status new">新增</span></h3>
                        <p>配置和管理多种存储服务，支持本地存储和云存储</p>
                        <div class="path">/file/storage-config</div>
                    </div>
                    <div class="menu-item">
                        <h3>文件统计 <span class="status new">新增</span></h3>
                        <p>查看文件上传和存储统计信息，提供详细的数据分析</p>
                        <div class="path">/file/file-statistics</div>
                    </div>
                </div>
            </div>

            <!-- 系统管理 -->
            <div class="menu-section">
                <div class="menu-title">
                    ⚙️ 系统管理 <span class="status enhanced">精简优化</span>
                </div>
                <div class="menu-grid">
                    <div class="menu-item">
                        <h3>管理员管理</h3>
                        <p>管理后台管理员账户，设置管理员权限和角色</p>
                        <div class="path">/system/admin</div>
                    </div>
                    <div class="menu-item">
                        <h3>角色管理</h3>
                        <p>管理用户角色和权限分配，支持自定义角色创建</p>
                        <div class="path">/system/role</div>
                    </div>
                    <div class="menu-item">
                        <h3>权限管理</h3>
                        <p>管理系统权限点和访问控制，细粒度权限配置</p>
                        <div class="path">/system/permission</div>
                    </div>
                    <div class="menu-item">
                        <h3>系统配置</h3>
                        <p>管理系统基础配置参数，支持配置热更新</p>
                        <div class="path">/system/config</div>
                    </div>
                    <div class="menu-item">
                        <h3>第三方登录配置</h3>
                        <p>配置微信、QQ等第三方登录服务，管理OAuth配置</p>
                        <div class="path">/system/auth-config</div>
                    </div>
                    <div class="menu-item">
                        <h3>实名认证配置</h3>
                        <p>配置实名认证服务，设置认证流程和规则</p>
                        <div class="path">/system/identity-verification-config</div>
                    </div>
                    <div class="menu-item">
                        <h3>短信配置</h3>
                        <p>配置短信发送服务，管理短信模板和发送策略</p>
                        <div class="path">/system/sms-config</div>
                    </div>
                    <div class="menu-item">
                        <h3>操作日志</h3>
                        <p>查看系统操作记录，支持日志搜索和导出</p>
                        <div class="path">/system/log</div>
                    </div>
                    <div class="menu-item">
                        <h3>系统监控</h3>
                        <p>实时监控系统运行状态，查看性能指标和告警信息</p>
                        <div class="path">/system/monitor</div>
                    </div>
                </div>
            </div>

            <!-- 对比说明 -->
            <div class="comparison">
                <div class="comparison-item before">
                    <h3>🔴 重构前 - 单一菜单</h3>
                    <ul>
                        <li>所有功能混合在一个"系统管理"菜单下</li>
                        <li>菜单层级深，查找功能困难</li>
                        <li>功能分类不清晰，用户体验差</li>
                        <li>扩展性差，新功能难以归类</li>
                        <li>管理员需要在长列表中寻找功能</li>
                    </ul>
                </div>
                <div class="comparison-item after">
                    <h3>🟢 重构后 - 三级架构</h3>
                    <ul>
                        <li>按业务领域分为三个一级菜单</li>
                        <li>功能分组清晰，逻辑性强</li>
                        <li>提升管理效率和用户体验</li>
                        <li>便于功能扩展和维护</li>
                        <li>符合管理员的使用习惯</li>
                    </ul>
                </div>
            </div>

            <!-- 测试链接 -->
            <div class="test-section">
                <h3>🧪 功能测试</h3>
                <p>点击下方链接测试新的菜单功能（需要先登录后台管理系统）</p>
                <div class="test-links">
                    <a href="http://localhost:3001/#/content/content-review" class="test-link" target="_blank">内容审核</a>
                    <a href="http://localhost:3001/#/content/report-management" class="test-link" target="_blank">举报管理</a>
                    <a href="http://localhost:3001/#/file/storage-config" class="test-link" target="_blank">存储配置</a>
                    <a href="http://localhost:3001/#/file/file-statistics" class="test-link" target="_blank">文件统计</a>
                    <a href="http://localhost:3001/#/system/monitor" class="test-link" target="_blank">系统监控</a>
                    <a href="http://localhost:3001/#/system/admin" class="test-link" target="_blank">管理员管理</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加点击效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                const path = this.querySelector('.path').textContent;
                console.log('导航到:', path);
                // 这里可以添加实际的导航逻辑
            });
        });

        // 显示加载完成信息
        console.log('📋 PhotoTagMoment 菜单结构测试页面加载完成');
        console.log('🎯 新增功能页面: 4个');
        console.log('📁 文件管理模块: 4个功能');
        console.log('📝 内容管理模块: 4个功能');
        console.log('⚙️ 系统管理模块: 9个功能');
    </script>
</body>
</html>
