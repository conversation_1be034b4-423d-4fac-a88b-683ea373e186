package com.phototagmoment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.common.ResultCode;
import com.phototagmoment.dto.LoginDTO;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.entity.UserVerification;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.mapper.UserVerificationMapper;
import com.phototagmoment.security.JwtTokenProvider;
import com.phototagmoment.service.AdminService;
import com.phototagmoment.vo.AdminVO;
import com.phototagmoment.vo.TokenVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 管理员服务实现类
 */
@Slf4j
@Service
public class AdminServiceImpl implements AdminService {

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserVerificationMapper userVerificationMapper;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private UserAuthMapper userAuthMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public TokenVO login(LoginDTO loginDTO) {
        try {
            log.info("管理员登录: {}", loginDTO.getUsername());

            // 查找用户
            User user = userMapper.selectByUsername(loginDTO.getUsername());
            if (user == null) {
                log.warn("登录失败: 用户不存在 - {}", loginDTO.getUsername());
                throw new BusinessException(ResultCode.USERNAME_OR_PASSWORD_ERROR);
            }

            // 检查是否是管理员
            if (user.getIsAdmin() != 1) {
                log.warn("登录失败: 非管理员账号 - {}", loginDTO.getUsername());
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "非管理员账号，无法登录");
            }

            // 检查用户状态
            if (user.getStatus() == 0) {
                log.warn("登录失败: 用户已禁用 - {}", loginDTO.getUsername());
                throw new BusinessException(ResultCode.USER_ACCOUNT_DISABLED);
            }

            log.info("用户存在，开始验证密码: {}", user.getUsername());

            // 查询用户认证信息
            UserAuth userAuth = userAuthMapper.selectByUserIdAndType(user.getId(), "username");
            if (userAuth == null) {
                log.error("用户认证信息不存在: {}", user.getUsername());
                throw new BusinessException(ResultCode.SYSTEM_ERROR, "用户认证信息不存在");
            }

            // 直接验证密码，不使用Spring Security的认证机制
            if (!passwordEncoder.matches(loginDTO.getPassword(), userAuth.getCredential())) {
                log.warn("密码验证失败: {}", user.getUsername());
                throw new BusinessException(ResultCode.USERNAME_OR_PASSWORD_ERROR);
            }

            log.info("密码验证成功: {}", user.getUsername());

            // 直接创建认证对象，不使用认证管理器
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));

            UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                "", // 密码为空，因为我们使用JWT认证
                user.getStatus() == 1, // 启用状态
                true, // 账号未过期
                true, // 凭证未过期
                true, // 账号未锁定
                authorities
            );

            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                userDetails, null, userDetails.getAuthorities());

            // 设置认证信息到SecurityContext
            SecurityContextHolder.getContext().setAuthentication(authentication);
            log.info("设置认证信息到SecurityContext成功: {}", user.getUsername());

            // 生成JWT
            String token = jwtTokenProvider.generateToken(user.getUsername(), user.getId());
            log.info("生成JWT成功: {}", user.getUsername());

            // 更新用户最后登录时间
            user.setLastLoginTime(LocalDateTime.now());
            userMapper.updateById(user);
            log.info("更新用户登录信息成功: {}", user.getUsername());

            // 构建返回对象
            TokenVO tokenVO = new TokenVO();
            tokenVO.setToken(token);
            tokenVO.setTokenType(tokenPrefix);
            tokenVO.setExpiresIn(jwtExpiration / 1000);
            tokenVO.setUser(convertToAdminVO(user));

            return tokenVO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("管理员登录异常: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "系统错误，请稍后再试");
        }
    }

    @Override
    public AdminVO getCurrentAdmin() {
        // 获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        // 获取用户名
        String username = null;
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            username = (String) principal;
        }

        if (username == null) {
            return null;
        }

        // 查询用户
        User user = userMapper.selectByUsername(username);
        if (user == null || user.getIsAdmin() != 1) {
            return null;
        }

        return convertToAdminVO(user);
    }

    @Override
    public boolean logout() {
        // 清除认证信息
        SecurityContextHolder.clearContext();
        return true;
    }

    @Override
    public IPage<UserDTO> getUserList(int page, int size, String username, String phone, String email, Integer status) {
        Page<User> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StrUtil.isNotBlank(username)) {
            queryWrapper.like(User::getUsername, username);
        }
        if (StrUtil.isNotBlank(phone)) {
            queryWrapper.like(User::getPhone, phone);
        }
        if (StrUtil.isNotBlank(email)) {
            queryWrapper.like(User::getEmail, email);
        }
        if (status != null) {
            queryWrapper.eq(User::getStatus, status);
        }

        // 排除管理员
        queryWrapper.eq(User::getIsAdmin, 0);
        queryWrapper.orderByDesc(User::getCreatedAt);

        // 查询用户列表
        IPage<User> userPage = userMapper.selectPage(pageParam, queryWrapper);

        // 转换为DTO
        IPage<UserDTO> userDTOPage = new Page<>(userPage.getCurrent(), userPage.getSize(), userPage.getTotal());
        List<UserDTO> userDTOList = new ArrayList<>();
        for (User user : userPage.getRecords()) {
            UserDTO userDTO = new UserDTO();
            BeanUtil.copyProperties(user, userDTO);
            userDTOList.add(userDTO);
        }
        userDTOPage.setRecords(userDTOList);

        return userDTOPage;
    }

    @Override
    public UserDTO getUserDetail(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        UserDTO userDTO = new UserDTO();
        BeanUtil.copyProperties(user, userDTO);

        // 获取实名认证信息
        UserVerification verification = userVerificationMapper.selectByUserId(id);
        if (verification != null) {
            userDTO.setRealName(verification.getRealName());
            userDTO.setIdCard(verification.getIdCard());
            userDTO.setVerificationStatus(verification.getStatus());
        }

        return userDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatus(Long id, Integer status) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 不能修改管理员状态
        if (user.getIsAdmin() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "不能修改管理员状态");
        }

        user.setStatus(status);
        return userMapper.updateById(user) > 0;
    }

    @Override
    public IPage<UserDTO> getVerificationList(int page, int size, Integer status) {
        Page<UserVerification> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<UserVerification> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (status != null) {
            queryWrapper.eq(UserVerification::getStatus, status);
        }

        queryWrapper.orderByDesc(UserVerification::getCreatedAt);

        // 查询实名认证列表
        IPage<UserVerification> verificationPage = userVerificationMapper.selectPage(pageParam, queryWrapper);

        // 转换为DTO
        IPage<UserDTO> userDTOPage = new Page<>(verificationPage.getCurrent(), verificationPage.getSize(), verificationPage.getTotal());
        List<UserDTO> userDTOList = new ArrayList<>();
        for (UserVerification verification : verificationPage.getRecords()) {
            User user = userMapper.selectById(verification.getUserId());
            if (user != null) {
                UserDTO userDTO = new UserDTO();
                BeanUtil.copyProperties(user, userDTO);
                userDTO.setRealName(verification.getRealName());
                userDTO.setIdCard(verification.getIdCard());
                userDTO.setVerificationStatus(verification.getStatus());
                userDTOList.add(userDTO);
            }
        }
        userDTOPage.setRecords(userDTOList);

        return userDTOPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyUser(Long id, Integer status, String reason) {
        UserVerification verification = userVerificationMapper.selectByUserId(id);
        if (verification == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户未提交实名认证");
        }

        // 只能审核待审核状态的认证
        if (verification.getStatus() != 0) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "该认证已审核");
        }

        // 更新认证状态
        verification.setStatus(status);
        verification.setRemark(reason);
        verification.setVerifiedAt(LocalDateTime.now());
        boolean result = userVerificationMapper.updateById(verification) > 0;

        // 如果审核通过，更新用户实名状态
        if (status == 1) {
            User user = userMapper.selectById(id);
            if (user != null) {
                user.setIsVerified(1);
                userMapper.updateById(user);
            }
        }

        return result;
    }

    @Override
    public AdminVO convertToAdminVO(User user) {
        if (user == null) {
            return null;
        }

        AdminVO adminVO = new AdminVO();
        BeanUtil.copyProperties(user, adminVO);

        // 设置角色和权限
        List<String> roles = new ArrayList<>();
        roles.add("ADMIN");
        adminVO.setRoles(roles);

        List<String> permissions = new ArrayList<>();
        permissions.add("user:view");
        permissions.add("user:edit");
        permissions.add("content:view");
        permissions.add("content:edit");
        permissions.add("system:view");
        permissions.add("system:edit");
        adminVO.setPermissions(permissions);

        return adminVO;
    }
}
