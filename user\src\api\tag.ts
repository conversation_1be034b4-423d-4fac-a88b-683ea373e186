import request from '@/utils/request'

// 搜索标签
export function searchTags(keyword: string, limit: number = 10) {
  return request({
    url: '/search/tags',
    method: 'get',
    params: {
      keyword,
      limit
    }
  })
}

// 获取热门标签
export function getHotTags(limit: number = 10) {
  return request({
    url: '/search/popular-tags',
    method: 'get',
    params: {
      limit
    }
  })
}

// 获取标签详情
export function getTagDetail(tagName: string) {
  return request({
    url: `/tag/${encodeURIComponent(tagName)}`,
    method: 'get'
  })
}

// 获取标签相关内容
export function getTagContent(tagName: string, page: number = 1, size: number = 10) {
  return request({
    url: `/tag/${encodeURIComponent(tagName)}/content`,
    method: 'get',
    params: {
      page,
      size
    }
  })
}
