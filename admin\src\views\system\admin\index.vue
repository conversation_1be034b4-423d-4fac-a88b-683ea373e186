<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="queryParams.keyword"
        placeholder="请输入用户名/姓名/手机号"
        style="width: 200px;"
        class="filter-item"
        clearable
        @keyup.enter="handleSearch"
      />
      <el-button type="primary" class="filter-item" @click="handleSearch">
        <el-icon><Search /></el-icon>
        搜索
      </el-button>
      <el-button type="primary" @click="handleCreate">
        <el-icon><Plus /></el-icon>
        新增管理员
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="adminList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" min-width="120" />
      <el-table-column prop="name" label="姓名" min-width="120" />
      <el-table-column prop="roleName" label="角色" min-width="120" />
      <el-table-column prop="phone" label="手机号" min-width="120" />
      <el-table-column prop="email" label="邮箱" min-width="150" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginTime" label="最后登录时间" min-width="150" />
      <el-table-column prop="lastLoginIp" label="最后登录IP" min-width="120" />
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleUpdate(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="warning"
            link
            @click="handleResetPassword(scope.row)"
          >
            重置密码
          </el-button>
          <el-button
            type="danger"
            link
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 管理员表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" :disabled="form.id !== undefined" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="角色" prop="roleId">
          <el-select v-model="form.roleId" placeholder="请选择角色" style="width: 100%">
            <el-option
              v-for="role in roleOptions"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="form.id === undefined">
          <el-input v-model="form.password" placeholder="请输入密码" type="password" show-password />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      v-model="passwordDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input v-model="passwordForm.oldPassword" placeholder="请输入原密码" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" placeholder="请输入新密码" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPasswordForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import {
  getAdminList,
  getAdminDetail,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  updateAdminStatus,
  resetAdminPassword,
  updateAdminPassword
} from '@/api/system/admin'
import { getRoleList } from '@/api/system/role'
import { useUserStore } from '@/stores/user'

// 用户存储
const userStore = useUserStore()

// 管理员列表
const adminList = ref([])
// 总记录数
const total = ref(0)
// 加载状态
const loading = ref(false)
// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  keyword: ''
})
// 对话框标题
const dialogTitle = ref('新增管理员')
// 对话框可见性
const dialogVisible = ref(false)
// 密码对话框可见性
const passwordDialogVisible = ref(false)
// 表单引用
const formRef = ref()
// 密码表单引用
const passwordFormRef = ref()
// 表单数据
const form = reactive({
  id: undefined,
  username: '',
  name: '',
  roleId: undefined,
  phone: '',
  email: '',
  password: '',
  status: 1
})
// 密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})
// 角色选项
const roleOptions = ref([])
// 表单校验规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}
// 密码表单校验规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取管理员列表
const fetchAdminList = async () => {
  loading.value = true
  try {
    const { data } = await getAdminList({
      page: queryParams.page,
      pageSize: queryParams.pageSize,
      keyword: queryParams.keyword
    })
    adminList.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('获取管理员列表失败', error)
    ElMessage.error('获取管理员列表失败')
  } finally {
    loading.value = false
  }
}

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const { data } = await getRoleList()
    roleOptions.value = data
  } catch (error) {
    console.error('获取角色列表失败', error)
    ElMessage.error('获取角色列表失败')
  }
}

// 重置表单
const resetForm = () => {
  form.id = undefined
  form.username = ''
  form.name = ''
  form.roleId = undefined
  form.phone = ''
  form.email = ''
  form.password = ''
  form.status = 1
  formRef.value?.resetFields()
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  passwordFormRef.value?.resetFields()
}

// 处理搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchAdminList()
}

// 处理页码变更
const handleCurrentChange = (page) => {
  queryParams.page = page
  fetchAdminList()
}

// 处理每页条数变更
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.page = 1
  fetchAdminList()
}

// 处理新增管理员
const handleCreate = () => {
  resetForm()
  dialogTitle.value = '新增管理员'
  dialogVisible.value = true
}

// 处理编辑管理员
const handleUpdate = async (row) => {
  resetForm()
  dialogTitle.value = '编辑管理员'

  try {
    const { data } = await getAdminDetail(row.id)
    Object.assign(form, data)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取管理员详情失败', error)
    ElMessage.error('获取管理员详情失败')
  }
}

// 处理删除管理员
const handleDelete = (row) => {
  // 不能删除自己
  if (row.id === userStore.userInfo.id) {
    ElMessage.warning('不能删除当前登录的管理员')
    return
  }

  ElMessageBox.confirm('确认删除该管理员吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteAdmin(row.id)
      ElMessage.success('删除成功')
      fetchAdminList()
    } catch (error) {
      console.error('删除管理员失败', error)
      ElMessage.error('删除管理员失败')
    }
  }).catch(() => {})
}

// 处理状态变更
const handleStatusChange = async (row) => {
  // 不能禁用自己
  if (row.id === userStore.userInfo.id && row.status === 0) {
    ElMessage.warning('不能禁用当前登录的管理员')
    row.status = 1
    return
  }

  try {
    await updateAdminStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('更新管理员状态失败', error)
    ElMessage.error('更新管理员状态失败')
    // 恢复状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 处理重置密码
const handleResetPassword = (row) => {
  ElMessageBox.confirm('确认重置该管理员的密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const { data } = await resetAdminPassword(row.id)
      ElMessageBox.alert(`密码重置成功，新密码为：${data}`, '提示', {
        confirmButtonText: '确定',
        type: 'success'
      })
    } catch (error) {
      console.error('重置密码失败', error)
      ElMessage.error('重置密码失败')
    }
  }).catch(() => {})
}

// 处理修改密码
const handleChangePassword = () => {
  resetPasswordForm()
  passwordDialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  formRef.value?.validate(async (valid) => {
    if (!valid) return

    try {
      if (form.id) {
        // 更新管理员
        await updateAdmin(form.id, form)
        ElMessage.success('更新成功')
      } else {
        // 创建管理员
        await createAdmin(form)
        ElMessage.success('创建成功')
      }
      dialogVisible.value = false
      fetchAdminList()
    } catch (error) {
      console.error('保存管理员失败', error)
      ElMessage.error('保存管理员失败')
    }
  })
}

// 提交密码表单
const submitPasswordForm = async () => {
  passwordFormRef.value?.validate(async (valid) => {
    if (!valid) return

    try {
      await updateAdminPassword(passwordForm)
      ElMessage.success('密码修改成功')
      passwordDialogVisible.value = false

      // 提示用户重新登录
      ElMessageBox.alert('密码已修改，请重新登录', '提示', {
        confirmButtonText: '确定',
        type: 'success',
        callback: () => {
          userStore.logoutAction()
        }
      })
    } catch (error) {
      console.error('修改密码失败', error)
      ElMessage.error('修改密码失败')
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchAdminList()
  fetchRoleList()
})
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
