package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 敏感词实体类
 */
@Data
@TableName("ptm_sensitive_word")
public class SensitiveWord {

    /**
     * 敏感词ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 敏感词
     */
    private String word;

    /**
     * 类型（政治、色情、暴力、广告等）
     */
    private String type;

    /**
     * 级别（1一般 2中等 3严重）
     */
    private Integer level;

    /**
     * 替换词
     */
    private String replaceWord;

    /**
     * 状态（0禁用 1启用）
     */
    private Boolean status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除（0否 1是）
     */
    @TableLogic
    private Boolean isDeleted;
}
