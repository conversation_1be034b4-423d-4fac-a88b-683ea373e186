<template>
  <div class="notifications-container">
    <van-nav-bar
      title="消息通知"
      left-arrow
      @click-left="goBack"
      fixed
    />

    <div class="notifications-content">
      <van-tabs v-model="activeTab" sticky>
        <van-tab title="全部" name="all">
          <notification-list
            :notifications="filteredNotifications"
            :loading="loading"
            @mark-read="markAsRead"
            @load-more="loadMore"
            @refresh="onRefresh"
          />
        </van-tab>
        <van-tab title="关注" name="follow">
          <notification-list
            :notifications="filteredNotifications"
            :loading="loading"
            @mark-read="markAsRead"
            @load-more="loadMore"
            @refresh="onRefresh"
          />
        </van-tab>
        <van-tab title="点赞" name="like">
          <notification-list
            :notifications="filteredNotifications"
            :loading="loading"
            @mark-read="markAsRead"
            @load-more="loadMore"
            @refresh="onRefresh"
          />
        </van-tab>
        <van-tab title="评论" name="comment">
          <notification-list
            :notifications="filteredNotifications"
            :loading="loading"
            @mark-read="markAsRead"
            @load-more="loadMore"
            @refresh="onRefresh"
          />
        </van-tab>
        <van-tab title="系统" name="system">
          <notification-list
            :notifications="filteredNotifications"
            :loading="loading"
            @mark-read="markAsRead"
            @load-more="loadMore"
            @refresh="onRefresh"
          />
        </van-tab>
      </van-tabs>

      <div class="mark-all-button" v-if="hasUnread">
        <van-button size="small" type="primary" plain @click="markAllAsRead">全部标为已读</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import { useUserStore } from '@/stores/user';
import NotificationList from '@/components/notification/NotificationList.vue';
import { getNotifications, markAsRead as markNotificationAsRead, markAllAsRead as markAllNotificationsAsRead, NotificationType } from '@/api/notification';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 当前激活的标签页
const activeTab = ref('all');

// 通知列表
const notifications = ref<any[]>([]);
const loading = ref(false);
const refreshing = ref(false);
const page = ref(1);
const size = ref(10);
const total = ref(0);
const hasMore = ref(true);

// 返回上一页
const goBack = () => {
  router.back();
};

// 过滤后的通知列表
const filteredNotifications = computed(() => {
  return notifications.value;
});

// 是否有未读通知
const hasUnread = computed(() => {
  return notifications.value.some(notification => !notification.isRead);
});

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: number | null = null;
  return function(this: any, ...args: any[]) {
    if (timer) {
      clearTimeout(timer);
    }
    timer = window.setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay);
  };
};

// 重试次数和当前重试次数
const maxRetries = 3;
const retryDelay = 1000; // 1秒
let currentRetry = 0;

// 加载通知的实际函数
const doLoadNotifications = async (retry = false) => {
  // 检查token是否存在
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('没有token，不加载通知');
    return;
  }

  if (loading.value && !retry) {
    console.log('已经在加载中，跳过请求');
    return;
  }

  // 防止重复加载：如果不是第一页，且通知列表数量不是10的倍数，可能已经加载完所有数据
  if (page.value > 1 && notifications.value.length > 0 && notifications.value.length % 10 !== 0) {
    console.log('通知列表数量不是10的倍数，可能已加载完所有数据');
    hasMore.value = false;
    return;
  }

  // 防止重复加载：如果已经没有更多数据，不再请求
  if (!hasMore.value && page.value > 1) {
    console.log('没有更多数据，跳过请求');
    return;
  }

  loading.value = true;
  console.log(`开始加载通知: 页码=${page.value}, 标签=${activeTab.value}, 重试=${retry ? '是' : '否'}`);

  try {
    // 如果是第一页，清空之前的通知
    if (page.value === 1) {
      notifications.value = [];
      hasMore.value = true; // 重置hasMore状态
    }

    // 获取通知类型
    let type = null;
    if (activeTab.value !== 'all') {
      type = activeTab.value as NotificationType;
    }

    // 调用API获取通知列表
    const res = await getNotifications({
      page: page.value,
      size: size.value,
      type
    });

    // 重置重试计数
    currentRetry = 0;

    // 更新通知列表
    if (res.data && res.data.records) {
      // 检查是否有新数据
      if (res.data.records.length === 0) {
        console.log('没有更多数据');
        hasMore.value = false;
      } else {
        console.log(`获取到 ${res.data.records.length} 条通知`);

        // 检查是否已经加载过这些数据（防止重复加载）
        const newRecords = res.data.records.filter((record: any) =>
          !notifications.value.some((n: any) => n.id === record.id)
        );

        if (newRecords.length === 0) {
          console.log('没有新数据，可能是重复请求');
          hasMore.value = false;
        } else {
          console.log(`添加 ${newRecords.length} 条新通知`);
          notifications.value = [...notifications.value, ...newRecords];
          total.value = res.data.total;

          // 只有当获取的记录数等于请求的size时，才认为可能还有更多数据
          hasMore.value = res.data.records.length === size.value && notifications.value.length < total.value;

          // 更新未读消息数量
          const unreadCount = notifications.value.filter((n: any) => !n.isRead).length;
          userStore.updateUnreadCount(unreadCount);
        }
      }
    } else {
      console.warn('响应数据格式不正确', res);
    }
  } catch (error: any) {
    console.error('加载通知失败', error);

    // 处理连接重置错误，尝试重试
    if ((error.code === 'ECONNRESET' || error.code === 'ERR_NETWORK') && currentRetry < maxRetries) {
      currentRetry++;
      console.log(`连接错误，${retryDelay}ms后进行第${currentRetry}次重试`);

      // 延迟一段时间后重试
      setTimeout(() => {
        console.log(`开始第${currentRetry}次重试`);
        doLoadNotifications(true);
      }, retryDelay * currentRetry);

      return;
    }

    // 超过最大重试次数或其他错误
    if (currentRetry >= maxRetries) {
      console.log(`已达到最大重试次数(${maxRetries})，不再重试`);
    }

    // 只有在非通知列表页面或非静默错误时才显示提示
    if (!error.config || !error.config.url || !error.config.url.includes('/notification/list')) {
      showToast('加载通知失败，请稍后重试');
    }

    // 出错时设置hasMore为false，防止继续加载
    hasMore.value = false;
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 使用防抖包装加载函数，300ms内的重复调用会被合并
const loadNotifications = debounce(doLoadNotifications, 300);

// 下拉刷新
const onRefresh = () => {
  page.value = 1;
  loadNotifications();
};

// 上次加载时间
const lastLoadTime = ref(0);

// 加载更多通知
const loadMore = () => {
  if (loading.value) {
    console.log('已经在加载中，跳过加载更多');
    return;
  }

  if (!hasMore.value) {
    console.log('没有更多数据，跳过加载更多');
    return;
  }

  // 防止重复加载：如果通知列表数量不是10的倍数，可能已经加载完所有数据
  if (notifications.value.length > 0 && notifications.value.length % 10 !== 0) {
    console.log('通知列表数量不是10的倍数，可能已加载完所有数据');
    hasMore.value = false;
    return;
  }

  // 防止短时间内多次触发
  const now = Date.now();
  if (lastLoadTime.value && now - lastLoadTime.value < 1000) {
    console.log('短时间内多次触发，跳过');
    return;
  }

  // 记录加载时间
  lastLoadTime.value = now;

  console.log(`加载更多通知，当前页码: ${page.value} -> ${page.value + 1}`);
  page.value++;
  loadNotifications();
};

// 标记为已读
const markAsRead = async (id: number) => {
  try {
    // 调用API标记为已读
    const res = await markNotificationAsRead(id);

    if (res.code === 200) {
      // 更新通知状态
      const notification = notifications.value.find(n => n.id === id);
      if (notification) {
        notification.isRead = true;

        // 更新未读消息数量
        const unreadCount = notifications.value.filter(n => !n.isRead).length;
        userStore.updateUnreadCount(unreadCount);
      }
    } else {
      showToast(res.message || '操作失败');
    }
  } catch (error) {
    console.error('标记已读失败', error);
    showToast('操作失败，请稍后重试');
  }
};

// 标记所有为已读
const markAllAsRead = async () => {
  try {
    // 调用API标记所有为已读
    const res = await markAllNotificationsAsRead();

    if (res.code === 200) {
      // 更新所有通知状态
      notifications.value.forEach(notification => {
        notification.isRead = true;
      });

      // 更新未读消息数量
      userStore.updateUnreadCount(0);

      showToast('已全部标为已读');
    } else {
      showToast(res.message || '操作失败');
    }
  } catch (error) {
    console.error('标记已读失败', error);
    showToast('操作失败，请稍后重试');
  }
};

// 标记是否正在处理标签页变化，防止循环触发
const isProcessingTabChange = ref(false);

// 监听标签页变化
watch(activeTab, (newTab, oldTab) => {
  // 检查token是否存在
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('token不存在，不处理标签页变化');
    return;
  }

  // 只有当标签页真正变化时才执行
  if (newTab !== oldTab && !isProcessingTabChange.value) {
    console.log(`标签页从 ${oldTab} 变为 ${newTab}`);

    // 标记正在处理标签页变化
    isProcessingTabChange.value = true;

    // 重置页码
    page.value = 1;

    // 加载新标签页的通知
    loadNotifications();

    // 防止循环：检查当前URL参数是否已经是newTab
    const currentType = route.query.type;
    if (currentType !== newTab) {
      console.log(`更新URL参数: type=${newTab}`);
      router.replace({
        query: { ...route.query, type: newTab }
      });
    }

    // 处理完成后，重置标记
    setTimeout(() => {
      isProcessingTabChange.value = false;
    }, 500);
  }
});

// 监听路由参数变化
watch(() => route.query.type, (newType, oldType) => {
  // 检查token是否存在
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('token不存在，不处理路由参数变化');
    return;
  }

  // 只有当参数真正变化时才执行
  if (newType !== oldType && !isProcessingTabChange.value) {
    console.log(`URL参数从 ${oldType} 变为 ${newType}`);

    // 标记正在处理标签页变化
    isProcessingTabChange.value = true;

    if (newType && ['all', 'follow', 'like', 'comment', 'system'].includes(newType as string)) {
      // 防止循环：检查当前标签页是否已经是newType
      if (activeTab.value !== newType) {
        console.log(`更新标签页: ${newType}`);
        activeTab.value = newType as string;

        // 重置页码
        page.value = 1;

        // 加载新标签页的通知
        loadNotifications();
      }
    }

    // 处理完成后，重置标记
    setTimeout(() => {
      isProcessingTabChange.value = false;
    }, 500);
  }
});

// 组件挂载时获取数据
onMounted(() => {
  // 检查token是否存在
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('token不存在，重定向到登录页面');
    router.push('/auth/login?redirect=/notifications');
    return;
  }

  console.log('组件挂载，开始加载通知');

  // 从URL参数中获取通知类型
  const { type } = route.query;
  if (type && ['all', 'follow', 'like', 'comment', 'system'].includes(type as string)) {
    activeTab.value = type as string;
  }

  // 加载通知
  loadNotifications();
});
</script>

<style lang="scss" scoped>
.notifications-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.notifications-content {
  position: relative;

  .mark-all-button {
    position: absolute;
    top: 8px;
    right: 16px;
    z-index: 10;
  }
}
</style>
