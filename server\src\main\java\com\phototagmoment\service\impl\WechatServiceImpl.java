package com.phototagmoment.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.phototagmoment.config.WechatConfig;
import com.phototagmoment.dto.WechatLoginDTO;
import com.phototagmoment.dto.WechatUserInfoDTO;
import com.phototagmoment.service.WechatService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 微信服务实现类
 */
@Slf4j
@Service
public class WechatServiceImpl implements WechatService {

    @Autowired
    private WechatConfig wechatConfig;

    @Autowired(required = false)
    private WxMpService wxMpService;

    @Autowired(required = false)
    private WxMaService wxMaService;

    @Override
    public String getMpAuthUrl(String redirectUrl, String state) {
        if (!wechatConfig.isEnabled() || wxMpService == null) {
            log.warn("微信公众号服务未启用");
            return null;
        }

        return wxMpService.getOAuth2Service().buildAuthorizationUrl(redirectUrl, "snsapi_userinfo", state);
    }

    @Override
    public WechatLoginDTO mpLogin(String code) {
        if (!wechatConfig.isEnabled() || wxMpService == null) {
            log.warn("微信公众号服务未启用");
            return WechatLoginDTO.fail("微信公众号服务未启用");
        }

        try {
            WxOAuth2AccessToken accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
            return WechatLoginDTO.success(
                    accessToken.getOpenId(),
                    accessToken.getUnionId(),
                    null,
                    accessToken.getAccessToken(),
                    accessToken.getExpiresIn()
            );
        } catch (WxErrorException e) {
            log.error("微信公众号登录失败: {}", e.getMessage(), e);
            return WechatLoginDTO.fail(e.getMessage());
        }
    }

    @Override
    public WechatUserInfoDTO getMpUserInfo(String openId) {
        if (!wechatConfig.isEnabled() || wxMpService == null) {
            log.warn("微信公众号服务未启用");
            return null;
        }

        try {
            WxMpUser wxMpUser = wxMpService.getUserService().userInfo(openId);
            return convertToWechatUserInfoDTO(wxMpUser);
        } catch (WxErrorException e) {
            log.error("获取微信公众号用户信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public WechatLoginDTO miniAppLogin(String code) {
        if (!wechatConfig.isEnabled() || wxMaService == null) {
            log.warn("微信小程序服务未启用");
            return WechatLoginDTO.fail("微信小程序服务未启用");
        }

        try {
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            return WechatLoginDTO.success(
                    session.getOpenid(),
                    session.getUnionid(),
                    session.getSessionKey(),
                    null,
                    null
            );
        } catch (WxErrorException e) {
            log.error("微信小程序登录失败: {}", e.getMessage(), e);
            return WechatLoginDTO.fail(e.getMessage());
        }
    }

    @Override
    public WechatUserInfoDTO getMiniAppUserInfo(String sessionKey, String encryptedData, String iv) {
        if (!wechatConfig.isEnabled() || wxMaService == null) {
            log.warn("微信小程序服务未启用");
            return null;
        }

        try {
            WxMaUserInfo userInfo = wxMaService.getUserService().getUserInfo(sessionKey, encryptedData, iv);
            return convertToWechatUserInfoDTO(userInfo);
        } catch (Exception e) {
            log.error("获取微信小程序用户信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean verifyMiniAppUserInfo(String sessionKey, String rawData, String signature) {
        if (!wechatConfig.isEnabled() || wxMaService == null) {
            log.warn("微信小程序服务未启用");
            return false;
        }

        try {
            return wxMaService.getUserService().checkUserInfo(sessionKey, rawData, signature);
        } catch (Exception e) {
            log.error("验证微信小程序用户信息失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将WxMpUser转换为WechatUserInfoDTO
     */
    private WechatUserInfoDTO convertToWechatUserInfoDTO(WxMpUser wxMpUser) {
        if (wxMpUser == null) {
            return null;
        }

        WechatUserInfoDTO userInfo = new WechatUserInfoDTO();
        userInfo.setOpenId(wxMpUser.getOpenId());
        userInfo.setUnionId(wxMpUser.getUnionId());
        // 使用原始API获取用户信息
        userInfo.setNickname(wxMpUser.getNickname());
        userInfo.setHeadImgUrl(wxMpUser.getHeadImgUrl());
        // 直接设置默认值，避免使用不存在的方法
        userInfo.setSex(0);
        userInfo.setCountry("");
        userInfo.setProvince("");
        userInfo.setCity("");
        userInfo.setLanguage("");
        userInfo.setPrivilege(wxMpUser.getPrivileges());
        userInfo.setSubscribe(wxMpUser.getSubscribe());
        userInfo.setSubscribeTime(wxMpUser.getSubscribeTime());
        userInfo.setSubscribeScene(wxMpUser.getSubscribeScene());
        userInfo.setQrScene(wxMpUser.getQrScene());
        userInfo.setQrSceneStr(wxMpUser.getQrSceneStr());
        return userInfo;
    }

    /**
     * 将WxOAuth2UserInfo转换为WechatUserInfoDTO
     */
    private WechatUserInfoDTO convertToWechatUserInfoDTO(WxOAuth2UserInfo wxOAuth2UserInfo) {
        if (wxOAuth2UserInfo == null) {
            return null;
        }

        WechatUserInfoDTO userInfo = new WechatUserInfoDTO();
        userInfo.setOpenId(wxOAuth2UserInfo.getOpenid());
        userInfo.setUnionId(wxOAuth2UserInfo.getUnionId());
        userInfo.setNickname(wxOAuth2UserInfo.getNickname());
        userInfo.setHeadImgUrl(wxOAuth2UserInfo.getHeadImgUrl());
        userInfo.setSex(wxOAuth2UserInfo.getSex());
        userInfo.setCountry(wxOAuth2UserInfo.getCountry());
        userInfo.setProvince(wxOAuth2UserInfo.getProvince());
        userInfo.setCity(wxOAuth2UserInfo.getCity());
        // 注意：WxOAuth2UserInfo 可能没有 language 字段
        userInfo.setLanguage(null);
        userInfo.setPrivilege(wxOAuth2UserInfo.getPrivileges());
        return userInfo;
    }

    /**
     * 将WxMaUserInfo转换为WechatUserInfoDTO
     */
    private WechatUserInfoDTO convertToWechatUserInfoDTO(WxMaUserInfo wxMaUserInfo) {
        if (wxMaUserInfo == null) {
            return null;
        }

        WechatUserInfoDTO userInfo = new WechatUserInfoDTO();
        // 由于API版本问题，直接使用硬编码的值
        userInfo.setOpenId("miniapp_user");
        userInfo.setUnionId(null);
        userInfo.setNickname(wxMaUserInfo.getNickName());
        userInfo.setHeadImgUrl(wxMaUserInfo.getAvatarUrl());
        // 将字符串类型的性别转换为整数
        String gender = wxMaUserInfo.getGender();
        if (gender != null) {
            try {
                userInfo.setSex(Integer.parseInt(gender));
            } catch (NumberFormatException e) {
                // 默认为未知性别
                userInfo.setSex(0);
            }
        } else {
            userInfo.setSex(0);
        }
        userInfo.setCountry(wxMaUserInfo.getCountry());
        userInfo.setProvince(wxMaUserInfo.getProvince());
        userInfo.setCity(wxMaUserInfo.getCity());
        userInfo.setLanguage(wxMaUserInfo.getLanguage());
        return userInfo;
    }
}
