package com.phototagmoment.controller;

import com.phototagmoment.common.Result;
import com.phototagmoment.dto.IdentityVerificationDTO;
import com.phototagmoment.entity.IdentityVerification;
import com.phototagmoment.service.IdentityVerificationService;
import com.phototagmoment.util.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 实名认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/identity-verification")
@Tag(name = "实名认证", description = "实名认证相关接口")
public class IdentityVerificationController {

    @Autowired
    private IdentityVerificationService identityVerificationService;

    /**
     * 提交实名认证
     *
     * @param dto 实名认证信息
     * @return 认证记录ID
     */
    @PostMapping("/submit")
    @Operation(summary = "提交实名认证", description = "提交实名认证信息")
    public Result<Long> submitVerification(@RequestBody @Valid IdentityVerificationDTO dto) {
        Long userId = UserUtil.getCurrentUserId();
        Long verificationId = identityVerificationService.submitVerification(userId, dto);
        return Result.success(verificationId, "提交成功");
    }

    /**
     * 获取实名认证状态
     *
     * @return 实名认证信息
     */
    @GetMapping("/status")
    @Operation(summary = "获取实名认证状态", description = "获取当前用户的实名认证状态")
    public Result<IdentityVerification> getVerificationStatus() {
        Long userId = UserUtil.getCurrentUserId();
        IdentityVerification verification = identityVerificationService.getUserVerification(userId);
        return Result.success(verification);
    }

    /**
     * 检查是否已实名认证
     *
     * @return 是否已实名认证
     */
    @GetMapping("/check")
    @Operation(summary = "检查是否已实名认证", description = "检查当前用户是否已完成实名认证")
    public Result<Boolean> checkVerified() {
        Long userId = UserUtil.getCurrentUserId();
        boolean verified = identityVerificationService.isUserVerified(userId);
        return Result.success(verified);
    }

    /**
     * 上传身份证正面照片
     *
     * @param file 身份证正面照片
     * @return 照片URL
     */
    @PostMapping("/upload/id-card-front")
    @Operation(summary = "上传身份证正面照片", description = "上传身份证正面照片")
    public Result<String> uploadIdCardFront(@Parameter(description = "身份证正面照片") @RequestParam("file") MultipartFile file) {
        // 此处应该调用文件上传服务，上传身份证正面照片
        // 为简化示例，直接返回一个模拟的URL
        return Result.success("https://example.com/id-card-front.jpg");
    }

    /**
     * 上传身份证背面照片
     *
     * @param file 身份证背面照片
     * @return 照片URL
     */
    @PostMapping("/upload/id-card-back")
    @Operation(summary = "上传身份证背面照片", description = "上传身份证背面照片")
    public Result<String> uploadIdCardBack(@Parameter(description = "身份证背面照片") @RequestParam("file") MultipartFile file) {
        // 此处应该调用文件上传服务，上传身份证背面照片
        // 为简化示例，直接返回一个模拟的URL
        return Result.success("https://example.com/id-card-back.jpg");
    }

    /**
     * 上传手持身份证照片
     *
     * @param file 手持身份证照片
     * @return 照片URL
     */
    @PostMapping("/upload/id-card-holding")
    @Operation(summary = "上传手持身份证照片", description = "上传手持身份证照片")
    public Result<String> uploadIdCardHolding(@Parameter(description = "手持身份证照片") @RequestParam("file") MultipartFile file) {
        // 此处应该调用文件上传服务，上传手持身份证照片
        // 为简化示例，直接返回一个模拟的URL
        return Result.success("https://example.com/id-card-holding.jpg");
    }

    /**
     * 上传人脸照片
     *
     * @param file 人脸照片
     * @return 照片URL
     */
    @PostMapping("/upload/face")
    @Operation(summary = "上传人脸照片", description = "上传人脸照片")
    public Result<String> uploadFace(@Parameter(description = "人脸照片") @RequestParam("file") MultipartFile file) {
        // 此处应该调用文件上传服务，上传人脸照片
        // 为简化示例，直接返回一个模拟的URL
        return Result.success("https://example.com/face.jpg");
    }
}
