-- PhotoTagMoment 数据库迁移验证脚本
-- 用于验证V1.0.8和V1.0.9迁移脚本的执行结果

-- 1. 检查照片笔记举报表是否创建成功
SELECT 
    'ptm_photo_note_report' as table_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 表已创建'
        ELSE '❌ 表不存在'
    END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_photo_note_report';

-- 2. 检查用户兴趣标签表是否创建成功
SELECT 
    'ptm_user_interest' as table_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 表已创建'
        ELSE '❌ 表不存在'
    END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_user_interest';

-- 3. 检查照片笔记举报表的字段结构
SELECT 
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_photo_note_report'
ORDER BY ORDINAL_POSITION;

-- 4. 检查用户兴趣标签表的字段结构
SELECT 
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable,
    COLUMN_DEFAULT as default_value,
    COLUMN_COMMENT as comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_user_interest'
ORDER BY ORDINAL_POSITION;

-- 5. 检查照片笔记举报表的索引
SELECT 
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    NON_UNIQUE as non_unique,
    INDEX_TYPE as index_type
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_photo_note_report'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 6. 检查用户兴趣标签表的索引
SELECT 
    INDEX_NAME as index_name,
    COLUMN_NAME as column_name,
    NON_UNIQUE as non_unique,
    INDEX_TYPE as index_type
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_user_interest'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 7. 检查外键约束
SELECT 
    CONSTRAINT_NAME as constraint_name,
    TABLE_NAME as table_name,
    COLUMN_NAME as column_name,
    REFERENCED_TABLE_NAME as referenced_table,
    REFERENCED_COLUMN_NAME as referenced_column
FROM information_schema.key_column_usage 
WHERE table_schema = DATABASE() 
AND (table_name = 'ptm_photo_note_report' OR table_name = 'ptm_user_interest')
AND referenced_table_name IS NOT NULL;

-- 8. 验证数据插入功能（测试数据）
-- 注意：这些是测试语句，实际执行时需要确保相关的外键数据存在

-- 测试照片笔记举报表插入
-- INSERT INTO ptm_photo_note_report (note_id, report_user_id, reason, description, report_type) 
-- VALUES (1, 1, '测试举报', '这是一个测试举报', 'other');

-- 测试用户兴趣标签表插入
-- INSERT INTO ptm_user_interest (user_id, tag_name, interest_score) 
-- VALUES (1, '测试标签', 1.0);

-- 9. 检查现有照片笔记表的结构（确保兼容性）
SELECT 
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_photo_note'
AND COLUMN_NAME IN ('id', 'user_id', 'title', 'content', 'view_count', 'like_count', 'comment_count')
ORDER BY ORDINAL_POSITION;

-- 10. 检查用户表的结构（确保外键兼容性）
SELECT 
    COLUMN_NAME as field_name,
    DATA_TYPE as data_type,
    IS_NULLABLE as nullable
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'ptm_user'
AND COLUMN_NAME IN ('id', 'nickname', 'avatar')
ORDER BY ORDINAL_POSITION;

-- 11. 生成迁移验证报告
SELECT 
    '=== PhotoTagMoment 数据库迁移验证报告 ===' as report_title
UNION ALL
SELECT CONCAT('验证时间: ', NOW()) as report_info
UNION ALL
SELECT CONCAT('数据库: ', DATABASE()) as report_info
UNION ALL
SELECT '===========================================' as separator;

-- 12. 检查Flyway迁移历史（如果使用Flyway）
SELECT 
    version,
    description,
    type,
    script,
    installed_on,
    success
FROM flyway_schema_history 
WHERE version IN ('1.0.8', '1.0.9')
ORDER BY installed_rank DESC
LIMIT 10;

-- 13. 最终状态检查
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'ptm_photo_note_report') > 0
        AND (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'ptm_user_interest') > 0
        THEN '✅ 数据库迁移成功完成'
        ELSE '❌ 数据库迁移未完成'
    END as migration_status;
