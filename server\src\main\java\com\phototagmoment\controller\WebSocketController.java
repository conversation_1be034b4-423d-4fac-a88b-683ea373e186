package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.NotificationService;
import com.phototagmoment.websocket.NotificationWebSocketHandler;
import com.phototagmoment.websocket.WebSocketMessage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket控制器
 */
@Slf4j
@RestController
@RequestMapping("/websocket")
@Tag(name = "WebSocket接口", description = "WebSocket相关接口")
public class WebSocketController {

    @Autowired
    private NotificationWebSocketHandler notificationWebSocketHandler;

    @Autowired
    private NotificationService notificationService;

    /**
     * 获取WebSocket连接信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取WebSocket连接信息", description = "获取WebSocket连接地址和当前在线用户数")
    public ApiResponse<Map<String, Object>> getWebSocketInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("notificationEndpoint", "/ws/notification");
        info.put("onlineUserCount", notificationWebSocketHandler.getOnlineUserCount());
        return ApiResponse.success(info);
    }

    /**
     * 发送系统通知
     */
    @PostMapping("/system-notification")
    @Operation(summary = "发送系统通知", description = "发送系统通知给指定用户")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> sendSystemNotification(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "通知内容") @RequestParam String content) {
        boolean result = notificationService.createSystemNotification(userId, content);
        return ApiResponse.success(result);
    }

    /**
     * 广播系统通知
     */
    @PostMapping("/broadcast")
    @Operation(summary = "广播系统通知", description = "广播系统通知给所有在线用户")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Boolean> broadcastMessage(
            @Parameter(description = "通知内容") @RequestParam String content) {
        WebSocketMessage message = WebSocketMessage.system(content);
        notificationWebSocketHandler.broadcastMessage(message);
        return ApiResponse.success(true);
    }

    /**
     * 发送测试通知
     */
    @PostMapping("/test-notification")
    @Operation(summary = "发送测试通知", description = "发送测试通知给当前用户")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> sendTestNotification() {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = notificationService.createSystemNotification(userId, "这是一条测试通知，用于验证WebSocket连接是否正常工作。");
        return ApiResponse.success(result);
    }
}
