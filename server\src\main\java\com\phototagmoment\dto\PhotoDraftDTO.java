package com.phototagmoment.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 照片草稿DTO
 */
@Data
public class PhotoDraftDTO {

    /**
     * 草稿ID
     */
    private Long id;

    /**
     * 草稿标题
     */
    @NotBlank(message = "草稿标题不能为空")
    @Size(max = 100, message = "草稿标题不能超过100个字符")
    private String title;

    /**
     * 草稿描述
     */
    @Size(max = 500, message = "草稿描述不能超过500个字符")
    private String description;

    /**
     * 拍摄地点
     */
    @Size(max = 100, message = "拍摄地点不能超过100个字符")
    private String location;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 提及用户列表
     */
    private List<MentionDTO> mentions;

    /**
     * 可见性: 0-私密, 1-公开, 2-好友可见
     */
    @NotNull(message = "可见性不能为空")
    private Integer visibility;

    /**
     * 是否允许评论: 0-不允许, 1-允许
     */
    @NotNull(message = "是否允许评论不能为空")
    private Integer allowComment;

    /**
     * 是否允许下载: 0-不允许, 1-允许
     */
    @NotNull(message = "是否允许下载不能为空")
    private Integer allowDownload;

    /**
     * 临时文件路径列表
     */
    private List<String> tempFilePaths;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
