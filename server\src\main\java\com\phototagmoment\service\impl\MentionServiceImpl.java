package com.phototagmoment.service.impl;

import com.phototagmoment.dto.MentionDTO;
import com.phototagmoment.entity.Notification;
import com.phototagmoment.entity.User;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.MentionService;
import com.phototagmoment.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 提及用户服务实现类
 */
@Slf4j
@Service
public class MentionServiceImpl implements MentionService {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processPhotoMentions(Long photoId, List<MentionDTO> mentions, Long userId) {
        if (mentions == null || mentions.isEmpty()) {
            return true;
        }

        // 获取发布用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        // 发送通知给被@的用户
        for (MentionDTO mention : mentions) {
            // 不给自己发通知
            if (mention.getUserId().equals(userId)) {
                continue;
            }

            // 创建通知
            Notification notification = new Notification();
            notification.setUserId(mention.getUserId());
            notification.setSenderId(userId);
            notification.setType(6); // @用户通知
            notification.setTargetId(photoId);
            notification.setTargetType(1); // 照片
            notification.setContent(user.getNickname() + " 在照片中@了你");
            notification.setIsRead(0);

            notificationService.pushNotification(notification);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processCommentMentions(Long commentId, String content, Long userId) {
        // 提取@用户
        List<MentionDTO> mentions = extractMentionsFromText(content);
        if (mentions.isEmpty()) {
            return true;
        }

        // 获取评论用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        // 发送通知给被@的用户
        for (MentionDTO mention : mentions) {
            // 不给自己发通知
            if (mention.getUserId().equals(userId)) {
                continue;
            }

            // 创建通知
            Notification notification = new Notification();
            notification.setUserId(mention.getUserId());
            notification.setSenderId(userId);
            notification.setType(6); // @用户通知
            notification.setTargetId(commentId);
            notification.setTargetType(2); // 评论
            notification.setContent(user.getNickname() + " 在评论中@了你");
            notification.setIsRead(0);

            notificationService.pushNotification(notification);
        }

        return true;
    }

    @Override
    public List<MentionDTO> extractMentionsFromText(String content) {
        List<MentionDTO> mentions = new ArrayList<>();
        if (content == null || content.isEmpty()) {
            return mentions;
        }

        // 匹配@用户
        Pattern pattern = Pattern.compile("@([\\w\\u4e00-\\u9fa5]+)");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            String username = matcher.group(1);
            // 查询用户
            User user = userMapper.selectByUsername(username);
            if (user != null) {
                MentionDTO mention = new MentionDTO();
                mention.setUserId(user.getId());
                mention.setUsername(user.getUsername());
                mentions.add(mention);
            }
        }

        return mentions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMention(Long fromUserId, Long toUserId, String type, Long targetId) {
        // 不给自己发通知
        if (fromUserId.equals(toUserId)) {
            return true;
        }

        // 获取发起用户信息
        User user = userMapper.selectById(fromUserId);
        if (user == null) {
            return false;
        }

        // 创建通知
        Notification notification = new Notification();
        notification.setUserId(toUserId);
        notification.setSenderId(fromUserId);
        notification.setType(6); // @用户通知
        notification.setTargetId(targetId);

        // 设置目标类型
        if ("photo".equals(type)) {
            notification.setTargetType(1); // 照片
            notification.setContent(user.getNickname() + " 在照片中@了你");
        } else if ("comment".equals(type)) {
            notification.setTargetType(2); // 评论
            notification.setContent(user.getNickname() + " 在评论中@了你");
        }

        notification.setIsRead(0);

        // 发送通知
        notificationService.pushNotification(notification);

        return true;
    }
}
