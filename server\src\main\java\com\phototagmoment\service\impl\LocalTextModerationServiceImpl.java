package com.phototagmoment.service.impl;

import com.phototagmoment.service.SensitiveWordService;
import com.phototagmoment.service.SystemConfigService;
import com.phototagmoment.service.TextModerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 本地文本审核服务实现类
 * 基于敏感词库和正则表达式进行文本内容过滤
 */
@Slf4j
@Service("localTextModerationServiceImpl")
@ConditionalOnProperty(name = "content-moderation.text.provider", havingValue = "local", matchIfMissing = true)
public class LocalTextModerationServiceImpl implements TextModerationService {

    @Autowired
    private SensitiveWordService sensitiveWordService;

    @Autowired
    private SystemConfigService configService;

    private String failReason;

    // 正则表达式模式
    private static final Pattern URL_PATTERN = Pattern.compile("(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]");
    private static final Pattern PHONE_PATTERN = Pattern.compile("\\b1[3-9]\\d{9}\\b");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,6}\\b");
    private static final Pattern QQ_PATTERN = Pattern.compile("\\b[1-9][0-9]{4,10}\\b");
    private static final Pattern WECHAT_PATTERN = Pattern.compile("\\b[a-zA-Z][a-zA-Z0-9_-]{5,19}\\b");

    @Override
    public boolean moderateText(String text) {
        if (text == null || text.isEmpty()) {
            failReason = "文本内容为空";
            return false;
        }

        // 检查是否启用文本审核
        boolean textModerationEnabled = configService.getBooleanValue("content-moderation.enabled", true);
        if (!textModerationEnabled) {
            return true;
        }

        // 检查敏感词
        if (containsSensitiveWords(text)) {
            return false;
        }

        // 检查是否包含联系方式
        if (containsContactInfo(text)) {
            return false;
        }

        return true;
    }

    @Override
    public String getFailReason() {
        return failReason;
    }

    /**
     * 检查文本是否包含敏感词
     *
     * @param text 文本内容
     * @return 是否包含敏感词
     */
    private boolean containsSensitiveWords(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        // 检查是否启用敏感词过滤
        boolean sensitiveWordFilterEnabled = configService.getBooleanValue("sensitive.word.filter", true);
        if (!sensitiveWordFilterEnabled) {
            return false;
        }

        // 获取文本中的敏感词
        Set<String> sensitiveWords = sensitiveWordService.getSensitiveWords(text);
        if (!sensitiveWords.isEmpty()) {
            failReason = "文本包含敏感词: " + String.join(", ", sensitiveWords);
            return true;
        }

        return false;
    }

    /**
     * 检查文本是否包含联系方式
     *
     * @param text 文本内容
     * @return 是否包含联系方式
     */
    private boolean containsContactInfo(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        // 检查是否启用联系方式过滤
        boolean contactInfoFilterEnabled = configService.getBooleanValue("content-moderation.contact-info.filter", true);
        if (!contactInfoFilterEnabled) {
            return false;
        }

        // 检查是否包含URL
        Matcher urlMatcher = URL_PATTERN.matcher(text);
        if (urlMatcher.find()) {
            failReason = "文本包含URL链接";
            return true;
        }

        // 检查是否包含手机号
        Matcher phoneMatcher = PHONE_PATTERN.matcher(text);
        if (phoneMatcher.find()) {
            failReason = "文本包含手机号";
            return true;
        }

        // 检查是否包含邮箱
        Matcher emailMatcher = EMAIL_PATTERN.matcher(text);
        if (emailMatcher.find()) {
            failReason = "文本包含邮箱地址";
            return true;
        }

        // 检查是否包含QQ号
        Matcher qqMatcher = QQ_PATTERN.matcher(text);
        if (qqMatcher.find()) {
            failReason = "文本包含QQ号";
            return true;
        }

        // 检查是否包含微信号
        Matcher wechatMatcher = WECHAT_PATTERN.matcher(text);
        if (wechatMatcher.find()) {
            failReason = "文本包含微信号";
            return true;
        }

        return false;
    }
}
