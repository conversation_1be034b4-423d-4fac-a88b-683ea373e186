package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.CommentDTO;
import com.phototagmoment.entity.Comment;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Map;

import java.util.List;

/**
 * 评论Mapper接口
 */
@Repository
public interface CommentMapper extends BaseMapper<Comment> {

    /**
     * 分页查询照片评论列表
     *
     * @param page 分页参数
     * @param photoId 照片ID
     * @param currentUserId 当前用户ID
     * @return 评论列表
     */
    IPage<CommentDTO> selectPhotoComments(Page<CommentDTO> page, @Param("photoId") Long photoId, @Param("currentUserId") Long currentUserId);

    /**
     * 查询评论回复列表
     *
     * @param page 分页参数
     * @param commentId 评论ID
     * @param currentUserId 当前用户ID
     * @return 回复列表
     */
    IPage<CommentDTO> selectCommentReplies(Page<CommentDTO> page, @Param("commentId") Long commentId, @Param("currentUserId") Long currentUserId);

    /**
     * 查询评论详情
     *
     * @param commentId 评论ID
     * @param currentUserId 当前用户ID
     * @return 评论详情
     */
    CommentDTO selectCommentDetail(@Param("commentId") Long commentId, @Param("currentUserId") Long currentUserId);

    /**
     * 增加评论点赞数
     *
     * @param commentId 评论ID
     * @return 影响行数
     */
    int incrementLikeCount(@Param("commentId") Long commentId);

    /**
     * 减少评论点赞数
     *
     * @param commentId 评论ID
     * @return 影响行数
     */
    int decrementLikeCount(@Param("commentId") Long commentId);

    /**
     * 增加评论回复数
     *
     * @param commentId 评论ID
     * @return 影响行数
     */
    int incrementReplyCount(@Param("commentId") Long commentId);

    /**
     * 减少评论回复数
     *
     * @param commentId 评论ID
     * @return 影响行数
     */
    int decrementReplyCount(@Param("commentId") Long commentId);

    /**
     * 管理员分页查询评论列表
     *
     * @param page 分页参数
     * @param queryRequest 查询条件
     * @return 评论列表
     */
    IPage<CommentDTO> selectCommentListForAdmin(Page<CommentDTO> page, @Param("query") com.phototagmoment.dto.CommentQueryRequest queryRequest);

    /**
     * 管理员获取评论详情
     *
     * @param commentId 评论ID
     * @return 评论详情
     */
    CommentDTO selectCommentDetailForAdmin(@Param("commentId") Long commentId);

    /**
     * 获取评论统计信息
     *
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalComments, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as approvedComments, " +
            "COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as todayNewComments, " +
            "COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as rootComments, " +
            "COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as replyComments " +
            "FROM ptm_comment WHERE is_deleted = 0")
    Map<String, Object> selectCommentStatistics();
}
