package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 管理员角色DTO
 */
@Data
@Schema(description = "管理员角色信息")
public class AdminRoleDTO {

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String name;

    @Schema(description = "角色编码")
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    private String code;

    @Schema(description = "角色描述")
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String description;

    @Schema(description = "状态：0-禁用，1-正常")
    private Integer status;

    @Schema(description = "权限ID列表")
    private List<Long> permissionIds;
}
