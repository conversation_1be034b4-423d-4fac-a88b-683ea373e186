package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.common.Result;
import com.phototagmoment.dto.FileUploadConfigDTO;
import com.phototagmoment.service.FileUploadConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 后台文件上传配置管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/file-upload-config")
@Tag(name = "后台文件上传配置管理", description = "后台管理系统文件上传配置相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminFileUploadConfigController {

    @Autowired
    private FileUploadConfigService configService;

    /**
     * 获取配置列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取配置列表", description = "分页获取文件上传配置列表")
    public Result<IPage<FileUploadConfigDTO>> getConfigList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "存储类型") @RequestParam(required = false) String storageType,
            @Parameter(description = "启用状态") @RequestParam(required = false) Boolean enabled,
            @Parameter(description = "配置状态") @RequestParam(required = false) Integer status) {

        Page<FileUploadConfigDTO> pageParam = new Page<>(page, size);
        IPage<FileUploadConfigDTO> result = configService.getConfigList(pageParam, keyword, storageType, enabled, status);
        return Result.success(result);
    }

    /**
     * 获取配置详情
     */
    @GetMapping("/{configId}")
    @Operation(summary = "获取配置详情", description = "根据配置ID获取详细信息")
    public Result<FileUploadConfigDTO> getConfigDetail(
            @Parameter(description = "配置ID") @PathVariable Long configId) {

        FileUploadConfigDTO config = configService.getConfigDetail(configId);
        if (config != null) {
            return Result.success(config);
        } else {
            return Result.fail("配置不存在");
        }
    }

    /**
     * 创建配置
     */
    @PostMapping
    @Operation(summary = "创建配置", description = "创建新的文件上传配置")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<Long> createConfig(@Valid @RequestBody FileUploadConfigDTO configDTO) {
        try {
            Long configId = configService.createConfig(configDTO);
            return Result.success(configId, "配置创建成功");
        } catch (Exception e) {
            log.error("创建配置失败", e);
            return Result.fail("创建配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新配置
     */
    @PutMapping("/{configId}")
    @Operation(summary = "更新配置", description = "更新文件上传配置")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<Boolean> updateConfig(
            @Parameter(description = "配置ID") @PathVariable Long configId,
            @Valid @RequestBody FileUploadConfigDTO configDTO) {

        configDTO.setId(configId);
        try {
            boolean success = configService.updateConfig(configDTO);
            if (success) {
                return Result.success(true, "配置更新成功");
            } else {
                return Result.fail("配置更新失败");
            }
        } catch (Exception e) {
            log.error("更新配置失败", e);
            return Result.fail("更新配置失败: " + e.getMessage());
        }
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{configId}")
    @Operation(summary = "删除配置", description = "删除指定的文件上传配置")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<Boolean> deleteConfig(
            @Parameter(description = "配置ID") @PathVariable Long configId) {

        try {
            boolean success = configService.deleteConfig(configId);
            if (success) {
                return Result.success(true, "配置删除成功");
            } else {
                return Result.fail("配置删除失败");
            }
        } catch (Exception e) {
            log.error("删除配置失败", e);
            return Result.fail("删除配置失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除配置
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除配置", description = "批量删除文件上传配置")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<Map<String, Object>> batchDeleteConfigs(
            @Parameter(description = "配置ID列表") @RequestBody List<Long> configIds) {

        if (configIds == null || configIds.isEmpty()) {
            return Result.fail("请选择要删除的配置");
        }

        Map<String, Object> result = configService.batchDeleteConfigs(configIds);
        return Result.success(result, "批量删除完成");
    }

    /**
     * 启用/禁用配置
     */
    @PutMapping("/{configId}/toggle")
    @Operation(summary = "启用/禁用配置", description = "切换配置的启用状态")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Boolean> toggleConfig(
            @Parameter(description = "配置ID") @PathVariable Long configId,
            @Parameter(description = "启用状态") @RequestParam Boolean enabled) {

        boolean success = configService.toggleConfig(configId, enabled);
        if (success) {
            String action = enabled ? "启用" : "禁用";
            return Result.success(true, "配置" + action + "成功");
        } else {
            return Result.fail("操作失败");
        }
    }

    /**
     * 设置默认配置
     */
    @PutMapping("/{configId}/set-default")
    @Operation(summary = "设置默认配置", description = "将指定配置设置为默认配置")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Boolean> setDefaultConfig(
            @Parameter(description = "配置ID") @PathVariable Long configId) {

        boolean success = configService.setDefaultConfig(configId);
        if (success) {
            return Result.success(true, "设置默认配置成功");
        } else {
            return Result.fail("设置默认配置失败");
        }
    }

    /**
     * 获取默认配置
     */
    @GetMapping("/default")
    @Operation(summary = "获取默认配置", description = "获取当前默认的文件上传配置")
    public Result<FileUploadConfigDTO> getDefaultConfig() {
        FileUploadConfigDTO config = configService.getDefaultConfig();
        if (config != null) {
            return Result.success(config);
        } else {
            return Result.fail("未找到默认配置");
        }
    }

    /**
     * 获取启用的配置列表
     */
    @GetMapping("/enabled")
    @Operation(summary = "获取启用的配置", description = "获取所有启用的文件上传配置")
    public Result<List<FileUploadConfigDTO>> getEnabledConfigs() {
        List<FileUploadConfigDTO> configs = configService.getEnabledConfigs();
        return Result.success(configs);
    }

    /**
     * 测试配置连接
     */
    @PostMapping("/{configId}/test")
    @Operation(summary = "测试配置连接", description = "测试指定配置的连接状态")
    public Result<FileUploadConfigDTO.TestResult> testConfig(
            @Parameter(description = "配置ID") @PathVariable Long configId) {

        try {
            FileUploadConfigDTO.TestResult result = configService.testConfig(configId);
            return Result.success(result, "测试完成");
        } catch (Exception e) {
            log.error("测试配置失败", e);
            return Result.fail("测试配置失败: " + e.getMessage());
        }
    }

    /**
     * 测试配置连接（不保存）
     */
    @PostMapping("/test-without-save")
    @Operation(summary = "测试配置连接（不保存）", description = "测试配置连接但不保存测试结果")
    public Result<FileUploadConfigDTO.TestResult> testConfigWithoutSave(
            @RequestBody FileUploadConfigDTO configDTO) {

        try {
            FileUploadConfigDTO.TestResult result = configService.testConfigWithoutSave(configDTO);
            return Result.success(result, "测试完成");
        } catch (Exception e) {
            log.error("测试配置失败", e);
            return Result.fail("测试配置失败: " + e.getMessage());
        }
    }

    /**
     * 批量测试配置
     */
    @PostMapping("/batch-test")
    @Operation(summary = "批量测试配置", description = "批量测试多个配置的连接状态")
    public Result<Map<String, Object>> batchTestConfigs(
            @Parameter(description = "配置ID列表") @RequestBody List<Long> configIds) {

        if (configIds == null || configIds.isEmpty()) {
            return Result.fail("请选择要测试的配置");
        }

        Map<String, Object> result = configService.batchTestConfigs(configIds);
        return Result.success(result, "批量测试完成");
    }

    /**
     * 复制配置
     */
    @PostMapping("/{configId}/copy")
    @Operation(summary = "复制配置", description = "复制现有配置创建新配置")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<Long> copyConfig(
            @Parameter(description = "配置ID") @PathVariable Long configId,
            @Parameter(description = "新配置名称") @RequestParam String newConfigName) {

        try {
            Long newConfigId = configService.copyConfig(configId, newConfigName);
            if (newConfigId != null) {
                return Result.success(newConfigId, "配置复制成功");
            } else {
                return Result.fail("配置复制失败");
            }
        } catch (Exception e) {
            log.error("复制配置失败", e);
            return Result.fail("复制配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取配置统计", description = "获取文件上传配置统计信息")
    public Result<Map<String, Object>> getConfigStatistics() {
        Map<String, Object> statistics = configService.getConfigStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取存储类型统计
     */
    @GetMapping("/storage-type-statistics")
    @Operation(summary = "获取存储类型统计", description = "获取各存储类型的配置数量统计")
    public Result<List<Map<String, Object>>> getStorageTypeStatistics() {
        List<Map<String, Object>> statistics = configService.getStorageTypeStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取配置健康状态
     */
    @GetMapping("/health-status")
    @Operation(summary = "获取配置健康状态", description = "获取配置的整体健康状态")
    public Result<Map<String, Object>> getConfigHealthStatus() {
        Map<String, Object> healthStatus = configService.getConfigHealthStatus();
        return Result.success(healthStatus);
    }

    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新配置缓存", description = "刷新文件上传配置缓存")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<Boolean> refreshConfigCache() {
        boolean success = configService.refreshConfigCache();
        return Result.success(success, "缓存刷新成功");
    }

    /**
     * 检查配置名称是否可用
     */
    @GetMapping("/check-name")
    @Operation(summary = "检查配置名称", description = "检查配置名称是否可用")
    public Result<Boolean> checkConfigName(
            @Parameter(description = "配置名称") @RequestParam String configName,
            @Parameter(description = "排除的配置ID") @RequestParam(required = false) Long excludeId) {

        boolean available = configService.isConfigNameAvailable(configName, excludeId);
        return Result.success(available, available ? "配置名称可用" : "配置名称已存在");
    }

    /**
     * 获取最近测试失败的配置
     */
    @GetMapping("/recent-failed")
    @Operation(summary = "获取最近失败的配置", description = "获取最近测试失败的配置列表")
    public Result<List<FileUploadConfigDTO>> getRecentFailedConfigs(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {

        List<FileUploadConfigDTO> configs = configService.getRecentFailedConfigs(limit);
        return Result.success(configs);
    }

    /**
     * 获取长时间未测试的配置
     */
    @GetMapping("/untested")
    @Operation(summary = "获取未测试的配置", description = "获取长时间未测试的配置列表")
    public Result<List<FileUploadConfigDTO>> getUntestedConfigs(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") Integer days,
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {

        List<FileUploadConfigDTO> configs = configService.getUntestedConfigs(days, limit);
        return Result.success(configs);
    }
}
