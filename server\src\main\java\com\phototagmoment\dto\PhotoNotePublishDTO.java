package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 照片笔记发布请求DTO
 */
@Data
@Schema(description = "照片笔记发布请求")
public class PhotoNotePublishDTO {

    @Schema(description = "标题（可选，最多100字符）")
    @Size(max = 100, message = "标题长度不能超过100个字符")
    private String title;

    @Schema(description = "正文内容（必填，最多2000字符）")
    @NotBlank(message = "正文内容不能为空")
    @Size(max = 2000, message = "正文内容长度不能超过2000个字符")
    private String content;

    @Schema(description = "照片信息列表")
    @NotEmpty(message = "至少需要上传一张照片")
    @Size(min = 1, max = 9, message = "照片数量必须在1-9张之间")
    private List<PhotoInfoDTO> photos;

    @Schema(description = "可见性: 0-私密, 1-公开, 2-好友可见")
    @Min(value = 0, message = "可见性值无效")
    @Max(value = 2, message = "可见性值无效")
    private Integer visibility = 1;

    @Schema(description = "是否允许评论: 0-不允许, 1-允许")
    @Min(value = 0, message = "评论设置值无效")
    @Max(value = 1, message = "评论设置值无效")
    private Integer allowComment = 1;

    @Schema(description = "地理位置")
    private String location;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    /**
     * 照片信息DTO
     */
    @Data
    @Schema(description = "照片信息")
    public static class PhotoInfoDTO {

        @Schema(description = "照片URL")
        @NotBlank(message = "照片URL不能为空")
        private String url;

        @Schema(description = "缩略图URL")
        private String thumbnailUrl;

        @Schema(description = "存储路径")
        private String storagePath;

        @Schema(description = "原始文件名")
        private String originalFilename;

        @Schema(description = "文件大小")
        private Long fileSize;

        @Schema(description = "文件类型")
        private String fileType;

        @Schema(description = "照片宽度")
        private Integer width;

        @Schema(description = "照片高度")
        private Integer height;

        @Schema(description = "排序顺序")
        @Min(value = 1, message = "排序顺序最小为1")
        @Max(value = 9, message = "排序顺序最大为9")
        private Integer sortOrder;
    }
}
