package com.phototagmoment.controller.admin;

import com.phototagmoment.common.Result;
import com.phototagmoment.dto.AdminLoginDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试登录控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/test")
public class TestLoginController {

    /**
     * 测试登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody @Valid AdminLoginDTO loginDTO) {
        log.info("测试登录请求: {}", loginDTO.getUsername());
        
        Map<String, Object> result = new HashMap<>();
        result.put("username", loginDTO.getUsername());
        result.put("token", "test-token");
        result.put("tokenType", "Bearer");
        result.put("expiresIn", 86400);
        
        return Result.success(result);
    }
}
