package com.phototagmoment.service.parser;

import com.phototagmoment.entity.SensitiveWord;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 敏感词文件解析器接口
 */
public interface SensitiveWordFileParser {

    /**
     * 解析文件，提取敏感词
     *
     * @param file 上传的文件
     * @return 敏感词列表
     * @throws Exception 解析异常
     */
    List<SensitiveWord> parse(MultipartFile file) throws Exception;

    /**
     * 判断是否支持该文件类型
     *
     * @param fileExtension 文件扩展名
     * @return 是否支持
     */
    boolean supports(String fileExtension);
}
