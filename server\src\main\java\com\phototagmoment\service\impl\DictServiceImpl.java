package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.entity.DictData;
import com.phototagmoment.entity.DictType;
import com.phototagmoment.mapper.DictDataMapper;
import com.phototagmoment.mapper.DictTypeMapper;
import com.phototagmoment.service.DictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据字典Service实现类
 */
@Slf4j
@Service
public class DictServiceImpl implements DictService {

    @Autowired
    private DictTypeMapper dictTypeMapper;

    @Autowired
    private DictDataMapper dictDataMapper;

    @Override
    public List<DictType> listAllDictTypes() {
        return dictTypeMapper.selectList(null);
    }

    @Override
    public IPage<DictType> pageDictTypes(int page, int pageSize, String keyword) {
        Page<DictType> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<DictType> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(DictType::getDictName, keyword)
                    .or().like(DictType::getDictType, keyword)
                    .or().like(DictType::getRemark, keyword);
        }
        
        queryWrapper.orderByAsc(DictType::getDictType);
        
        return dictTypeMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public DictType getDictTypeById(Long id) {
        return dictTypeMapper.selectById(id);
    }

    @Override
    @Cacheable(value = "dict_type", key = "#dictType", unless = "#result == null")
    public DictType getDictTypeByType(String dictType) {
        return dictTypeMapper.getByDictType(dictType);
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_type", key = "#dictType.dictType")
    public boolean saveDictType(DictType dictType) {
        // 检查字典类型是否已存在
        DictType existDictType = dictTypeMapper.getByDictType(dictType.getDictType());
        if (existDictType != null) {
            return false;
        }
        
        int result = dictTypeMapper.insert(dictType);
        return result > 0;
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_type", key = "#dictType.dictType")
    public boolean updateDictType(DictType dictType) {
        // 检查字典类型是否已存在
        DictType existDictType = dictTypeMapper.getByDictType(dictType.getDictType());
        if (existDictType != null && !existDictType.getId().equals(dictType.getId())) {
            return false;
        }
        
        int result = dictTypeMapper.updateById(dictType);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteDictType(Long id) {
        DictType dictType = dictTypeMapper.selectById(id);
        if (dictType == null) {
            return false;
        }
        
        // 删除字典类型
        int result = dictTypeMapper.deleteById(id);
        if (result > 0) {
            // 删除字典数据
            LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DictData::getDictTypeId, id);
            dictDataMapper.delete(queryWrapper);
            
            // 清除缓存
            refreshCache();
        }
        
        return result > 0;
    }

    @Override
    public List<DictData> listAllDictData() {
        return dictDataMapper.selectList(null);
    }

    @Override
    public IPage<DictData> pageDictData(int page, int pageSize, Long dictTypeId, String keyword) {
        Page<DictData> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<DictData> queryWrapper = new LambdaQueryWrapper<>();
        
        if (dictTypeId != null) {
            queryWrapper.eq(DictData::getDictTypeId, dictTypeId);
        }
        
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(DictData::getDictLabel, keyword)
                    .or().like(DictData::getDictValue, keyword)
                    .or().like(DictData::getRemark, keyword);
        }
        
        queryWrapper.orderByAsc(DictData::getDictSort);
        
        return dictDataMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public DictData getDictDataById(Long id) {
        return dictDataMapper.selectById(id);
    }

    @Override
    @Cacheable(value = "dict_data", key = "'typeId_' + #dictTypeId", unless = "#result == null || #result.isEmpty()")
    public List<DictData> getDictDataByTypeId(Long dictTypeId) {
        return dictDataMapper.getByDictTypeId(dictTypeId);
    }

    @Override
    @Cacheable(value = "dict_data", key = "'type_' + #dictType", unless = "#result == null || #result.isEmpty()")
    public List<DictData> getDictDataByType(String dictType) {
        return dictDataMapper.getByDictType(dictType);
    }

    @Override
    @Cacheable(value = "dict_data", key = "'map_' + #dictType", unless = "#result == null || #result.isEmpty()")
    public Map<String, String> getDictDataMapByType(String dictType) {
        List<DictData> dictDataList = getDictDataByType(dictType);
        if (dictDataList == null || dictDataList.isEmpty()) {
            return new HashMap<>();
        }
        
        return dictDataList.stream()
                .collect(Collectors.toMap(DictData::getDictValue, DictData::getDictLabel, (v1, v2) -> v1));
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_data", allEntries = true)
    public boolean saveDictData(DictData dictData) {
        int result = dictDataMapper.insert(dictData);
        return result > 0;
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_data", allEntries = true)
    public boolean updateDictData(DictData dictData) {
        int result = dictDataMapper.updateById(dictData);
        return result > 0;
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_data", allEntries = true)
    public boolean deleteDictData(Long id) {
        int result = dictDataMapper.deleteById(id);
        return result > 0;
    }

    @Override
    @CacheEvict(value = {"dict_type", "dict_data"}, allEntries = true)
    public void refreshCache() {
        log.info("数据字典缓存已刷新");
    }
}
