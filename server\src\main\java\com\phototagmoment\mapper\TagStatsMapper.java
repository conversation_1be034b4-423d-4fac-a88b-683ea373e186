package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.dto.TagSearchResultDTO;
import com.phototagmoment.entity.TagStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 标签统计Mapper接口
 */
@Mapper
public interface TagStatsMapper extends BaseMapper<TagStats> {

    /**
     * 增加标签使用次数
     *
     * @param tagName 标签名称
     * @return 影响行数
     */
    @Update("INSERT INTO ptm_tag_stats (tag_name, use_count, note_count, last_used_at) " +
            "VALUES (#{tagName}, 1, 1, NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "use_count = use_count + 1, " +
            "note_count = note_count + 1, " +
            "last_used_at = NOW()")
    int incrementTagUsage(@Param("tagName") String tagName);

    /**
     * 减少标签使用次数
     *
     * @param tagName 标签名称
     * @return 影响行数
     */
    @Update("UPDATE ptm_tag_stats SET " +
            "use_count = GREATEST(use_count - 1, 0), " +
            "note_count = GREATEST(note_count - 1, 0) " +
            "WHERE tag_name = #{tagName}")
    int decrementTagUsage(@Param("tagName") String tagName);

    /**
     * 更新标签热度分数
     *
     * @param tagName 标签名称
     * @param viewCount 浏览量增量
     * @param likeCount 点赞数增量
     * @return 影响行数
     */
    @Update("UPDATE ptm_tag_stats SET " +
            "total_view_count = total_view_count + #{viewCount}, " +
            "total_like_count = total_like_count + #{likeCount}, " +
            "hot_score = (total_view_count + #{viewCount}) * 0.1 + (total_like_count + #{likeCount}) * 0.5 + use_count * 0.3 " +
            "WHERE tag_name = #{tagName}")
    int updateTagHotScore(@Param("tagName") String tagName, 
                         @Param("viewCount") Integer viewCount, 
                         @Param("likeCount") Integer likeCount);

    /**
     * 查询标签详情
     *
     * @param tagName 标签名称
     * @return 标签统计信息
     */
    @Select("SELECT * FROM ptm_tag_stats WHERE tag_name = #{tagName}")
    TagStats selectByTagName(@Param("tagName") String tagName);

    /**
     * 查询热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    @Select("SELECT tag_name, use_count, hot_score FROM ptm_tag_stats " +
            "ORDER BY hot_score DESC, use_count DESC " +
            "LIMIT #{limit}")
    List<TagSearchResultDTO.RelatedTagDTO> selectHotTags(@Param("limit") Integer limit);

    /**
     * 查询相关标签
     *
     * @param tagName 标签名称
     * @param limit 限制数量
     * @return 相关标签列表
     */
    @Select("SELECT tag_name, use_count, hot_score FROM ptm_tag_stats " +
            "WHERE tag_name != #{tagName} " +
            "ORDER BY hot_score DESC, use_count DESC " +
            "LIMIT #{limit}")
    List<TagSearchResultDTO.RelatedTagDTO> selectRelatedTags(@Param("tagName") String tagName, 
                                                            @Param("limit") Integer limit);

    /**
     * 根据标签名称模糊查询
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 标签列表
     */
    @Select("SELECT tag_name, use_count, hot_score FROM ptm_tag_stats " +
            "WHERE tag_name LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY hot_score DESC, use_count DESC " +
            "LIMIT #{limit}")
    List<TagSearchResultDTO.RelatedTagDTO> searchTagsByKeyword(@Param("keyword") String keyword, 
                                                              @Param("limit") Integer limit);

    /**
     * 统计标签下的照片笔记数量
     *
     * @param tagName 标签名称
     * @return 照片笔记数量
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_note_tag pnt " +
            "LEFT JOIN ptm_photo_note pn ON pnt.note_id = pn.id " +
            "WHERE pnt.tag_name = #{tagName} AND pn.status = 1")
    Long countNotesByTag(@Param("tagName") String tagName);
}
