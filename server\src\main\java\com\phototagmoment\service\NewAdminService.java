package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.dto.*;
import com.phototagmoment.entity.Admin;
import com.phototagmoment.entity.AdminPermission;
import com.phototagmoment.entity.AdminRole;
import com.phototagmoment.vo.AdminPermissionTreeVO;
import com.phototagmoment.vo.AdminVO;
import com.phototagmoment.vo.LoginResultVO;

import java.util.List;

/**
 * 新管理员服务接口
 */
public interface NewAdminService {

    /**
     * 管理员登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    LoginResultVO login(AdminLoginDTO loginDTO);

    /**
     * 获取管理员信息
     *
     * @param id 管理员ID
     * @return 管理员信息
     */
    AdminVO getAdminInfo(Long id);

    /**
     * 获取当前管理员信息
     *
     * @return 管理员信息
     */
    AdminVO getCurrentAdmin();

    /**
     * 管理员登出
     *
     * @return 是否成功
     */
    boolean logout();

    /**
     * 创建管理员
     *
     * @param adminDTO 管理员信息
     * @param creatorId 创建者ID
     * @return 管理员ID
     */
    Long createAdmin(AdminDTO adminDTO, Long creatorId);

    /**
     * 更新管理员信息
     *
     * @param id 管理员ID
     * @param adminDTO 管理员信息
     * @return 是否成功
     */
    boolean updateAdmin(Long id, AdminDTO adminDTO);

    /**
     * 删除管理员
     *
     * @param id 管理员ID
     * @return 是否成功
     */
    boolean deleteAdmin(Long id);

    /**
     * 修改管理员密码
     *
     * @param id 管理员ID
     * @param passwordDTO 密码信息
     * @return 是否成功
     */
    boolean updatePassword(Long id, AdminPasswordDTO passwordDTO);

    /**
     * 重置管理员密码
     *
     * @param id 管理员ID
     * @param operatorId 操作者ID
     * @return 新密码
     */
    String resetPassword(Long id, Long operatorId);

    /**
     * 启用/禁用管理员
     *
     * @param id 管理员ID
     * @param status 状态（0-禁用，1-启用）
     * @param operatorId 操作者ID
     * @return 是否成功
     */
    boolean updateStatus(Long id, Integer status, Long operatorId);

    /**
     * 分页查询管理员列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param keyword 关键字
     * @return 管理员列表
     */
    IPage<AdminVO> getAdminPage(int page, int size, String keyword);

    /**
     * 检查管理员是否有指定权限
     *
     * @param adminId 管理员ID
     * @param permission 权限标识
     * @return 是否有权限
     */
    boolean hasPermission(Long adminId, String permission);

    /**
     * 获取管理员所有权限
     *
     * @param adminId 管理员ID
     * @return 权限列表
     */
    List<String> getPermissions(Long adminId);

    /**
     * 记录管理员操作日志
     *
     * @param adminId 管理员ID
     * @param module 模块
     * @param operation 操作
     * @param content 内容
     * @param ip IP地址
     */
    void logOperation(Long adminId, String module, String operation, String content, String ip);

    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 角色ID
     */
    Long createRole(AdminRoleDTO roleDTO);

    /**
     * 更新角色
     *
     * @param id 角色ID
     * @param roleDTO 角色信息
     * @return 是否成功
     */
    boolean updateRole(Long id, AdminRoleDTO roleDTO);

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 是否成功
     */
    boolean deleteRole(Long id);

    /**
     * 获取角色列表
     *
     * @return 角色列表
     */
    List<AdminRole> getRoleList();

    /**
     * 获取权限列表
     *
     * @return 权限列表
     */
    List<AdminPermission> getPermissionList();

    /**
     * 获取角色权限
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getRolePermissions(Long roleId);

    /**
     * 设置角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean setRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 将Admin转换为AdminVO
     *
     * @param admin 管理员信息
     * @return 管理员VO
     */
    AdminVO convertToAdminVO(Admin admin);

    /**
     * 获取权限树
     *
     * @return 权限树
     */
    List<AdminPermissionTreeVO> getPermissionTree();

    /**
     * 根据ID获取权限
     *
     * @param id 权限ID
     * @return 权限信息
     */
    AdminPermission getPermissionById(Long id);

    /**
     * 创建权限
     *
     * @param permissionDTO 权限信息
     * @return 权限ID
     */
    Long createPermission(AdminPermissionDTO permissionDTO);

    /**
     * 更新权限
     *
     * @param id 权限ID
     * @param permissionDTO 权限信息
     * @return 是否成功
     */
    boolean updatePermission(Long id, AdminPermissionDTO permissionDTO);

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 是否成功
     */
    boolean deletePermission(Long id);

    /**
     * 更新权限状态
     *
     * @param id 权限ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    boolean updatePermissionStatus(Long id, Integer status);

    /**
     * 获取角色列表
     *
     * @param page 页码
     * @param pageSize 每页条数
     * @param keyword 关键字
     * @return 角色列表
     */
    IPage<AdminRole> getRoleList(Integer page, Integer pageSize, String keyword);

    /**
     * 获取角色权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getRolePermissionIds(Long roleId);

    /**
     * 分配角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    boolean assignRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 根据ID获取角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    AdminRole getRoleById(Long id);

    /**
     * 更新角色状态
     *
     * @param id 角色ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    boolean updateRoleStatus(Long id, Integer status);
}
