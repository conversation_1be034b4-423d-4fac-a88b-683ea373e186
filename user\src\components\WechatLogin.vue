<template>
  <div class="wechat-login-container">
    <!-- PC端微信扫码登录 -->
    <div v-if="!isMobile" class="wechat-login-button" @click="handleWechatLogin">
      <img src="@/assets/images/wechat-logo.png" alt="微信登录" class="wechat-logo" />
      <span>微信登录</span>
    </div>

    <!-- 移动端微信登录 -->
    <div v-else class="wechat-login-button mobile" @click="handleWechatLogin">
      <img src="@/assets/images/wechat-logo.png" alt="微信登录" class="wechat-logo" />
      <span>微信登录</span>
    </div>

    <!-- 微信扫码弹窗 -->
    <van-popup v-model:show="showQrCode" round closeable @closed="handlePopupClosed">
      <div class="qrcode-container">
        <h3>微信扫码登录</h3>
        <SafeQRCode
          v-if="qrCodeUrl"
          :text="qrCodeUrl"
          :width="200"
          :height="200"
          :correct-level="3"
          ref="safeQrCodeRef"
        />
        <p>请使用微信扫一扫</p>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getWechatAuthUrl, checkWechatLoginStatus } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'
import SafeQRCode from '@/components/common/SafeQRCode.vue'

const router = useRouter()
const userStore = useUserStore()

// 是否为移动设备
const isMobile = ref(false)
// 是否显示二维码弹窗
const showQrCode = ref(false)
// 二维码URL
const qrCodeUrl = ref('')
// 安全二维码组件引用
const safeQrCodeRef = ref<InstanceType<typeof SafeQRCode> | null>(null)
// 轮询定时器
let pollingTimer: number | null = null
// 登录状态ID
let loginStateId = ''

// 检测设备类型
onMounted(() => {
  isMobile.value = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
})

// 处理微信登录
const handleWechatLogin = async () => {
  try {
    if (isMobile.value) {
      // 移动端直接跳转微信授权
      const res = await getWechatAuthUrl()
      if (res.code === 200 && res.data.authUrl) {
        window.location.href = res.data.authUrl
      } else {
        showToast('获取微信授权URL失败')
      }
    } else {
      // 先清空二维码URL
      qrCodeUrl.value = ''

      // PC端显示扫码弹窗
      showQrCode.value = true

      // 获取微信扫码登录URL
      const res = await getWechatAuthUrl()
      if (res.code === 200 && res.data) {
        // 保存登录状态ID
        loginStateId = res.data.state

        // 设置二维码URL，SafeQRCode组件会自动生成二维码
        qrCodeUrl.value = res.data.authUrl

        // 开始轮询检查登录状态
        startPolling()
      } else {
        showQrCode.value = false
        showToast('获取微信二维码失败')
      }
    }
  } catch (error) {
    console.error('微信登录失败', error)
    showToast('微信登录失败，请稍后再试')
  }
}

// 处理弹窗关闭
const handlePopupClosed = () => {
  // 清除轮询定时器
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }

  // 清空二维码URL
  qrCodeUrl.value = ''
}

// 开始轮询检查登录状态
const startPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
  }

  pollingTimer = setInterval(async () => {
    try {
      // 检查登录状态
      const res = await checkWechatLoginStatus(loginStateId)
      if (res.code === 200 && res.data) {
        // 登录成功
        clearInterval(pollingTimer as number)
        pollingTimer = null
        showQrCode.value = false

        // 保存登录信息
        userStore.setToken(res.data.token)
        userStore.setUser(res.data.user)

        // 跳转到首页
        router.push('/')
        showToast('登录成功')
      }
    } catch (error) {
      console.error('检查微信登录状态失败', error)
    }
  }, 2000) // 每2秒检查一次
}
</script>

<style scoped>
.wechat-login-container {
  margin: 10px 0;
}

.wechat-login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  background-color: #07c160;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.wechat-login-button:hover {
  background-color: #06ad56;
}

.wechat-login-button.mobile {
  width: 100%;
  padding: 12px 15px;
}

.wechat-logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.qrcode-container {
  padding: 20px;
  text-align: center;
}

.qrcode-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #323233;
}

.qrcode {
  margin: 0 auto;
  width: 200px;
  height: 200px;
}

.qrcode-container p {
  margin-top: 20px;
  color: #969799;
  font-size: 14px;
}
</style>
