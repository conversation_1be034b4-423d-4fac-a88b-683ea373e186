#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PhotoTagMoment照片笔记功能调试脚本
用于调试照片笔记发布和后台管理显示问题
"""

import json
import time
import socket
import os
from datetime import datetime

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    print("⚠️ requests库未安装，将跳过API测试")

try:
    import mysql.connector
    HAS_MYSQL = True
except ImportError:
    HAS_MYSQL = False
    print("⚠️ mysql-connector-python未安装，将跳过数据库测试")

# 配置
BASE_URL = "http://localhost:8081/api"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "123456"

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'phototag_moment',
    'charset': 'utf8mb4'
}

def check_database_tables():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构...")

    if not HAS_MYSQL:
        print("⚠️ mysql-connector-python未安装，跳过数据库检查")
        print("💡 建议安装: pip install mysql-connector-python")
        return False

    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 检查ptm_photo_note表是否存在
        cursor.execute("SHOW TABLES LIKE 'ptm_photo_note'")
        result = cursor.fetchone()

        if result:
            print("✅ ptm_photo_note表存在")

            # 检查表结构
            cursor.execute("DESCRIBE ptm_photo_note")
            columns = cursor.fetchall()
            print("📋 ptm_photo_note表结构:")
            for column in columns:
                print(f"   - {column[0]}: {column[1]} {column[2] if column[2] == 'NO' else 'NULL'}")

            # 检查表中的数据
            cursor.execute("SELECT COUNT(*) FROM ptm_photo_note")
            count = cursor.fetchone()[0]
            print(f"📊 ptm_photo_note表中共有 {count} 条记录")

            if count > 0:
                # 显示最新的几条记录
                cursor.execute("""
                    SELECT id, user_id, title, content, status, created_at
                    FROM ptm_photo_note
                    ORDER BY created_at DESC
                    LIMIT 5
                """)
                records = cursor.fetchall()
                print("📝 最新的5条记录:")
                for record in records:
                    print(f"   ID: {record[0]}, 用户: {record[1]}, 标题: {record[2]}, 状态: {record[3]}, 时间: {record[5]}")
        else:
            print("❌ ptm_photo_note表不存在")
            return False

        # 检查相关表
        related_tables = [
            'ptm_photo_note_image',
            'ptm_photo_note_tag',
            'ptm_photo_note_mention',
            'ptm_tag_stats',
            'ptm_photo_note_like',
            'ptm_photo_note_collection'
        ]

        for table in related_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            result = cursor.fetchone()
            if result:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table}表存在，共有 {count} 条记录")
            else:
                print(f"❌ {table}表不存在")

        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("\n🔍 检查服务器状态...")

    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 8081))
        sock.close()

        if result == 0:
            print("✅ 服务器端口8081可访问")
            return True
        else:
            print("❌ 服务器端口8081不可访问")
            return False
    except Exception as e:
        print(f"❌ 服务器连接检查失败: {str(e)}")
        return False

def test_photo_note_apis():
    """测试照片笔记相关API（如果服务器可用）"""
    print("\n🔍 测试照片笔记API...")

    if not HAS_REQUESTS:
        print("⚠️ requests库未安装，跳过API测试")
        print("💡 建议安装: pip install requests")
        return

    if not check_server_status():
        print("⚠️ 服务器不可用，跳过API测试")
        return

    try:
        # 测试管理端照片笔记列表接口
        print("📋 测试管理端照片笔记列表接口...")
        response = requests.get(f"{BASE_URL}/admin/photo-notes/list", params={
            "page": 1,
            "size": 10
        }, timeout=10)

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result['data']
                print(f"✅ 管理端接口调用成功，返回 {data.get('total', 0)} 条记录")
                if data.get('records'):
                    print("📝 返回的记录:")
                    for record in data['records'][:3]:
                        print(f"   - ID: {record.get('id')}, 标题: {record.get('title', 'N/A')}")
            else:
                print(f"❌ 管理端接口返回失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ 管理端接口请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")

    except Exception as e:
        print(f"❌ API测试异常: {str(e)}")

def analyze_photo_note_flow():
    """分析照片笔记发布流程"""
    print("\n🔍 分析照片笔记发布流程...")

    # 检查PhotoNotePublishDTO结构
    print("📋 PhotoNotePublishDTO应包含的字段:")
    required_fields = [
        "title (可选)",
        "content (必填)",
        "photos (照片列表)",
        "visibility (可见性)",
        "allowComment (是否允许评论)",
        "location (位置信息)",
        "longitude (经度)",
        "latitude (纬度)"
    ]

    for field in required_fields:
        print(f"   - {field}")

    print("\n📋 发布流程应该包含的步骤:")
    steps = [
        "1. 前端调用 POST /api/photo-notes/publish",
        "2. PhotoNoteController.publishPhotoNote 接收请求",
        "3. PhotoNoteService.publishPhotoNote 处理业务逻辑",
        "4. 保存照片笔记主记录到 ptm_photo_note 表",
        "5. 保存照片信息到 ptm_photo 表",
        "6. 保存照片关联到 ptm_photo_note_image 表",
        "7. 处理标签并保存到 ptm_photo_note_tag 表",
        "8. 处理@用户并保存到 ptm_photo_note_mention 表",
        "9. 更新标签统计到 ptm_tag_stats 表",
        "10. 返回照片笔记ID"
    ]

    for step in steps:
        print(f"   {step}")

def check_mapper_xml_files():
    """检查Mapper XML文件"""
    print("\n🔍 检查Mapper XML文件...")

    import os

    mapper_files = [
        "server/src/main/resources/mapper/PhotoNoteMapper.xml",
        "server/src/main/resources/mapper/PhotoNoteImageMapper.xml",
        "server/src/main/resources/mapper/PhotoNoteTagMapper.xml",
        "server/src/main/resources/mapper/PhotoNoteMentionMapper.xml"
    ]

    for mapper_file in mapper_files:
        if os.path.exists(mapper_file):
            print(f"✅ {mapper_file} 存在")
        else:
            print(f"❌ {mapper_file} 不存在")

def generate_test_data():
    """生成测试数据"""
    print("\n🔍 生成测试数据...")

    if not HAS_MYSQL:
        print("⚠️ mysql-connector-python未安装，跳过测试数据生成")
        return False

    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # 检查是否有测试用户
        cursor.execute("SELECT id FROM ptm_user WHERE username = 'testuser' LIMIT 1")
        user = cursor.fetchone()

        if not user:
            print("⚠️ 没有找到测试用户，创建测试用户...")
            # 创建测试用户
            cursor.execute("""
                INSERT INTO ptm_user (username, nickname, password, email, status, created_at, updated_at)
                VALUES ('testuser', '测试用户', '$2a$10$example', '<EMAIL>', 1, NOW(), NOW())
            """)
            conn.commit()
            user_id = cursor.lastrowid
            print(f"✅ 创建测试用户成功，ID: {user_id}")
        else:
            user_id = user[0]
            print(f"✅ 找到测试用户，ID: {user_id}")

        # 插入测试照片笔记
        print("📝 插入测试照片笔记...")
        cursor.execute("""
            INSERT INTO ptm_photo_note (
                user_id, title, content, photo_count, status, visibility,
                allow_comment, created_at, updated_at
            ) VALUES (
                %s, '测试照片笔记', '这是一条测试照片笔记内容 #测试标签# @测试用户',
                1, 0, 1, 1, NOW(), NOW()
            )
        """, (user_id,))
        conn.commit()
        note_id = cursor.lastrowid
        print(f"✅ 插入测试照片笔记成功，ID: {note_id}")

        cursor.close()
        conn.close()

        return True

    except Exception as e:
        print(f"❌ 生成测试数据失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 PhotoTagMoment照片笔记功能调试开始")
    print("=" * 60)

    # 1. 检查数据库表结构
    db_ok = check_database_tables()

    # 2. 分析发布流程
    analyze_photo_note_flow()

    # 3. 检查Mapper文件
    check_mapper_xml_files()

    # 4. 测试API接口
    test_photo_note_apis()

    # 5. 生成测试数据（如果需要）
    if db_ok:
        generate_test_data()

    print("\n" + "=" * 60)
    print("📊 调试总结")
    print("=" * 60)

    print("\n🔍 可能的问题原因:")
    print("1. 服务器Java版本不兼容，无法启动")
    print("2. 前端发布请求可能没有正确调用后端接口")
    print("3. 数据库事务可能回滚导致数据未保存")
    print("4. 后台管理查询可能有权限或状态过滤问题")

    print("\n🛠️ 建议的修复步骤:")
    print("1. 解决Java版本问题，确保服务器能正常启动")
    print("2. 检查前端网络请求，确认发布接口被正确调用")
    print("3. 添加详细的日志记录，跟踪数据保存过程")
    print("4. 检查后台管理查询的状态过滤条件")
    print("5. 验证数据库事务配置和异常处理")

if __name__ == "__main__":
    main()
