package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.entity.PhotoAudit;
import com.phototagmoment.service.PhotoAuditService;
import com.phototagmoment.vo.PhotoAuditVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 照片审核控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/photo-audit")
public class PhotoAuditController {

    @Autowired
    private PhotoAuditService photoAuditService;

    /**
     * 获取待审核照片列表
     */
    @GetMapping("/pending")
    public ApiResponse<IPage<PhotoAuditVO>> getPendingAuditPhotos(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        IPage<PhotoAuditVO> photoPage = photoAuditService.getPendingAuditPhotos(page, size);
        return ApiResponse.success(photoPage);
    }

    /**
     * 审核照片
     */
    @PostMapping("/audit")
    public ApiResponse<Boolean> auditPhoto(
            @RequestParam Long photoId,
            @RequestParam Boolean approved,
            @RequestParam(required = false) String rejectReason) {
        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Long adminId = Long.parseLong(authentication.getName());

        boolean result = photoAuditService.auditPhoto(photoId, approved, rejectReason, adminId);
        return ApiResponse.success(result);
    }

    /**
     * 批量审核照片
     */
    @PostMapping("/batch-audit")
    public ApiResponse<Boolean> batchAuditPhotos(
            @RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> photoIds = (List<Long>) params.get("photoIds");
        Boolean approved = (Boolean) params.get("approved");
        String rejectReason = (String) params.get("rejectReason");

        // 获取当前登录用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Long adminId = Long.parseLong(authentication.getName());

        boolean result = photoAuditService.batchAuditPhotos(photoIds, approved, rejectReason, adminId);
        return ApiResponse.success(result);
    }

    /**
     * 获取照片审核历史
     */
    @GetMapping("/history/{photoId}")
    public ApiResponse<List<PhotoAudit>> getPhotoAuditHistory(@PathVariable Long photoId) {
        List<PhotoAudit> auditHistory = photoAuditService.getPhotoAuditHistory(photoId);
        return ApiResponse.success(auditHistory);
    }
}
