<template>
  <div class="post-detail">
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#3498db" />
      <p>加载中...</p>
    </div>
    <template v-else>
      <div class="post-header">
        <div class="post-user">
          <img :src="privateUserAvatarUrl || post.userAvatar" :alt="post.userName" class="user-avatar" @click="goToUserProfile" />
          <div class="user-info">
            <div class="user-name" @click="goToUserProfile">{{ post.userName }}</div>
            <div class="post-time">{{ formatTime(post.createdAt) }}</div>
          </div>
        </div>
        <div class="post-actions">
          <van-button v-if="isCurrentUser" icon="delete-o" round plain size="small" @click="deletePost">删除</van-button>
          <van-button v-else-if="!isFollowing" icon="plus" round plain size="small" @click="followUser">关注</van-button>
          <van-button v-else icon="success" round plain size="small" @click="unfollowUser">已关注</van-button>
        </div>
      </div>

      <div class="post-content">
        <h1 class="post-title">{{ post.title }}</h1>
        <div class="post-description" v-html="post.description"></div>

        <div class="post-tags" v-if="post.tags && post.tags.length > 0">
          <span v-for="tag in post.tags" :key="tag" class="post-tag" @click="searchTag(tag)">#{{ tag }}</span>
        </div>

        <div class="post-location" v-if="post.location">
          <van-icon name="location-o" />
          <span>{{ post.location }}</span>
        </div>
      </div>

      <div class="photo-gallery">
        <div v-for="(photo, index) in post.photos" :key="photo.id" class="photo-item" @click="showPhotoViewer(index)">
          <img :src="photo.thumbnailUrl" :alt="photo.title" />
        </div>
      </div>

      <div class="post-stats">
        <div class="stat-item" @click="toggleLike">
          <van-icon :name="post.isLiked ? 'like' : 'like-o'" :class="{ active: post.isLiked }" />
          <span>{{ post.likeCount || 0 }}</span>
        </div>
        <div class="stat-item" @click="focusCommentInput">
          <van-icon name="comment-o" />
          <span>{{ post.commentCount || 0 }}</span>
        </div>
        <div class="stat-item" @click="toggleCollect">
          <van-icon :name="post.isCollected ? 'star' : 'star-o'" :class="{ active: post.isCollected }" />
          <span>{{ post.collectCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <van-icon name="eye-o" />
          <span>{{ post.viewCount || 0 }}</span>
        </div>
      </div>

      <div class="comment-section">
        <h2 class="section-title">评论 ({{ post.commentCount || 0 }})</h2>

        <div class="comment-input">
          <img :src="privateCurrentUserAvatarUrl || currentUserAvatar" :alt="currentUserName" class="user-avatar" />
          <div class="input-wrapper">
            <van-field
              v-model="commentText"
              placeholder="添加评论..."
              :disabled="!isLoggedIn"
              ref="commentInput"
            />
            <van-button
              type="primary"
              size="small"
              :disabled="!commentText.trim() || !isLoggedIn"
              @click="addComment"
            >
              发送
            </van-button>
          </div>
        </div>

        <div v-if="!isLoggedIn" class="login-tip">
          请<router-link to="/login">登录</router-link>后发表评论
        </div>

        <div class="comments-list">
          <div v-if="comments.length === 0" class="no-comments">
            暂无评论，快来发表第一条评论吧！
          </div>
          <div v-else v-for="comment in comments" :key="comment.id" class="comment-item">
            <img :src="comment.userAvatar" :alt="comment.userName" class="user-avatar" />
            <div class="comment-content">
              <div class="comment-header">
                <span class="comment-user">{{ comment.userName }}</span>
                <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
              </div>
              <div class="comment-text">{{ comment.content }}</div>
              <div class="comment-actions">
                <span class="action-item" @click="likeComment(comment)">
                  <van-icon :name="comment.isLiked ? 'like' : 'like-o'" :class="{ active: comment.isLiked }" />
                  <span>{{ comment.likeCount || 0 }}</span>
                </span>
                <span class="action-item" @click="replyComment(comment)">
                  <van-icon name="comment-o" />
                  <span>回复</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 照片查看器 -->
    <van-image-preview
      v-model:show="showViewer"
      :images="photoUrls"
      :start-position="currentIndex"
      :show-index="true"
      @change="onPhotoChange"
    >
      <template #index>{{ currentIndex + 1 }} / {{ photoUrls.length }}</template>
    </van-image-preview>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
// import { getPostDetail, likePost, collectPost, deletePost } from '@/api/post'
import { getPhotoComments, addPhotoComment, likeComment } from '@/api/photo'
import { followUser, unfollowUser } from '@/api/user'
import { useUserStore } from '@/stores/user'
import { getPrivateImageUrl } from '@/api/file'

// 路由和用户信息
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 笔记详情
const post = ref({} as any)
const loading = ref(true)
const postId = computed(() => route.params.id)

// 评论相关
const comments = ref([] as any[])
const commentText = ref('')
const commentInput = ref(null)

// 照片查看器
const showViewer = ref(false)
const currentIndex = ref(0)
const photoUrls = computed(() => {
  if (!post.value.photos) return []
  return post.value.photos.map((photo: any) => photo.url)
})

// 私有图片URL
const privatePhotoUrls = ref([] as string[])
const privateUserAvatarUrl = ref('')
const privateCurrentUserAvatarUrl = ref('')

// 用户信息
const isLoggedIn = computed(() => userStore.isLoggedIn)
const currentUserId = computed(() => userStore.userId)
const currentUserName = computed(() => userStore.username)
const currentUserAvatar = computed(() => userStore.avatar)
const isCurrentUser = computed(() => post.value.userId === currentUserId.value)
const isFollowing = ref(false)

// 加载笔记详情
const loadPostDetail = async () => {
  try {
    loading.value = true
    const res = await getPostDetail(Number(postId.value))
    if (res.code === 200) {
      post.value = res.data

      // 处理照片URL
      if (post.value.photos && post.value.photos.length > 0) {
        privatePhotoUrls.value = await Promise.all(
          post.value.photos.map(async (photo: any) => {
            return await getPrivateImageUrl(photo.url)
          })
        )
      }

      // 处理用户头像URL
      if (post.value.userAvatar) {
        privateUserAvatarUrl.value = await getPrivateImageUrl(post.value.userAvatar)
      }

      // 处理当前用户头像URL
      if (currentUserAvatar.value && !currentUserAvatar.value.startsWith('/')) {
        privateCurrentUserAvatarUrl.value = await getPrivateImageUrl(currentUserAvatar.value)
      }

      // 加载评论
      loadComments()
    }
  } catch (error) {
    console.error('加载笔记详情失败', error)
  } finally {
    loading.value = false
  }
}

// 加载评论
const loadComments = async () => {
  try {
    const res = await getPhotoComments(Number(postId.value))
    if (res.code === 200) {
      comments.value = res.data
    }
  } catch (error) {
    console.error('加载评论失败', error)
  }
}

// 添加评论
const addComment = async () => {
  if (!commentText.value.trim()) return

  try {
    const res = await addPhotoComment({
      photoId: Number(postId.value),
      content: commentText.value
    })

    if (res.code === 200) {
      // 重新加载评论
      loadComments()
      // 清空评论框
      commentText.value = ''
      // 更新评论数
      post.value.commentCount = (post.value.commentCount || 0) + 1
    }
  } catch (error) {
    console.error('添加评论失败', error)
    ElMessage.error('添加评论失败')
  }
}

// 点赞评论
const likeComment = async (comment: any) => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    const res = await likeComment(comment.id)
    if (res.code === 200) {
      comment.isLiked = !comment.isLiked
      comment.likeCount = comment.isLiked
        ? (comment.likeCount || 0) + 1
        : Math.max((comment.likeCount || 0) - 1, 0)
    }
  } catch (error) {
    console.error('点赞评论失败', error)
  }
}

// 回复评论
const replyComment = (comment: any) => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    return
  }

  // 聚焦评论框
  focusCommentInput()
  // 添加@用户前缀
  commentText.value = `@${comment.userName} `
}

// 聚焦评论框
const focusCommentInput = () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    return
  }

  if (commentInput.value) {
    // @ts-ignore
    commentInput.value.focus()
  }
}

// 点赞笔记
const toggleLike = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    const res = await likePost(Number(postId.value))
    if (res.code === 200) {
      post.value.isLiked = !post.value.isLiked
      post.value.likeCount = post.value.isLiked
        ? (post.value.likeCount || 0) + 1
        : Math.max((post.value.likeCount || 0) - 1, 0)
    }
  } catch (error) {
    console.error('点赞失败', error)
  }
}

// 收藏笔记
const toggleCollect = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    const res = await collectPost(Number(postId.value))
    if (res.code === 200) {
      post.value.isCollected = !post.value.isCollected
      post.value.collectCount = post.value.isCollected
        ? (post.value.collectCount || 0) + 1
        : Math.max((post.value.collectCount || 0) - 1, 0)
    }
  } catch (error) {
    console.error('收藏失败', error)
  }
}

// 关注用户
const followUser = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    const res = await followUser(post.value.userId)
    if (res.code === 200) {
      isFollowing.value = true
      ElMessage.success('关注成功')
    }
  } catch (error) {
    console.error('关注失败', error)
  }
}

// 取消关注
const unfollowUser = async () => {
  try {
    const res = await unfollowUser(post.value.userId)
    if (res.code === 200) {
      isFollowing.value = false
      ElMessage.success('已取消关注')
    }
  } catch (error) {
    console.error('取消关注失败', error)
  }
}

// 删除笔记
const deletePost = async () => {
  if (!isCurrentUser.value) return

  try {
    const res = await deletePost(Number(postId.value))
    if (res.code === 200) {
      ElMessage.success('删除成功')
      router.push('/user/profile')
    }
  } catch (error) {
    console.error('删除失败', error)
    ElMessage.error('删除失败')
  }
}

// 前往用户主页
const goToUserProfile = () => {
  router.push(`/user/${post.value.userId}`)
}

// 搜索标签
const searchTag = (tag: string) => {
  router.push(`/search?tag=${encodeURIComponent(tag)}`)
}

// 显示照片查看器
const showPhotoViewer = (index: number) => {
  currentIndex.value = index
  showViewer.value = true
}

// 照片切换事件
const onPhotoChange = (index: number) => {
  currentIndex.value = index
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''

  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }

  // 小于1周
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }

  // 格式化日期
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 页面加载时获取笔记详情
onMounted(() => {
  loadPostDetail()
})
</script>

<style lang="scss" scoped>
.post-detail {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;

    p {
      margin-top: 16px;
      color: #666;
    }
  }

  .post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .post-user {
      display: flex;
      align-items: center;

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 12px;
        cursor: pointer;
      }

      .user-info {
        .user-name {
          font-weight: 500;
          font-size: 16px;
          color: #333;
          cursor: pointer;

          &:hover {
            color: #3498db;
          }
        }

        .post-time {
          font-size: 12px;
          color: #999;
          margin-top: 2px;
        }
      }
    }
  }

  .post-content {
    margin-bottom: 20px;

    .post-title {
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #333;
    }

    .post-description {
      font-size: 16px;
      line-height: 1.6;
      color: #444;
      margin-bottom: 16px;
      white-space: pre-wrap;
    }

    .post-tags {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 12px;

      .post-tag {
        background-color: #f0f7fc;
        color: #3498db;
        padding: 4px 10px;
        border-radius: 16px;
        font-size: 14px;
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;

        &:hover {
          background-color: #e1f0fa;
        }
      }
    }

    .post-location {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;

      .van-icon {
        margin-right: 6px;
      }
    }
  }

  .photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 20px;

    .photo-item {
      aspect-ratio: 1;
      overflow: hidden;
      border-radius: 8px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }

  .post-stats {
    display: flex;
    justify-content: space-around;
    padding: 12px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;

    .stat-item {
      display: flex;
      align-items: center;
      cursor: pointer;

      .van-icon {
        font-size: 20px;
        margin-right: 6px;
        color: #666;

        &.active {
          color: #3498db;
        }
      }

      span {
        font-size: 14px;
        color: #666;
      }

      &:hover {
        .van-icon:not(.active) {
          color: #3498db;
        }

        span {
          color: #3498db;
        }
      }
    }
  }

  .comment-section {
    .section-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #333;
    }

    .comment-input {
      display: flex;
      margin-bottom: 20px;

      .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 12px;
      }

      .input-wrapper {
        flex: 1;
        display: flex;

        .van-field {
          flex: 1;
          margin-right: 8px;
        }
      }
    }

    .login-tip {
      text-align: center;
      margin-bottom: 20px;
      font-size: 14px;
      color: #666;

      a {
        color: #3498db;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .comments-list {
      .no-comments {
        text-align: center;
        padding: 20px 0;
        color: #999;
        font-size: 14px;
      }

      .comment-item {
        display: flex;
        margin-bottom: 16px;

        .user-avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          object-fit: cover;
          margin-right: 12px;
        }

        .comment-content {
          flex: 1;

          .comment-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;

            .comment-user {
              font-weight: 500;
              font-size: 14px;
              color: #333;
            }

            .comment-time {
              font-size: 12px;
              color: #999;
            }
          }

          .comment-text {
            font-size: 14px;
            line-height: 1.5;
            color: #444;
            margin-bottom: 8px;
          }

          .comment-actions {
            display: flex;

            .action-item {
              display: flex;
              align-items: center;
              margin-right: 16px;
              cursor: pointer;

              .van-icon {
                font-size: 16px;
                margin-right: 4px;
                color: #999;

                &.active {
                  color: #3498db;
                }
              }

              span {
                font-size: 12px;
                color: #999;
              }

              &:hover {
                .van-icon:not(.active) {
                  color: #3498db;
                }

                span {
                  color: #3498db;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
