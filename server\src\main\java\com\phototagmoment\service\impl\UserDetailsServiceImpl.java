package com.phototagmoment.service.impl;

import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.service.AuthenticationService;
import com.phototagmoment.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户详情服务实现类
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserService userService;
    private final AuthenticationService authenticationService;

    public UserDetailsServiceImpl(@Lazy UserService userService, AuthenticationService authenticationService) {
        this.userService = userService;
        this.authenticationService = authenticationService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 查询用户
        User user = userService.getUserByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在");
        }

        // 查询用户认证信息
        UserAuth userAuth = authenticationService.getUserAuth(user.getId(), "username");
        if (userAuth == null) {
            throw new UsernameNotFoundException("用户认证信息不存在");
        }

        // 使用认证服务创建用户详情
        UserDetails userDetails = authenticationService.createUserDetails(user);

        // 返回带有密码的UserDetails
        return new org.springframework.security.core.userdetails.User(
                userDetails.getUsername(),
                userAuth.getCredential(),
                userDetails.isEnabled(),
                userDetails.isAccountNonExpired(),
                userDetails.isCredentialsNonExpired(),
                userDetails.isAccountNonLocked(),
                userDetails.getAuthorities()
        );
    }
}
