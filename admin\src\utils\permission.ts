/**
 * 权限检查工具函数
 */

import { useUserStore } from '@/stores/user'

/**
 * 检查用户是否具有指定权限
 * @param permission 权限标识
 * @returns 是否具有权限
 */
export function hasPermission(permission: string): boolean {
  const userStore = useUserStore()
  const userRoles = userStore.roles || []
  const userPermissions = userStore.permissions || []

  // 如果用户已登录且有token，则认为是管理员，临时给予权限
  if (userStore.token && userStore.userInfo) {
    // 特定管理员用户给予所有权限
    const adminUsers = ['admin', 'admin_test', 'superadmin']
    if (adminUsers.includes(userStore.userInfo.username)) {
      return true
    }

    // 其他已登录用户给予基本管理员权限
    if (permission === 'ADMIN' || permission === 'SUPER_ADMIN') {
      return true
    }
  }

  // 超级管理员拥有所有权限
  if (userRoles.includes('SUPER_ADMIN')) {
    return true
  }

  // 检查是否具有指定权限
  return userRoles.includes(permission) || userPermissions.includes(permission)
}

/**
 * 检查用户是否具有任意一个权限
 * @param permissions 权限列表
 * @returns 是否具有任意权限
 */
export function hasAnyPermission(permissions: string[]): boolean {
  return permissions.some(permission => hasPermission(permission))
}

/**
 * 检查用户是否具有所有权限
 * @param permissions 权限列表
 * @returns 是否具有所有权限
 */
export function hasAllPermissions(permissions: string[]): boolean {
  return permissions.every(permission => hasPermission(permission))
}

/**
 * 检查用户是否为管理员
 * @returns 是否为管理员
 */
export function isAdmin(): boolean {
  return hasAnyPermission(['ADMIN', 'SUPER_ADMIN'])
}

/**
 * 检查用户是否为超级管理员
 * @returns 是否为超级管理员
 */
export function isSuperAdmin(): boolean {
  return hasPermission('SUPER_ADMIN')
}

/**
 * 权限指令
 * 用于在模板中进行权限控制
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding

    if (value) {
      const hasAuth = Array.isArray(value)
        ? hasAnyPermission(value)
        : hasPermission(value)

      if (!hasAuth) {
        el.style.display = 'none'
      }
    }
  },
  updated(el: HTMLElement, binding: any) {
    const { value } = binding

    if (value) {
      const hasAuth = Array.isArray(value)
        ? hasAnyPermission(value)
        : hasPermission(value)

      el.style.display = hasAuth ? '' : 'none'
    }
  }
}

/**
 * 权限常量
 */
export const PERMISSIONS = {
  // 系统管理
  SYSTEM_ADMIN: 'SYSTEM_ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',

  // 用户管理
  USER_VIEW: 'USER_VIEW',
  USER_CREATE: 'USER_CREATE',
  USER_UPDATE: 'USER_UPDATE',
  USER_DELETE: 'USER_DELETE',

  // 角色管理
  ROLE_VIEW: 'ROLE_VIEW',
  ROLE_CREATE: 'ROLE_CREATE',
  ROLE_UPDATE: 'ROLE_UPDATE',
  ROLE_DELETE: 'ROLE_DELETE',

  // 权限管理
  PERMISSION_VIEW: 'PERMISSION_VIEW',
  PERMISSION_CREATE: 'PERMISSION_CREATE',
  PERMISSION_UPDATE: 'PERMISSION_UPDATE',
  PERMISSION_DELETE: 'PERMISSION_DELETE',

  // 文件管理
  FILE_VIEW: 'FILE_VIEW',
  FILE_UPLOAD: 'FILE_UPLOAD',
  FILE_DELETE: 'FILE_DELETE',
  FILE_MANAGE: 'FILE_MANAGE',

  // 文件上传配置
  FILE_CONFIG_VIEW: 'FILE_CONFIG_VIEW',
  FILE_CONFIG_CREATE: 'FILE_CONFIG_CREATE',
  FILE_CONFIG_UPDATE: 'FILE_CONFIG_UPDATE',
  FILE_CONFIG_DELETE: 'FILE_CONFIG_DELETE',
  FILE_CONFIG_TEST: 'FILE_CONFIG_TEST',

  // 敏感词管理
  SENSITIVE_WORD_VIEW: 'SENSITIVE_WORD_VIEW',
  SENSITIVE_WORD_CREATE: 'SENSITIVE_WORD_CREATE',
  SENSITIVE_WORD_UPDATE: 'SENSITIVE_WORD_UPDATE',
  SENSITIVE_WORD_DELETE: 'SENSITIVE_WORD_DELETE',

  // 日志管理
  LOG_VIEW: 'LOG_VIEW',
  LOG_DELETE: 'LOG_DELETE',
  LOG_EXPORT: 'LOG_EXPORT'
} as const

/**
 * 角色常量
 */
export const ROLES = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  USER: 'USER',
  GUEST: 'GUEST'
} as const

/**
 * 权限组
 */
export const PERMISSION_GROUPS = {
  SYSTEM: [
    PERMISSIONS.SYSTEM_ADMIN,
    PERMISSIONS.SUPER_ADMIN,
    PERMISSIONS.ADMIN
  ],
  USER_MANAGEMENT: [
    PERMISSIONS.USER_VIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE
  ],
  ROLE_MANAGEMENT: [
    PERMISSIONS.ROLE_VIEW,
    PERMISSIONS.ROLE_CREATE,
    PERMISSIONS.ROLE_UPDATE,
    PERMISSIONS.ROLE_DELETE
  ],
  PERMISSION_MANAGEMENT: [
    PERMISSIONS.PERMISSION_VIEW,
    PERMISSIONS.PERMISSION_CREATE,
    PERMISSIONS.PERMISSION_UPDATE,
    PERMISSIONS.PERMISSION_DELETE
  ],
  FILE_MANAGEMENT: [
    PERMISSIONS.FILE_VIEW,
    PERMISSIONS.FILE_UPLOAD,
    PERMISSIONS.FILE_DELETE,
    PERMISSIONS.FILE_MANAGE
  ],
  FILE_CONFIG_MANAGEMENT: [
    PERMISSIONS.FILE_CONFIG_VIEW,
    PERMISSIONS.FILE_CONFIG_CREATE,
    PERMISSIONS.FILE_CONFIG_UPDATE,
    PERMISSIONS.FILE_CONFIG_DELETE,
    PERMISSIONS.FILE_CONFIG_TEST
  ]
} as const

/**
 * 获取权限描述
 * @param permission 权限标识
 * @returns 权限描述
 */
export function getPermissionDescription(permission: string): string {
  const descriptions: Record<string, string> = {
    [PERMISSIONS.SYSTEM_ADMIN]: '系统管理',
    [PERMISSIONS.SUPER_ADMIN]: '超级管理员',
    [PERMISSIONS.ADMIN]: '管理员',
    [PERMISSIONS.USER_VIEW]: '查看用户',
    [PERMISSIONS.USER_CREATE]: '创建用户',
    [PERMISSIONS.USER_UPDATE]: '更新用户',
    [PERMISSIONS.USER_DELETE]: '删除用户',
    [PERMISSIONS.ROLE_VIEW]: '查看角色',
    [PERMISSIONS.ROLE_CREATE]: '创建角色',
    [PERMISSIONS.ROLE_UPDATE]: '更新角色',
    [PERMISSIONS.ROLE_DELETE]: '删除角色',
    [PERMISSIONS.FILE_VIEW]: '查看文件',
    [PERMISSIONS.FILE_UPLOAD]: '上传文件',
    [PERMISSIONS.FILE_DELETE]: '删除文件',
    [PERMISSIONS.FILE_MANAGE]: '管理文件',
    [PERMISSIONS.FILE_CONFIG_VIEW]: '查看文件配置',
    [PERMISSIONS.FILE_CONFIG_CREATE]: '创建文件配置',
    [PERMISSIONS.FILE_CONFIG_UPDATE]: '更新文件配置',
    [PERMISSIONS.FILE_CONFIG_DELETE]: '删除文件配置',
    [PERMISSIONS.FILE_CONFIG_TEST]: '测试文件配置'
  }

  return descriptions[permission] || permission
}

/**
 * 获取角色描述
 * @param role 角色标识
 * @returns 角色描述
 */
export function getRoleDescription(role: string): string {
  const descriptions: Record<string, string> = {
    [ROLES.SUPER_ADMIN]: '超级管理员',
    [ROLES.ADMIN]: '管理员',
    [ROLES.USER]: '普通用户',
    [ROLES.GUEST]: '访客'
  }

  return descriptions[role] || role
}
