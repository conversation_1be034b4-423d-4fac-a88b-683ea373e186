package com.phototagmoment.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.entity.PhotoAudit;
import com.phototagmoment.mapper.PhotoMapper;
import com.phototagmoment.service.ContentModerationService;
import com.phototagmoment.service.NotificationService;
import com.phototagmoment.service.PhotoAuditService;
import com.phototagmoment.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 照片审核任务
 */
@Slf4j
@Component
public class PhotoAuditTask {

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private PhotoAuditService photoAuditService;

    @Autowired
    private ContentModerationService contentModerationService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SystemConfigService configService;

    /**
     * 提交自动审核任务
     *
     * @param photoId 照片ID
     */
    @Async
    public void submitAutoAuditTask(Long photoId) {
        try {
            log.info("开始自动审核照片，照片ID: {}", photoId);

            // 查询照片信息
            // 使用 LambdaQueryWrapper 指定要查询的字段，避免查询 taken_time 字段
            LambdaQueryWrapper<Photo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Photo::getId, photoId)
                    .select(
                            Photo::getId,
                            Photo::getUserId,
                            Photo::getTitle,
                            Photo::getDescription,
                            Photo::getUrl,
                            Photo::getThumbnailUrl,
                            Photo::getStoragePath,
                            Photo::getFileType,
                            Photo::getWidth,
                            Photo::getHeight,
                            Photo::getLocation,
                            Photo::getVisibility,
                            Photo::getAllowComment,
                            Photo::getAllowDownload,
                            Photo::getLikeCount,
                            Photo::getCommentCount,
                            Photo::getViewCount,
                            Photo::getStatus,
                            Photo::getRejectReason,
                            Photo::getIsDeleted,
                            Photo::getCreatedAt,
                            Photo::getUpdatedAt
                    );
            Photo photo = photoMapper.selectOne(queryWrapper);
            if (photo == null) {
                log.error("照片不存在或已删除，照片ID: {}", photoId);
                return;
            }

            // 检查是否启用内容审核
            boolean contentModerationEnabled = configService.getBooleanValue("content-moderation.enabled", false);
            boolean autoApprove = configService.getBooleanValue("content-moderation.auto-approve", false);

            // 如果内容审核未启用，直接通过
            if (!contentModerationEnabled || autoApprove) {
                log.info("内容审核未启用或设置为自动通过，照片ID: {}", photoId);
                photoAuditService.createAuditRecord(photoId, 0, 1, null, null);

                // 发送通知给用户
                notificationService.createSystemNotification(
                    photo.getUserId(),
                    "您的照片《" + photo.getTitle() + "》已通过审核，现已公开显示。"
                );

                return;
            }

            // 进行内容审核
            boolean textPassed = true;
            boolean imagePassed = true;
            String rejectReason = null;

            // 审核文本内容
            if (photo.getDescription() != null && !photo.getDescription().isEmpty()) {
                textPassed = contentModerationService.moderateText(photo.getDescription());
                if (!textPassed) {
                    rejectReason = contentModerationService.getFailReason();
                    log.info("文本内容审核不通过，照片ID: {}, 原因: {}", photoId, rejectReason);
                }
            }

            // 审核图片内容
            if (textPassed && photo.getUrl() != null && !photo.getUrl().isEmpty()) {
                imagePassed = contentModerationService.moderateImageByUrl(photo.getUrl());
                if (!imagePassed) {
                    rejectReason = contentModerationService.getFailReason();
                    log.info("图片内容审核不通过，照片ID: {}, 原因: {}", photoId, rejectReason);
                }
            }

            // 创建审核记录
            if (textPassed && imagePassed) {
                // 审核通过
                photoAuditService.createAuditRecord(photoId, 0, 1, null, null);
                log.info("自动审核通过，照片ID: {}", photoId);

                // 发送通知给用户
                notificationService.createSystemNotification(
                    photo.getUserId(),
                    "您的照片《" + photo.getTitle() + "》已通过审核，现已公开显示。"
                );
            } else {
                // 审核不通过
                photoAuditService.createAuditRecord(photoId, 0, 2, rejectReason, null);
                log.info("自动审核不通过，照片ID: {}, 原因: {}", photoId, rejectReason);

                // 发送通知给用户
                notificationService.createSystemNotification(
                    photo.getUserId(),
                    "您的照片《" + photo.getTitle() + "》未通过审核，原因：" + rejectReason
                );
            }
        } catch (Exception e) {
            log.error("自动审核照片失败，照片ID: " + photoId, e);
            // 审核失败，转入人工审核
            try {
                photoAuditService.submitForManualAudit(photoId);
                log.info("自动审核失败，转入人工审核，照片ID: {}", photoId);
            } catch (Exception ex) {
                log.error("转入人工审核失败，照片ID: " + photoId, ex);
            }
        }
    }
}
