package com.phototagmoment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文件上传配置DTO
 */
@Data
@Schema(description = "文件上传配置")
public class FileUploadConfigDTO {

    @Schema(description = "配置ID")
    private Long id;

    @NotBlank(message = "配置名称不能为空")
    @Schema(description = "配置名称")
    private String configName;

    @NotBlank(message = "存储类型不能为空")
    @Schema(description = "存储服务商类型")
    private String storageType;

    @NotNull(message = "启用状态不能为空")
    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "是否为默认配置")
    private Boolean isDefault;

    @Schema(description = "存储服务商配置参数")
    private StorageConfigParams configParams;

    @Schema(description = "上传限制配置")
    private UploadLimitsConfig uploadLimits;

    @Schema(description = "存储路径配置")
    private PathConfig pathConfig;

    @Schema(description = "配置描述")
    private String description;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "配置状态")
    private Integer status;

    @Schema(description = "最后测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastTestTime;

    @Schema(description = "最后测试结果")
    private String lastTestResult;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 存储服务商配置参数
     */
    @Data
    @Schema(description = "存储服务商配置参数")
    public static class StorageConfigParams {
        
        // 通用配置
        @Schema(description = "访问域名")
        private String domain;
        
        @Schema(description = "是否使用HTTPS")
        private Boolean useHttps;
        
        @Schema(description = "连接超时时间（秒）")
        private Integer connectTimeout;
        
        @Schema(description = "读取超时时间（秒）")
        private Integer readTimeout;

        // 本地存储配置
        @Schema(description = "本地存储路径")
        private String localPath;

        // 七牛云配置
        @Schema(description = "七牛云AccessKey")
        private String qiniuAccessKey;
        
        @Schema(description = "七牛云SecretKey")
        private String qiniuSecretKey;
        
        @Schema(description = "七牛云存储空间名称")
        private String qiniuBucket;
        
        @Schema(description = "七牛云存储区域")
        private String qiniuRegion;

        // 阿里云OSS配置
        @Schema(description = "阿里云AccessKeyId")
        private String aliyunAccessKeyId;
        
        @Schema(description = "阿里云AccessKeySecret")
        private String aliyunAccessKeySecret;
        
        @Schema(description = "阿里云OSS Bucket名称")
        private String aliyunBucket;
        
        @Schema(description = "阿里云OSS Endpoint")
        private String aliyunEndpoint;

        // 腾讯云COS配置
        @Schema(description = "腾讯云SecretId")
        private String tencentSecretId;
        
        @Schema(description = "腾讯云SecretKey")
        private String tencentSecretKey;
        
        @Schema(description = "腾讯云COS Bucket名称")
        private String tencentBucket;
        
        @Schema(description = "腾讯云COS地域")
        private String tencentRegion;

        // AWS S3配置
        @Schema(description = "AWS AccessKey")
        private String awsAccessKey;
        
        @Schema(description = "AWS SecretKey")
        private String awsSecretKey;
        
        @Schema(description = "AWS S3 Bucket名称")
        private String awsBucket;
        
        @Schema(description = "AWS S3地域")
        private String awsRegion;

        // MinIO配置
        @Schema(description = "MinIO服务器地址")
        private String minioEndpoint;
        
        @Schema(description = "MinIO AccessKey")
        private String minioAccessKey;
        
        @Schema(description = "MinIO SecretKey")
        private String minioSecretKey;
        
        @Schema(description = "MinIO Bucket名称")
        private String minioBucket;
    }

    /**
     * 上传限制配置
     */
    @Data
    @Schema(description = "上传限制配置")
    public static class UploadLimitsConfig {
        
        @Schema(description = "最大文件大小（MB）")
        private Integer maxFileSize;
        
        @Schema(description = "单次最大上传数量")
        private Integer maxFileCount;
        
        @Schema(description = "允许的文件类型")
        private List<String> allowedFileTypes;
        
        @Schema(description = "禁止的文件类型")
        private List<String> forbiddenFileTypes;
        
        @Schema(description = "图片文件最大尺寸（像素）")
        private Map<String, Integer> imageMaxDimensions;
        
        @Schema(description = "是否启用文件类型检查")
        private Boolean enableFileTypeCheck;
        
        @Schema(description = "是否启用文件内容检查")
        private Boolean enableContentCheck;
        
        @Schema(description = "是否启用病毒扫描")
        private Boolean enableVirusScan;
    }

    /**
     * 存储路径配置
     */
    @Data
    @Schema(description = "存储路径配置")
    public static class PathConfig {
        
        @Schema(description = "根目录")
        private String rootPath;
        
        @Schema(description = "文件命名规则")
        private String fileNamingRule;
        
        @Schema(description = "目录结构规则")
        private String directoryStructure;
        
        @Schema(description = "是否按日期分目录")
        private Boolean enableDateDirectory;
        
        @Schema(description = "是否按用户分目录")
        private Boolean enableUserDirectory;
        
        @Schema(description = "是否按文件类型分目录")
        private Boolean enableTypeDirectory;
        
        @Schema(description = "自定义路径前缀")
        private String customPrefix;
        
        @Schema(description = "缩略图目录名称")
        private String thumbnailDirectory;
        
        @Schema(description = "临时文件目录名称")
        private String tempDirectory;
    }

    /**
     * 配置测试结果
     */
    @Data
    @Schema(description = "配置测试结果")
    public static class TestResult {
        
        @Schema(description = "测试是否成功")
        private Boolean success;
        
        @Schema(description = "测试消息")
        private String message;
        
        @Schema(description = "响应时间（毫秒）")
        private Long responseTime;
        
        @Schema(description = "测试时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime testTime;
        
        @Schema(description = "错误详情")
        private String errorDetails;
    }
}
