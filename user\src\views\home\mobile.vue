<template>
  <div class="home-mobile">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="PhotoTagMoment" fixed>
      <template #left>
        <van-icon name="search" @click="goToSearch" />
      </template>
      <template #right>
        <van-icon name="plus" @click="goToPublish" />
      </template>
    </van-nav-bar>

    <!-- 标签页切换 -->
    <div class="tab-container">
      <van-tabs
        v-model="activeTab"
        @change="onTabChange"
        sticky
        offset-top="46"
        swipeable
      >
        <van-tab title="推荐" name="recommend" />
        <van-tab title="关注" name="following" />
        <van-tab title="最新" name="latest" />
        <van-tab title="热门" name="hot" />
      </van-tabs>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 照片流 -->
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <PhotoNoteCard
            v-for="note in photoNotes"
            :key="note.id"
            :note="note"
            @like="handleLike"
            @collect="handleCollect"
            @comment="handleComment"
          />
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && photoNotes.length === 0"
      :image="emptyImage"
      :description="emptyDescription"
    >
      <van-button
        v-if="activeTab === 'following'"
        round
        type="primary"
        @click="goToDiscover"
      >
        去发现更多用户
      </van-button>
    </van-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import {
  getHomePhotoNotes,
  getRecommendedPhotoNotes,
  getFollowingPhotoNotes,
  getLatestPhotoNotes,
  getHotPhotoNotes,
  incrementPhotoNoteView,
  type HomePhotoNote,
  type HomeQueryParams
} from '@/api/home'
import PhotoNoteCard from '@/components/photo/PhotoNoteCard.vue'

// Router
const router = useRouter()

// 响应式数据
const activeTab = ref<'recommend' | 'following' | 'latest' | 'hot'>('recommend')
const photoNotes = ref<HomePhotoNote[]>([])
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const page = ref(1)
const lastId = ref<number>()

// 计算属性
const emptyImage = computed(() => {
  switch (activeTab.value) {
    case 'following':
      return 'network'
    case 'latest':
      return 'search'
    case 'hot':
      return 'default'
    default:
      return 'default'
  }
})

const emptyDescription = computed(() => {
  switch (activeTab.value) {
    case 'following':
      return '还没有关注任何用户'
    case 'latest':
      return '暂无最新内容'
    case 'hot':
      return '暂无热门内容'
    default:
      return '暂无推荐内容'
  }
})

// 获取数据的API函数映射
const apiMap = {
  recommend: getRecommendedPhotoNotes,
  following: getFollowingPhotoNotes,
  latest: getLatestPhotoNotes,
  hot: getHotPhotoNotes
}

// 加载数据
const loadData = async (isRefresh = false) => {
  if (loading.value) return

  loading.value = true

  try {
    const params: HomeQueryParams = {
      page: isRefresh ? 1 : page.value,
      size: 10,
      lastId: isRefresh ? undefined : lastId.value
    }

    const apiFunction = apiMap[activeTab.value]
    const response = await apiFunction(params)

    if (isRefresh) {
      photoNotes.value = response.data.records
      page.value = 1
    } else {
      photoNotes.value.push(...response.data.records)
    }

    // 更新分页信息
    finished.value = !response.data.hasMore
    page.value++

    // 更新lastId用于游标分页
    if (response.data.records.length > 0) {
      lastId.value = response.data.records[response.data.records.length - 1].id
    }

  } catch (error) {
    console.error('加载数据失败:', error)
    showToast('加载失败，请重试')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 事件处理
const onLoad = () => loadData()

const onRefresh = () => {
  finished.value = false
  loadData(true)
}

const onTabChange = () => {
  page.value = 1
  lastId.value = undefined
  finished.value = false
  photoNotes.value = []
  loadData(true)
}

// 处理点赞
const handleLike = (noteId: number, isLiked: boolean, likeCount: number) => {
  const note = photoNotes.value.find(n => n.id === noteId)
  if (note) {
    note.isLiked = isLiked
    note.likeCount = likeCount
  }
}

// 处理收藏
const handleCollect = (noteId: number, isCollected: boolean, collectCount: number) => {
  const note = photoNotes.value.find(n => n.id === noteId)
  if (note) {
    note.isCollected = isCollected
    // 注意：后端可能没有collectCount字段，这里暂时不更新
  }
}

// 处理评论
const handleComment = (noteId: number) => {
  router.push(`/photo-note/${noteId}?focus=comment`)
}

// 导航方法
const goToSearch = () => {
  router.push('/search')
}

const goToPublish = () => {
  router.push('/publish')
}

const goToDiscover = () => {
  router.push('/discover')
}

// 浏览量统计
const handleNoteView = (noteId: number) => {
  // 防抖处理，避免频繁调用
  incrementPhotoNoteView(noteId).catch(error => {
    console.error('更新浏览量失败:', error)
  })
}

// 监听滚动，统计浏览量
let viewedNotes = new Set<number>()
const handleScroll = () => {
  const cards = document.querySelectorAll('.photo-note-card')
  cards.forEach((card) => {
    const rect = card.getBoundingClientRect()
    const noteId = parseInt(card.getAttribute('data-note-id') || '0')

    // 如果卡片在视窗内且未统计过
    if (rect.top < window.innerHeight && rect.bottom > 0 && !viewedNotes.has(noteId)) {
      viewedNotes.add(noteId)
      handleNoteView(noteId)
    }
  })
}

// 生命周期
onMounted(() => {
  loadData(true)
  window.addEventListener('scroll', handleScroll, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.home-mobile {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 46px; // 导航栏高度
}

.tab-container {
  background: white;

  :deep(.van-tabs__wrap) {
    border-bottom: 1px solid #ebedf0;
  }

  :deep(.van-tab) {
    font-size: 14px;
    font-weight: 500;
  }

  :deep(.van-tab--active) {
    color: #1989fa;
  }
}

.content-area {
  padding: 8px 0;

  :deep(.van-list) {
    padding: 0 8px;
  }

  :deep(.van-pull-refresh) {
    min-height: calc(100vh - 92px); // 减去导航栏和标签栏高度
  }
}

// 空状态样式
:deep(.van-empty) {
  padding: 60px 0;

  .van-button {
    margin-top: 16px;
  }
}

// 加载状态样式
:deep(.van-loading) {
  padding: 20px 0;
}

// 响应式设计
@media (max-width: 768px) {
  .content-area {
    :deep(.van-list) {
      padding: 0 4px;
    }
  }
}
</style>
