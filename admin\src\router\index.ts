import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '../stores/user'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录',
      hidden: true
    }
  },
  {
    path: '/',
    component: () => import('../views/Layout.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/Index.vue'),
        meta: {
          title: '控制台',
          icon: 'Odometer'
        }
      }
    ]
  },
  {
    path: '/user',
    component: () => import('../views/Layout.vue'),
    redirect: '/user/list',
    meta: {
      title: '用户管理',
      icon: 'User'
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('../views/user/List.vue'),
        meta: {
          title: '用户列表',
          icon: 'List'
        }
      },
      {
        path: 'verification',
        name: 'UserVerification',
        component: () => import('../views/user/Verification.vue'),
        meta: {
          title: '实名认证',
          icon: 'Check'
        }
      }
    ]
  },
  // 内容管理
  {
    path: '/content',
    component: () => import('../views/Layout.vue'),
    redirect: '/content/photo-note-management',
    meta: {
      title: '内容管理',
      icon: 'DocumentChecked'
    },
    children: [
      {
        path: 'photo-note-management',
        name: 'PhotoNoteManagement',
        component: () => import('../views/content/PhotoNoteManagement.vue'),
        meta: {
          title: '照片笔记管理',
          icon: 'Picture'
        }
      },
      {
        path: 'sensitive-word',
        name: 'ContentSensitiveWord',
        component: () => import('../views/system/sensitiveWord/index.vue'),
        meta: {
          title: '敏感词管理',
          icon: 'ChatLineSquare'
        }
      },
      {
        path: 'content-moderation',
        name: 'ContentModerationConfig',
        component: () => import('../views/system/ContentModerationConfig.vue'),
        meta: {
          title: '内容审核配置',
          icon: 'Filter'
        }
      },
      {
        path: 'report-management',
        name: 'ReportManagement',
        component: () => import('../views/content/ReportManagement.vue'),
        meta: {
          title: '举报管理',
          icon: 'Warning'
        }
      },
      {
        path: 'comment',
        name: 'CommentList',
        component: () => import('../views/content/Comment.vue'),
        meta: {
          title: '评论管理',
          icon: 'ChatDotRound'
        }
      },
      {
        path: 'tags',
        name: 'TagManagement',
        component: () => import('../views/content/TagManagement.vue'),
        meta: {
          title: '标签管理',
          icon: 'CollectionTag'
        }
      },
      // 向后兼容的路由别名
      {
        path: 'photo',
        redirect: '/content/photo-note-management'
      },
      {
        path: 'photo-audit',
        redirect: '/content/photo-note-management'
      },
      {
        path: 'content-review',
        redirect: '/content/photo-note-management'
      },
      {
        path: 'photo-notes',
        redirect: '/content/photo-note-management'
      }
    ]
  },
  // 文件管理
  {
    path: '/file',
    component: () => import('../views/Layout.vue'),
    redirect: '/file/management',
    meta: {
      title: '文件管理',
      icon: 'FolderOpened'
    },
    children: [
      {
        path: 'management',
        name: 'FileManagement',
        component: () => import('../views/system/file/index.vue'),
        meta: {
          title: '文件管理',
          icon: 'Folder'
        }
      },
      {
        path: 'upload-config',
        name: 'FileUploadConfig',
        component: () => import('../views/system/file-upload-config/index.vue'),
        meta: {
          title: '上传配置',
          icon: 'Upload'
        }
      },
      {
        path: 'file-statistics',
        name: 'FileStatistics',
        component: () => import('../views/file/FileStatistics.vue'),
        meta: {
          title: '文件统计',
          icon: 'DataAnalysis'
        }
      },
      // 向后兼容的路由别名
      {
        path: 'storage-config',
        redirect: '/file/upload-config'
      }
    ]
  },
  // 系统管理
  {
    path: '/system',
    component: () => import('../views/Layout.vue'),
    redirect: '/system/admin',
    meta: {
      title: '系统管理',
      icon: 'Setting'
    },
    children: [
      // 用户权限管理
      {
        path: 'admin',
        name: 'SystemAdmin',
        component: () => import('../views/system/admin/index.vue'),
        meta: {
          title: '管理员管理',
          icon: 'User'
        }
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('../views/system/role/index.vue'),
        meta: {
          title: '角色管理',
          icon: 'Avatar'
        }
      },
      {
        path: 'permission',
        name: 'SystemPermission',
        component: () => import('../views/system/permission/index.vue'),
        meta: {
          title: '权限管理',
          icon: 'Lock'
        }
      },
      // 系统配置
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('../views/system/ConfigManager.vue'),
        meta: {
          title: '系统配置',
          icon: 'Tools'
        }
      },
      {
        path: 'auth-config',
        name: 'AuthConfig',
        component: () => import('../views/system/AuthConfig.vue'),
        meta: {
          title: '第三方登录配置',
          icon: 'Connection'
        }
      },
      {
        path: 'identity-verification-config',
        name: 'IdentityVerificationConfig',
        component: () => import('../views/system/IdentityVerificationConfig.vue'),
        meta: {
          title: '实名认证配置',
          icon: 'CreditCard'
        }
      },
      {
        path: 'sms-config',
        name: 'SmsConfig',
        component: () => import('../views/system/SmsConfig.vue'),
        meta: {
          title: '短信配置',
          icon: 'Message'
        }
      },
      // 监控日志
      {
        path: 'log',
        name: 'SystemLog',
        component: () => import('../views/system/log/index.vue'),
        meta: {
          title: '操作日志',
          icon: 'Document'
        }
      },
      {
        path: 'monitor',
        name: 'SystemMonitor',
        component: () => import('../views/system/monitor/index.vue'),
        meta: {
          title: '系统监控',
          icon: 'Monitor'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title || '管理后台'} - PhotoTagMoment`

  // 检查是否需要登录权限
  if (to.path !== '/login') {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      next({ path: '/login', query: { redirect: to.fullPath } })
      return
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    const userStore = useUserStore()
    if (token && !userStore.userInfo) {
      try {
        await userStore.fetchUserInfo()
        console.log('路由守卫中加载用户信息成功')
      } catch (error) {
        console.error('路由守卫中加载用户信息失败', error)
        // 如果获取用户信息失败，可能是token已过期
        localStorage.removeItem('admin_token')
        next({ path: '/login', query: { redirect: to.fullPath } })
        return
      }
    }
  }

  next()
})

export default router
