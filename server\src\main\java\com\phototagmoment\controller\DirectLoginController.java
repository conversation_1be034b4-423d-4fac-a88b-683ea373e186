package com.phototagmoment.controller;

import com.phototagmoment.dto.LoginDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.security.JwtTokenProvider;
import com.phototagmoment.service.AuthenticationService;
import com.phototagmoment.vo.TokenVO;
import com.phototagmoment.vo.UserVO;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.common.ResultCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 直接登录控制器 - 绕过Spring Security认证流程
 */
@Slf4j
@RestController
@RequestMapping("/auth/direct")
@Tag(name = "直接登录接口", description = "绕过Spring Security认证流程的登录接口")
public class DirectLoginController {

    private final UserMapper userMapper;
    private final UserAuthMapper userAuthMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider jwtTokenProvider;
    private final AuthenticationService authenticationService;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Autowired
    public DirectLoginController(UserMapper userMapper,
                                UserAuthMapper userAuthMapper,
                                PasswordEncoder passwordEncoder,
                                JwtTokenProvider jwtTokenProvider,
                                AuthenticationService authenticationService) {
        this.userMapper = userMapper;
        this.userAuthMapper = userAuthMapper;
        this.passwordEncoder = passwordEncoder;
        this.jwtTokenProvider = jwtTokenProvider;
        this.authenticationService = authenticationService;
    }

    /**
     * 用户直接登录 - 绕过Spring Security认证流程
     */
    @PostMapping("/login")
    @Operation(summary = "用户直接登录", description = "绕过Spring Security认证流程的用户登录")
    public ApiResponse<TokenVO> login(@Validated @RequestBody LoginDTO loginDTO) {
        try {
            log.info("用户直接登录请求: {}", loginDTO.getUsername());

            // 查找用户 - 直接使用Mapper查询
            User user = null;
            if (loginDTO.getUsername() != null && !loginDTO.getUsername().isEmpty()) {
                // 尝试用户名登录
                user = userMapper.selectByUsername(loginDTO.getUsername());
                if (user == null) {
                    // 尝试邮箱登录
                    user = userMapper.selectByEmail(loginDTO.getUsername());
                    if (user == null) {
                        // 尝试手机号登录
                        user = userMapper.selectByPhone(loginDTO.getUsername());
                    }
                }
            }

            if (user == null) {
                log.warn("登录失败: 用户不存在 - {}", loginDTO.getUsername());
                return ApiResponse.failed("用户名或密码错误");
            }

            // 检查用户状态
            if (user.getStatus() == 0) {
                log.warn("登录失败: 用户已禁用 - {}", loginDTO.getUsername());
                return ApiResponse.failed("账号已被禁用");
            }

            log.info("用户存在，开始验证密码: {}", user.getUsername());

            // 查询用户认证信息
            UserAuth userAuth = userAuthMapper.selectByUserIdAndType(user.getId(), "username");
            if (userAuth == null) {
                log.error("用户认证信息不存在: {}", user.getUsername());
                return ApiResponse.failed("系统错误，请联系管理员");
            }

            // 直接验证密码
            if (!passwordEncoder.matches(loginDTO.getPassword(), userAuth.getCredential())) {
                log.warn("密码验证失败: {}", user.getUsername());
                return ApiResponse.failed("用户名或密码错误");
            }

            log.info("密码验证成功: {}", user.getUsername());

            // 直接生成JWT
            String token = jwtTokenProvider.generateToken(user.getUsername(), user.getId());
            log.info("生成JWT成功: {}", user.getUsername());

            // 使用AuthenticationService设置认证信息
            try {
                // 使用AuthenticationService设置认证信息
                Authentication authentication = authenticationService.setAuthentication(user, null);

                if (authentication != null) {
                    log.info("使用AuthenticationService设置认证信息成功: {}", user.getUsername());

                    // 检查认证状态
                    boolean isAuthenticated = authenticationService.isAuthenticated();
                    log.info("认证状态: {}", isAuthenticated);

                    // 获取当前用户ID
                    Long currentUserId = authenticationService.getCurrentUserId();
                    log.info("当前用户ID: {}", currentUserId);
                } else {
                    log.error("使用AuthenticationService设置认证信息失败: {}", user.getUsername());

                    // 尝试使用传统方式设置认证信息
                    // 创建权限列表
                    java.util.List<SimpleGrantedAuthority> authorities = new java.util.ArrayList<>();
                    authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
                    if (user.getIsAdmin() != null && user.getIsAdmin() == 1) {
                        authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
                    }

                    // 创建UserDetails对象
                    UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                        user.getUsername(),
                        "", // 密码为空，因为我们使用JWT认证
                        user.getStatus() == 1, // 启用状态
                        true, // 账号未过期
                        true, // 凭证未过期
                        true, // 账号未锁定
                        authorities
                    );

                    // 创建认证令牌
                    UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(
                        userDetails, null, authorities);

                    // 设置请求详情
                    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
                    auth.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    // 设置认证信息到SecurityContext
                    SecurityContextHolder.getContext().setAuthentication(auth);
                    log.info("使用传统方式设置认证信息成功: {}", user.getUsername());
                }
            } catch (Exception e) {
                log.error("设置认证信息到SecurityContext失败: {}", e.getMessage(), e);
                // 即使设置失败，也继续生成token返回给客户端
            }

            // 更新用户最后登录时间和IP
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(getClientIp());
            userMapper.updateById(user);
            log.info("更新用户登录信息成功: {}", user.getUsername());

            // 构建返回对象
            UserVO userVO = convertToUserVO(user);
            TokenVO tokenVO = new TokenVO();
            tokenVO.setToken(token);
            tokenVO.setTokenType(tokenPrefix);
            tokenVO.setExpiresIn(jwtExpiration / 1000);
            tokenVO.setUser(userVO);

            log.info("登录成功: {}", user.getUsername());
            return ApiResponse.success(tokenVO, "登录成功");
        } catch (Exception e) {
            log.error("登录失败: {}", e.getMessage(), e);
            return ApiResponse.failed("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 将User转换为UserVO
     */
    private UserVO convertToUserVO(User user) {
        if (user == null) {
            return null;
        }

        UserVO userVO = new UserVO();
        userVO.setId(user.getId());
        userVO.setUsername(user.getUsername());
        userVO.setNickname(user.getNickname());
        userVO.setAvatar(user.getAvatar());
        userVO.setGender(user.getGender());
        userVO.setBirthday(user.getBirthday());
        userVO.setBio(user.getBio());
        userVO.setIsVerified(user.getIsVerified());
        userVO.setIsAdmin(user.getIsAdmin());
        userVO.setFollowingCount(user.getFollowingCount());
        userVO.setFollowerCount(user.getFollowerCount());
        userVO.setPhotoCount(user.getPhotoCount());
        userVO.setCreatedAt(user.getCreatedAt());

        return userVO;
    }
}
