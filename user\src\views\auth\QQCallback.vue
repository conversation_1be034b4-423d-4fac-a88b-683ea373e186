<template>
  <div class="qq-callback-container">
    <div class="callback-content">
      <van-loading v-if="loading" size="24px" vertical>处理QQ登录...</van-loading>
      <div v-else-if="error" class="error-message">
        <van-icon name="close" size="48" color="#ee0a24" />
        <p>{{ error }}</p>
        <van-button type="primary" @click="goToLogin">返回登录</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { loginByQQ, ApiResponse } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { showToast, showLoadingToast, closeToast } from 'vant'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loading = ref(true)
const error = ref('')

// 处理QQ登录回调
onMounted(async () => {
  try {
    // 获取授权码和状态参数
    const code = route.query.code as string
    const state = route.query.state as string

    // 验证参数
    if (!code) {
      error.value = '授权失败：未获取到授权码'
      loading.value = false
      return
    }

    // 验证状态参数，防止CSRF攻击
    const savedState = localStorage.getItem('qq_login_state')
    if (!savedState || savedState !== state) {
      console.error('状态验证失败', { savedState, state })
      error.value = '登录状态验证失败，请重新登录'
      loading.value = false
      return
    }

    // 验证登录时间戳，防止重放攻击
    const loginTimestamp = localStorage.getItem('qq_login_timestamp')
    if (loginTimestamp) {
      const timestamp = parseInt(loginTimestamp)
      const now = Date.now()
      // 如果登录请求超过30分钟，则认为无效
      if (now - timestamp > 30 * 60 * 1000) {
        console.error('登录请求已过期', { timestamp, now })
        error.value = '登录请求已过期，请重新登录'
        loading.value = false
        return
      }
    }

    // 清除状态和时间戳
    localStorage.removeItem('qq_login_state')
    localStorage.removeItem('qq_login_timestamp')

    // 显示加载提示
    showLoadingToast({
      message: '正在处理登录...',
      forbidClick: true,
      duration: 0
    })

    // 调用QQ登录接口
    const res = await loginByQQ(code, state)
    closeToast()

    if (res && res.code === 200 && res.data) {
      // 保存登录信息
      userStore.setToken(res.data.token)
      userStore.setUser(res.data.user)

      // 显示成功提示
      showToast({
        message: '登录成功',
        type: 'success'
      })

      // 跳转到首页
      setTimeout(() => {
        router.push('/')
      }, 1000)
    } else {
      console.error('QQ登录失败', res)
      error.value = (res && res.message) || '登录失败，请重新尝试'
      loading.value = false
    }
  } catch (err: any) {
    closeToast()
    console.error('QQ登录回调处理失败', err)
    error.value = err.message || '登录失败，请重新尝试'
    loading.value = false
  }
})

// 返回登录页
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.qq-callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f7f8fa;
}

.callback-content {
  text-align: center;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  width: 400px;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-message p {
  color: #666;
  margin: 0;
  word-break: break-word;
}
</style>
