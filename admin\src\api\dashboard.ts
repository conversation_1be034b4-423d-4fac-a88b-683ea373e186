import request from '@/utils/request'

// 控制台统计数据接口
export interface DashboardStats {
  userCount: number
  userTrend: number
  photoCount: number
  photoTrend: number
  commentCount: number
  commentTrend: number
  storageUsed: number
  storageUsedFormatted: string
  storageTrend: number
  todayUsers: number
  todayPhotos: number
  todayComments: number
  todayStorage: number
  activeUsers: number
  pendingPhotos: number
  pendingComments: number
  systemRunDays: number
}

// 用户增长趋势数据接口
export interface UserGrowthTrend {
  newUsers: Array<{ date: string; count: number }>
  activeUsers: Array<{ date: string; count: number }>
  labels: string[]
}

// 内容分布数据接口
export interface ContentDistribution {
  name: string
  value: number
  percentage: number
}

// 最新用户接口
export interface LatestUser {
  id: number
  nickname: string
  avatar: string
  createdAt: string
  status: number
}

// 最新照片接口
export interface LatestPhoto {
  id: number
  title: string
  thumbnailUrl: string
  createdAt: string
  status: number
  user: {
    id: number
    nickname: string
  }
}

// 系统信息接口
export interface SystemInfo {
  serverTime: string
  javaVersion: string
  osName: string
  osVersion: string
  totalMemory: string
  usedMemory: string
  freeMemory: string
  maxMemory: string
  memoryUsagePercent: number
}

/**
 * 获取控制台统计数据
 */
export function getDashboardStats() {
  return request<DashboardStats>({
    url: '/admin/dashboard/stats',
    method: 'get'
  })
}

/**
 * 获取用户增长趋势
 * @param period 周期类型：week/month/year
 */
export function getUserGrowthTrend(period: string = 'week') {
  return request<UserGrowthTrend>({
    url: '/admin/dashboard/user-growth',
    method: 'get',
    params: { period }
  })
}

/**
 * 获取内容分布
 */
export function getContentDistribution() {
  return request<ContentDistribution[]>({
    url: '/admin/dashboard/content-distribution',
    method: 'get'
  })
}

/**
 * 获取最新用户
 * @param limit 数量限制
 */
export function getLatestUsers(limit: number = 5) {
  return request<LatestUser[]>({
    url: '/admin/dashboard/latest-users',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取最新照片
 * @param limit 数量限制
 */
export function getLatestPhotos(limit: number = 5) {
  return request<LatestPhoto[]>({
    url: '/admin/dashboard/latest-photos',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取系统信息
 */
export function getSystemInfo() {
  return request<SystemInfo>({
    url: '/admin/dashboard/system-info',
    method: 'get'
  })
}
