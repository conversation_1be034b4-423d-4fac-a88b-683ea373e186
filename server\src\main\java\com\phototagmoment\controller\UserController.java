package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.service.UserService;
import com.phototagmoment.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Tag(name = "用户接口", description = "用户相关接口")
@SecurityRequirement(name = "Bearer Authentication")
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ApiResponse<UserVO> getCurrentUser(HttpServletRequest request) {
        log.info("获取当前用户信息");

        // 打印请求头信息
        log.info("请求头信息:");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            log.info("  {} = {}", headerName, request.getHeader(headerName));
        }

        // 检查当前认证状态
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("当前认证状态: {}", authentication != null ? authentication.getName() : "null");
        if (authentication != null) {
            log.info("当前认证类型: {}", authentication.getClass().getName());
            log.info("当前认证权限: {}", authentication.getAuthorities());
            log.info("当前认证Principal: {}", authentication.getPrincipal());
            log.info("当前认证是否已认证: {}", authentication.isAuthenticated());
        }

        // 尝试从请求中获取token
        String token = null;
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
            log.info("从请求头中获取到token: {}", token);
        } else {
            // 尝试从Cookie中获取token
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    if ("token".equals(cookie.getName()) || "Authorization".equals(cookie.getName())) {
                        String cookieValue = cookie.getValue();
                        log.info("从Cookie中获取到token: {}", cookieValue);

                        // 检查是否带有Bearer前缀
                        if (cookieValue.startsWith("Bearer ")) {
                            token = cookieValue.substring(7);
                        } else {
                            token = cookieValue;
                        }
                    }
                }
            }
        }

        // 如果从请求中获取到token，记录日志
        if (token != null) {
            log.info("从请求中获取到token: {}", token);
            // 不再手动验证token，依赖于Spring Security的过滤器链
        }

        UserVO userVO = userService.getCurrentUser();
        if (userVO == null) {
            log.warn("获取当前用户信息失败: 未找到用户信息");
            return ApiResponse.failed(401, "暂未登录或token已经过期");
        }

        log.info("获取当前用户信息成功: {}", userVO.getUsername());
        return ApiResponse.success(userVO);
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户信息")
    public ApiResponse<UserVO> getUserById(@PathVariable Long id) {
        UserVO userVO = userService.convertToUserVO(userService.getUserById(id));
        return ApiResponse.success(userVO);
    }

    /**
     * 更新密码
     */
    @PutMapping("/password")
    @Operation(summary = "更新密码", description = "更新当前用户的密码")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> updatePassword(@RequestParam String oldPassword, @RequestParam String newPassword) {
        UserVO currentUser = userService.getCurrentUser();
        boolean success = userService.updatePassword(currentUser.getId(), oldPassword, newPassword);
        return ApiResponse.success(success, "密码更新成功");
    }
}
