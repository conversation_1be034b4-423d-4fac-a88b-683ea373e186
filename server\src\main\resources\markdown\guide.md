# PhotoTagMoment API 使用指南

## 简介

PhotoTagMoment是一个照片分享社交网站，提供照片上传、分享、点赞、评论等功能。本文档提供API的使用说明。

## 认证方式

大部分API需要认证才能访问。认证方式为JWT Token认证，在请求头中添加`Authorization`字段，值为`Bearer {token}`。

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 通用返回格式

所有API返回的数据格式统一为：

```json
{
  "code": 0,       // 状态码，0表示成功，非0表示失败
  "message": "成功", // 状态信息
  "data": {},      // 返回数据，可能是对象、数组或基本类型
  "timestamp": 1621234567890 // 时间戳
}
```

## 常见错误码

- 0: 成功
- 1001: 参数错误
- 1002: 未登录或登录已过期
- 1003: 权限不足
- 2001: 用户不存在
- 2002: 密码错误
- 3001: 照片不存在
- 9001: 系统错误

## 接口分组说明

API接口分为两大类：

1. 用户接口：供普通用户使用的接口，包括用户认证、照片管理、标签管理等
2. 管理接口：供管理员使用的接口，包括系统配置、敏感词管理、用户管理等
