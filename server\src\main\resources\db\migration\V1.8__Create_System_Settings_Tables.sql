-- 系统参数配置表
CREATE TABLE IF NOT EXISTS `ptm_system_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT COMMENT '配置值',
    `config_name` VARCHAR(100) NOT NULL COMMENT '配置名称',
    `config_type` VARCHAR(50) NOT NULL COMMENT '配置类型（string, number, boolean, json）',
    `remark` VARCHAR(500) COMMENT '备注',
    `is_system` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否系统内置（0否 1是）',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统参数配置表';

-- 检查并添加缺失的列
SET @column_exists_config_name = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_system_config' AND column_name = 'config_name');
SET @column_exists_config_type = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_system_config' AND column_name = 'config_type');
SET @column_exists_remark = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_system_config' AND column_name = 'remark');
SET @column_exists_is_system = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_system_config' AND column_name = 'is_system');

SET @add_config_name_column = IF(@column_exists_config_name = 0, 'ALTER TABLE `ptm_system_config` ADD COLUMN `config_name` VARCHAR(100) NOT NULL COMMENT "配置名称" AFTER `config_value`', 'SELECT "Column config_name already exists"');
SET @add_config_type_column = IF(@column_exists_config_type = 0, 'ALTER TABLE `ptm_system_config` ADD COLUMN `config_type` VARCHAR(50) NOT NULL COMMENT "配置类型（string, number, boolean, json）" AFTER `config_name`', 'SELECT "Column config_type already exists"');
SET @add_remark_column = IF(@column_exists_remark = 0, 'ALTER TABLE `ptm_system_config` ADD COLUMN `remark` VARCHAR(500) COMMENT "备注" AFTER `config_type`', 'SELECT "Column remark already exists"');
SET @add_is_system_column = IF(@column_exists_is_system = 0, 'ALTER TABLE `ptm_system_config` ADD COLUMN `is_system` TINYINT(1) NOT NULL DEFAULT 1 COMMENT "是否系统内置（0否 1是）" AFTER `remark`', 'SELECT "Column is_system already exists"');

PREPARE stmt1 FROM @add_config_name_column;
EXECUTE stmt1;
DEALLOCATE PREPARE stmt1;

PREPARE stmt2 FROM @add_config_type_column;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

PREPARE stmt3 FROM @add_remark_column;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

PREPARE stmt4 FROM @add_is_system_column;
EXECUTE stmt4;
DEALLOCATE PREPARE stmt4;

-- 敏感词表
CREATE TABLE IF NOT EXISTS `ptm_sensitive_word` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
    `word` VARCHAR(100) NOT NULL COMMENT '敏感词',
    `type` VARCHAR(50) NOT NULL COMMENT '类型（政治、色情、暴力、广告等）',
    `level` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '级别（1一般 2中等 3严重）',
    `replace_word` VARCHAR(100) COMMENT '替换词',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_word` (`word`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- 数据字典类型表
CREATE TABLE IF NOT EXISTS `ptm_dict_type` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '字典类型ID',
    `dict_name` VARCHAR(100) NOT NULL COMMENT '字典名称',
    `dict_type` VARCHAR(100) NOT NULL COMMENT '字典类型',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
    `remark` VARCHAR(500) COMMENT '备注',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典类型表';

-- 数据字典数据表
CREATE TABLE IF NOT EXISTS `ptm_dict_data` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '字典数据ID',
    `dict_type_id` BIGINT NOT NULL COMMENT '字典类型ID',
    `dict_label` VARCHAR(100) NOT NULL COMMENT '字典标签',
    `dict_value` VARCHAR(100) NOT NULL COMMENT '字典键值',
    `dict_sort` INT NOT NULL DEFAULT 0 COMMENT '排序',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
    `remark` VARCHAR(500) COMMENT '备注',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
    PRIMARY KEY (`id`),
    KEY `idx_dict_type_id` (`dict_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典数据表';

-- 插入一些初始系统配置（如果不存在）
INSERT IGNORE INTO `ptm_system_config` (`config_key`, `config_value`, `config_name`, `config_type`, `remark`, `is_system`) VALUES
('system.name', 'PhotoTagMoment', '系统名称', 'string', '系统名称', 1),
('system.logo', '/logo.png', '系统Logo', 'string', '系统Logo路径', 1),
('system.description', '照片社交分享平台', '系统描述', 'string', '系统描述', 1),
('system.version', '1.0.0', '系统版本', 'string', '系统版本号', 1),
('system.copyright', 'Copyright © 2023 PhotoTagMoment', '版权信息', 'string', '系统版权信息', 1),
('system.record.number', '', '备案号', 'string', '网站备案号', 1),
('upload.max.size', '10', '上传文件大小限制', 'number', '上传文件大小限制（MB）', 1),
('upload.allowed.types', 'jpg,jpeg,png,gif,webp', '允许上传的文件类型', 'string', '允许上传的文件类型，逗号分隔', 1),
('user.default.avatar', '/default-avatar.png', '用户默认头像', 'string', '用户默认头像路径', 1),
('user.register.verify', 'true', '注册是否需要验证', 'boolean', '注册是否需要验证（短信验证码）', 1),
('user.login.verify', 'true', '登录是否需要验证', 'boolean', '登录是否需要验证（短信验证码）', 1),
('user.identity.verify', 'true', '是否开启实名认证', 'boolean', '是否开启实名认证', 1),
('content.audit.enabled', 'true', '是否开启内容审核', 'boolean', '是否开启内容审核', 1),
('content.audit.provider', 'local', '内容审核服务提供商', 'string', '内容审核服务提供商（local, aliyun, tencent, baidu）', 1),
('sensitive.word.filter', 'true', '是否开启敏感词过滤', 'boolean', '是否开启敏感词过滤', 1),
('notification.email.enabled', 'false', '是否开启邮件通知', 'boolean', '是否开启邮件通知', 1),
('notification.sms.enabled', 'true', '是否开启短信通知', 'boolean', '是否开启短信通知', 1),
('notification.websocket.enabled', 'true', '是否开启WebSocket通知', 'boolean', '是否开启WebSocket通知', 1);

-- 插入一些初始数据字典类型（如果不存在）
INSERT IGNORE INTO `ptm_dict_type` (`dict_name`, `dict_type`, `remark`) VALUES
('用户状态', 'user_status', '用户状态'),
('性别类型', 'gender_type', '性别类型'),
('内容审核状态', 'content_audit_status', '内容审核状态'),
('实名认证状态', 'identity_verify_status', '实名认证状态'),
('敏感词类型', 'sensitive_word_type', '敏感词类型'),
('敏感词级别', 'sensitive_word_level', '敏感词级别'),
('通知类型', 'notification_type', '通知类型');

-- 插入一些初始数据字典数据（如果不存在）
-- 获取字典类型ID
SET @user_status_id = (SELECT id FROM `ptm_dict_type` WHERE dict_type = 'user_status' LIMIT 1);
SET @gender_type_id = (SELECT id FROM `ptm_dict_type` WHERE dict_type = 'gender_type' LIMIT 1);
SET @content_audit_status_id = (SELECT id FROM `ptm_dict_type` WHERE dict_type = 'content_audit_status' LIMIT 1);
SET @identity_verify_status_id = (SELECT id FROM `ptm_dict_type` WHERE dict_type = 'identity_verify_status' LIMIT 1);
SET @sensitive_word_type_id = (SELECT id FROM `ptm_dict_type` WHERE dict_type = 'sensitive_word_type' LIMIT 1);
SET @sensitive_word_level_id = (SELECT id FROM `ptm_dict_type` WHERE dict_type = 'sensitive_word_level' LIMIT 1);
SET @notification_type_id = (SELECT id FROM `ptm_dict_type` WHERE dict_type = 'notification_type' LIMIT 1);

INSERT IGNORE INTO `ptm_dict_data` (`dict_type_id`, `dict_label`, `dict_value`, `dict_sort`, `remark`) VALUES
(@user_status_id, '正常', '1', 1, '正常状态'),
(@user_status_id, '禁用', '0', 2, '禁用状态'),
(@gender_type_id, '男', 'male', 1, '男性'),
(@gender_type_id, '女', 'female', 2, '女性'),
(@gender_type_id, '未知', 'unknown', 3, '未知性别'),
(@content_audit_status_id, '待审核', '0', 1, '待审核状态'),
(@content_audit_status_id, '通过', '1', 2, '审核通过'),
(@content_audit_status_id, '拒绝', '2', 3, '审核拒绝'),
(@identity_verify_status_id, '未认证', '0', 1, '未进行实名认证'),
(@identity_verify_status_id, '已认证', '1', 2, '已通过实名认证'),
(@identity_verify_status_id, '认证失败', '2', 3, '实名认证失败'),
(@sensitive_word_type_id, '政治', 'political', 1, '政治类敏感词'),
(@sensitive_word_type_id, '色情', 'porn', 2, '色情类敏感词'),
(@sensitive_word_type_id, '暴力', 'violence', 3, '暴力类敏感词'),
(@sensitive_word_type_id, '广告', 'ad', 4, '广告类敏感词'),
(@sensitive_word_type_id, '其他', 'other', 5, '其他类敏感词'),
(@sensitive_word_level_id, '一般', '1', 1, '一般级别'),
(@sensitive_word_level_id, '中等', '2', 2, '中等级别'),
(@sensitive_word_level_id, '严重', '3', 3, '严重级别'),
(@notification_type_id, '系统通知', 'system', 1, '系统通知'),
(@notification_type_id, '关注通知', 'follow', 2, '关注通知'),
(@notification_type_id, '点赞通知', 'like', 3, '点赞通知'),
(@notification_type_id, '评论通知', 'comment', 4, '评论通知'),
(@notification_type_id, '私信通知', 'message', 5, '私信通知');
