package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.config.WechatConfig;
import com.phototagmoment.dto.WechatLoginDTO;
import com.phototagmoment.dto.WechatUserInfoDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.security.JwtTokenProvider;
import com.phototagmoment.service.UserService;
import com.phototagmoment.service.WechatService;
import com.phototagmoment.vo.TokenVO;
import com.phototagmoment.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信控制器
 */
@Slf4j
@RestController
@RequestMapping("/wechat")
@Tag(name = "微信接口", description = "微信相关接口")
public class WechatController {

    @Autowired
    private WechatService wechatService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private WechatConfig wechatConfig;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserAuthMapper userAuthMapper;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    /**
     * 获取微信公众号授权URL
     */
    @GetMapping("/mp/auth-url")
    @Operation(summary = "获取微信公众号授权URL", description = "获取微信公众号授权URL")
    public ApiResponse<Map<String, String>> getMpAuthUrl(
            @Parameter(description = "回调URL") @RequestParam String redirectUrl,
            @Parameter(description = "状态参数") @RequestParam(required = false) String state) {
        if (!wechatConfig.isEnabled()) {
            return ApiResponse.failed("微信服务未启用");
        }

        String authUrl = wechatService.getMpAuthUrl(redirectUrl, state);
        if (authUrl == null) {
            return ApiResponse.failed("获取授权URL失败");
        }

        Map<String, String> result = new HashMap<>();
        result.put("authUrl", authUrl);
        return ApiResponse.success(result);
    }

    /**
     * 微信公众号登录
     */
    @PostMapping("/mp/login")
    @Operation(summary = "微信公众号登录", description = "微信公众号登录")
    public ApiResponse<Map<String, Object>> mpLogin(
            @Parameter(description = "授权码") @RequestParam String code) {
        if (!wechatConfig.isEnabled()) {
            return ApiResponse.failed("微信服务未启用");
        }

        // 微信登录
        WechatLoginDTO loginResult = wechatService.mpLogin(code);
        if (!loginResult.isSuccess()) {
            return ApiResponse.failed(loginResult.getErrorMsg());
        }

        // 获取用户信息
        WechatUserInfoDTO userInfo = wechatService.getMpUserInfo(loginResult.getOpenId());
        if (userInfo == null) {
            return ApiResponse.failed("获取用户信息失败");
        }

        // 查询用户是否已存在
        UserAuth userAuth = userAuthMapper.selectByTypeAndIdentifier("wechat", userInfo.getOpenId());
        User user;

        if (userAuth == null) {
            // 用户不存在，创建新用户
            user = createUserFromWechatInfo(userInfo, "mp");
            userMapper.insert(user);
            // 创建用户认证信息
            createUserAuth(user.getId(), "wechat", userInfo.getOpenId(), loginResult.getAccessToken());
        } else {
            // 用户已存在，更新用户信息
            user = userMapper.selectById(userAuth.getUserId());
            if (user == null) {
                return ApiResponse.failed("用户不存在");
            }
            // 更新用户信息
            updateUserFromWechatInfo(user, userInfo);
            userMapper.updateById(user);
            // 更新用户认证信息
            updateUserAuth(userAuth, loginResult.getAccessToken());
        }

        // 生成JWT
        String token = jwtTokenProvider.generateToken(user.getUsername(), user.getId());

        // 更新用户最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 构建返回对象
        UserVO userVO = convertToUserVO(user);
        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(token);
        tokenVO.setTokenType(tokenPrefix);
        tokenVO.setExpiresIn(jwtExpiration / 1000);
        tokenVO.setUser(userVO);

        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", userVO);

        return ApiResponse.success(result);
    }

    /**
     * 微信公众号登录/注册
     */
    @PostMapping("/mp/login-register")
    @Operation(summary = "微信公众号登录/注册", description = "微信公众号登录/注册")
    public ApiResponse<Map<String, Object>> mpLoginRegister(
            @Parameter(description = "授权码") @RequestParam String code) {
        return mpLogin(code);
    }

    /**
     * 微信小程序登录
     */
    @PostMapping("/mini-app/login")
    @Operation(summary = "微信小程序登录", description = "微信小程序登录")
    public ApiResponse<Map<String, Object>> miniAppLogin(
            @Parameter(description = "授权码") @RequestParam String code,
            @Parameter(description = "加密数据") @RequestParam(required = false) String encryptedData,
            @Parameter(description = "加密算法的初始向量") @RequestParam(required = false) String iv) {
        if (!wechatConfig.isEnabled()) {
            return ApiResponse.failed("微信服务未启用");
        }

        // 微信登录
        WechatLoginDTO loginResult = wechatService.miniAppLogin(code);
        if (!loginResult.isSuccess()) {
            return ApiResponse.failed(loginResult.getErrorMsg());
        }

        // 获取用户信息
        WechatUserInfoDTO userInfo = null;
        if (encryptedData != null && iv != null) {
            userInfo = wechatService.getMiniAppUserInfo(loginResult.getSessionKey(), encryptedData, iv);
        }

        // 查询用户是否已存在
        UserAuth userAuth = userAuthMapper.selectByTypeAndIdentifier("wechat_mini", loginResult.getOpenId());
        User user;

        if (userAuth == null) {
            // 用户不存在，创建新用户
            user = createUserFromWechatInfo(userInfo, "mini_app");
            if (userInfo == null) {
                user.setUsername("wx_" + loginResult.getOpenId().substring(0, 8));
                user.setNickname("微信用户");
            }
            userMapper.insert(user);
            // 创建用户认证信息
            createUserAuth(user.getId(), "wechat_mini", loginResult.getOpenId(), loginResult.getSessionKey());
        } else {
            // 用户已存在，更新用户信息
            user = userMapper.selectById(userAuth.getUserId());
            if (user == null) {
                return ApiResponse.failed("用户不存在");
            }
            if (userInfo != null) {
                // 更新用户信息
                updateUserFromWechatInfo(user, userInfo);
                userMapper.updateById(user);
            }
            // 更新用户认证信息
            updateUserAuth(userAuth, loginResult.getSessionKey());
        }

        // 生成JWT
        String token = jwtTokenProvider.generateToken(user.getUsername(), user.getId());

        // 更新用户最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 构建返回对象
        UserVO userVO = convertToUserVO(user);
        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(token);
        tokenVO.setTokenType(tokenPrefix);
        tokenVO.setExpiresIn(jwtExpiration / 1000);
        tokenVO.setUser(userVO);

        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", userVO);
        result.put("openId", loginResult.getOpenId());

        return ApiResponse.success(result);
    }

    /**
     * 微信小程序登录/注册
     */
    @PostMapping("/mini-app/login-register")
    @Operation(summary = "微信小程序登录/注册", description = "微信小程序登录/注册")
    public ApiResponse<Map<String, Object>> miniAppLoginRegister(
            @Parameter(description = "授权码") @RequestParam String code,
            @Parameter(description = "加密数据") @RequestParam(required = false) String encryptedData,
            @Parameter(description = "加密算法的初始向量") @RequestParam(required = false) String iv) {
        return miniAppLogin(code, encryptedData, iv);
    }

    /**
     * 从微信用户信息创建用户
     */
    private User createUserFromWechatInfo(WechatUserInfoDTO userInfo, String source) {
        User user = new User();

        if (userInfo != null) {
            user.setUsername("wx_" + userInfo.getOpenId().substring(0, 8));
            user.setNickname(userInfo.getNickname());
            user.setAvatar(userInfo.getHeadImgUrl());
            user.setGender(userInfo.getSex());
        } else {
            user.setUsername("wx_user_" + System.currentTimeMillis());
            user.setNickname("微信用户");
            user.setGender(0);
        }

        user.setStatus(1);
        user.setIsVerified(0);
        user.setIsAdmin(0);
        user.setLastLoginTime(LocalDateTime.now());
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        return user;
    }

    /**
     * 从微信用户信息更新用户
     */
    private void updateUserFromWechatInfo(User user, WechatUserInfoDTO userInfo) {
        if (userInfo == null) {
            return;
        }

        if (user.getNickname() == null || user.getNickname().isEmpty() || user.getNickname().startsWith("微信用户")) {
            user.setNickname(userInfo.getNickname());
        }

        if (user.getAvatar() == null || user.getAvatar().isEmpty()) {
            user.setAvatar(userInfo.getHeadImgUrl());
        }

        if (user.getGender() == null || user.getGender() == 0) {
            user.setGender(userInfo.getSex());
        }

        user.setLastLoginTime(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 创建用户认证信息
     */
    private void createUserAuth(Long userId, String identityType, String identifier, String credential) {
        UserAuth userAuth = new UserAuth();
        userAuth.setUserId(userId);
        userAuth.setIdentityType(identityType);
        userAuth.setIdentifier(identifier);
        userAuth.setCredential(credential);
        userAuth.setVerified(1);
        userAuth.setCreatedAt(LocalDateTime.now());
        userAuth.setUpdatedAt(LocalDateTime.now());
        userAuthMapper.insert(userAuth);
    }

    /**
     * 更新用户认证信息
     */
    private void updateUserAuth(UserAuth userAuth, String credential) {
        userAuth.setCredential(credential);
        userAuth.setUpdatedAt(LocalDateTime.now());
        userAuthMapper.updateById(userAuth);
    }

    /**
     * 转换为用户VO
     */
    private UserVO convertToUserVO(User user) {
        UserVO userVO = new UserVO();
        userVO.setId(user.getId());
        userVO.setUsername(user.getUsername());
        userVO.setNickname(user.getNickname());
        userVO.setAvatar(user.getAvatar());
        userVO.setEmail(user.getEmail());
        userVO.setPhone(user.getPhone());
        userVO.setGender(user.getGender());
        userVO.setBirthday(user.getBirthday());
        userVO.setBio(user.getBio());
        userVO.setIsVerified(user.getIsVerified());
        return userVO;
    }
}
