package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.PhotoUploadDTO;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.entity.PhotoCollect;
import com.phototagmoment.entity.PhotoLike;
import com.phototagmoment.entity.PhotoTag;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.PhotoCollectMapper;
import com.phototagmoment.service.*;
import com.phototagmoment.vo.PhotoVO;
import com.phototagmoment.mapper.PhotoLikeMapper;
import com.phototagmoment.mapper.PhotoMapper;
import com.phototagmoment.mapper.PhotoTagMapper;
import com.phototagmoment.util.ImageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import com.phototagmoment.dto.PhotoUploadWithMentionsDTO;
import com.phototagmoment.security.SecurityUtil;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 照片服务实现类
 */
@Slf4j
@Service
public class PhotoServiceImpl extends ServiceImpl<PhotoMapper, Photo> implements PhotoService {

    @Autowired
    private PhotoTagMapper photoTagMapper;

    @Autowired
    private PhotoLikeMapper photoLikeMapper;

    @Autowired
    private PhotoCollectMapper photoCollectMapper;

    @Autowired
    private StorageService storageService;

    @Autowired
    private ContentModerationService contentModerationService;

    @Autowired
    private PhotoAuditService photoAuditService;

    @Autowired
    private QiniuStorageService qiniuStorageService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long uploadPhoto(MultipartFile file, PhotoUploadDTO photoUploadDTO, Long userId) {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new BusinessException("只能上传图片文件");
        }

        // 内容审核
        boolean isContentModerationEnabled = systemConfigService.getBooleanValue("content-moderation.enabled", false);
        if (isContentModerationEnabled) {
            boolean passed = contentModerationService.moderateImage(file);
            if (!passed) {
                String reason = contentModerationService.getFailReason();
                throw new BusinessException("图片审核不通过：" + reason);
            }
        }

        try {
            // 生成存储路径
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = UUID.randomUUID().toString().replace("-", "") + extension;
            String storagePath = "photos/" + userId + "/" + filename;

            // 获取图片尺寸
            int[] dimensions = ImageUtil.getImageDimensions(file.getInputStream());
            int width = dimensions[0];
            int height = dimensions[1];

            // 上传原图
            String url = storageService.uploadFile(file.getInputStream(), storagePath, contentType);

            // 生成缩略图
            byte[] thumbnailBytes = ImageUtil.generateThumbnail(file.getInputStream(), 300, 300);
            String thumbnailPath = "photos/" + userId + "/thumbnails/" + filename;
            String thumbnailUrl = storageService.uploadFile(thumbnailBytes, thumbnailPath, contentType);

            // 创建照片实体
            Photo photo = new Photo();
            photo.setUserId(userId);
            photo.setTitle(photoUploadDTO.getTitle());
            photo.setDescription(photoUploadDTO.getDescription());
            photo.setUrl(url);
            photo.setThumbnailUrl(thumbnailUrl);
            photo.setStoragePath(storagePath);
            photo.setOriginalFilename(originalFilename);
            photo.setFileSize(file.getSize());
            photo.setFileType(contentType);
            photo.setWidth(width);
            photo.setHeight(height);
            photo.setLocation(photoUploadDTO.getLocation());
            photo.setVisibility(photoUploadDTO.getVisibility());
            photo.setAllowComment(photoUploadDTO.getAllowComment());
            photo.setAllowDownload(photoUploadDTO.getAllowDownload());
            photo.setLikeCount(0);
            photo.setCommentCount(0);
            photo.setCollectCount(0);
            photo.setViewCount(0);

            // 设置照片状态
            // 如果启用了内容审核，则设置为待审核状态，否则设置为正常状态
            boolean contentModerationEnabled = systemConfigService.getBooleanValue("content-moderation.enabled", false);
            photo.setStatus(contentModerationEnabled ? 0 : 1);

            // 保存照片
            this.save(photo);

            // 保存标签
            if (photoUploadDTO.getTags() != null && !photoUploadDTO.getTags().isEmpty()) {
                List<PhotoTag> photoTags = photoUploadDTO.getTags().stream()
                        .map(tag -> {
                            PhotoTag photoTag = new PhotoTag();
                            photoTag.setPhotoId(photo.getId());
                            photoTag.setName(tag);
                            return photoTag;
                        })
                        .collect(Collectors.toList());
                photoTagMapper.batchInsert(photoTags);
            }

            return photo.getId();
        } catch (IOException e) {
            log.error("上传照片失败", e);
            throw new BusinessException("上传照片失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchUploadPhotos(List<MultipartFile> files, List<PhotoUploadDTO> photoUploadDTOs, Long userId) {
        if (files.size() != photoUploadDTOs.size()) {
            throw new BusinessException("文件数量与信息数量不匹配");
        }

        List<Long> photoIds = new ArrayList<>();
        for (int i = 0; i < files.size(); i++) {
            Long photoId = uploadPhoto(files.get(i), photoUploadDTOs.get(i), userId);
            photoIds.add(photoId);
        }
        return photoIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePhotoInfo(PhotoUploadDTO photoUploadDTO, Long userId, String url, String thumbnailUrl,
                             Integer width, Integer height, String key) {
        try {
            // 创建照片实体
            Photo photo = new Photo();
            photo.setUserId(userId);
            photo.setTitle(photoUploadDTO.getTitle());
            photo.setDescription(photoUploadDTO.getDescription());
            photo.setUrl(url);
            photo.setThumbnailUrl(thumbnailUrl);
            photo.setStoragePath(key);
            // 不设置不存在的字段
            // photo.setOriginalFilename(photoUploadDTO.getOriginalFilename() != null ?
            //     photoUploadDTO.getOriginalFilename() : key.substring(key.lastIndexOf("/") + 1));
            // photo.setFileSize(0L); // 直接上传到七牛云，无法获取文件大小
            photo.setFileType("image/jpeg"); // 默认为JPEG
            photo.setWidth(width);
            photo.setHeight(height);
            photo.setLocation(photoUploadDTO.getLocation());
            photo.setVisibility(photoUploadDTO.getVisibility());
            photo.setAllowComment(photoUploadDTO.getAllowComment());
            photo.setAllowDownload(photoUploadDTO.getAllowDownload());
            photo.setLikeCount(0);
            photo.setCommentCount(0);
            // photo.setCollectCount(0); // 不设置不存在的字段
            photo.setViewCount(0);

            // 设置照片状态
            // 如果启用了内容审核，则设置为待审核状态，否则设置为正常状态
            boolean contentModerationEnabled = systemConfigService.getBooleanValue("content-moderation.enabled", false);
            photo.setStatus(contentModerationEnabled ? 0 : 1);

            // 保存照片
            this.save(photo);

            // 如果启用了内容审核，提交照片进行自动审核
            if (contentModerationEnabled) {
                photoAuditService.submitForAutoAudit(photo.getId());
                log.info("照片已提交自动审核，照片ID: {}", photo.getId());
            }

            // 保存标签
            if (photoUploadDTO.getTags() != null && !photoUploadDTO.getTags().isEmpty()) {
                List<PhotoTag> photoTags = photoUploadDTO.getTags().stream()
                        .map(tag -> {
                            PhotoTag photoTag = new PhotoTag();
                            photoTag.setPhotoId(photo.getId());
                            photoTag.setName(tag);
                            return photoTag;
                        })
                        .collect(Collectors.toList());
                photoTagMapper.batchInsert(photoTags);
            }

            return photo.getId();
        } catch (Exception e) {
            log.error("保存照片信息失败", e);
            throw new BusinessException("保存照片信息失败");
        }
    }

    @Override
    public PhotoDTO getPhotoDetail(Long photoId, Long userId) {
        // 获取照片详情
        PhotoDTO photoDTO = baseMapper.selectPhotoDetail(photoId, userId);
        if (photoDTO == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 检查权限
        if (photoDTO.getVisibility() == 0 && !photoDTO.getUserId().equals(userId)) {
            throw new BusinessException("无权查看该照片");
        }

        // 增加浏览数
        incrementViewCount(photoId);

        // 如果是七牛云私有空间，生成带下载凭证的URL
        boolean qiniuIsPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", false);
        if (qiniuIsPrivate) {
            processPrivateUrls(photoDTO);
        }

        return photoDTO;
    }

    /**
     * 处理私有空间URL，生成带下载凭证的URL
     * @param photoDTO 照片DTO
     */
    private void processPrivateUrls(PhotoDTO photoDTO) {
        try {
            // 处理照片URL
            if (photoDTO.getUrl() != null && !photoDTO.getUrl().isEmpty()) {
                String fileName = extractFileNameFromUrl(photoDTO.getUrl());
                String privateUrl = qiniuStorageService.getFileUrl(fileName);
                if (privateUrl != null && !privateUrl.isEmpty()) {
                    photoDTO.setUrl(privateUrl);
                }
            }

            // 处理缩略图URL
            if (photoDTO.getThumbnailUrl() != null && !photoDTO.getThumbnailUrl().isEmpty()) {
                String fileName = extractFileNameFromUrl(photoDTO.getThumbnailUrl());
                String privateUrl = qiniuStorageService.getFileUrl(fileName);
                if (privateUrl != null && !privateUrl.isEmpty()) {
                    photoDTO.setThumbnailUrl(privateUrl);
                }
            }

            // 处理用户头像URL
            if (photoDTO.getAvatar() != null && !photoDTO.getAvatar().isEmpty()) {
                String fileName = extractFileNameFromUrl(photoDTO.getAvatar());
                String privateUrl = qiniuStorageService.getFileUrl(fileName);
                if (privateUrl != null && !privateUrl.isEmpty()) {
                    photoDTO.setAvatar(privateUrl);
                }
            }
        } catch (Exception e) {
            log.error("处理私有空间URL失败", e);
            // 不影响业务，继续执行
        }
    }

    /**
     * 从URL中提取文件名
     * @param url URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        // 移除域名部分
        int domainEndIndex = url.indexOf("/", 8); // 跳过 "https://"
        if (domainEndIndex != -1) {
            return url.substring(domainEndIndex + 1);
        }

        return url;
    }

    @Override
    public IPage<PhotoDTO> getPhotoPage(int page, int size, Long userId, Long currentUserId) {
        Page<PhotoDTO> pageParam = new Page<>(page, size);
        IPage<PhotoDTO> photoPage = baseMapper.selectPhotoPage(pageParam, userId, currentUserId);

        // 如果是七牛云私有空间，生成带下载凭证的URL
        boolean qiniuIsPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", false);
        if (qiniuIsPrivate && photoPage.getRecords() != null && !photoPage.getRecords().isEmpty()) {
            for (PhotoDTO photoDTO : photoPage.getRecords()) {
                processPrivateUrls(photoDTO);
            }
        }

        return photoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePhoto(Long photoId, PhotoUploadDTO photoUploadDTO, Long userId) {
        // 检查照片是否存在
        Photo photo = this.getById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 检查权限
        if (!photo.getUserId().equals(userId)) {
            throw new BusinessException("无权修改该照片");
        }

        // 更新照片信息
        // 内容审核
        boolean contentModerationEnabled = systemConfigService.getBooleanValue("content-moderation.enabled", false);
        if (contentModerationEnabled && photoUploadDTO.getDescription() != null) {
            boolean passed = contentModerationService.moderateText(photoUploadDTO.getDescription());
            if (!passed) {
                String reason = contentModerationService.getFailReason();
                throw new BusinessException("描述文本审核不通过：" + reason);
            }
        }

        photo.setTitle(photoUploadDTO.getTitle());
        photo.setDescription(photoUploadDTO.getDescription());
        photo.setLocation(photoUploadDTO.getLocation());
        photo.setVisibility(photoUploadDTO.getVisibility());
        photo.setAllowComment(photoUploadDTO.getAllowComment());
        photo.setAllowDownload(photoUploadDTO.getAllowDownload());
        this.updateById(photo);

        // 更新标签
        if (photoUploadDTO.getTags() != null) {
            // 删除原有标签
            photoTagMapper.deleteByPhotoId(photoId);

            // 添加新标签
            if (!photoUploadDTO.getTags().isEmpty()) {
                List<PhotoTag> photoTags = photoUploadDTO.getTags().stream()
                        .map(tag -> {
                            PhotoTag photoTag = new PhotoTag();
                            photoTag.setPhotoId(photoId);
                            photoTag.setName(tag);
                            return photoTag;
                        })
                        .collect(Collectors.toList());
                photoTagMapper.batchInsert(photoTags);
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePhoto(Long photoId, Long userId) {
        // 检查照片是否存在
        Photo photo = this.getById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 检查权限
        if (!photo.getUserId().equals(userId)) {
            throw new BusinessException("无权删除该照片");
        }

        // 删除照片
        this.removeById(photoId);

        // 删除标签
        photoTagMapper.deleteByPhotoId(photoId);

        // 删除存储的文件
        try {
            storageService.deleteFile(photo.getStoragePath());
            // 删除缩略图
            String thumbnailPath = photo.getStoragePath().replace("photos/", "photos/thumbnails/");
            storageService.deleteFile(thumbnailPath);
        } catch (Exception e) {
            log.error("删除照片文件失败", e);
            // 不影响业务，继续执行
        }

        return true;
    }

    @Override
    public boolean incrementViewCount(Long photoId) {
        return baseMapper.incrementViewCount(photoId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean likePhoto(Long photoId, Long userId) {
        // 检查照片是否存在
        Photo photo = this.getById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 检查是否已点赞
        LambdaQueryWrapper<PhotoLike> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoLike::getPhotoId, photoId)
                .eq(PhotoLike::getUserId, userId);
        if (photoLikeMapper.selectCount(queryWrapper) > 0) {
            return true;
        }

        // 创建点赞记录
        PhotoLike photoLike = new PhotoLike();
        photoLike.setPhotoId(photoId);
        photoLike.setUserId(userId);
        photoLikeMapper.insert(photoLike);

        // 增加照片点赞数
        baseMapper.incrementLikeCount(photoId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlikePhoto(Long photoId, Long userId) {
        // 检查照片是否存在
        Photo photo = this.getById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 检查是否已点赞
        LambdaQueryWrapper<PhotoLike> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoLike::getPhotoId, photoId)
                .eq(PhotoLike::getUserId, userId);
        if (photoLikeMapper.selectCount(queryWrapper) == 0) {
            return true;
        }

        // 删除点赞记录
        photoLikeMapper.delete(queryWrapper);

        // 减少照片点赞数
        baseMapper.decrementLikeCount(photoId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean collectPhoto(Long photoId, Long userId) {
        // 检查照片是否存在
        Photo photo = this.getById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 检查是否已收藏
        LambdaQueryWrapper<PhotoCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoCollect::getPhotoId, photoId)
                .eq(PhotoCollect::getUserId, userId);
        if (photoCollectMapper.selectCount(queryWrapper) > 0) {
            return true;
        }

        // 创建收藏记录
        PhotoCollect photoCollect = new PhotoCollect();
        photoCollect.setPhotoId(photoId);
        photoCollect.setUserId(userId);
        photoCollect.setCollectTime(LocalDateTime.now());
        photoCollect.setCreatedAt(LocalDateTime.now());
        photoCollect.setUpdatedAt(LocalDateTime.now());
        photoCollectMapper.insert(photoCollect);

        // 增加照片收藏数 - 暂时注释掉，因为数据库中没有 collect_count 字段
        // baseMapper.incrementCollectCount(photoId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean uncollectPhoto(Long photoId, Long userId) {
        // 检查照片是否存在
        Photo photo = this.getById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 检查是否已收藏
        LambdaQueryWrapper<PhotoCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoCollect::getPhotoId, photoId)
                .eq(PhotoCollect::getUserId, userId);
        if (photoCollectMapper.selectCount(queryWrapper) == 0) {
            return true;
        }

        // 物理删除收藏记录
        photoCollectMapper.delete(queryWrapper);

        // 减少照片收藏数 - 暂时注释掉，因为数据库中没有 collect_count 字段
        // baseMapper.decrementCollectCount(photoId);

        return true;
    }

    @Override
    public boolean checkLiked(Long photoId, Long userId) {
        if (userId == null) {
            return false;
        }

        LambdaQueryWrapper<PhotoLike> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoLike::getPhotoId, photoId)
                .eq(PhotoLike::getUserId, userId);
        return photoLikeMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean checkCollected(Long photoId, Long userId) {
        if (userId == null) {
            return false;
        }

        LambdaQueryWrapper<PhotoCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoCollect::getPhotoId, photoId)
                .eq(PhotoCollect::getUserId, userId);
        return photoCollectMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public IPage<PhotoDTO> getUserCollections(int page, int size, Long userId) {
        Page<PhotoDTO> pageParam = new Page<>(page, size);

        // 查询用户收藏的照片ID列表
        LambdaQueryWrapper<PhotoCollect> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoCollect::getUserId, userId)
                .orderByDesc(PhotoCollect::getCreatedAt);

        Page<PhotoCollect> collectionPage = new Page<>(page, size);
        IPage<PhotoCollect> collectionIPage = photoCollectMapper.selectPage(collectionPage, queryWrapper);

        // 如果没有收藏记录，返回空结果
        if (collectionIPage.getRecords().isEmpty()) {
            return new Page<>(page, size);
        }

        // 获取收藏的照片ID列表
        List<Long> photoIds = collectionIPage.getRecords().stream()
                .map(PhotoCollect::getPhotoId)
                .collect(Collectors.toList());

        // 查询照片详情
        return baseMapper.selectPhotosByIds(pageParam, photoIds, userId);
    }

    @Override
    public IPage<PhotoVO> convertToPhotoVOPage(IPage<Photo> photoPage) {
        // 创建新的分页对象
        Page<PhotoVO> voPage = new Page<>();
        voPage.setCurrent(photoPage.getCurrent());
        voPage.setSize(photoPage.getSize());

        // 转换记录
        List<PhotoVO> allPhotos = photoPage.getRecords().stream()
                .map(this::convertToPhotoVO)
                .collect(Collectors.toList());

        // 按照分组ID进行分组
        Map<String, List<PhotoVO>> groupedPhotos = new HashMap<>();

        // 处理没有分组ID的照片
        for (PhotoVO photo : allPhotos) {
            String groupId = photo.getGroupId();
            if (groupId == null || groupId.isEmpty()) {
                // 没有分组ID的照片单独显示
                photo.setIsGrouped(false);
                photo.setGroupPhotoCount(1);
                List<PhotoVO> singlePhotoList = new ArrayList<>();
                singlePhotoList.add(photo);
                groupedPhotos.put("single_" + photo.getId(), singlePhotoList);
            } else {
                // 有分组ID的照片放入对应的组
                if (!groupedPhotos.containsKey(groupId)) {
                    groupedPhotos.put(groupId, new ArrayList<>());
                }
                groupedPhotos.get(groupId).add(photo);
            }
        }

        // 创建最终的照片列表，每个分组只显示第一张照片
        List<PhotoVO> groupedVoList = new ArrayList<>();

        for (Map.Entry<String, List<PhotoVO>> entry : groupedPhotos.entrySet()) {
            List<PhotoVO> group = entry.getValue();
            if (!group.isEmpty()) {
                PhotoVO firstPhoto = group.get(0);

                // 设置分组信息
                firstPhoto.setIsGrouped(group.size() > 1);
                firstPhoto.setGroupPhotoCount(group.size());

                // 如果有多张照片，添加同组照片（最多4张）
                if (group.size() > 1) {
                    List<PhotoVO> groupPhotos = group.subList(0, Math.min(4, group.size()));
                    firstPhoto.setGroupPhotos(groupPhotos);

                    // 设置同组照片ID列表
                    List<Long> groupPhotoIds = groupPhotos.stream()
                            .map(PhotoVO::getId)
                            .collect(Collectors.toList());
                    firstPhoto.setGroupPhotoIds(groupPhotoIds);
                }

                groupedVoList.add(firstPhoto);
            }
        }

        // 设置总数
        voPage.setTotal(groupedVoList.size());
        voPage.setRecords(groupedVoList);

        return voPage;
    }

    @Override
    public IPage<PhotoDTO> convertToPhotoDTO(IPage<Photo> photoPage, Long currentUserId) {
        // 创建新的分页对象
        Page<PhotoDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(photoPage.getCurrent());
        dtoPage.setSize(photoPage.getSize());
        dtoPage.setTotal(photoPage.getTotal());

        // 如果没有记录，返回空结果
        if (photoPage.getRecords() == null || photoPage.getRecords().isEmpty()) {
            dtoPage.setRecords(new ArrayList<>());
            return dtoPage;
        }

        // 获取照片ID列表
        List<Long> photoIds = photoPage.getRecords().stream()
                .map(Photo::getId)
                .collect(Collectors.toList());

        // 查询照片详情
        List<PhotoDTO> photoDTOs = baseMapper.selectPhotoDetailsByIds(photoIds, currentUserId);

        // 如果是七牛云私有空间，生成带下载凭证的URL
        boolean isQiniuPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", false);
        if (isQiniuPrivate && photoDTOs != null && !photoDTOs.isEmpty()) {
            for (PhotoDTO photoDTO : photoDTOs) {
                processPrivateUrls(photoDTO);
            }
        }

        dtoPage.setRecords(photoDTOs);
        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePhotoInfo(PhotoUploadWithMentionsDTO dto) {
        // 获取当前用户ID
        Long userId = SecurityUtil.getCurrentUserId();

        // 生成分组ID（如果没有提供）
        String groupId = dto.getGroupId();
        if (groupId == null || groupId.isEmpty()) {
            // 生成一个唯一的分组ID
            groupId = UUID.randomUUID().toString();
        }

        // 创建照片对象
        Photo photo = new Photo();
        photo.setUserId(userId);
        photo.setGroupId(groupId); // 设置分组ID
        photo.setTitle(dto.getTitle());
        photo.setDescription(dto.getDescription());
        photo.setLocation(dto.getLocation());
        photo.setUrl(dto.getUrl());
        photo.setThumbnailUrl(dto.getThumbnailUrl());
        photo.setWidth(dto.getWidth());
        photo.setHeight(dto.getHeight());
        photo.setStoragePath(dto.getKey());
        photo.setOriginalFilename(dto.getOriginalFilename());
        photo.setFileSize(dto.getFileSize()); // 设置文件大小
        photo.setFileType("image/jpeg"); // 默认为JPEG
        photo.setVisibility(dto.getVisibility());
        photo.setAllowComment(dto.getAllowComment());
        photo.setAllowDownload(dto.getAllowDownload());
        boolean isContentModerationEnabled = systemConfigService.getBooleanValue("content-moderation.enabled", false);
        photo.setStatus(isContentModerationEnabled ? 0 : 1); // 默认待审核状态
        photo.setLikeCount(0);
        photo.setCommentCount(0);
        photo.setCollectCount(0); // 初始化收藏数
        photo.setViewCount(0);
        photo.setIsDeleted(0);

        // 保存照片
        this.save(photo);

        // 获取照片ID
        final Long photoId = photo.getId();

        // 如果启用了内容审核，在事务提交后提交照片进行自动审核
        if (isContentModerationEnabled) {
            // 注册事务提交后的回调
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 事务提交后执行审核
                    photoAuditService.submitForAutoAudit(photoId);
                    log.info("照片已提交自动审核，照片ID: {}", photoId);
                }
            });
        }

        // 保存标签
        if (dto.getTags() != null && !dto.getTags().isEmpty()) {
            savePhotoTags(photo.getId(), dto.getTags());
        }

        // 返回照片ID
        return photoId;
    }

    /**
     * 保存照片标签
     *
     * @param photoId 照片ID
     * @param tags 标签列表
     */
    private void savePhotoTags(Long photoId, List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return;
        }

        List<PhotoTag> photoTags = tags.stream()
                .map(tag -> {
                    PhotoTag photoTag = new PhotoTag();
                    photoTag.setPhotoId(photoId);
                    photoTag.setName(tag);
                    return photoTag;
                })
                .collect(Collectors.toList());
        photoTagMapper.batchInsert(photoTags);
    }

    /**
     * 保存照片提及用户
     *
     * @param photoId 照片ID
     * @param mentions 提及用户列表
     */
    private void savePhotoMentions(Long photoId, List<com.phototagmoment.dto.MentionDTO> mentions) {
        // 此方法由MentionService实现，这里只是为了代码完整性
    }

    @Override
    public List<PhotoDTO> getPhotosByGroupId(String groupId, Long userId) {
        if (groupId == null || groupId.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询同一分组的所有照片
        LambdaQueryWrapper<Photo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Photo::getGroupId, groupId)
                .eq(Photo::getIsDeleted, 0)
                .orderBy(true, true, Photo::getCreatedAt);
        List<Photo> photos = this.list(queryWrapper);

        if (photos == null || photos.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取照片ID列表
        List<Long> photoIds = photos.stream()
                .map(Photo::getId)
                .collect(Collectors.toList());

        // 查询照片详情
        List<PhotoDTO> photoDTOs = baseMapper.selectPhotoDetailsByIds(photoIds, userId);

        // 如果是七牛云私有空间，生成带下载凭证的URL
        boolean isQiniuPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", false);
        if (isQiniuPrivate && photoDTOs != null && !photoDTOs.isEmpty()) {
            for (PhotoDTO photoDTO : photoDTOs) {
                processPrivateUrls(photoDTO);
            }
        }

        return photoDTOs;
    }

    /**
     * 将Photo实体转换为PhotoVO
     */
    private PhotoVO convertToPhotoVO(Photo photo) {
        if (photo == null) {
            return null;
        }

        PhotoVO vo = new PhotoVO();
        vo.setId(photo.getId());
        vo.setUserId(photo.getUserId());
        vo.setTitle(photo.getTitle());
        vo.setDescription(photo.getDescription());
        vo.setUrl(photo.getUrl());
        vo.setThumbnailUrl(photo.getThumbnailUrl());
        vo.setLocation(photo.getLocation());
        vo.setStatus(photo.getStatus());
        vo.setViewCount(photo.getViewCount());
        vo.setLikeCount(photo.getLikeCount());
        vo.setCommentCount(photo.getCommentCount());
        vo.setCollectCount(photo.getCollectCount());
        vo.setCreatedAt(photo.getCreatedAt());
        vo.setUpdatedAt(photo.getUpdatedAt());

        // 设置分组ID
        vo.setGroupId(photo.getGroupId());

        // 默认值
        vo.setIsGrouped(false);
        vo.setGroupPhotoCount(1);
        vo.setGroupPhotos(new ArrayList<>());

        // 获取标签
        List<PhotoTag> photoTags = photoTagMapper.getPhotoTags(photo.getId());
        if (photoTags != null && !photoTags.isEmpty()) {
            List<String> tags = photoTags.stream()
                    .map(PhotoTag::getName)
                    .collect(Collectors.toList());
            vo.setTags(tags);
        }

        return vo;
    }
}
