import request from '@/utils/request'

// 文件统计概览数据接口
export interface FileOverviewStats {
  totalFiles: number
  totalSize: number
  todayUploads: number
  activeUsers: number
  filesChange: number
  sizeChange: number
  uploadsChange: number
  usersChange: number
}

// 文件详细统计数据接口
export interface FileDetailStats {
  date: string
  uploadCount: number
  uploadSize: number
  deleteCount: number
  deleteSize: number
  activeUsers: number
  avgFileSize: number
  successRate: number
}

// 文件类型分布接口
export interface FileTypeDistribution {
  type: string
  count: number
  size: number
  percentage: number
}

// 存储服务使用情况接口
export interface StorageServiceUsage {
  serviceName: string
  fileCount: number
  totalSize: number
  percentage: number
}

// 上传趋势数据接口
export interface UploadTrendData {
  date: string
  uploadCount: number
  uploadSize: number
}

// 查询参数接口
export interface FileStatsQueryParams {
  startDate?: string
  endDate?: string
  dimension?: 'day' | 'week' | 'month'
  page?: number
  size?: number
}

/**
 * 获取文件统计概览
 * @param params 查询参数
 */
export function getFileOverviewStats(params?: FileStatsQueryParams) {
  return request<FileOverviewStats>({
    url: '/admin/file/statistics/overview',
    method: 'get',
    params
  })
}

/**
 * 获取文件详细统计
 * @param params 查询参数
 */
export function getFileDetailStats(params: FileStatsQueryParams) {
  return request<{
    records: FileDetailStats[]
    total: number
    current: number
    size: number
  }>({
    url: '/admin/file/statistics/detail',
    method: 'get',
    params
  })
}

/**
 * 获取文件类型分布
 */
export function getFileTypeDistribution() {
  return request<FileTypeDistribution[]>({
    url: '/admin/file/statistics/type-distribution',
    method: 'get'
  })
}

/**
 * 获取存储服务使用情况
 */
export function getStorageServiceUsage() {
  return request<StorageServiceUsage[]>({
    url: '/admin/file/statistics/storage-usage',
    method: 'get'
  })
}

/**
 * 获取上传趋势数据
 * @param days 统计天数
 */
export function getUploadTrendData(days: number = 7) {
  return request<UploadTrendData[]>({
    url: '/admin/file/statistics/upload-trend',
    method: 'get',
    params: { days }
  })
}

/**
 * 导出统计数据
 * @param params 查询参数
 */
export function exportFileStats(params: FileStatsQueryParams) {
  return request({
    url: '/admin/file/statistics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
