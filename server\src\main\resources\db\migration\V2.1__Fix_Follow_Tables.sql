-- 修复关注表结构问题
-- 创建时间：2025-05-27
-- 目的：解决推荐系统中的表名和字段不匹配问题

-- 1. 为 ptm_user_follow 表添加 is_deleted 字段
ALTER TABLE `ptm_user_follow`
ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0否，1是' AFTER `created_at`;

-- 2. 为 ptm_user_follow 表添加 updated_at 字段
ALTER TABLE `ptm_user_follow`
ADD COLUMN `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `is_deleted`;

-- 3. 创建 ptm_follow 表作为 ptm_user_follow 的别名视图（为了兼容现有代码）
CREATE OR REPLACE VIEW `ptm_follow` AS
SELECT
    `id`,
    `user_id` AS `follower_id`,
    `follow_user_id` AS `followed_id`,
    `created_at`,
    `updated_at`,
    `is_deleted`
FROM `ptm_user_follow`;

-- 4. 添加索引优化查询性能
CREATE INDEX `idx_user_follow_is_deleted` ON `ptm_user_follow` (`is_deleted`);
CREATE INDEX `idx_user_follow_user_deleted` ON `ptm_user_follow` (`user_id`, `is_deleted`);
CREATE INDEX `idx_user_follow_follow_deleted` ON `ptm_user_follow` (`follow_user_id`, `is_deleted`);

-- 5. 插入一些测试数据（如果表为空）
INSERT IGNORE INTO `ptm_user_follow` (`user_id`, `follow_user_id`) VALUES
(1, 2),
(1, 3),
(2, 1),
(2, 3);
