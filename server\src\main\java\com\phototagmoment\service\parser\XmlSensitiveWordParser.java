package com.phototagmoment.service.parser;

import com.phototagmoment.entity.SensitiveWord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * XML敏感词解析器
 */
@Slf4j
@Component
public class XmlSensitiveWordParser implements SensitiveWordFileParser {

    private static final String SUPPORTED_EXTENSION = "xml";

    @Override
    public List<SensitiveWord> parse(MultipartFile file) throws Exception {
        log.info("解析XML文件: {}", file.getOriginalFilename());
        List<SensitiveWord> sensitiveWords = new ArrayList<>();

        try (InputStream is = file.getInputStream()) {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            // 防止XXE攻击
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            factory.setXIncludeAware(false);
            factory.setExpandEntityReferences(false);
            
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(is);
            document.getDocumentElement().normalize();
            
            // 获取根元素
            Element root = document.getDocumentElement();
            
            // 处理两种可能的XML格式
            if ("sensitiveWords".equals(root.getNodeName()) || "words".equals(root.getNodeName())) {
                // 格式1: <sensitiveWords><word>...</word>...</sensitiveWords>
                NodeList wordNodes = root.getElementsByTagName("word");
                for (int i = 0; i < wordNodes.getLength(); i++) {
                    try {
                        Node wordNode = wordNodes.item(i);
                        if (wordNode.getNodeType() == Node.ELEMENT_NODE) {
                            Element wordElement = (Element) wordNode;
                            SensitiveWord sensitiveWord = parseSensitiveWordFromElement(wordElement);
                            if (sensitiveWord != null) {
                                sensitiveWords.add(sensitiveWord);
                            }
                        }
                    } catch (Exception e) {
                        log.error("解析XML敏感词节点失败: {}", e.getMessage(), e);
                    }
                }
            } else if ("word".equals(root.getNodeName())) {
                // 格式2: <word>...</word>
                try {
                    SensitiveWord sensitiveWord = parseSensitiveWordFromElement(root);
                    if (sensitiveWord != null) {
                        sensitiveWords.add(sensitiveWord);
                    }
                } catch (Exception e) {
                    log.error("解析XML单个敏感词节点失败: {}", e.getMessage(), e);
                }
            }
        }
        
        log.info("XML文件解析完成，共解析出{}个敏感词", sensitiveWords.size());
        return sensitiveWords;
    }

    @Override
    public boolean supports(String fileExtension) {
        return SUPPORTED_EXTENSION.equalsIgnoreCase(fileExtension);
    }
    
    /**
     * 从XML元素中解析敏感词对象
     */
    private SensitiveWord parseSensitiveWordFromElement(Element wordElement) {
        // 获取敏感词文本
        String wordText;
        
        // 检查是否有value属性或text子元素
        if (wordElement.hasAttribute("value")) {
            wordText = wordElement.getAttribute("value").trim();
        } else {
            NodeList textNodes = wordElement.getElementsByTagName("text");
            if (textNodes.getLength() > 0) {
                wordText = textNodes.item(0).getTextContent().trim();
            } else {
                // 直接获取元素内容
                wordText = wordElement.getTextContent().trim();
            }
        }
        
        if (wordText == null || wordText.isEmpty()) {
            return null;
        }
        
        SensitiveWord sensitiveWord = new SensitiveWord();
        sensitiveWord.setWord(wordText);
        
        // 获取类型
        String type = "其他";
        if (wordElement.hasAttribute("type")) {
            type = wordElement.getAttribute("type").trim();
        } else {
            NodeList typeNodes = wordElement.getElementsByTagName("type");
            if (typeNodes.getLength() > 0) {
                type = typeNodes.item(0).getTextContent().trim();
            }
        }
        sensitiveWord.setType(type.isEmpty() ? "其他" : type);
        
        // 获取级别
        int level = 1;
        if (wordElement.hasAttribute("level")) {
            try {
                level = Integer.parseInt(wordElement.getAttribute("level").trim());
            } catch (NumberFormatException e) {
                // 使用默认级别
            }
        } else {
            NodeList levelNodes = wordElement.getElementsByTagName("level");
            if (levelNodes.getLength() > 0) {
                try {
                    level = Integer.parseInt(levelNodes.item(0).getTextContent().trim());
                } catch (NumberFormatException e) {
                    // 使用默认级别
                }
            }
        }
        sensitiveWord.setLevel(level);
        
        // 获取替换词
        String replaceWord = "";
        if (wordElement.hasAttribute("replaceWord")) {
            replaceWord = wordElement.getAttribute("replaceWord");
        } else {
            NodeList replaceWordNodes = wordElement.getElementsByTagName("replaceWord");
            if (replaceWordNodes.getLength() > 0) {
                replaceWord = replaceWordNodes.item(0).getTextContent();
            }
        }
        sensitiveWord.setReplaceWord(replaceWord);
        
        // 获取状态
        boolean status = true;
        if (wordElement.hasAttribute("status")) {
            String statusStr = wordElement.getAttribute("status").trim().toLowerCase();
            status = !("false".equals(statusStr) || "0".equals(statusStr) || "no".equals(statusStr));
        } else {
            NodeList statusNodes = wordElement.getElementsByTagName("status");
            if (statusNodes.getLength() > 0) {
                String statusStr = statusNodes.item(0).getTextContent().trim().toLowerCase();
                status = !("false".equals(statusStr) || "0".equals(statusStr) || "no".equals(statusStr));
            }
        }
        sensitiveWord.setStatus(status);
        
        // 设置时间
        sensitiveWord.setCreatedAt(LocalDateTime.now());
        sensitiveWord.setUpdatedAt(LocalDateTime.now());
        
        return sensitiveWord;
    }
}
