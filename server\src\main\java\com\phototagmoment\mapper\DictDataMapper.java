package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.DictData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据字典数据Mapper接口
 */
@Mapper
public interface DictDataMapper extends BaseMapper<DictData> {

    /**
     * 根据字典类型ID获取字典数据列表
     *
     * @param dictTypeId 字典类型ID
     * @return 字典数据列表
     */
    @Select("SELECT * FROM ptm_dict_data WHERE dict_type_id = #{dictTypeId} AND status = 1 AND is_deleted = 0 ORDER BY dict_sort")
    List<DictData> getByDictTypeId(@Param("dictTypeId") Long dictTypeId);

    /**
     * 根据字典类型获取字典数据列表
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    @Select("SELECT d.* FROM ptm_dict_data d " +
            "JOIN ptm_dict_type t ON d.dict_type_id = t.id " +
            "WHERE t.dict_type = #{dictType} AND d.status = 1 AND d.is_deleted = 0 AND t.is_deleted = 0 " +
            "ORDER BY d.dict_sort")
    List<DictData> getByDictType(@Param("dictType") String dictType);
}
