<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.NotificationMapper">

    <!-- 分页查询用户通知列表 -->
    <select id="selectUserNotifications" resultType="com.phototagmoment.dto.NotificationDTO">
        SELECT
            n.*,
            u.username AS senderName,
            u.avatar AS senderAvatar,
            CASE
                WHEN n.target_type = 1 THEN p.title
                WHEN n.target_type = 2 THEN c.content
                WHEN n.target_type = 3 THEN u2.nickname
                ELSE NULL
            END AS targetContent,
            CASE
                WHEN n.target_type = 1 THEN p.thumbnail_url
                WHEN n.target_type = 3 THEN u2.avatar
                ELSE NULL
            END AS targetThumbnail
        FROM
            ptm_notification n
        LEFT JOIN
            ptm_user u ON n.sender_id = u.id
        LEFT JOIN
            ptm_photo p ON n.target_type = 1 AND n.target_id = p.id
        LEFT JOIN
            ptm_comment c ON n.target_type = 2 AND n.target_id = c.id
        LEFT JOIN
            ptm_user u2 ON n.target_type = 3 AND n.target_id = u2.id
        WHERE
            n.user_id = #{userId}
            <if test="type != null">
                AND n.type = #{type}
            </if>
        ORDER BY
            n.created_at DESC
    </select>

    <!-- 获取用户未读通知数量 -->
    <select id="countUnreadNotifications" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            ptm_notification
        WHERE
            user_id = #{userId}
            AND is_read = 0
    </select>

</mapper>
