package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.PhotoUploadDTO;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.vo.PhotoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 照片服务接口
 */
public interface PhotoService extends IService<Photo> {

    /**
     * 上传照片
     *
     * @param file 照片文件
     * @param photoUploadDTO 照片上传信息
     * @param userId 用户ID
     * @return 照片ID
     */
    Long uploadPhoto(MultipartFile file, PhotoUploadDTO photoUploadDTO, Long userId);

    /**
     * 批量上传照片
     *
     * @param files 照片文件列表
     * @param photoUploadDTOs 照片上传信息列表
     * @param userId 用户ID
     * @return 照片ID列表
     */
    List<Long> batchUploadPhotos(List<MultipartFile> files, List<PhotoUploadDTO> photoUploadDTOs, Long userId);

    /**
     * 保存照片信息（不包括文件上传）
     * 用于前端直接上传到七牛云后，保存照片信息
     *
     * @param photoUploadDTO 照片上传信息
     * @param userId 用户ID
     * @param url 照片URL
     * @param thumbnailUrl 缩略图URL
     * @param width 照片宽度
     * @param height 照片高度
     * @param key 七牛云存储的key
     * @return 照片ID
     */
    Long savePhotoInfo(PhotoUploadDTO photoUploadDTO, Long userId, String url, String thumbnailUrl,
                      Integer width, Integer height, String key);

    /**
     * 保存照片信息（带提及用户）
     * 用于前端直接上传到七牛云后，保存照片信息
     *
     * @param dto 照片上传信息（带提及用户）
     * @return 照片ID
     */
    Long savePhotoInfo(com.phototagmoment.dto.PhotoUploadWithMentionsDTO dto);

    /**
     * 根据分组ID获取照片列表
     *
     * @param groupId 分组ID
     * @param userId 当前用户ID
     * @return 照片列表
     */
    List<PhotoDTO> getPhotosByGroupId(String groupId, Long userId);

    /**
     * 获取照片详情
     *
     * @param photoId 照片ID
     * @param userId 当前用户ID
     * @return 照片详情
     */
    PhotoDTO getPhotoDetail(Long photoId, Long userId);

    /**
     * 分页获取照片列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID，如果为null则获取所有公开照片
     * @param currentUserId 当前用户ID
     * @return 照片列表
     */
    IPage<PhotoDTO> getPhotoPage(int page, int size, Long userId, Long currentUserId);

    /**
     * 更新照片信息
     *
     * @param photoId 照片ID
     * @param photoUploadDTO 照片信息
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updatePhoto(Long photoId, PhotoUploadDTO photoUploadDTO, Long userId);

    /**
     * 删除照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deletePhoto(Long photoId, Long userId);

    /**
     * 增加照片浏览数
     *
     * @param photoId 照片ID
     * @return 是否成功
     */
    boolean incrementViewCount(Long photoId);

    /**
     * 点赞照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean likePhoto(Long photoId, Long userId);

    /**
     * 取消点赞照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unlikePhoto(Long photoId, Long userId);

    /**
     * 收藏照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean collectPhoto(Long photoId, Long userId);

    /**
     * 取消收藏照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean uncollectPhoto(Long photoId, Long userId);

    /**
     * 检查用户是否已点赞照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    boolean checkLiked(Long photoId, Long userId);

    /**
     * 检查用户是否已收藏照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否已收藏
     */
    boolean checkCollected(Long photoId, Long userId);

    /**
     * 获取用户收藏的照片列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @return 照片列表
     */
    IPage<PhotoDTO> getUserCollections(int page, int size, Long userId);

    /**
     * 将Photo实体分页对象转换为PhotoVO分页对象
     *
     * @param photoPage Photo实体分页对象
     * @return PhotoVO分页对象
     */
    IPage<PhotoVO> convertToPhotoVOPage(IPage<Photo> photoPage);

    /**
     * 将Photo实体分页对象转换为PhotoDTO分页对象
     *
     * @param photoPage Photo实体分页对象
     * @param currentUserId 当前用户ID
     * @return PhotoDTO分页对象
     */
    IPage<PhotoDTO> convertToPhotoDTO(IPage<Photo> photoPage, Long currentUserId);
}
