import request from '@/utils/request'

// 用户查询参数接口
interface UserListParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: number
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: string
  [key: string]: any
}

// 用户数据接口
interface UserData {
  id: number
  username: string
  nickname: string
  email?: string
  phone?: string
  avatar?: string
  status: number
  createTime: string
  updateTime: string
  lastLoginTime?: string
  photoCount?: number
  followersCount?: number
  followingCount?: number
  [key: string]: any
}

// 用户状态更新接口
interface UserStatusData {
  status: number
  reason?: string
}

/**
 * 获取用户管理列表
 * @param params 查询参数
 * @returns Promise
 */
export function getUserList(params: UserListParams) {
  return request({
    url: '/admin/users/list',
    method: 'get',
    params
  })
}

/**
 * 获取用户详情
 * @param userId 用户ID
 * @returns Promise
 */
export function getUserDetail(userId: number) {
  return request({
    url: `/admin/users/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户状态
 * @param userId 用户ID
 * @param status 用户状态
 * @param reason 操作原因
 * @returns Promise
 */
export function updateUserStatus(userId: number, status: number, reason?: string) {
  return request({
    url: `/admin/users/${userId}/status`,
    method: 'put',
    data: { 
      status,
      reason
    }
  })
}

/**
 * 批量更新用户状态
 * @param userIds 用户ID列表
 * @param status 用户状态
 * @param reason 操作原因
 * @returns Promise
 */
export function batchUpdateUserStatus(userIds: number[], status: number, reason?: string) {
  return request({
    url: '/admin/users/batch-status',
    method: 'put',
    data: {
      userIds,
      status,
      reason
    }
  })
}

/**
 * 重置用户密码
 * @param userId 用户ID
 * @param newPassword 新密码
 * @returns Promise
 */
export function resetUserPassword(userId: number, newPassword: string) {
  return request({
    url: `/admin/users/${userId}/reset-password`,
    method: 'post',
    data: {
      newPassword
    }
  })
}

/**
 * 获取用户统计信息
 * @param userId 用户ID
 * @returns Promise
 */
export function getUserStats(userId: number) {
  return request({
    url: `/admin/users/${userId}/stats`,
    method: 'get'
  })
}

/**
 * 获取用户操作日志
 * @param userId 用户ID
 * @param params 查询参数
 * @returns Promise
 */
export function getUserOperationLogs(userId: number, params: any) {
  return request({
    url: `/admin/users/${userId}/operation-logs`,
    method: 'get',
    params
  })
}

/**
 * 删除用户（软删除）
 * @param userId 用户ID
 * @param reason 删除原因
 * @returns Promise
 */
export function deleteUser(userId: number, reason: string) {
  return request({
    url: `/admin/users/${userId}`,
    method: 'delete',
    data: {
      reason
    }
  })
}

// 导出类型定义
export type {
  UserListParams,
  UserData,
  UserStatusData
}
