<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.CommentMentionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.phototagmoment.entity.CommentMention">
        <id column="id" property="id" />
        <result column="comment_id" property="commentId" />
        <result column="mentioned_user_id" property="mentionedUserId" />
        <result column="mention_user_id" property="mentionUserId" />
        <result column="mentioned_username" property="mentionedUsername" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, comment_id, mentioned_user_id, mention_user_id, mentioned_username, created_at
    </sql>

    <!-- 批量插入评论用户提及 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ptm_comment_mention (comment_id, mentioned_user_id, mention_user_id, mentioned_username, created_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.commentId}, #{item.mentionedUserId}, #{item.mentionUserId}, #{item.mentionedUsername}, #{item.createdAt})
        </foreach>
    </insert>

    <!-- 根据评论ID删除用户提及 -->
    <delete id="deleteByCommentId" parameterType="java.lang.Long">
        DELETE FROM ptm_comment_mention WHERE comment_id = #{commentId}
    </delete>

    <!-- 根据用户ID获取被提及的评论列表 -->
    <select id="selectMentionedComments" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            cm.comment_id as commentId,
            cm.mentioned_username as mentionedUsername,
            cm.created_at as mentionedAt,
            c.content as commentContent,
            c.photo_id as photoId,
            u.nickname as mentionUserNickname
        FROM ptm_comment_mention cm
        LEFT JOIN ptm_comment c ON cm.comment_id = c.id
        LEFT JOIN ptm_user u ON cm.mention_user_id = u.id
        WHERE cm.mentioned_user_id = #{userId}
        ORDER BY cm.created_at DESC
    </select>

    <!-- 统计用户被提及次数 -->
    <select id="countMentionsByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ptm_comment_mention WHERE mentioned_user_id = #{userId}
    </select>

</mapper>
