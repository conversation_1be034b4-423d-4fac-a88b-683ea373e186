param(
    [Parameter(Position=0)]
    [ValidateSet("dev", "test", "prod")]
    [string]$Environment = "dev",
    
    [Parameter(Position=1)]
    [switch]$RunTests = $false,
    
    [Parameter()]
    [switch]$Clean = $true,
    
    [Parameter()]
    [switch]$Verbose = $false
)

# PhotoTagMoment PowerShell 高级多环境打包脚本
Write-Host "==========================================" -ForegroundColor Green
Write-Host "PhotoTagMoment 高级多环境打包脚本" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "目标环境: $Environment" -ForegroundColor Cyan
Write-Host "运行测试: $RunTests" -ForegroundColor Cyan
Write-Host "清理构建: $Clean" -ForegroundColor Cyan
Write-Host "详细输出: $Verbose" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Green

# 设置Java 17环境
$env:JAVA_HOME = "C:\Program Files\Java\jdk-17"
Write-Host "使用Java版本: $env:JAVA_HOME" -ForegroundColor Yellow

# 检查Java环境
try {
    $javaVersion = & java -version 2>&1 | Select-Object -First 1
    Write-Host "Java环境检查通过: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: Java环境未正确配置" -ForegroundColor Red
    Write-Host "请确保Java 17已安装并配置在PATH中" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查Maven环境
try {
    $mavenVersion = & mvn -version 2>&1 | Select-Object -First 1
    Write-Host "Maven环境检查通过: $mavenVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: Maven环境未正确配置" -ForegroundColor Red
    Write-Host "请确保Maven已安装并配置在PATH中" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查是否在项目根目录
if (-not (Test-Path "..\pom.xml")) {
    Write-Host "错误: 请在项目根目录运行此脚本" -ForegroundColor Red
    Write-Host "当前目录: $(Get-Location)" -ForegroundColor Red
    Write-Host "应该包含: ..\pom.xml" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查环境配置文件是否存在
$configFile = "..\src\main\resources\application-$Environment.yml"
if (-not (Test-Path $configFile)) {
    Write-Host "警告: 环境配置文件不存在: application-$Environment.yml" -ForegroundColor Yellow
    Write-Host "将使用默认配置文件" -ForegroundColor Yellow
} else {
    Write-Host "使用配置文件: application-$Environment.yml" -ForegroundColor Green
}

Write-Host "[1/5] 进入后端目录..." -ForegroundColor Yellow
Set-Location ..\

# 设置Maven参数
$mavenArgs = @()
if (-not $Verbose) {
    $mavenArgs += "-q"
}

if ($Clean) {
    Write-Host "[2/5] 清理之前的构建..." -ForegroundColor Yellow
    try {
        & mvn clean @mavenArgs
        if ($LASTEXITCODE -ne 0) {
            throw "Maven清理失败"
        }
        Write-Host "清理完成" -ForegroundColor Green
    } catch {
        Write-Host "错误: Maven清理失败 - $_" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
} else {
    Write-Host "[2/5] 跳过清理..." -ForegroundColor Yellow
}

Write-Host "[3/5] 编译项目..." -ForegroundColor Yellow
try {
    & mvn compile @mavenArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Maven编译失败"
    }
    Write-Host "编译完成" -ForegroundColor Green
} catch {
    Write-Host "错误: Maven编译失败 - $_" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 根据参数决定是否运行测试
if ($RunTests) {
    Write-Host "[4/5] 运行单元测试..." -ForegroundColor Yellow
    try {
        $testArgs = @("test", "-Dspring.profiles.active=$Environment")
        if (-not $Verbose) {
            $testArgs += "-q"
        }
        & mvn @testArgs
        if ($LASTEXITCODE -ne 0) {
            Write-Host "警告: 测试失败，但继续打包" -ForegroundColor Yellow
            $choice = Read-Host "是否继续打包? (y/n)"
            if ($choice -ne "y" -and $choice -ne "Y") {
                Write-Host "构建已取消" -ForegroundColor Red
                Read-Host "按任意键退出"
                exit 1
            }
        } else {
            Write-Host "测试通过" -ForegroundColor Green
        }
    } catch {
        Write-Host "警告: 测试执行失败，但继续打包" -ForegroundColor Yellow
    }
} else {
    Write-Host "[4/5] 跳过测试..." -ForegroundColor Yellow
}

Write-Host "[5/5] 开始打包 (环境: $Environment)..." -ForegroundColor Yellow
try {
    $packageArgs = @("package", "-Dspring.profiles.active=$Environment")
    if (-not $RunTests) {
        $packageArgs += "-DskipTests"
    }
    if (-not $Verbose) {
        $packageArgs += "-q"
    }
    
    & mvn @packageArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Maven打包失败"
    }
    Write-Host "打包完成" -ForegroundColor Green
} catch {
    Write-Host "错误: Maven打包失败 - $_" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 获取JAR文件信息
$jarFile = "target\phototagmoment-0.0.1-SNAPSHOT.jar"
if (-not (Test-Path $jarFile)) {
    Write-Host "错误: JAR文件未生成" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

$fileInfo = Get-Item $jarFile
$fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
$buildTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# 创建环境特定的JAR文件名
$envJarFile = "target\phototagmoment-$Environment-0.0.1-SNAPSHOT.jar"
Copy-Item $jarFile $envJarFile -Force

Write-Host ""
Write-Host "==========================================" -ForegroundColor Green
Write-Host "打包成功！" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "环境配置: $Environment" -ForegroundColor Cyan
Write-Host "运行测试: $RunTests" -ForegroundColor Cyan
Write-Host "JAR文件位置: $(Get-Location)\$jarFile" -ForegroundColor Cyan
Write-Host "环境JAR文件: $(Get-Location)\$envJarFile" -ForegroundColor Cyan
Write-Host "文件大小: $fileSizeMB MB" -ForegroundColor Cyan
Write-Host "生成时间: $buildTime" -ForegroundColor Cyan
Write-Host ""
Write-Host "运行命令:" -ForegroundColor Yellow
Write-Host "  java -jar $jarFile" -ForegroundColor White
Write-Host "  或者使用环境特定JAR:" -ForegroundColor Yellow
Write-Host "  java -jar $envJarFile" -ForegroundColor White
Write-Host "  或者指定环境:" -ForegroundColor Yellow
Write-Host "  java -jar -Dspring.profiles.active=$Environment $jarFile" -ForegroundColor White
Write-Host ""
Write-Host "配置文件: application-$Environment.yml" -ForegroundColor Cyan

# 根据环境显示不同的信息
$port = switch ($Environment) {
    "dev" { 
        Write-Host "API文档: http://localhost:8081/api/doc.html" -ForegroundColor Cyan
        Write-Host "开发环境特性: 详细日志、API文档启用、简单缓存" -ForegroundColor Gray
        "8081" 
    }
    "test" { 
        Write-Host "API文档: http://localhost:8082/api/doc.html" -ForegroundColor Cyan
        Write-Host "测试环境特性: 中等日志、API文档需认证、Redis缓存" -ForegroundColor Gray
        "8082" 
    }
    "prod" { 
        Write-Host "API文档: http://localhost:8081/api/doc.html" -ForegroundColor Cyan
        Write-Host "生产环境特性: 精简日志、API文档关闭、Redis缓存、安全增强" -ForegroundColor Gray
        "8081" 
    }
    default { "8081" }
}

Write-Host "==========================================" -ForegroundColor Green

Read-Host "按任意键退出"
