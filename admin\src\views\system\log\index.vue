<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="queryParams.keyword"
        placeholder="请输入用户名/操作内容"
        style="width: 200px;"
        class="filter-item"
        clearable
        @keyup.enter="handleSearch"
      />
      <el-select
        v-model="queryParams.module"
        placeholder="请选择模块"
        style="width: 150px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="module in moduleOptions"
          :key="module"
          :label="module"
          :value="module"
        />
      </el-select>
      <el-select
        v-model="queryParams.operation"
        placeholder="请选择操作类型"
        style="width: 150px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="operation in operationOptions"
          :key="operation"
          :label="operation"
          :value="operation"
        />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        class="filter-item"
        style="width: 240px;"
        @change="handleDateRangeChange"
      />
      <el-button type="primary" class="filter-item" @click="handleSearch">
        <el-icon><Search /></el-icon>
        搜索
      </el-button>
      <el-button type="warning" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出
      </el-button>
      <el-button type="danger" @click="handleClear">
        <el-icon><Delete /></el-icon>
        清空
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="logList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" min-width="120" />
      <el-table-column prop="module" label="模块" width="120" />
      <el-table-column prop="operation" label="操作类型" width="120" />
      <el-table-column prop="content" label="操作内容" min-width="200" />
      <el-table-column prop="ip" label="IP地址" min-width="120" />
      <el-table-column prop="userAgent" label="User-Agent" min-width="200" show-overflow-tooltip />
      <el-table-column prop="createdAt" label="操作时间" min-width="150" />
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Download, Delete } from '@element-plus/icons-vue'
import {
  getOperationLogList,
  exportOperationLog,
  clearOperationLog
} from '@/api/system/log'

// 日志列表
const logList = ref([])
// 总记录数
const total = ref(0)
// 加载状态
const loading = ref(false)
// 日期范围
const dateRange = ref([])
// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  keyword: '',
  module: '',
  operation: '',
  startDate: '',
  endDate: ''
})
// 模块选项
const moduleOptions = ref([])
// 操作类型选项
const operationOptions = ref([])

// 获取操作日志列表
const fetchLogList = async () => {
  loading.value = true
  try {
    const { data } = await getOperationLogList({
      page: queryParams.page,
      pageSize: queryParams.pageSize,
      keyword: queryParams.keyword,
      module: queryParams.module,
      operation: queryParams.operation,
      startDate: queryParams.startDate,
      endDate: queryParams.endDate
    })
    logList.value = data.records
    total.value = data.total
    
    // 提取模块和操作类型选项
    const modules = new Set()
    const operations = new Set()
    data.records.forEach(log => {
      if (log.module) modules.add(log.module)
      if (log.operation) operations.add(log.operation)
    })
    moduleOptions.value = Array.from(modules)
    operationOptions.value = Array.from(operations)
  } catch (error) {
    console.error('获取操作日志列表失败', error)
    ElMessage.error('获取操作日志列表失败')
  } finally {
    loading.value = false
  }
}

// 处理日期范围变更
const handleDateRangeChange = (val) => {
  if (val) {
    queryParams.startDate = val[0]
    queryParams.endDate = val[1]
  } else {
    queryParams.startDate = ''
    queryParams.endDate = ''
  }
}

// 处理搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchLogList()
}

// 处理页码变更
const handleCurrentChange = (page) => {
  queryParams.page = page
  fetchLogList()
}

// 处理每页条数变更
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.page = 1
  fetchLogList()
}

// 处理导出
const handleExport = async () => {
  try {
    const response = await exportOperationLog({
      keyword: queryParams.keyword,
      module: queryParams.module,
      operation: queryParams.operation,
      startDate: queryParams.startDate,
      endDate: queryParams.endDate
    })
    
    // 创建Blob对象
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = '操作日志.xlsx'
    link.click()
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出操作日志失败', error)
    ElMessage.error('导出操作日志失败')
  }
}

// 处理清空
const handleClear = () => {
  ElMessageBox.confirm('确认清空所有操作日志吗？此操作不可恢复！', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await clearOperationLog()
      ElMessage.success('操作日志清空成功')
      fetchLogList()
    } catch (error) {
      console.error('清空操作日志失败', error)
      ElMessage.error('清空操作日志失败')
    }
  }).catch(() => {})
}

// 页面加载时获取数据
onMounted(() => {
  fetchLogList()
})
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
