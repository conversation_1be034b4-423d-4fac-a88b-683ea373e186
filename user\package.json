{"name": "phototagmoment-user", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-check": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.5.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "element-plus": "^2.9.10", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "phototagmoment-user": "file:", "pinia": "^2.1.6", "qiniu-js": "^4.0.0-beta.6", "qrcode": "^1.5.3", "qrcodejs2": "^0.0.2", "vant": "^4.6.8", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/crypto-js": "^4.1.2", "@types/lodash-es": "^4.17.9", "@types/node": "^20.6.0", "@types/qrcode": "^1.5.2", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.15", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "postcss": "^8.4.29", "sass": "^1.69.5", "sass-loader": "^13.3.2", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "vite": "^4.4.9", "vue-tsc": "^2.2.10"}}