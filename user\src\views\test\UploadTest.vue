<template>
  <div class="upload-test">
    <h2>上传功能测试</h2>
    
    <div class="test-section">
      <h3>1. 测试上传凭证获取</h3>
      <button @click="testGetUploadToken" :disabled="loading">
        测试获取上传凭证
      </button>
      <div v-if="tokenResult" class="result">
        <h4>结果：</h4>
        <pre>{{ JSON.stringify(tokenResult, null, 2) }}</pre>
      </div>
      <div v-if="tokenError" class="error">
        <h4>错误：</h4>
        <pre>{{ tokenError }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 测试照片笔记上传</h3>
      <input type="file" @change="onFileSelect" accept="image/*" />
      <button @click="testUploadPhotoForNote" :disabled="loading || !selectedFile">
        测试照片笔记上传
      </button>
      <div v-if="uploadResult" class="result">
        <h4>结果：</h4>
        <pre>{{ JSON.stringify(uploadResult, null, 2) }}</pre>
      </div>
      <div v-if="uploadError" class="error">
        <h4>错误：</h4>
        <pre>{{ uploadError }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 网络请求监控</h3>
      <p>请打开浏览器开发者工具的网络标签页，观察实际的API请求。</p>
      <div class="requests-log">
        <h4>请求日志：</h4>
        <div v-for="(request, index) in requestsLog" :key="index" class="request-item">
          <strong>{{ request.method }} {{ request.url }}</strong>
          <div>状态: {{ request.status }}</div>
          <div>时间: {{ request.timestamp }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getUploadToken, uploadPhotoForNote } from '@/api/photo'

const loading = ref(false)
const tokenResult = ref(null)
const tokenError = ref('')
const uploadResult = ref(null)
const uploadError = ref('')
const selectedFile = ref<File | null>(null)
const requestsLog = ref<Array<{
  method: string
  url: string
  status: string
  timestamp: string
}>>([])

// 监控网络请求
const originalFetch = window.fetch
window.fetch = function(...args) {
  const url = args[0] as string
  const options = args[1] || {}
  const method = options.method || 'GET'
  
  requestsLog.value.push({
    method: method.toUpperCase(),
    url,
    status: '发送中...',
    timestamp: new Date().toLocaleTimeString()
  })
  
  return originalFetch.apply(this, args).then(response => {
    // 更新最后一个请求的状态
    const lastRequest = requestsLog.value[requestsLog.value.length - 1]
    if (lastRequest) {
      lastRequest.status = `${response.status} ${response.statusText}`
    }
    return response
  }).catch(error => {
    // 更新最后一个请求的状态
    const lastRequest = requestsLog.value[requestsLog.value.length - 1]
    if (lastRequest) {
      lastRequest.status = `错误: ${error.message}`
    }
    throw error
  })
}

const testGetUploadToken = async () => {
  loading.value = true
  tokenResult.value = null
  tokenError.value = ''
  
  try {
    console.log('开始测试获取上传凭证...')
    const result = await getUploadToken()
    console.log('获取上传凭证成功:', result)
    tokenResult.value = result
  } catch (error) {
    console.error('获取上传凭证失败:', error)
    tokenError.value = error instanceof Error ? error.message : String(error)
  } finally {
    loading.value = false
  }
}

const onFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    selectedFile.value = target.files[0]
  }
}

const testUploadPhotoForNote = async () => {
  if (!selectedFile.value) return
  
  loading.value = true
  uploadResult.value = null
  uploadError.value = ''
  
  try {
    console.log('开始测试照片笔记上传...')
    const result = await uploadPhotoForNote(selectedFile.value)
    console.log('照片笔记上传成功:', result)
    uploadResult.value = result
  } catch (error) {
    console.error('照片笔记上传失败:', error)
    uploadError.value = error instanceof Error ? error.message : String(error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.upload-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f8ff;
  border-radius: 4px;
}

.error {
  margin-top: 10px;
  padding: 10px;
  background-color: #ffe6e6;
  border-radius: 4px;
  color: #d00;
}

.requests-log {
  margin-top: 10px;
}

.request-item {
  padding: 8px;
  margin: 4px 0;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

button {
  padding: 8px 16px;
  margin: 8px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

pre {
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
