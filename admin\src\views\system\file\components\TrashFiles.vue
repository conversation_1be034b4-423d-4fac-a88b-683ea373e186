<template>
  <div class="trash-files-container">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-button type="danger" @click="handleClearTrash">
        <el-icon><delete /></el-icon>
        清空回收站
      </el-button>
      <el-button @click="handleRefresh">
        <el-icon><refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 文件列表 -->
    <el-table
      v-loading="loading"
      :data="trashFiles"
      @selection-change="handleSelectionChange"
      style="width: 100%; margin-top: 16px;"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="文件名" min-width="200">
        <template #default="{ row }">
          <div class="file-info">
            <div class="file-name" :title="row.originalName">{{ row.originalName }}</div>
            <div class="file-meta">
              <el-tag size="small" type="info">{{ row.extension?.toUpperCase() }}</el-tag>
              <span class="file-size">{{ row.fileSizeFormatted }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="文件类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getFileTypeTagType(row.category)">
            {{ getFileTypeLabel(row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="删除时间" width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.deletedAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="success" @click="handleRestore(row)">
            <el-icon><refresh-right /></el-icon>
            恢复
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            <el-icon><delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Refresh, RefreshRight } from '@element-plus/icons-vue'
import { getTrashFiles, restoreFile, clearTrash, batchDeleteFiles, type FileInfo } from '@/api/file'
import { formatDateTime } from '@/utils/date'

// Emits定义
const emit = defineEmits<{
  restore: []
  clear: []
}>()

// 响应式数据
const loading = ref(false)
const trashFiles = ref<FileInfo[]>([])
const selectedFiles = ref<FileInfo[]>([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 生命周期
onMounted(() => {
  loadTrashFiles()
})

// 方法
const loadTrashFiles = async () => {
  loading.value = true
  try {
    const response = await getTrashFiles(pagination.page, pagination.size)
    if ((response as any).code === 200) {
      trashFiles.value = (response as any).data.records
      pagination.total = (response as any).data.total
    }
  } catch (error) {
    console.error('加载回收站文件失败:', error)
    ElMessage.error('加载回收站文件失败')
  } finally {
    loading.value = false
  }
}

const handleRefresh = () => {
  loadTrashFiles()
}

const handleSelectionChange = (selection: FileInfo[]) => {
  selectedFiles.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadTrashFiles()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTrashFiles()
}

const handleRestore = async (file: FileInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复文件 "${file.originalName}" 吗？`,
      '确认恢复',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await restoreFile(file.id)
    if ((response as any).code === 200) {
      ElMessage.success('文件恢复成功')
      loadTrashFiles()
      emit('restore')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复文件失败:', error)
      ElMessage.error('恢复文件失败')
    }
  }
}

const handleDelete = async (file: FileInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要永久删除文件 "${file.originalName}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await batchDeleteFiles([file.id])
    if ((response as any).code === 200) {
      ElMessage.success('文件删除成功')
      loadTrashFiles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const handleClearTrash = async () => {
  if (trashFiles.value.length === 0) {
    ElMessage.warning('回收站为空')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要清空回收站吗？这将永久删除 ${trashFiles.value.length} 个文件，此操作不可恢复！`,
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await clearTrash()
    if ((response as any).code === 200) {
      ElMessage.success('回收站清空成功')
      loadTrashFiles()
      emit('clear')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空回收站失败:', error)
      ElMessage.error('清空回收站失败')
    }
  }
}

// 工具方法
const getFileTypeTagType = (category: string) => {
  const typeMap = {
    image: 'success',
    video: 'warning',
    audio: 'info',
    document: 'primary',
    other: 'info'
  }
  return typeMap[category] || 'info'
}

const getFileTypeLabel = (category: string) => {
  const labelMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档',
    other: '其他'
  }
  return labelMap[category] || '其他'
}
</script>

<style scoped>
.trash-files-container {
  padding: 16px;
}

.operation-bar {
  display: flex;
  gap: 8px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
