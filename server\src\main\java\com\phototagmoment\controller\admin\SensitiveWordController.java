package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.entity.SensitiveWord;
import com.phototagmoment.service.SensitiveWordImportService;
import com.phototagmoment.service.SensitiveWordService;
import com.phototagmoment.vo.SensitiveWordImportResultVO;
import com.phototagmoment.vo.SensitiveWordStatVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 敏感词Controller
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/sensitive-word")
@Tag(name = "敏感词管理", description = "敏感词管理相关接口")
public class SensitiveWordController {

    @Autowired
    private SensitiveWordService sensitiveWordService;

    @Autowired
    private SensitiveWordImportService sensitiveWordImportService;

    /**
     * 获取所有敏感词
     */
    @GetMapping("/list")
    @Operation(summary = "获取所有敏感词", description = "获取所有敏感词")
    public Result<List<SensitiveWord>> list() {
        List<SensitiveWord> words = sensitiveWordService.listAllWords();
        return Result.success(words);
    }

    /**
     * 分页获取敏感词
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取敏感词", description = "分页获取敏感词")
    public Result<IPage<SensitiveWord>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "类型") @RequestParam(required = false) String type,
            @Parameter(description = "级别") @RequestParam(required = false) Integer level) {
        // 限制最大页面大小为50
        if (pageSize > 50) {
            pageSize = 50;
        }
        IPage<SensitiveWord> pageResult = sensitiveWordService.getSensitiveWordList(page, pageSize, keyword, type, level);
        return Result.success(pageResult);
    }

    /**
     * 根据ID获取敏感词
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取敏感词", description = "根据ID获取敏感词")
    public Result<SensitiveWord> getById(@Parameter(description = "敏感词ID") @PathVariable Long id) {
        SensitiveWord word = sensitiveWordService.getWordById(id);
        return Result.success(word);
    }

    /**
     * 保存敏感词
     */
    @PostMapping
    @Operation(summary = "保存敏感词", description = "保存敏感词")
    public Result<Boolean> save(@RequestBody SensitiveWord word) {
        boolean result = sensitiveWordService.saveWord(word);
        return Result.success(result);
    }

    /**
     * 批量保存敏感词
     */
    @PostMapping("/batch")
    @Operation(summary = "批量保存敏感词", description = "批量保存敏感词")
    public Result<Boolean> batchSave(@RequestBody List<SensitiveWord> words) {
        boolean result = sensitiveWordService.batchSaveWord(words);
        return Result.success(result);
    }

    /**
     * 更新敏感词
     */
    @PutMapping
    @Operation(summary = "更新敏感词", description = "更新敏感词")
    public Result<Boolean> update(@RequestBody SensitiveWord word) {
        log.info("更新敏感词: {}", word);
        if (word.getId() == null) {
            return Result.fail("敏感词ID不能为空");
        }

        // 确保必要字段不为空
        if (word.getWord() == null || word.getWord().trim().isEmpty()) {
            return Result.fail("敏感词内容不能为空");
        }

        if (word.getType() == null || word.getType().trim().isEmpty()) {
            return Result.fail("敏感词类型不能为空");
        }

        if (word.getLevel() == null) {
            return Result.fail("敏感词级别不能为空");
        }

        // 如果状态为空，设置默认值
        if (word.getStatus() == null) {
            word.setStatus(true);
        }

        try {
            boolean result = sensitiveWordService.updateWord(word);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新敏感词失败: {}", e.getMessage(), e);
            return Result.fail("更新敏感词失败: " + e.getMessage());
        }
    }

    /**
     * 删除敏感词
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除敏感词", description = "删除敏感词")
    public Result<Boolean> delete(@Parameter(description = "敏感词ID") @PathVariable Long id) {
        boolean result = sensitiveWordService.deleteWord(id);
        return Result.success(result);
    }

    /**
     * 批量删除敏感词
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除敏感词", description = "批量删除敏感词")
    public Result<Boolean> batchDelete(@RequestBody List<Long> ids) {
        boolean result = sensitiveWordService.batchDeleteWord(ids);
        return Result.success(result);
    }

    /**
     * 检查文本是否包含敏感词
     */
    @PostMapping("/check")
    @Operation(summary = "检查文本是否包含敏感词", description = "检查文本是否包含敏感词")
    public Result<Boolean> check(@Parameter(description = "文本") @RequestParam String text) {
        boolean result = sensitiveWordService.containsSensitiveWord(text);
        return Result.success(result);
    }

    /**
     * 获取文本中的敏感词
     */
    @PostMapping("/get")
    @Operation(summary = "获取文本中的敏感词", description = "获取文本中的敏感词")
    public Result<Set<String>> get(@Parameter(description = "文本") @RequestParam String text) {
        Set<String> words = sensitiveWordService.getSensitiveWords(text);
        return Result.success(words);
    }

    /**
     * 替换文本中的敏感词
     */
    @PostMapping("/replace")
    @Operation(summary = "替换文本中的敏感词", description = "替换文本中的敏感词")
    public Result<String> replace(
            @Parameter(description = "文本") @RequestParam String text,
            @Parameter(description = "替换字符") @RequestParam(required = false) Character replaceChar) {
        String result;
        if (replaceChar != null) {
            result = sensitiveWordService.replaceSensitiveWords(text, replaceChar);
        } else {
            result = sensitiveWordService.replaceSensitiveWords(text);
        }
        return Result.success(result);
    }

    /**
     * 刷新敏感词库
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新敏感词库", description = "刷新敏感词库")
    public Result<Boolean> refreshSensitiveWordMap() {
        sensitiveWordService.refreshSensitiveWordMap();
        return Result.success(true);
    }

    /**
     * 获取敏感词类型列表
     */
    @GetMapping("/types")
    @Operation(summary = "获取敏感词类型列表", description = "获取敏感词类型列表")
    public Result<List<String>> getTypes() {
        List<String> types = sensitiveWordService.getSensitiveWordTypes();
        return Result.success(types);
    }

    /**
     * 获取敏感词统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取敏感词统计信息", description = "获取敏感词统计信息")
    public Result<SensitiveWordStatVO> getStats() {
        SensitiveWordStatVO stats = sensitiveWordService.getSensitiveWordStats();
        return Result.success(stats);
    }

    /**
     * 从文件导入敏感词
     */
    @PostMapping("/import")
    @Operation(summary = "从文件导入敏感词", description = "从多种格式文件导入敏感词")
    public Result<SensitiveWordImportResultVO> importFromFile(@RequestParam("file") MultipartFile file) {
        log.info("从文件导入敏感词: {}", file.getOriginalFilename());
        try {
            SensitiveWordImportResultVO result = sensitiveWordImportService.importFromFile(file);
            return Result.success(result);
        } catch (Exception e) {
            log.error("从文件导入敏感词失败: {}", e.getMessage(), e);
            return Result.fail("导入失败: " + e.getMessage());
        }
    }

    /**
     * 从目录导入敏感词
     */
    @PostMapping("/import/directory")
    @Operation(summary = "从目录导入敏感词", description = "从指定目录导入敏感词文件")
    public Result<SensitiveWordImportResultVO> importFromDirectory(
            @Parameter(description = "目录路径，默认为Sensitive-lexicon")
            @RequestParam(required = false) String directoryPath) {
        log.info("从目录导入敏感词，目录路径: {}", directoryPath);

        SensitiveWordImportResultVO result;
        if (directoryPath != null && !directoryPath.isEmpty()) {
            result = sensitiveWordImportService.importSensitiveWords(directoryPath);
        } else {
            result = sensitiveWordImportService.importSensitiveWords();
        }

        return Result.success(result);
    }
}
