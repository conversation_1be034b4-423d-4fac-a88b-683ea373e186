package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.CommentAddRequest;
import com.phototagmoment.dto.CommentDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 评论控制器
 */
@Slf4j
@RestController
@RequestMapping("/comment")
@Tag(name = "评论接口", description = "评论相关接口")
@Validated
public class CommentController {

    @Autowired
    private CommentService commentService;

    @PostMapping
    @Operation(summary = "添加评论", description = "添加照片评论")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Long> addComment(
            @Parameter(description = "照片ID") @RequestParam @NotNull(message = "照片ID不能为空") Long photoId,
            @Parameter(description = "评论内容") @RequestParam @NotBlank(message = "评论内容不能为空") String content) {
        Long userId = SecurityUtil.getCurrentUserId();
        Long commentId = commentService.addComment(photoId, content, userId);
        return ApiResponse.success(commentId);
    }

    @PostMapping("/add")
    @Operation(summary = "添加评论(完整版)", description = "支持回复、标签、用户提及的评论添加")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Long> addCommentComplete(
            @Parameter(description = "评论参数") @RequestBody CommentAddRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        Long commentId = commentService.addCommentComplete(request, userId);
        return ApiResponse.success(commentId);
    }

    @PostMapping("/reply")
    @Operation(summary = "回复评论", description = "回复评论")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Long> replyComment(
            @Parameter(description = "评论ID") @RequestParam @NotNull(message = "评论ID不能为空") Long commentId,
            @Parameter(description = "回复内容") @RequestParam @NotBlank(message = "回复内容不能为空") String content,
            @Parameter(description = "回复的用户ID") @RequestParam(required = false) Long replyToUserId) {
        Long userId = SecurityUtil.getCurrentUserId();
        Long replyId = commentService.replyComment(commentId, content, userId, replyToUserId);
        return ApiResponse.success(replyId);
    }

    @GetMapping("/{commentId}")
    @Operation(summary = "获取评论详情", description = "根据评论ID获取评论详情")
    public ApiResponse<CommentDTO> getCommentDetail(
            @Parameter(description = "评论ID") @PathVariable Long commentId) {
        Long userId = SecurityUtil.getCurrentUserId();
        CommentDTO commentDTO = commentService.getCommentDetail(commentId, userId);
        return ApiResponse.success(commentDTO);
    }

    @GetMapping("/photo/{photoId}")
    @Operation(summary = "获取照片评论列表", description = "分页获取照片评论列表")
    public ApiResponse<IPage<CommentDTO>> getPhotoComments(
            @Parameter(description = "照片ID") @PathVariable Long photoId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        Long userId = SecurityUtil.getCurrentUserId();
        IPage<CommentDTO> commentPage = commentService.getPhotoComments(photoId, page, size, userId);
        return ApiResponse.success(commentPage);
    }

    @GetMapping("/replies/{commentId}")
    @Operation(summary = "获取评论回复列表", description = "分页获取评论回复列表")
    public ApiResponse<IPage<CommentDTO>> getCommentReplies(
            @Parameter(description = "评论ID") @PathVariable Long commentId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        Long userId = SecurityUtil.getCurrentUserId();
        IPage<CommentDTO> replyPage = commentService.getCommentReplies(commentId, page, size, userId);
        return ApiResponse.success(replyPage);
    }

    @PostMapping("/like/{commentId}")
    @Operation(summary = "点赞评论", description = "点赞评论")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> likeComment(
            @Parameter(description = "评论ID") @PathVariable Long commentId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = commentService.likeComment(commentId, userId);
        return ApiResponse.success(result);
    }

    @PostMapping("/unlike/{commentId}")
    @Operation(summary = "取消点赞评论", description = "取消点赞评论")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> unlikeComment(
            @Parameter(description = "评论ID") @PathVariable Long commentId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = commentService.unlikeComment(commentId, userId);
        return ApiResponse.success(result);
    }

    @DeleteMapping("/{commentId}")
    @Operation(summary = "删除评论", description = "删除评论")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> deleteComment(
            @Parameter(description = "评论ID") @PathVariable Long commentId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = commentService.deleteComment(commentId, userId);
        return ApiResponse.success(result);
    }
}
