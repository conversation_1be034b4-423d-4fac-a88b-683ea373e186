import request from '@/utils/request'

/**
 * 通知类型
 */
export enum NotificationType {
  FOLLOW = 'follow',
  LIKE = 'like',
  COMMENT = 'comment',
  REPLY = 'reply',
  SYSTEM = 'system'
}

/**
 * 通知对象类型
 */
export enum NotificationTargetType {
  PHOTO = 'photo',
  COMMENT = 'comment',
  USER = 'user'
}

/**
 * 通知对象
 */
export interface NotificationTarget {
  id: number;
  type: NotificationTargetType;
  title?: string;
  thumbnailUrl?: string;
}

/**
 * 通知发送者
 */
export interface NotificationSender {
  id: number;
  nickname: string;
  avatar: string;
}

/**
 * 通知
 */
export interface Notification {
  id: number;
  type: NotificationType;
  content: string;
  sender?: NotificationSender;
  target?: NotificationTarget;
  isRead: boolean;
  createTime: string;
}

/**
 * 获取通知列表
 * @param params 查询参数
 * @returns 通知列表
 */
export function getNotifications(params: {
  page?: number;
  size?: number;
  type?: NotificationType | null;
}) {
  // 获取token
  const token = localStorage.getItem('token');
  console.log('getNotifications API调用，当前token:', token);

  if (!token) {
    console.warn('没有token，无法获取通知列表');
    return Promise.reject(new Error('暂未登录或token已经过期'));
  }

  return request({
    url: '/notification/list',
    method: 'get',
    params,
    headers: { 'Authorization': `Bearer ${token}` },
    timeout: 5000, // 设置超时时间为5秒
    // 添加错误处理
    validateStatus: function (status) {
      // 对于401错误，我们在响应拦截器中处理
      // 这里只处理其他错误
      return (status >= 200 && status < 300) || status === 401;
    }
  })
}

/**
 * 获取未读通知数量
 * @returns 未读通知数量
 */
export function getUnreadCount() {
  // 获取token
  const token = localStorage.getItem('token');
  console.log('getUnreadCount API调用，当前token:', token);

  if (!token) {
    console.warn('没有token，无法获取未读通知数量');
    return Promise.resolve({ code: 200, data: 0 });
  }

  return request({
    url: '/notification/unread/count',
    method: 'get',
    headers: { 'Authorization': `Bearer ${token}` },
    timeout: 5000, // 设置超时时间为5秒
    // 添加错误处理
    validateStatus: function (status) {
      // 对于401错误，我们在响应拦截器中处理
      // 这里只处理其他错误
      return (status >= 200 && status < 300) || status === 401;
    }
  })
}

/**
 * 标记通知为已读
 * @param id 通知ID
 * @returns 是否成功
 */
export function markAsRead(id: number) {
  // 获取token
  const token = localStorage.getItem('token');
  console.log('markAsRead API调用，当前token:', token);

  if (!token) {
    console.warn('没有token，无法标记通知为已读');
    return Promise.reject(new Error('暂未登录或token已经过期'));
  }

  return request({
    url: `/notification/read/${id}`,
    method: 'post',
    headers: { 'Authorization': `Bearer ${token}` },
    timeout: 5000, // 设置超时时间为5秒
    // 添加错误处理
    validateStatus: function (status) {
      // 对于401错误，我们在响应拦截器中处理
      // 这里只处理其他错误
      return (status >= 200 && status < 300) || status === 401;
    }
  })
}

/**
 * 标记所有通知为已读
 * @returns 是否成功
 */
export function markAllAsRead() {
  // 获取token
  const token = localStorage.getItem('token');
  console.log('markAllAsRead API调用，当前token:', token);

  if (!token) {
    console.warn('没有token，无法标记所有通知为已读');
    return Promise.reject(new Error('暂未登录或token已经过期'));
  }

  return request({
    url: '/notification/read/all',
    method: 'post',
    headers: { 'Authorization': `Bearer ${token}` },
    timeout: 5000, // 设置超时时间为5秒
    // 添加错误处理
    validateStatus: function (status) {
      // 对于401错误，我们在响应拦截器中处理
      // 这里只处理其他错误
      return (status >= 200 && status < 300) || status === 401;
    }
  })
}

/**
 * 删除通知
 * @param id 通知ID
 * @returns 是否成功
 */
export function deleteNotification(id: number) {
  // 获取token
  const token = localStorage.getItem('token');
  console.log('deleteNotification API调用，当前token:', token);

  if (!token) {
    console.warn('没有token，无法删除通知');
    return Promise.reject(new Error('暂未登录或token已经过期'));
  }

  return request({
    url: `/notification/${id}`,
    method: 'delete',
    headers: { 'Authorization': `Bearer ${token}` },
    timeout: 5000, // 设置超时时间为5秒
    // 添加错误处理
    validateStatus: function (status) {
      // 对于401错误，我们在响应拦截器中处理
      // 这里只处理其他错误
      return (status >= 200 && status < 300) || status === 401;
    }
  })
}
