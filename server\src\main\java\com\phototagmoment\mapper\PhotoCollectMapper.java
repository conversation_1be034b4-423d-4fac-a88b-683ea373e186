package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.PhotoCollect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 照片收藏Mapper接口
 */
@Mapper
@Repository
public interface PhotoCollectMapper extends BaseMapper<PhotoCollect> {

    /**
     * 统计照片的收藏数
     *
     * @param photoId 照片ID
     * @return 收藏数
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_collect WHERE photo_id = #{photoId}")
    int countByPhotoId(@Param("photoId") Long photoId);

    /**
     * 获取用户收藏的照片ID列表
     *
     * @param userId 用户ID
     * @return 照片ID列表
     */
    @Select("SELECT photo_id FROM ptm_photo_collect WHERE user_id = #{userId}")
    List<Long> getCollectedPhotoIds(@Param("userId") Long userId);

    /**
     * 获取照片的收藏用户ID列表
     *
     * @param photoId 照片ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM ptm_photo_collect WHERE photo_id = #{photoId}")
    List<Long> getCollectedUserIds(@Param("photoId") Long photoId);

    /**
     * 检查用户是否收藏了照片
     *
     * @param userId  用户ID
     * @param photoId 照片ID
     * @return 是否收藏
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_collect WHERE user_id = #{userId} AND photo_id = #{photoId}")
    int checkUserCollected(@Param("userId") Long userId, @Param("photoId") Long photoId);
}
