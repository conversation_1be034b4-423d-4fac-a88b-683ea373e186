server:
  port: 8082
  servlet:
    context-path: /api
  tomcat:
    uri-encoding: UTF-8

spring:
  application:
    name: PhotoTagMoment
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    lazy-initialization: false

  # 测试环境数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    hikari:
      minimum-idle: 3
      maximum-pool-size: 15
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000

  # 测试环境Redis配置
  data:
    redis:
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:123456}
      database: ${REDIS_DATABASE:7}
      timeout: 10s
      lettuce:
        pool:
          min-idle: 2
          max-idle: 10
          max-active: 15
          max-wait: -1ms

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000  # 30分钟
      cache-null-values: false

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 20MB
      max-request-size: 40MB

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    default-property-inclusion: non_null

  # Flyway配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    out-of-order: true
    clean-disabled: true
    table: flyway_schema_history

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.phototagmoment.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: AUTO
      table-prefix: ptm_
      logic-delete-field: is_deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 测试环境日志配置
logging:
  level:
    root: info
    com.phototagmoment: info
    org.apache.ibatis: warn
    com.baomidou.mybatisplus: warn
    com.phototagmoment.service.impl.SensitiveWordServiceImpl: warn
    org.springframework.security: info
    org.springframework.web: info
  file:
    name: logs/phototagmoment-test.log
    max-size: 50MB
    max-history: 10

# JWT配置
jwt:
  secret: ${JWT_SECRET:phototagmoment_test_secret_key_for_jwt_token_generation_and_validation}
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer

# 加密配置
encryption:
  enabled: true
  user-data-key: ${ENCRYPTION_USER_DATA_KEY:test_user_data_key}
  photo-key: ${ENCRYPTION_PHOTO_KEY:test_photo_key}
  rsa-public-key: ${ENCRYPTION_RSA_PUBLIC_KEY:test_rsa_public_key}
  rsa-private-key: ${ENCRYPTION_RSA_PRIVATE_KEY:test_rsa_private_key}
  encrypted-user-fields: phone,email,realName,idCard

# Knife4j配置 - 测试环境启用但需要认证
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-version: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Copyright © 2023 PhotoTagMoment
  production: false
  basic:
    enable: true
    username: ${API_DOC_USERNAME:admin}
    password: ${API_DOC_PASSWORD:test123}
  openapi:
    title: PhotoTagMoment API (测试环境)
    description: PhotoTagMoment照片社交网站API文档 - 测试环境
    email: <EMAIL>
    concat: PhotoTagMoment Test Team
    url: http://test.phototagmoment.com
    version: v1.0.0-test
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html

# OpenAPI配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.phototagmoment.controller
  group-configs:
    - group: 用户接口
      paths-to-match: /user/**, /auth/**, /photo/**, /tag/**, /search/**, /recommendation/**
      packages-to-scan: com.phototagmoment.controller
    - group: 管理接口
      paths-to-match: /admin/**
      packages-to-scan: com.phototagmoment.controller.admin

# 存储配置
storage:
  local:
    path: ./uploads/test
