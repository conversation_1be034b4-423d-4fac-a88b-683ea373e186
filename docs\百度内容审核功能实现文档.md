# 百度内容审核功能实现文档

## 1. 功能概述

本文档详细说明了使用百度内容审核平台SDK实现图像内容审核功能的实现方式和使用方法。该功能主要用于对用户上传的图片进行内容审核，识别并过滤不良内容，保障平台内容的健康与合规。

## 2. 技术架构

### 2.1 技术选型

- **审核服务提供商**：百度内容审核平台
- **SDK**：百度AIP内容审核SDK (java-sdk 4.16.8)
- **接口**：百度图像审核API、文本审核API
- **实现方式**：Spring Boot服务集成

### 2.2 系统架构

```
用户上传图片 -> 保存到数据库(状态为待审核) -> 异步提交审核任务 -> 百度内容审核服务 -> 
根据审核结果更新图片状态 -> 通知用户审核结果
```

## 3. 实现细节

### 3.1 依赖配置

在`pom.xml`中添加百度内容审核SDK依赖：

```xml
<!-- Baidu Content Moderation SDK -->
<dependency>
    <groupId>com.baidu.aip</groupId>
    <artifactId>java-sdk</artifactId>
    <version>4.16.8</version>
</dependency>
```

### 3.2 配置参数

在`application.yml`中配置百度内容审核服务参数：

```yaml
# 内容审核配置
content-moderation:
  enabled: true
  provider: baidu  # 设置为baidu启用百度内容审核
  # 审核模式: auto-自动审核, manual-人工审核, mixed-混合审核
  mode: mixed
  # 自动审核敏感度: 0-100，值越大越严格
  sensitivity: 80
  baidu:
    app-id: ${CONTENT_MODERATION_BAIDU_APP_ID:}
    api-key: ${CONTENT_MODERATION_BAIDU_API_KEY:}
    secret-key: ${CONTENT_MODERATION_BAIDU_SECRET_KEY:}
```

### 3.3 服务实现

创建`BaiduContentModerationServiceImpl`类实现`ContentModerationService`接口：

```java
@Slf4j
@Service
@ConditionalOnProperty(name = "content-moderation.provider", havingValue = "baidu")
public class BaiduContentModerationServiceImpl implements ContentModerationService {
    // 实现代码详见源文件
}
```

主要实现了以下功能：

1. **初始化百度内容审核客户端**：使用配置的AppID、API Key和Secret Key初始化客户端
2. **图片审核**：支持通过文件、输入流和URL三种方式提交图片进行审核
3. **文本审核**：支持对文本内容进行审核
4. **结果解析**：解析百度内容审核API返回的结果，判断内容是否通过审核

### 3.4 审核流程

1. **图片上传**：用户上传图片时，系统先将图片保存到数据库，状态设为"待审核"
2. **异步审核**：通过异步任务队列提交图片进行内容审核
3. **审核处理**：
   - 自动审核：调用百度内容审核API进行审核
   - 人工审核：管理员在后台进行人工审核
   - 混合审核：先进行自动审核，不确定的情况下转入人工审核队列
4. **状态更新**：根据审核结果更新图片状态
   - 通过：状态设为"正常"
   - 不通过：状态设为"审核拒绝"，记录拒绝原因
5. **通知用户**：通过系统消息通知用户审核结果

## 4. 使用方法

### 4.1 配置百度内容审核服务

1. 注册百度AI开放平台账号：https://ai.baidu.com/
2. 创建内容审核应用，获取AppID、API Key和Secret Key
3. 在`application.yml`中配置相关参数，或设置环境变量：
   - `CONTENT_MODERATION_BAIDU_APP_ID`
   - `CONTENT_MODERATION_BAIDU_API_KEY`
   - `CONTENT_MODERATION_BAIDU_SECRET_KEY`

### 4.2 启用百度内容审核

修改`application.yml`中的配置：

```yaml
content-moderation:
  enabled: true
  provider: baidu
```

### 4.3 调用示例

在需要进行内容审核的服务中注入`ContentModerationService`：

```java
@Autowired
private ContentModerationService contentModerationService;

// 审核图片
boolean passed = contentModerationService.moderateImage(file);
if (!passed) {
    String reason = contentModerationService.getFailReason();
    log.warn("图片审核不通过：{}", reason);
}

// 审核图片URL
boolean passed = contentModerationService.moderateImageByUrl(imageUrl);

// 审核文本
boolean passed = contentModerationService.moderateText(text);
```

## 5. 审核结果处理

### 5.1 审核状态

照片状态定义：
- **待审核(0)**：照片刚上传，等待审核
- **正常(1)**：审核通过，可正常显示
- **审核拒绝(2)**：审核不通过，仅创建者可见
- **已删除(3)**：照片已被删除

### 5.2 违规内容处理

对于审核不通过的内容：
1. 更新照片状态为"审核拒绝"
2. 记录拒绝原因
3. 限制照片可见性，仅创建者可见
4. 通知用户审核结果和拒绝原因

### 5.3 人工复审

对于自动审核不确定或用户申诉的情况：
1. 管理员可在后台查看待审核的照片
2. 进行人工审核，决定是否通过
3. 更新照片状态和审核记录

## 6. 注意事项

1. **API限制**：百度内容审核API有调用频率和次数限制，请合理控制调用频率
2. **敏感度设置**：根据业务需求调整审核敏感度，避免误判
3. **异常处理**：做好异常处理，确保审核服务不可用时不影响用户上传功能
4. **安全性**：妥善保管API密钥，避免泄露
5. **合规性**：确保使用符合相关法律法规和百度平台规定

## 7. 参考资料

- [百度内容审核平台官方文档](https://cloud.baidu.com/doc/ANTIPORN/s/Jk3h6x8t2)
- [百度AIP SDK文档](https://ai.baidu.com/ai-doc/REFERENCE/Ck3dwjhhu)
- [百度图像审核API文档](https://ai.baidu.com/ai-doc/ANTIPORN/Nk3h6xbb2)
- [百度文本审核API文档](https://ai.baidu.com/ai-doc/ANTIPORN/Rk3h6xdua)
