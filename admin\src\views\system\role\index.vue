<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="queryParams.keyword"
        placeholder="请输入角色名称或编码"
        style="width: 200px;"
        class="filter-item"
        clearable
        @keyup.enter="handleSearch"
      />
      <el-button type="primary" class="filter-item" @click="handleSearch">
        <el-icon><Search /></el-icon>
        搜索
      </el-button>
      <el-button type="primary" @click="handleCreate">
        <el-icon><Plus /></el-icon>
        新增角色
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="roleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="角色名称" min-width="120" />
      <el-table-column prop="code" label="角色编码" min-width="120" />
      <el-table-column prop="description" label="描述" min-width="200" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" min-width="150" />
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="scope">
          <el-button
            v-if="scope.row.isSystem !== 1"
            type="primary"
            link
            @click="handleUpdate(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="success"
            link
            @click="handlePermission(scope.row)"
          >
            权限
          </el-button>
          <el-button
            v-if="scope.row.isSystem !== 1"
            type="danger"
            link
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
          <el-button
            v-if="scope.row.isSystem === 1"
            type="info"
            link
            disabled
          >
            系统内置
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 角色表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog
      title="分配权限"
      v-model="permissionDialogVisible"
      width="600px"
      append-to-body
    >
      <el-form label-width="80px">
        <el-form-item label="角色名称">
          <span>{{ currentRole.name }}</span>
        </el-form-item>
        <el-form-item label="权限列表">
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTree"
            :props="{ label: 'name', children: 'children' }"
            show-checkbox
            node-key="id"
            default-expand-all
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPermission">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import {
  getRoleList,
  getRoleDetail,
  createRole,
  updateRole,
  deleteRole,
  updateRoleStatus,
  getRolePermissions,
  assignRolePermissions
} from '@/api/system/role'
import { getPermissionTree } from '@/api/system/permission'

// 角色列表
const roleList = ref([])
// 总记录数
const total = ref(0)
// 加载状态
const loading = ref(false)
// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  keyword: ''
})
// 对话框标题
const dialogTitle = ref('新增角色')
// 对话框可见性
const dialogVisible = ref(false)
// 权限对话框可见性
const permissionDialogVisible = ref(false)
// 表单引用
const formRef = ref()
// 权限树引用
const permissionTreeRef = ref()
// 表单数据
const form = reactive({
  id: undefined,
  name: '',
  code: '',
  description: '',
  status: 1
})
// 当前角色
const currentRole = reactive({
  id: undefined,
  name: ''
})
// 权限树
const permissionTree = ref([])
// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
}

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true
  try {
    const { data } = await getRoleList({
      page: queryParams.page,
      pageSize: queryParams.pageSize,
      keyword: queryParams.keyword
    })
    roleList.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('获取角色列表失败', error)
    ElMessage.error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

// 获取权限树
const fetchPermissionTree = async () => {
  try {
    const { data } = await getPermissionTree()
    permissionTree.value = data
  } catch (error) {
    console.error('获取权限树失败', error)
    ElMessage.error('获取权限树失败')
  }
}

// 重置表单
const resetForm = () => {
  form.id = undefined
  form.name = ''
  form.code = ''
  form.description = ''
  form.status = 1
  formRef.value?.resetFields()
}

// 处理搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchRoleList()
}

// 处理页码变更
const handleCurrentChange = (page) => {
  queryParams.page = page
  fetchRoleList()
}

// 处理每页条数变更
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.page = 1
  fetchRoleList()
}

// 处理新增角色
const handleCreate = () => {
  resetForm()
  dialogTitle.value = '新增角色'
  dialogVisible.value = true
}

// 处理编辑角色
const handleUpdate = async (row) => {
  resetForm()
  dialogTitle.value = '编辑角色'
  
  try {
    const { data } = await getRoleDetail(row.id)
    Object.assign(form, data)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取角色详情失败', error)
    ElMessage.error('获取角色详情失败')
  }
}

// 处理删除角色
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该角色吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteRole(row.id)
      ElMessage.success('删除成功')
      fetchRoleList()
    } catch (error) {
      console.error('删除角色失败', error)
      ElMessage.error('删除角色失败')
    }
  }).catch(() => {})
}

// 处理状态变更
const handleStatusChange = async (row) => {
  try {
    await updateRoleStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('更新角色状态失败', error)
    ElMessage.error('更新角色状态失败')
    // 恢复状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 处理权限分配
const handlePermission = async (row) => {
  currentRole.id = row.id
  currentRole.name = row.name
  
  try {
    // 获取权限树
    await fetchPermissionTree()
    
    // 获取角色已有权限
    const { data } = await getRolePermissions(row.id)
    
    // 设置选中节点
    permissionTreeRef.value?.setCheckedKeys(data)
    
    permissionDialogVisible.value = true
  } catch (error) {
    console.error('获取角色权限失败', error)
    ElMessage.error('获取角色权限失败')
  }
}

// 提交表单
const submitForm = async () => {
  formRef.value?.validate(async (valid) => {
    if (!valid) return
    
    try {
      if (form.id) {
        // 更新角色
        await updateRole(form.id, form)
        ElMessage.success('更新成功')
      } else {
        // 创建角色
        await createRole(form)
        ElMessage.success('创建成功')
      }
      dialogVisible.value = false
      fetchRoleList()
    } catch (error) {
      console.error('保存角色失败', error)
      ElMessage.error('保存角色失败')
    }
  })
}

// 提交权限分配
const submitPermission = async () => {
  try {
    // 获取选中的权限ID
    const permissionIds = permissionTreeRef.value?.getCheckedKeys()
    
    // 分配权限
    await assignRolePermissions(currentRole.id, permissionIds)
    
    ElMessage.success('权限分配成功')
    permissionDialogVisible.value = false
  } catch (error) {
    console.error('权限分配失败', error)
    ElMessage.error('权限分配失败')
  }
}

// 页面加载时获取角色列表
onMounted(() => {
  fetchRoleList()
})
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
