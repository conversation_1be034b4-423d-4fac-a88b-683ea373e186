<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传配置管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .config-list {
            margin-top: 20px;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .config-info {
            flex: 1;
        }
        .config-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .config-details {
            color: #666;
            font-size: 12px;
        }
        .config-actions {
            display: flex;
            gap: 5px;
        }
        .config-actions button {
            padding: 5px 10px;
            font-size: 12px;
            margin: 0;
        }
        .form-row {
            display: flex;
            gap: 20px;
        }
        .form-col {
            flex: 1;
        }
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        .status-enabled {
            background-color: #d4edda;
            color: #155724;
        }
        .status-disabled {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-default {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PhotoTagMoment 文件上传配置管理测试</h1>
        
        <!-- 创建配置表单 -->
        <h2>创建配置</h2>
        <div class="form-row">
            <div class="form-col">
                <div class="form-group">
                    <label for="configName">配置名称:</label>
                    <input type="text" id="configName" placeholder="请输入配置名称">
                </div>
            </div>
            <div class="form-col">
                <div class="form-group">
                    <label for="storageType">存储类型:</label>
                    <select id="storageType">
                        <option value="LOCAL">本地存储</option>
                        <option value="QINIU">七牛云</option>
                        <option value="ALIYUN_OSS">阿里云OSS</option>
                        <option value="TENCENT_COS">腾讯云COS</option>
                        <option value="AWS_S3">AWS S3</option>
                        <option value="MINIO">MinIO</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="form-row">
            <div class="form-col">
                <div class="form-group">
                    <label for="enabled">启用状态:</label>
                    <select id="enabled">
                        <option value="true">启用</option>
                        <option value="false">禁用</option>
                    </select>
                </div>
            </div>
            <div class="form-col">
                <div class="form-group">
                    <label for="isDefault">设为默认:</label>
                    <select id="isDefault">
                        <option value="false">否</option>
                        <option value="true">是</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="description">配置描述:</label>
            <textarea id="description" rows="3" placeholder="请输入配置描述"></textarea>
        </div>

        <div class="form-group">
            <button onclick="createConfig()" id="createBtn">创建配置</button>
            <button onclick="loadConfigList()">刷新列表</button>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="getStatistics()">获取统计信息</button>
        </div>

        <div id="result"></div>
    </div>

    <div class="container">
        <h2>配置列表</h2>
        <div id="configList" class="config-list"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        // 模拟管理员token（实际使用时需要从登录获取）
        const ADMIN_TOKEN = 'your-admin-token-here';

        // 页面加载时获取配置列表
        window.onload = function() {
            loadConfigList();
        };

        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
        }

        async function testAPI() {
            showResult('正在测试API连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/admin/file-upload-config/statistics`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`API连接成功！\n响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`API连接失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult(`API连接错误: ${error.message}`, 'error');
            }
        }

        async function createConfig() {
            const configName = document.getElementById('configName').value;
            const storageType = document.getElementById('storageType').value;
            const enabled = document.getElementById('enabled').value === 'true';
            const isDefault = document.getElementById('isDefault').value === 'true';
            const description = document.getElementById('description').value;

            if (!configName) {
                showResult('请输入配置名称', 'error');
                return;
            }

            const configData = {
                configName,
                storageType,
                enabled,
                isDefault,
                description,
                configParams: {
                    domain: 'http://localhost:8080',
                    useHttps: false,
                    connectTimeout: 30,
                    readTimeout: 60
                },
                uploadLimits: {
                    maxFileSize: 50,
                    maxFileCount: 10,
                    allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
                    enableFileTypeCheck: true,
                    enableContentCheck: false,
                    enableVirusScan: false
                },
                pathConfig: {
                    rootPath: 'uploads',
                    fileNamingRule: 'UUID',
                    directoryStructure: 'DATE_USER_TYPE',
                    enableDateDirectory: true,
                    enableUserDirectory: true,
                    enableTypeDirectory: true,
                    thumbnailDirectory: 'thumbnails',
                    tempDirectory: 'temp'
                }
            };

            const createBtn = document.getElementById('createBtn');
            createBtn.disabled = true;
            createBtn.textContent = '创建中...';

            try {
                showResult('正在创建配置...', 'info');

                const response = await fetch(`${API_BASE}/admin/file-upload-config`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(configData)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`配置创建成功！\n配置ID: ${result.data}\n响应数据: ${JSON.stringify(result, null, 2)}`, 'success');
                    
                    // 清空表单
                    document.getElementById('configName').value = '';
                    document.getElementById('description').value = '';
                    
                    // 刷新配置列表
                    loadConfigList();
                } else {
                    const error = await response.text();
                    showResult(`配置创建失败！\n状态码: ${response.status}\n错误信息: ${error}`, 'error');
                }
            } catch (error) {
                showResult(`创建配置失败: ${error.message}`, 'error');
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = '创建配置';
            }
        }

        async function loadConfigList() {
            try {
                const response = await fetch(`${API_BASE}/admin/file-upload-config/list?page=1&size=20`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    displayConfigList(result.data.records);
                } else {
                    showResult(`获取配置列表失败！\n状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`获取配置列表失败: ${error.message}`, 'error');
            }
        }

        function displayConfigList(configs) {
            const configList = document.getElementById('configList');
            
            if (!configs || configs.length === 0) {
                configList.innerHTML = '<p>暂无配置</p>';
                return;
            }

            let html = '';
            configs.forEach(config => {
                const statusClass = config.enabled ? 'status-enabled' : 'status-disabled';
                const statusText = config.enabled ? '启用' : '禁用';
                
                html += `
                    <div class="config-item">
                        <div class="config-info">
                            <div class="config-name">
                                ${config.configName}
                                ${config.isDefault ? '<span class="status-badge status-default">默认</span>' : ''}
                            </div>
                            <div class="config-details">
                                存储类型: ${getStorageTypeLabel(config.storageType)} | 
                                状态: <span class="status-badge ${statusClass}">${statusText}</span> | 
                                创建时间: ${formatDateTime(config.createdAt)}
                                ${config.description ? ' | ' + config.description : ''}
                            </div>
                        </div>
                        <div class="config-actions">
                            <button onclick="testConfig(${config.id})">测试</button>
                            <button onclick="toggleConfig(${config.id}, ${!config.enabled})">${config.enabled ? '禁用' : '启用'}</button>
                            ${!config.isDefault ? `<button onclick="setDefaultConfig(${config.id})">设为默认</button>` : ''}
                            ${!config.isDefault ? `<button onclick="deleteConfig(${config.id})" style="background-color: #dc3545;">删除</button>` : ''}
                        </div>
                    </div>
                `;
            });
            
            configList.innerHTML = html;
        }

        async function testConfig(configId) {
            try {
                showResult(`正在测试配置 ${configId}...`, 'info');
                
                const response = await fetch(`${API_BASE}/admin/file-upload-config/${configId}/test`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const testResult = result.data;
                    showResult(`配置测试完成！\n结果: ${testResult.success ? '成功' : '失败'}\n消息: ${testResult.message}\n响应时间: ${testResult.responseTime}ms`, testResult.success ? 'success' : 'error');
                    loadConfigList(); // 刷新列表以更新测试结果
                } else {
                    showResult(`测试配置失败！\n状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`测试配置失败: ${error.message}`, 'error');
            }
        }

        async function toggleConfig(configId, enabled) {
            try {
                const response = await fetch(`${API_BASE}/admin/file-upload-config/${configId}/toggle?enabled=${enabled}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`配置状态更新成功！\n${result.message}`, 'success');
                    loadConfigList();
                } else {
                    showResult(`更新配置状态失败！\n状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`更新配置状态失败: ${error.message}`, 'error');
            }
        }

        async function setDefaultConfig(configId) {
            try {
                const response = await fetch(`${API_BASE}/admin/file-upload-config/${configId}/set-default`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`设置默认配置成功！\n${result.message}`, 'success');
                    loadConfigList();
                } else {
                    showResult(`设置默认配置失败！\n状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`设置默认配置失败: ${error.message}`, 'error');
            }
        }

        async function deleteConfig(configId) {
            if (!confirm('确定要删除这个配置吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/admin/file-upload-config/${configId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`配置删除成功！\n${result.message}`, 'success');
                    loadConfigList();
                } else {
                    showResult(`删除配置失败！\n状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`删除配置失败: ${error.message}`, 'error');
            }
        }

        async function getStatistics() {
            try {
                const response = await fetch(`${API_BASE}/admin/file-upload-config/statistics`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`统计信息：\n${JSON.stringify(result.data, null, 2)}`, 'success');
                } else {
                    showResult(`获取统计信息失败！\n状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`获取统计信息失败: ${error.message}`, 'error');
            }
        }

        // 工具函数
        function getStorageTypeLabel(storageType) {
            const labelMap = {
                LOCAL: '本地存储',
                QINIU: '七牛云',
                ALIYUN_OSS: '阿里云OSS',
                TENCENT_COS: '腾讯云COS',
                AWS_S3: 'AWS S3',
                MINIO: 'MinIO'
            };
            return labelMap[storageType] || '未知';
        }

        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        }
    </script>
</body>
</html>
