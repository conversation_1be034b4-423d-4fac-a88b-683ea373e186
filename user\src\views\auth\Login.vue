<template>
  <div class="login-container">
    <div class="login-header">
      <h2>登录</h2>
      <p>欢迎回来，请登录您的账号</p>
    </div>

    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          v-model="username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请输入用户名' }]"
        />
        <van-field
          v-model="password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请输入密码' }]"
        />
      </van-cell-group>

      <div class="form-actions">
        <van-button round block type="primary" native-type="submit" :loading="loading">
          登录
        </van-button>
      </div>
    </van-form>

    <div class="login-options">
      <div class="divider">
        <span>其他登录方式</span>
      </div>

      <div class="social-login">
        <WechatLogin />
        <QQLogin />
      </div>

      <div class="register-link">
        <span>还没有账号？</span>
        <router-link to="/register">立即注册</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { login } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'
import WechatLogin from '@/components/WechatLogin.vue'
import QQLogin from '@/components/QQLogin.vue'

const router = useRouter()
const userStore = useUserStore()

const username = ref('')
const password = ref('')
const loading = ref(false)

// 提交表单
const onSubmit = async () => {
  try {
    loading.value = true

    console.log('开始登录请求，用户名:', username.value);

    // 调用登录接口
    const res = await login({
      username: username.value,
      password: password.value
    })

    console.log('登录响应:', res);

    if (res.code === 200 && res.data) {
      console.log('登录成功，获取到token:', res.data.token);
      console.log('登录成功，获取到用户信息:', res.data.user);

      // 保存登录信息
      userStore.setToken(res.data.token)
      userStore.setUser(res.data.user)

      // 跳转到首页
      router.push('/')
      showToast('登录成功')
    } else {
      console.error('登录失败，响应码:', res.code, '错误信息:', res.message);
      showToast(res.message || '登录失败，请检查用户名和密码')
    }
  } catch (error: any) {
    console.error('登录请求异常:', error);

    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }

    showToast(error.message || '登录失败，请稍后再试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  padding: 20px;
  max-width: 500px;
  margin: 0 auto;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  font-size: 24px;
  color: #323233;
  margin-bottom: 10px;
}

.login-header p {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.form-actions {
  margin: 20px 16px;
}

.login-options {
  margin-top: 30px;
}

.divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: #ebedf0;
}

.divider span {
  padding: 0 16px;
  font-size: 14px;
  color: #969799;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #969799;
}

.register-link a {
  color: #1989fa;
  text-decoration: none;
}
</style>
