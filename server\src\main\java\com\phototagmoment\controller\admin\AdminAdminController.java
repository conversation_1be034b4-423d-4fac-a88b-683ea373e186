package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.dto.AdminDTO;
import com.phototagmoment.dto.AdminPasswordDTO;
import com.phototagmoment.service.NewAdminService;
import com.phototagmoment.vo.AdminVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 管理员管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/admin")
@Tag(name = "管理员管理", description = "管理员管理相关接口")
public class AdminAdminController {

    @Autowired
    private NewAdminService adminService;

    /**
     * 分页查询管理员列表
     *
     * @param page    页码
     * @param pageSize 每页大小
     * @param keyword 关键字
     * @return 管理员列表
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询管理员列表", description = "分页查询管理员列表")
    public Result<IPage<AdminVO>> getAdminList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        log.info("分页查询管理员列表: page={}, pageSize={}, keyword={}", page, pageSize, keyword);
        IPage<AdminVO> adminPage = adminService.getAdminPage(page, pageSize, keyword);
        return Result.success(adminPage);
    }

    /**
     * 获取管理员详情
     *
     * @param id 管理员ID
     * @return 管理员详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取管理员详情", description = "获取管理员详情")
    public Result<AdminVO> getAdminDetail(@Parameter(description = "管理员ID") @PathVariable Long id) {
        log.info("获取管理员详情: id={}", id);
        AdminVO adminVO = adminService.getAdminInfo(id);
        return Result.success(adminVO);
    }

    /**
     * 创建管理员
     *
     * @param adminDTO 管理员信息
     * @return 管理员ID
     */
    @PostMapping
    @Operation(summary = "创建管理员", description = "创建新管理员")
    public Result<Long> createAdmin(@RequestBody @Valid AdminDTO adminDTO) {
        log.info("创建管理员: {}", adminDTO.getUsername());
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        Long adminId = adminService.createAdmin(adminDTO, currentAdmin.getId());
        return Result.success(adminId);
    }

    /**
     * 更新管理员信息
     *
     * @param id       管理员ID
     * @param adminDTO 管理员信息
     * @return 是否成功
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新管理员信息", description = "更新管理员信息")
    public Result<Boolean> updateAdmin(
            @Parameter(description = "管理员ID") @PathVariable Long id,
            @RequestBody @Valid AdminDTO adminDTO) {
        log.info("更新管理员信息: id={}, username={}", id, adminDTO.getUsername());
        boolean result = adminService.updateAdmin(id, adminDTO);
        return Result.success(result);
    }

    /**
     * 删除管理员
     *
     * @param id 管理员ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除管理员", description = "删除管理员")
    public Result<Boolean> deleteAdmin(@Parameter(description = "管理员ID") @PathVariable Long id) {
        log.info("删除管理员: id={}", id);
        boolean result = adminService.deleteAdmin(id);
        return Result.success(result);
    }

    /**
     * 修改管理员密码
     *
     * @param passwordDTO 密码信息
     * @return 是否成功
     */
    @PutMapping("/password")
    @Operation(summary = "修改管理员密码", description = "修改当前管理员密码")
    public Result<Boolean> updatePassword(@RequestBody @Valid AdminPasswordDTO passwordDTO) {
        log.info("修改管理员密码");
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        boolean result = adminService.updatePassword(currentAdmin.getId(), passwordDTO);
        return Result.success(result);
    }

    /**
     * 重置管理员密码
     *
     * @param id 管理员ID
     * @return 新密码
     */
    @PutMapping("/{id}/reset-password")
    @Operation(summary = "重置管理员密码", description = "重置管理员密码")
    public Result<String> resetPassword(@Parameter(description = "管理员ID") @PathVariable Long id) {
        log.info("重置管理员密码: id={}", id);
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        String newPassword = adminService.resetPassword(id, currentAdmin.getId());
        return Result.success(newPassword);
    }

    /**
     * 启用/禁用管理员
     *
     * @param id     管理员ID
     * @param status 状态（0-禁用，1-启用）
     * @return 是否成功
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "启用/禁用管理员", description = "启用或禁用管理员")
    public Result<Boolean> updateStatus(
            @Parameter(description = "管理员ID") @PathVariable Long id,
            @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam Integer status) {
        log.info("更新管理员状态: id={}, status={}", id, status);
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        boolean result = adminService.updateStatus(id, status, currentAdmin.getId());
        return Result.success(result);
    }
}
