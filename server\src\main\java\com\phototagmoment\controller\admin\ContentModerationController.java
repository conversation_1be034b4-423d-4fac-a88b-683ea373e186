package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.common.Result;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.service.ContentModerationService;
import com.phototagmoment.service.PhotoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 内容审核控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/content-moderation")
@Tag(name = "内容审核管理", description = "内容审核相关接口")
public class ContentModerationController {

    @Autowired
    private ContentModerationService contentModerationService;

    @Autowired
    private PhotoService photoService;

    /**
     * 审核图片
     *
     * @param file 图片文件
     * @return 审核结果
     */
    @PostMapping("/moderate-image")
    @Operation(summary = "审核图片", description = "上传图片进行内容审核")
    public Result<Boolean> moderateImage(@Parameter(description = "图片文件") @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.fail("文件不能为空");
        }

        boolean passed = contentModerationService.moderateImage(file);
        if (passed) {
            return Result.success(true, "图片审核通过");
        } else {
            String reason = contentModerationService.getFailReason();
            return Result.fail("图片审核不通过：" + reason);
        }
    }

    /**
     * 审核图片URL
     *
     * @param imageUrl 图片URL
     * @return 审核结果
     */
    @PostMapping("/moderate-image-url")
    @Operation(summary = "审核图片URL", description = "通过图片URL进行内容审核")
    public Result<Boolean> moderateImageByUrl(@Parameter(description = "图片URL") @RequestParam("imageUrl") String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return Result.fail("图片URL不能为空");
        }

        boolean passed = contentModerationService.moderateImageByUrl(imageUrl);
        if (passed) {
            return Result.success(true, "图片审核通过");
        } else {
            String reason = contentModerationService.getFailReason();
            return Result.fail("图片审核不通过：" + reason);
        }
    }

    /**
     * 审核文本
     *
     * @param text 文本内容
     * @return 审核结果
     */
    @PostMapping("/moderate-text")
    @Operation(summary = "审核文本", description = "对文本内容进行审核")
    public Result<Boolean> moderateText(@Parameter(description = "文本内容") @RequestParam("text") String text) {
        if (text == null || text.isEmpty()) {
            return Result.fail("文本内容不能为空");
        }

        boolean passed = contentModerationService.moderateText(text);
        if (passed) {
            return Result.success(true, "文本审核通过");
        } else {
            String reason = contentModerationService.getFailReason();
            return Result.fail("文本审核不通过：" + reason);
        }
    }

    /**
     * 获取待审核的照片列表
     *
     * @param page 页码
     * @param size 每页大小
     * @return 照片列表
     */
    @GetMapping("/pending-photos")
    @Operation(summary = "获取待审核的照片列表", description = "分页获取待审核的照片列表")
    public Result<IPage<Photo>> getPendingPhotos(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Page<Photo> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Photo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Photo::getStatus, 0)  // 0表示待审核
                .orderByDesc(Photo::getCreatedAt);
        IPage<Photo> photoPage = photoService.page(pageParam, queryWrapper);
        return Result.success(photoPage);
    }

    /**
     * 审核照片
     *
     * @param photoId 照片ID
     * @param status  审核状态（1：通过，2：拒绝）
     * @param reason  拒绝原因
     * @return 审核结果
     */
    @PostMapping("/review-photo")
    @Operation(summary = "审核照片", description = "管理员审核照片")
    public Result<Boolean> reviewPhoto(
            @Parameter(description = "照片ID") @RequestParam Long photoId,
            @Parameter(description = "审核状态（1：通过，2：拒绝）") @RequestParam Integer status,
            @Parameter(description = "拒绝原因") @RequestParam(required = false) String reason) {
        if (photoId == null) {
            return Result.fail("照片ID不能为空");
        }

        if (status == null || (status != 1 && status != 2)) {
            return Result.fail("审核状态不正确");
        }

        if (status == 2 && (reason == null || reason.isEmpty())) {
            return Result.fail("拒绝原因不能为空");
        }

        Photo photo = photoService.getById(photoId);
        if (photo == null) {
            return Result.fail("照片不存在");
        }

        photo.setStatus(status);
        photo.setRejectReason(status == 2 ? reason : null);
        photoService.updateById(photo);

        return Result.success(true, "审核成功");
    }
}
