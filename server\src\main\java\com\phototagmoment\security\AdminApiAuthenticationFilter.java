package com.phototagmoment.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 管理员API认证过滤器
 * 专门用于处理后台管理接口的认证
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class AdminApiAuthenticationFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        // 检查是否是后台管理接口
        if (requestURI.startsWith("/api/admin/system/")) {
            log.info("AdminApiAuthenticationFilter处理后台管理接口: {} {}", method, requestURI);
            
            // 设置一个标记，表示该请求已经被处理
            request.setAttribute("ADMIN_API_FILTER_APPLIED", Boolean.TRUE);
            
            // 直接放行，不进行认证
            filterChain.doFilter(request, response);
            return;
        }
        
        // 非后台管理接口，继续过滤链
        filterChain.doFilter(request, response);
    }
}
