package com.phototagmoment.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * JWT认证入口点
 * 当用户尝试访问需要认证的资源而未提供有效凭证时，将调用此类
 */
@Slf4j
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper;

    public JwtAuthenticationEntryPoint(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException)
            throws IOException {
        String requestURI = request.getRequestURI();

        // 检查是否是白名单请求
        if (requestURI.contains("/login") ||
            requestURI.contains("/auth/") ||
            requestURI.contains("/swagger") ||
            requestURI.contains("/v3/api-docs") ||
            requestURI.contains("/doc.html") ||
            requestURI.contains("/api/doc.html") ||
            requestURI.contains("/webjars") ||
            requestURI.contains("/search") ||
            requestURI.contains("/recommendation") ||
            requestURI.contains("/photo/list") ||
            requestURI.contains("/photo/detail") ||
            requestURI.contains("/tag") ||
            requestURI.contains("/user/profile") ||
            requestURI.contains("/user/photos") ||
            requestURI.contains("/user/collections") ||
            requestURI.contains("/dict") ||
            requestURI.contains("/notification")) {
            log.info("白名单请求，不处理认证异常: {}", requestURI);

            // 对于白名单请求，返回200 OK和空内容，而不是401 Unauthorized
            response.setStatus(HttpServletResponse.SC_OK);
            return;
        }

        log.error("Unauthorized error: {}", authException.getMessage());

        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        ApiResponse<Void> apiResponse = ApiResponse.failed(ResultCode.UNAUTHORIZED);

        response.getWriter().write(objectMapper.writeValueAsString(apiResponse));
    }
}
