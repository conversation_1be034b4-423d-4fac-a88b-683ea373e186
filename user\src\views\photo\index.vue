<template>
  <div class="photo-list-container">
    <van-nav-bar
      title="照片列表"
      left-arrow
      @click-left="goBack"
      fixed
    />

    <div class="photo-list-content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          :loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div class="photo-grid" v-if="photos.length > 0">
            <div
              v-for="photo in photos"
              :key="photo.id"
              class="photo-item"
              @click="goToPhotoDetail(photo.id)"
            >
              <div class="photo-card">
                <div class="photo-image">
                  <van-image
                    :src="photo.thumbnailUrl || photo.url"
                    :alt="photo.title"
                    fit="cover"
                    lazy-load
                  />
                </div>
                <div class="photo-info">
                  <div class="photo-title">{{ photo.title }}</div>
                  <div class="photo-user" @click.stop="goToUserProfile(photo.userId)">
                    <van-image
                      round
                      width="20"
                      height="20"
                      :src="photo.userAvatar || 'https://randomuser.me/api/portraits/men/1.jpg'"
                      :alt="photo.userName"
                    />
                    <span class="user-name">{{ photo.userName }}</span>
                  </div>
                  <div class="photo-stats">
                    <div class="stat-item">
                      <van-icon name="eye-o" />
                      <span>{{ photo.viewCount || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <van-icon name="like-o" />
                      <span>{{ photo.likeCount || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <van-icon name="chat-o" />
                      <span>{{ photo.commentCount || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="!loading" class="empty-state">
            <van-empty description="暂无照片" />
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';

const router = useRouter();
const route = useRoute();

// 照片列表
interface Photo {
  id: number;
  title: string;
  url: string;
  thumbnailUrl: string;
  userName: string;
  userAvatar: string;
  userId: number;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  uploadTime: string;
  tags: string[];
}

const photos = ref<Photo[]>([]);
const loading = ref(false);
const refreshing = ref(false);
const finished = ref(false);
const page = ref(1);
const pageSize = ref(20);

// 返回上一页
const goBack = () => {
  router.back();
};

// 跳转到照片详情
const goToPhotoDetail = (id: number) => {
  router.push(`/photo/detail/${id}`);
};

// 跳转到用户主页
const goToUserProfile = (userId: number) => {
  router.push(`/user/${userId}`);
};

// 下拉刷新
const onRefresh = () => {
  page.value = 1;
  finished.value = false;
  loadPhotos();
};

// 加载更多
const onLoad = () => {
  if (!loading.value && !finished.value) {
    loadPhotos();
  }
};

// 加载照片列表
const loadPhotos = async () => {
  if (loading.value) return;

  loading.value = true;

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 生成模拟数据
    const mockPhotos: Photo[] = Array.from({ length: 10 }, (_, i) => {
      const id = (page.value - 1) * pageSize.value + i + 1;
      return {
        id,
        title: `照片标题 ${id}`,
        url: `https://picsum.photos/id/${(id % 100) + 1}/800/600`,
        thumbnailUrl: `https://picsum.photos/id/${(id % 100) + 1}/400/300`,
        userName: `用户${id % 20 + 1}`,
        userAvatar: `https://randomuser.me/api/portraits/${id % 2 ? 'men' : 'women'}/${(id % 100) + 1}.jpg`,
        userId: id % 20 + 1,
        viewCount: Math.floor(Math.random() * 1000),
        likeCount: Math.floor(Math.random() * 500),
        commentCount: Math.floor(Math.random() * 100),
        uploadTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toISOString(),
        tags: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, j) => `标签${j + 1}`)
      };
    });

    // 更新照片列表
    if (page.value === 1) {
      photos.value = mockPhotos;
    } else {
      photos.value = [...photos.value, ...mockPhotos];
    }

    // 更新分页参数
    page.value++;
    finished.value = page.value > 5; // 模拟只有5页数据
  } catch (error) {
    console.error('加载照片列表失败', error);
    showToast('加载失败，请稍后重试');
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadPhotos();
});
</script>

<style lang="scss" scoped>
.photo-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px; // 导航栏高度

  .photo-list-content {
    padding: 16px;
  }

  .photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 16px;

    @media (min-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .photo-item {
      cursor: pointer;

      .photo-card {
        background-color: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;

        &:hover {
          transform: translateY(-5px);
        }

        .photo-image {
          height: 0;
          padding-bottom: 75%; // 4:3 aspect ratio
          position: relative;
          overflow: hidden;

          .van-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }
        }

        .photo-info {
          padding: 12px;

          .photo-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .photo-user {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .user-name {
              margin-left: 6px;
              font-size: 12px;
              color: #666;
            }

            &:hover .user-name {
              color: #3498db;
            }
          }

          .photo-stats {
            display: flex;
            font-size: 12px;
            color: #999;

            .stat-item {
              display: flex;
              align-items: center;
              margin-right: 12px;

              .van-icon {
                margin-right: 4px;
              }
            }
          }
        }
      }
    }
  }

  .empty-state {
    padding: 40px 0;
  }
}
</style>
