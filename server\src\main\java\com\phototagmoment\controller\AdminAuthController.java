package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.LoginDTO;
import com.phototagmoment.service.AdminService;
import com.phototagmoment.vo.TokenVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/auth")
@Tag(name = "管理员认证接口", description = "包括管理员登录、登出等接口")
public class AdminAuthController {

    @Autowired
    private AdminService adminService;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    @Operation(summary = "管理员登录", description = "管理员登录并返回token")
    public ApiResponse<TokenVO> login(@Validated @RequestBody LoginDTO loginDTO) {
        TokenVO tokenVO = adminService.login(loginDTO);
        return ApiResponse.success(tokenVO, "登录成功");
    }

    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    @Operation(summary = "管理员登出", description = "管理员登出")
    public ApiResponse<Void> logout() {
        adminService.logout();
        return ApiResponse.success(null, "登出成功");
    }
}
