server:
  port: 8081
  servlet:
    context-path: /api
  tomcat:
    uri-encoding: UTF-8

spring:
  application:
    name: PhotoTagMoment
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    lazy-initialization: false

  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000

  # Redis配置
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password: 123456
      database: 6
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
  # 缓存配置
  cache:
    type: simple # 使用简单内存缓存代替Redis，避免Redis连接问题

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 20MB

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

  # Flyway配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    out-of-order: true
    clean-disabled: true
    table: flyway_schema_history

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.phototagmoment.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 修改MyBatis日志实现，使用SLF4J而不是直接输出到控制台
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: AUTO
      table-prefix: ptm_
      logic-delete-field: is_deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    root: info
    com.phototagmoment: debug
    # 将MyBatis的日志级别设置为WARN，减少SQL查询结果的日志输出
    org.apache.ibatis: warn
    com.baomidou.mybatisplus: warn
    # 将敏感词服务的日志级别设置为INFO，只输出必要的信息
    com.phototagmoment.service.impl.SensitiveWordServiceImpl: info
    org.springframework.security: debug
    org.springframework.web: debug
  file:
    name: logs/phototagmoment.log

# JWT配置
jwt:
  secret: phototagmoment_secret_key_for_jwt_token_generation_and_validation
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer

# 加密配置
encryption:
  enabled: true
  user-data-key: ${ENCRYPTION_USER_DATA_KEY:}
  photo-key: ${ENCRYPTION_PHOTO_KEY:}
  rsa-public-key: ${ENCRYPTION_RSA_PUBLIC_KEY:}
  rsa-private-key: ${ENCRYPTION_RSA_PRIVATE_KEY:}
  encrypted-user-fields: phone,email,realName,idCard

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-version: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Copyright © 2023 PhotoTagMoment
  documents:
    - group: 默认分组
      name: 接口说明文档
      locations: classpath:markdown/*
  production: false
  basic:
    enable: false
    username: admin
    password: 123456
  # 指定Knife4j的路径前缀，与应用的context-path保持一致
  openapi:
    title: PhotoTagMoment API
    description: PhotoTagMoment照片社交网站API文档
    email: <EMAIL>
    concat: PhotoTagMoment Team
    url: https://www.phototagmoment.com
    version: v1.0.0
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html

# OpenAPI配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.phototagmoment.controller
  group-configs:
    - group: 用户接口
      paths-to-match: /user/**, /auth/**, /photo/**, /tag/**, /search/**, /recommendation/**
      packages-to-scan: com.phototagmoment.controller
    - group: 管理接口
      paths-to-match: /admin/**
      packages-to-scan: com.phototagmoment.controller.admin

# 存储配置已迁移到数据库
storage:
  # 本地存储配置
  local:
    path: ./uploads
