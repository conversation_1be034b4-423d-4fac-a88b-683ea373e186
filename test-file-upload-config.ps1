# PhotoTagMoment 文件上传配置API测试脚本

Write-Host "=== PhotoTagMoment 文件上传配置API测试 ===" -ForegroundColor Green

# 配置
$baseUrl = "http://localhost:8081/api"
$adminUrl = "$baseUrl/admin/file-upload-config"

# 测试用户登录获取Token
function Get-AuthToken {
    param(
        [string]$username,
        [string]$password
    )
    
    try {
        $loginData = @{
            username = $username
            password = $password
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -Body $loginData -ContentType "application/json"
        
        if ($response.code -eq 200) {
            Write-Host "✅ 用户 $username 登录成功" -ForegroundColor Green
            return $response.data.token
        } else {
            Write-Host "❌ 用户 $username 登录失败: $($response.message)" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 测试获取配置列表
function Test-GetConfigList {
    param([string]$token)
    
    Write-Host "`n--- 测试获取配置列表 ---" -ForegroundColor Yellow
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$adminUrl/list?page=1&size=10" -Method GET -Headers $headers
        
        if ($response.code -eq 200) {
            Write-Host "✅ 获取配置列表成功，共 $($response.data.total) 个配置" -ForegroundColor Green
            return $response.data.records
        } else {
            Write-Host "❌ 获取配置列表失败: $($response.message)" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "❌ 获取配置列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 测试创建配置
function Test-CreateConfig {
    param([string]$token)
    
    Write-Host "`n--- 测试创建配置 ---" -ForegroundColor Yellow
    
    $configData = @{
        configName = "测试配置-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        storageType = "LOCAL"
        configParams = @{
            localPath = "test-uploads"
            domain = "http://localhost:8081"
            useHttps = $false
            connectTimeout = 30
            readTimeout = 60
        } | ConvertTo-Json
        uploadLimits = @{
            maxFileSize = 10
            maxFileCount = 5
            allowedFileTypes = @("jpg", "png", "pdf")
            enableFileTypeCheck = $true
        } | ConvertTo-Json
        pathConfig = @{
            rootPath = "test-uploads"
            fileNamingRule = "UUID"
            directoryStructure = "DATE_TYPE"
            enableDateDirectory = $true
        } | ConvertTo-Json
        enabled = $true
        isDefault = $false
        description = "API测试创建的配置"
    } | ConvertTo-Json
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$adminUrl/create" -Method POST -Body $configData -Headers $headers
        
        if ($response.code -eq 200) {
            Write-Host "✅ 创建配置成功，配置ID: $($response.data.id)" -ForegroundColor Green
            return $response.data
        } else {
            Write-Host "❌ 创建配置失败: $($response.message)" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "❌ 创建配置请求失败: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            Write-Host "错误详情: $errorBody" -ForegroundColor Red
        }
        return $null
    }
}

# 测试切换配置状态
function Test-ToggleConfig {
    param(
        [string]$token,
        [int]$configId
    )
    
    Write-Host "`n--- 测试切换配置状态 ---" -ForegroundColor Yellow
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$adminUrl/$configId/toggle?enabled=false" -Method PUT -Headers $headers
        
        if ($response.code -eq 200) {
            Write-Host "✅ 切换配置状态成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 切换配置状态失败: $($response.message)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ 切换配置状态请求失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主测试流程
function Main {
    Write-Host "开始测试..." -ForegroundColor Cyan
    
    # 测试超级管理员登录
    Write-Host "`n=== 测试超级管理员权限 ===" -ForegroundColor Magenta
    $superAdminToken = Get-AuthToken -username "superadmin" -password "123456"
    
    if ($superAdminToken) {
        # 测试获取配置列表
        $configs = Test-GetConfigList -token $superAdminToken
        
        # 测试创建配置
        $newConfig = Test-CreateConfig -token $superAdminToken
        
        # 如果创建成功，测试切换状态
        if ($newConfig) {
            Test-ToggleConfig -token $superAdminToken -configId $newConfig.id
        }
    }
    
    # 测试普通管理员登录
    Write-Host "`n=== 测试普通管理员权限 ===" -ForegroundColor Magenta
    $adminToken = Get-AuthToken -username "admin_test" -password "123456"
    
    if ($adminToken) {
        # 测试获取配置列表
        Test-GetConfigList -token $adminToken
        
        # 测试创建配置（应该失败）
        Write-Host "`n--- 测试普通管理员创建配置（预期失败） ---" -ForegroundColor Yellow
        Test-CreateConfig -token $adminToken
    }
    
    Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
}

# 执行测试
Main
