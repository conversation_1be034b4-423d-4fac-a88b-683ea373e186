package com.phototagmoment.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 敏感词导入结果VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SensitiveWordImportResultVO {

    /**
     * 总处理文件数
     */
    private Integer totalFiles;

    /**
     * 成功处理文件数
     */
    private Integer successFiles;

    /**
     * 失败处理文件数
     */
    private Integer failedFiles;

    /**
     * 总敏感词数
     */
    private Integer totalWords;

    /**
     * 成功导入敏感词数
     */
    private Integer successWords;

    /**
     * 失败导入敏感词数
     */
    private Integer failedWords;

    /**
     * 重复敏感词数
     */
    private Integer duplicateWords;

    /**
     * 按类型统计
     */
    private Map<String, Integer> typeStats;

    /**
     * 按级别统计
     */
    private Map<Integer, Integer> levelStats;

    /**
     * 失败文件列表
     */
    private List<FileError> failedFileList;

    /**
     * 文件错误信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileError {
        /**
         * 文件名
         */
        private String fileName;

        /**
         * 错误信息
         */
        private String errorMessage;
    }
}
