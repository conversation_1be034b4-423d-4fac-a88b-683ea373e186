-- 创建评论标签关联表和评论用户提及关联表
-- 版本：V2.1
-- 描述：为评论功能添加标签和用户提及支持

-- 创建评论标签关联表
CREATE TABLE IF NOT EXISTS `ptm_comment_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_tag_name` (`tag_name`),
  UNIQUE KEY `uk_comment_tag` (`comment_id`, `tag_name`),
  CONSTRAINT `fk_comment_tag_comment` FOREIGN KEY (`comment_id`) REFERENCES `ptm_comment` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论标签关联表';

-- 创建评论用户提及关联表
CREATE TABLE IF NOT EXISTS `ptm_comment_mention` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `mentioned_user_id` bigint DEFAULT NULL COMMENT '被@用户ID（如果用户存在）',
  `mention_user_id` bigint NOT NULL COMMENT '@用户ID',
  `mentioned_username` varchar(50) NOT NULL COMMENT '被@用户名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_mentioned_user_id` (`mentioned_user_id`),
  KEY `idx_mention_user_id` (`mention_user_id`),
  KEY `idx_mentioned_username` (`mentioned_username`),
  CONSTRAINT `fk_comment_mention_comment` FOREIGN KEY (`comment_id`) REFERENCES `ptm_comment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comment_mention_user` FOREIGN KEY (`mention_user_id`) REFERENCES `ptm_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论用户提及关联表';

-- 为ptm_comment表添加回复数统计字段（如果不存在）
ALTER TABLE `ptm_comment` 
ADD COLUMN IF NOT EXISTS `reply_count` int DEFAULT 0 COMMENT '回复数';

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_comment_parent_id` ON `ptm_comment` (`parent_id`);
CREATE INDEX IF NOT EXISTS `idx_comment_reply_to_user_id` ON `ptm_comment` (`reply_to_user_id`);
CREATE INDEX IF NOT EXISTS `idx_comment_photo_parent` ON `ptm_comment` (`photo_id`, `parent_id`);

-- 插入初始化数据（如果需要）
-- 这里可以添加一些测试数据或默认配置

-- 创建视图用于快速查询评论及其关联信息（可选）
CREATE OR REPLACE VIEW `v_comment_with_details` AS
SELECT 
    c.id,
    c.user_id,
    c.photo_id,
    c.parent_id,
    c.reply_to_user_id,
    c.content,
    c.like_count,
    c.reply_count,
    c.status,
    c.created_at,
    c.updated_at,
    u.username,
    u.nickname,
    u.avatar,
    GROUP_CONCAT(DISTINCT ct.tag_name ORDER BY ct.tag_name SEPARATOR ',') AS tags,
    GROUP_CONCAT(DISTINCT cm.mentioned_username ORDER BY cm.mentioned_username SEPARATOR ',') AS mentions
FROM ptm_comment c
LEFT JOIN ptm_user u ON c.user_id = u.id
LEFT JOIN ptm_comment_tag ct ON c.id = ct.comment_id
LEFT JOIN ptm_comment_mention cm ON c.id = cm.comment_id
GROUP BY c.id, c.user_id, c.photo_id, c.parent_id, c.reply_to_user_id, 
         c.content, c.like_count, c.reply_count, c.status, c.created_at, c.updated_at,
         u.username, u.nickname, u.avatar;
