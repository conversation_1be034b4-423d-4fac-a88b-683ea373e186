package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.dto.CommentAddRequest;
import com.phototagmoment.dto.CommentDTO;
import com.phototagmoment.dto.CommentQueryRequest;
import com.phototagmoment.entity.Comment;
import com.phototagmoment.entity.CommentMention;

import java.util.List;

/**
 * 评论服务接口
 */
public interface CommentService extends IService<Comment> {

    /**
     * 添加评论
     *
     * @param photoId 照片ID
     * @param content 评论内容
     * @param userId 用户ID
     * @return 评论ID
     */
    Long addComment(Long photoId, String content, Long userId);

    /**
     * 添加完整评论（支持回复、标签、用户提及）
     *
     * @param request 评论请求
     * @param userId 用户ID
     * @return 评论ID
     */
    Long addCommentComplete(CommentAddRequest request, Long userId);

    /**
     * 获取评论的标签列表
     *
     * @param commentId 评论ID
     * @return 标签列表
     */
    List<String> getCommentTags(Long commentId);

    /**
     * 获取评论的用户提及列表
     *
     * @param commentId 评论ID
     * @return 用户提及列表
     */
    List<CommentMention> getCommentMentions(Long commentId);

    /**
     * 回复评论
     *
     * @param commentId 评论ID
     * @param content 回复内容
     * @param userId 用户ID
     * @param replyToUserId 回复的用户ID
     * @return 回复ID
     */
    Long replyComment(Long commentId, String content, Long userId, Long replyToUserId);

    /**
     * 获取评论详情
     *
     * @param commentId 评论ID
     * @param userId 当前用户ID
     * @return 评论详情
     */
    CommentDTO getCommentDetail(Long commentId, Long userId);

    /**
     * 分页获取照片评论列表
     *
     * @param photoId 照片ID
     * @param page 页码
     * @param size 每页大小
     * @param userId 当前用户ID
     * @return 评论列表
     */
    IPage<CommentDTO> getPhotoComments(Long photoId, int page, int size, Long userId);

    /**
     * 分页获取评论回复列表
     *
     * @param commentId 评论ID
     * @param page 页码
     * @param size 每页大小
     * @param userId 当前用户ID
     * @return 回复列表
     */
    IPage<CommentDTO> getCommentReplies(Long commentId, int page, int size, Long userId);

    /**
     * 点赞评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean likeComment(Long commentId, Long userId);

    /**
     * 取消点赞评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unlikeComment(Long commentId, Long userId);

    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteComment(Long commentId, Long userId);

    /**
     * 管理员获取评论列表
     *
     * @param queryRequest 查询请求
     * @return 评论列表
     */
    IPage<CommentDTO> getCommentListForAdmin(CommentQueryRequest queryRequest);

    /**
     * 管理员获取评论详情
     *
     * @param commentId 评论ID
     * @return 评论详情
     */
    CommentDTO getCommentDetailForAdmin(Long commentId);

    /**
     * 管理员修改评论状态
     *
     * @param commentId 评论ID
     * @param status 状态
     * @param reason 操作原因
     * @return 是否成功
     */
    boolean updateCommentStatusByAdmin(Long commentId, Integer status, String reason);

    /**
     * 管理员批量修改评论状态
     *
     * @param commentIds 评论ID数组
     * @param status 状态
     * @param reason 操作原因
     * @return 是否成功
     */
    boolean batchUpdateCommentStatusByAdmin(Long[] commentIds, Integer status, String reason);

    /**
     * 管理员修改回复状态
     *
     * @param replyId 回复ID
     * @param status 状态
     * @param reason 操作原因
     * @return 是否成功
     */
    boolean updateReplyStatusByAdmin(Long replyId, Integer status, String reason);

    /**
     * 获取评论统计信息
     *
     * @return 统计信息
     */
    Object getCommentStatistics();

    /**
     * 管理员永久删除评论
     *
     * @param commentId 评论ID
     * @param reason 删除原因
     * @return 是否成功
     */
    boolean deleteCommentPermanentlyByAdmin(Long commentId, String reason);
}
