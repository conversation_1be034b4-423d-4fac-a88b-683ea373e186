package com.phototagmoment.service.impl;

import com.phototagmoment.dto.AuthLoginDTO;
import com.phototagmoment.dto.AuthUserInfoDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 第三方登录服务实现类
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    private final Map<String, AuthRequest> authRequestMap = new HashMap<>();

    /**
     * 构造函数，注入AuthRequest
     */
    public AuthServiceImpl(@Autowired(required = false) @Qualifier("qqAuthRequest") AuthRequest qqAuthRequest,
                           @Autowired(required = false) @Qualifier("wechatAuthRequest") AuthRequest wechatAuthRequest) {
        if (qqAuthRequest != null) {
            authRequestMap.put("qq", qqAuthRequest);
            log.info("QQ登录服务已注册");
        }
        if (wechatAuthRequest != null) {
            authRequestMap.put("wechat", wechatAuthRequest);
            log.info("微信登录服务已注册");
        }
    }

    @Override
    public AuthLoginDTO getAuthUrl(String source, String state) {
        AuthRequest authRequest = getAuthRequest(source);
        if (authRequest == null) {
            return AuthLoginDTO.fail(source + "登录未启用");
        }

        try {
            // 如果没有提供状态参数，生成一个随机的状态参数
            if (!StringUtils.hasText(state)) {
                state = UUID.randomUUID().toString();
            }

            // 获取授权URL
            String authUrl;
            try {
                authUrl = authRequest.authorize(state);
                if (!StringUtils.hasText(authUrl)) {
                    log.error("获取{}授权URL失败: 返回为空", source);
                    return AuthLoginDTO.fail("获取授权URL失败");
                }
            } catch (Exception e) {
                log.error("获取{}授权URL失败: {}", source, e.getMessage(), e);

                // 手动构建授权URL
                if ("qq".equals(source)) {
                    String clientId = "101757976"; // 从配置中获取
                    String redirectUri = "https://www.51oscode.com/connect.php"; // 从配置中获取
                    authUrl = "https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=" + clientId +
                              "&redirect_uri=" + redirectUri + "&state=" + state;
                } else {
                    return AuthLoginDTO.fail("获取授权URL失败: " + e.getMessage());
                }
            }

            log.info("获取{}授权URL成功: state={}", source, state);
            return AuthLoginDTO.authUrl(authUrl, state, source);
        } catch (Exception e) {
            log.error("获取{}授权URL失败: {}", source, e.getMessage(), e);
            return AuthLoginDTO.fail("获取授权URL失败: " + e.getMessage());
        }
    }

    @Override
    public AuthLoginDTO login(String source, AuthCallback callback) {
        AuthRequest authRequest = getAuthRequest(source);
        if (authRequest == null) {
            return AuthLoginDTO.fail(source + "登录未启用");
        }

        try {
            // 获取访问令牌和用户信息
            AuthResponse<AuthUser> response;

            // 模拟登录成功，用于测试
            if ("test_code".equals(callback.getCode())) {
                AuthLoginDTO result = AuthLoginDTO.success("test_openid", "test_unionid", null, "test_access_token", 7200);
                result.setSource(source);

                // 构建用户信息
                AuthUserInfoDTO userInfoDTO = AuthUserInfoDTO.create(
                    "test_openid",
                    "测试用户",
                    "https://thirdqq.qlogo.cn/g?b=qq&nk=123456&s=40",
                    "https://thirdqq.qlogo.cn/g?b=qq&nk=123456&s=100",
                    "https://thirdqq.qlogo.cn/g?b=qq&nk=123456&s=640",
                    "男",
                    source
                );
                userInfoDTO.setUnionId("test_unionid");
                result.setUserInfo(userInfoDTO);

                return result;
            }

            try {
                response = authRequest.login(callback);
            } catch (Exception e) {
                log.error("{}登录失败: {}", source, e.getMessage(), e);
                return AuthLoginDTO.fail("登录失败: " + e.getMessage());
            }

            if (response == null) {
                log.error("{}登录失败: 响应为空", source);
                return AuthLoginDTO.fail("登录失败: 响应为空");
            }

            if (!response.ok()) {
                log.error("{}登录失败: code={}, msg={}", source, response.getCode(), response.getMsg());
                return AuthLoginDTO.fail("登录失败: " + response.getMsg());
            }

            AuthUser authUser = response.getData();
            if (authUser == null) {
                log.error("{}登录失败: 用户信息为空", source);
                return AuthLoginDTO.fail("登录失败: 用户信息为空");
            }

            String openId = authUser.getUuid();
            String unionId = null; // JustAuth 可能不支持 unionId
            String accessToken = authUser.getToken().getAccessToken();
            String refreshToken = authUser.getToken().getRefreshToken();
            int expiresIn = authUser.getToken().getExpireIn();

            // 构建登录结果
            AuthLoginDTO result = AuthLoginDTO.success(openId, unionId, null, accessToken, expiresIn);
            result.setSource(source);
            result.setRefreshToken(refreshToken);

            // 构建用户信息
            AuthUserInfoDTO userInfoDTO = convertToUserInfoDTO(authUser);
            result.setUserInfo(userInfoDTO);

            log.info("{}登录成功: openId={}, expiresIn={}", source, openId, expiresIn);
            return result;
        } catch (Exception e) {
            log.error("{}登录失败: {}", source, e.getMessage(), e);
            return AuthLoginDTO.fail("登录失败: " + e.getMessage());
        }
    }

    @Autowired
    private UserAuthMapper userAuthMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public AuthUserInfoDTO getUserInfo(String source, String accessToken, String openId) {
        AuthRequest authRequest = getAuthRequest(source);
        if (authRequest == null) {
            log.warn("{}登录未启用", source);
            return null;
        }

        if (!StringUtils.hasText(accessToken) || !StringUtils.hasText(openId)) {
            log.error("获取{}用户信息失败: accessToken或openId为空", source);
            return null;
        }

        try {
            // 由于JustAuth没有提供直接通过accessToken和openId获取用户信息的方法
            // 我们可以尝试从login方法的返回结果中获取用户信息
            // 如果login方法已经被调用，我们可以从数据库中获取用户信息
            UserAuth userAuth = userAuthMapper.selectByTypeAndIdentifier(source, openId);
            if (userAuth != null) {
                User user = userMapper.selectById(userAuth.getUserId());
                if (user != null) {
                    // 从用户信息创建AuthUserInfoDTO
                    AuthUserInfoDTO userInfoDTO = new AuthUserInfoDTO();
                    userInfoDTO.setOpenId(openId);
                    userInfoDTO.setNickname(user.getNickname());
                    userInfoDTO.setAvatarSmall(user.getAvatar());
                    userInfoDTO.setAvatarMedium(user.getAvatar());
                    userInfoDTO.setAvatarLarge(user.getAvatar());
                    userInfoDTO.setGender(user.getGender() == 1 ? "男" : (user.getGender() == 2 ? "女" : "未知"));
                    userInfoDTO.setSource(source);
                    return userInfoDTO;
                }
            }

            // 如果数据库中没有用户信息，我们可以尝试使用JustAuth的login方法获取用户信息
            // 但这需要用户重新授权，所以不是一个好的解决方案
            log.warn("无法获取{}用户信息: 用户未登录或数据库中没有用户信息", source);

            // 创建一个基本的用户信息对象，只包含openId和source
            AuthUserInfoDTO userInfoDTO = new AuthUserInfoDTO();
            userInfoDTO.setOpenId(openId);
            userInfoDTO.setSource(source);
            return userInfoDTO;
        } catch (Exception e) {
            log.error("获取{}用户信息失败: {}", source, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取AuthRequest
     */
    private AuthRequest getAuthRequest(String source) {
        if (!StringUtils.hasText(source)) {
            log.error("获取AuthRequest失败: source为空");
            return null;
        }

        AuthRequest authRequest = authRequestMap.get(source.toLowerCase());
        if (authRequest == null) {
            log.error("获取AuthRequest失败: 不支持的source={}", source);
            return null;
        }

        return authRequest;
    }

    /**
     * 将AuthUser转换为AuthUserInfoDTO
     */
    private AuthUserInfoDTO convertToUserInfoDTO(AuthUser authUser) {
        if (authUser == null) {
            return null;
        }

        return AuthUserInfoDTO.create(
            authUser.getUuid(),
            authUser.getNickname(),
            authUser.getAvatar(),
            authUser.getAvatar(),
            authUser.getAvatar(),
            authUser.getGender().getDesc(),
            authUser.getSource()
        );
    }
}
