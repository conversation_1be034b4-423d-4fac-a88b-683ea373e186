package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.dto.TagDTO;
import com.phototagmoment.entity.PhotoTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 照片标签Mapper接口
 */
@Mapper
@Repository
public interface PhotoTagMapper extends BaseMapper<PhotoTag> {

    /**
     * 批量插入照片标签
     *
     * @param photoTags 照片标签列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<PhotoTag> photoTags);

    /**
     * 根据照片ID查询标签列表
     *
     * @param photoId 照片ID
     * @return 标签列表
     */
    List<String> selectTagsByPhotoId(@Param("photoId") Long photoId);

    /**
     * 根据照片ID删除标签
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int deleteByPhotoId(@Param("photoId") Long photoId);

    /**
     * 搜索标签
     *
     * @param keyword 关键词
     * @param limit 数量限制
     * @return 标签列表
     */
    List<PhotoTag> searchTags(@Param("keyword") String keyword, @Param("limit") int limit);

    /**
     * 获取热门标签
     *
     * @param limit 数量限制
     * @return 热门标签列表
     */
    List<TagDTO> getPopularTags(@Param("limit") int limit);

    /**
     * 查询热门标签
     *
     * @param limit 数量限制
     * @return 热门标签列表
     */
    @Select("SELECT tag_name as tagName, COUNT(*) as count " +
            "FROM ptm_photo_tag " +
            "GROUP BY tag_name " +
            "ORDER BY count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectHotTags(@Param("limit") int limit);

    /**
     * 获取照片的标签
     *
     * @param photoId 照片ID
     * @return 标签列表
     */
    @Select("SELECT * FROM ptm_photo_tag WHERE photo_id = #{photoId}")
    List<PhotoTag> getPhotoTags(@Param("photoId") Long photoId);

    /**
     * 获取用户使用最多的标签
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 标签列表
     */
    @Select("SELECT t.* FROM ptm_photo_tag t " +
            "JOIN ptm_photo p ON t.photo_id = p.id " +
            "WHERE p.user_id = #{userId} AND p.is_deleted = 0 " +
            "GROUP BY t.tag_name " +
            "ORDER BY COUNT(t.id) DESC " +
            "LIMIT #{limit}")
    List<PhotoTag> getUserMostUsedTags(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 获取与指定标签相关的标签
     *
     * @param tagName 标签名称
     * @param limit   限制数量
     * @return 相关标签列表
     */
    @Select("SELECT t2.tag_name, COUNT(t2.id) as count " +
            "FROM ptm_photo_tag t1 " +
            "JOIN ptm_photo_tag t2 ON t1.photo_id = t2.photo_id AND t1.tag_name != t2.tag_name " +
            "WHERE t1.tag_name = #{tagName} " +
            "GROUP BY t2.tag_name " +
            "ORDER BY count DESC " +
            "LIMIT #{limit}")
    List<TagDTO> getRelatedTags(@Param("tagName") String tagName, @Param("limit") int limit);
}
