# PhotoTagMoment 权限调试脚本

Write-Host "=== PhotoTagMoment 权限调试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:8081/api"

# 测试管理员登录并获取权限信息
function Test-AdminPermissions {
    param(
        [string]$username,
        [string]$password
    )
    
    Write-Host "`n--- 测试用户: $username ---" -ForegroundColor Yellow
    
    try {
        # 1. 登录获取token
        $loginData = @{
            username = $username
            password = $password
        } | ConvertTo-Json
        
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/admin/system/login" -Method POST -Body $loginData -ContentType "application/json"
        
        if ($loginResponse.code -eq 200) {
            Write-Host "✅ 登录成功" -ForegroundColor Green
            $token = $loginResponse.data.token
            
            # 2. 获取用户信息和权限
            $headers = @{
                "Authorization" = "Bearer $token"
                "Content-Type" = "application/json"
            }
            
            $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/admin/system/info" -Method GET -Headers $headers
            
            if ($userInfoResponse.code -eq 200) {
                Write-Host "✅ 获取用户信息成功" -ForegroundColor Green
                $userInfo = $userInfoResponse.data
                
                Write-Host "用户ID: $($userInfo.id)" -ForegroundColor Cyan
                Write-Host "用户名: $($userInfo.username)" -ForegroundColor Cyan
                Write-Host "姓名: $($userInfo.name)" -ForegroundColor Cyan
                Write-Host "角色: $($userInfo.roles -join ', ')" -ForegroundColor Cyan
                Write-Host "权限: $($userInfo.permissions -join ', ')" -ForegroundColor Cyan
                
                # 3. 测试配置列表权限
                Write-Host "`n--- 测试配置列表权限 ---" -ForegroundColor Magenta
                try {
                    $configResponse = Invoke-RestMethod -Uri "$baseUrl/admin/file-upload-config/list?page=1&size=5" -Method GET -Headers $headers
                    
                    if ($configResponse.code -eq 200) {
                        Write-Host "✅ 配置列表访问成功，共 $($configResponse.data.total) 个配置" -ForegroundColor Green
                        
                        if ($configResponse.data.records.Count -gt 0) {
                            $firstConfig = $configResponse.data.records[0]
                            Write-Host "第一个配置: $($firstConfig.configName) (ID: $($firstConfig.id))" -ForegroundColor Cyan
                            
                            # 4. 测试切换配置状态权限
                            Write-Host "`n--- 测试切换配置状态权限 ---" -ForegroundColor Magenta
                            try {
                                $toggleResponse = Invoke-RestMethod -Uri "$baseUrl/admin/file-upload-config/$($firstConfig.id)/toggle?enabled=$(!$firstConfig.enabled)" -Method PUT -Headers $headers
                                
                                if ($toggleResponse.code -eq 200) {
                                    Write-Host "✅ 切换配置状态成功" -ForegroundColor Green
                                } else {
                                    Write-Host "❌ 切换配置状态失败: $($toggleResponse.message)" -ForegroundColor Red
                                }
                            } catch {
                                Write-Host "❌ 切换配置状态请求失败: $($_.Exception.Message)" -ForegroundColor Red
                                if ($_.Exception.Response) {
                                    $statusCode = $_.Exception.Response.StatusCode
                                    Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
                                }
                            }
                            
                            # 5. 测试设为默认权限
                            Write-Host "`n--- 测试设为默认权限 ---" -ForegroundColor Magenta
                            try {
                                $defaultResponse = Invoke-RestMethod -Uri "$baseUrl/admin/file-upload-config/$($firstConfig.id)/default" -Method PUT -Headers $headers
                                
                                if ($defaultResponse.code -eq 200) {
                                    Write-Host "✅ 设为默认成功" -ForegroundColor Green
                                } else {
                                    Write-Host "❌ 设为默认失败: $($defaultResponse.message)" -ForegroundColor Red
                                }
                            } catch {
                                Write-Host "❌ 设为默认请求失败: $($_.Exception.Message)" -ForegroundColor Red
                                if ($_.Exception.Response) {
                                    $statusCode = $_.Exception.Response.StatusCode
                                    Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
                                }
                            }
                        }
                    } else {
                        Write-Host "❌ 配置列表访问失败: $($configResponse.message)" -ForegroundColor Red
                    }
                } catch {
                    Write-Host "❌ 配置列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
                    if ($_.Exception.Response) {
                        $statusCode = $_.Exception.Response.StatusCode
                        Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
                    }
                }
                
            } else {
                Write-Host "❌ 获取用户信息失败: $($userInfoResponse.message)" -ForegroundColor Red
            }
            
        } else {
            Write-Host "❌ 登录失败: $($loginResponse.message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
        }
    }
}

# 测试不同用户的权限
Write-Host "开始权限调试..." -ForegroundColor Cyan

# 测试admin_test用户
Test-AdminPermissions -username "admin_test" -password "123456"

# 测试superadmin用户
Test-AdminPermissions -username "superadmin" -password "123456"

Write-Host "`n=== 权限调试完成 ===" -ForegroundColor Green
