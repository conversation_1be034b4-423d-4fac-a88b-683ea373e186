<template>
  <div class="tag-search">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      :title="`#${tagName}#`"
      left-arrow
      @click-left="$router.back()"
      class="search-navbar"
    />

    <!-- 标签信息 -->
    <div class="tag-info">
      <div class="tag-header">
        <h1 class="tag-title">#{{ tagName }}#</h1>
        <div class="tag-stats">
          <span>{{ totalCount }} 篇笔记</span>
          <span v-if="tagStats?.hotScore">热度: {{ tagStats.hotScore }}</span>
        </div>
      </div>
    </div>

    <!-- 排序选择 -->
    <div class="sort-section">
      <van-tabs v-model:active="activeSortTab" @change="onSortChange">
        <van-tab title="最热" name="hot" />
        <van-tab title="最新" name="time" />
      </van-tabs>
    </div>

    <!-- 照片笔记列表 -->
    <div class="notes-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="note in notesList"
            :key="note.id"
            class="note-item"
            @click="goToNoteDetail(note.id)"
          >
            <!-- 用户信息 -->
            <div class="note-user">
              <van-image
                :src="note.avatar"
                round
                width="32"
                height="32"
                fit="cover"
                class="user-avatar"
              />
              <div class="user-info">
                <div class="user-name">{{ note.nickname }}</div>
                <div class="publish-time">{{ formatTime(note.createdAt) }}</div>
              </div>
            </div>

            <!-- 照片展示 -->
            <div class="note-photos">
              <div class="photo-grid" :class="getPhotoGridClass(note.images?.length || 0)">
                <van-image
                  v-for="(image, index) in note.images?.slice(0, 9)"
                  :key="index"
                  :src="image.thumbnailUrl || image.url"
                  fit="cover"
                  width="100%"
                  height="100%"
                  class="photo-item"
                />
              </div>
            </div>

            <!-- 内容 -->
            <div class="note-content">
              <h3 v-if="note.title" class="note-title">{{ note.title }}</h3>
              <div class="note-text" v-html="note.processedContent"></div>
            </div>

            <!-- 互动数据 -->
            <div class="note-stats">
              <div class="stats-item">
                <van-icon name="eye-o" />
                <span>{{ note.viewCount }}</span>
              </div>
              <div class="stats-item">
                <van-icon name="good-job-o" />
                <span>{{ note.likeCount }}</span>
              </div>
              <div class="stats-item">
                <van-icon name="chat-o" />
                <span>{{ note.commentCount }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 相关标签 -->
    <div v-if="relatedTags.length > 0" class="related-tags">
      <h3>相关标签</h3>
      <div class="tags-list">
        <div
          v-for="tag in relatedTags"
          :key="tag.tagName"
          class="tag-item"
          @click="goToTag(tag.tagName)"
        >
          <span class="tag-name">#{{ tag.tagName }}#</span>
          <span class="tag-count">{{ tag.useCount }}</span>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && notesList.length === 0"
      description="暂无相关照片笔记"
      image="search"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { searchPhotoNotesByTag } from '@/api/photo'
import { formatRelativeTime } from '@/utils/time'

const route = useRoute()
const router = useRouter()

// 响应式数据
const tagName = ref(route.params.tagName)
const notesList = ref([])
const relatedTags = ref([])
const tagStats = ref(null)
const totalCount = ref(0)
const activeSortTab = ref('hot')
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const sortType = computed(() => activeSortTab.value)

// 方法
const loadNotes = async (page = 1, isRefresh = false) => {
  try {
    if (page === 1 && !isRefresh) {
      loading.value = true
    }

    const response = await searchPhotoNotesByTag({
      tagName: tagName.value,
      page,
      size: pageSize.value,
      sortType: sortType.value
    })

    const { notes, totalCount: total, relatedTags: related } = response.data

    if (page === 1) {
      notesList.value = notes
      relatedTags.value = related || []
      totalCount.value = total || 0
    } else {
      notesList.value.push(...notes)
    }

    // 检查是否还有更多数据
    finished.value = notes.length < pageSize.value
    currentPage.value = page

  } catch (error) {
    console.error('加载标签照片笔记失败:', error)
    showToast('加载失败')
    finished.value = true
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onLoad = () => {
  if (!finished.value) {
    loadNotes(currentPage.value + 1)
  }
}

const onRefresh = () => {
  finished.value = false
  currentPage.value = 1
  loadNotes(1, true)
}

const onSortChange = () => {
  finished.value = false
  currentPage.value = 1
  notesList.value = []
  loadNotes(1)
}

const goToNoteDetail = (noteId) => {
  router.push(`/photo-note/${noteId}`)
}

const goToTag = (tag) => {
  if (tag !== tagName.value) {
    router.push(`/tag/${encodeURIComponent(tag)}`)
  }
}

const getPhotoGridClass = (count) => {
  if (count === 1) return 'grid-1'
  if (count <= 4) return 'grid-2x2'
  return 'grid-3x3'
}

const formatTime = (time) => {
  return formatRelativeTime(new Date(time))
}

// 监听路由变化
watch(() => route.params.tagName, (newTagName) => {
  if (newTagName && newTagName !== tagName.value) {
    tagName.value = newTagName
    finished.value = false
    currentPage.value = 1
    notesList.value = []
    loadNotes(1)
  }
})

// 生命周期
onMounted(() => {
  loadNotes(1)
})
</script>

<style scoped>
.tag-search {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.search-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.tag-info {
  background-color: #fff;
  padding: 20px 16px;
  border-bottom: 1px solid #ebedf0;
}

.tag-title {
  font-size: 24px;
  font-weight: 600;
  color: #1989fa;
  margin-bottom: 8px;
}

.tag-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.sort-section {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.notes-list {
  padding: 0 16px;
}

.note-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  cursor: pointer;
  transition: all 0.3s;
}

.note-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.note-user {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.user-avatar {
  margin-right: 8px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.publish-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.note-photos {
  margin-bottom: 12px;
}

.photo-grid {
  display: grid;
  gap: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.grid-1 {
  grid-template-columns: 1fr;
  max-height: 300px;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.photo-item {
  aspect-ratio: 1;
}

.note-content {
  margin-bottom: 12px;
}

.note-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.note-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-text :deep(.tag) {
  color: #1989fa;
  font-weight: 600;
}

.note-text :deep(.mention) {
  color: #ff6a00;
  font-weight: 600;
}

.note-stats {
  display: flex;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.stats-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.stats-item .van-icon {
  margin-right: 4px;
}

.related-tags {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  padding: 16px;
}

.related-tags h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 16px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.tag-item:hover {
  background-color: #e8f4fd;
}

.tag-name {
  color: #1989fa;
  font-size: 14px;
  margin-right: 4px;
}

.tag-count {
  color: #999;
  font-size: 12px;
}
</style>
