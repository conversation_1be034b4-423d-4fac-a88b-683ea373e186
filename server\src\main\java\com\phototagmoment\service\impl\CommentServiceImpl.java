package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.dto.CommentAddRequest;
import com.phototagmoment.dto.CommentDTO;
import com.phototagmoment.dto.CommentQueryRequest;
import com.phototagmoment.entity.*;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.*;
import com.phototagmoment.service.CommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论服务实现类
 */
@Slf4j
@Service
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private CommentLikeMapper commentLikeMapper;

    @Autowired
    private CommentTagMapper commentTagMapper;

    @Autowired
    private CommentMentionMapper commentMentionMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addComment(Long photoId, String content, Long userId) {
        // 检查照片是否存在
        Photo photo = photoMapper.selectById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 创建评论
        Comment comment = new Comment();
        comment.setPhotoId(photoId);
        comment.setUserId(userId);
        comment.setContent(content);
        comment.setLikeCount(0);
        // 确保设置回复数为0
        comment.setReplyCount(0);
        comment.setStatus(1);

        // 保存评论
        this.save(comment);

        // 增加照片评论数
        photoMapper.incrementCommentCount(photoId);

        return comment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCommentComplete(CommentAddRequest request, Long userId) {
        // 1. 验证照片是否存在
        Photo photo = photoMapper.selectById(request.getPhotoId());
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 2. 如果是回复，验证父评论是否存在
        if (request.getParentId() != null) {
            Comment parentComment = this.getById(request.getParentId());
            if (parentComment == null) {
                throw new BusinessException("父评论不存在或已删除");
            }
        }

        // 3. 创建评论
        Comment comment = new Comment();
        comment.setPhotoId(request.getPhotoId());
        comment.setUserId(userId);
        comment.setContent(request.getContent());
        comment.setParentId(request.getParentId());
        comment.setReplyToUserId(request.getReplyToUserId());
        comment.setLikeCount(0);
        comment.setReplyCount(0);
        comment.setStatus(1);

        // 4. 保存评论
        this.save(comment);

        // 5. 处理标签
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            saveCommentTags(comment.getId(), request.getTags());
        }

        // 6. 处理用户提及
        if (request.getMentions() != null && !request.getMentions().isEmpty()) {
            saveCommentMentions(comment.getId(), request.getMentions(), userId);
        }

        // 7. 更新统计数据
        if (request.getParentId() != null) {
            // 增加父评论回复数
            baseMapper.incrementReplyCount(request.getParentId());
        }
        // 增加照片评论数
        photoMapper.incrementCommentCount(request.getPhotoId());

        return comment.getId();
    }

    /**
     * 保存评论标签
     */
    private void saveCommentTags(Long commentId, List<String> tags) {
        for (String tagName : tags) {
            if (StringUtils.hasText(tagName)) {
                CommentTag commentTag = new CommentTag();
                commentTag.setCommentId(commentId);
                commentTag.setTagName(tagName.trim());
                commentTag.setCreatedAt(LocalDateTime.now());
                commentTagMapper.insert(commentTag);
            }
        }
    }

    /**
     * 保存评论用户提及
     */
    private void saveCommentMentions(Long commentId, List<String> mentions, Long mentionUserId) {
        for (String username : mentions) {
            if (StringUtils.hasText(username)) {
                // 查找被提及的用户
                User mentionedUser = userMapper.selectByUsername(username.trim());

                CommentMention commentMention = new CommentMention();
                commentMention.setCommentId(commentId);
                commentMention.setMentionedUserId(mentionedUser != null ? mentionedUser.getId() : null);
                commentMention.setMentionUserId(mentionUserId);
                commentMention.setMentionedUsername(username.trim());
                commentMention.setCreatedAt(LocalDateTime.now());
                commentMentionMapper.insert(commentMention);
            }
        }
    }

    @Override
    public List<String> getCommentTags(Long commentId) {
        return commentTagMapper.selectTagsByCommentId(commentId);
    }

    @Override
    public List<CommentMention> getCommentMentions(Long commentId) {
        return commentMentionMapper.selectMentionsByCommentId(commentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long replyComment(Long commentId, String content, Long userId, Long replyToUserId) {
        // 检查评论是否存在
        Comment parentComment = this.getById(commentId);
        if (parentComment == null) {
            throw new BusinessException("评论不存在或已删除");
        }

        // 创建回复
        Comment reply = new Comment();
        reply.setPhotoId(parentComment.getPhotoId());
        reply.setUserId(userId);
        reply.setContent(content);
        reply.setParentId(commentId);
        reply.setReplyToUserId(replyToUserId);
        reply.setLikeCount(0);
        reply.setReplyCount(0);
        reply.setStatus(1);

        // 保存回复
        this.save(reply);

        // 增加父评论回复数
        baseMapper.incrementReplyCount(commentId);

        // 增加照片评论数
        photoMapper.incrementCommentCount(parentComment.getPhotoId());

        return reply.getId();
    }

    @Override
    public CommentDTO getCommentDetail(Long commentId, Long userId) {
        return baseMapper.selectCommentDetail(commentId, userId);
    }

    @Override
    public IPage<CommentDTO> getPhotoComments(Long photoId, int page, int size, Long userId) {
        Page<CommentDTO> pageParam = new Page<>(page, size);
        return baseMapper.selectPhotoComments(pageParam, photoId, userId);
    }

    @Override
    public IPage<CommentDTO> getCommentReplies(Long commentId, int page, int size, Long userId) {
        Page<CommentDTO> pageParam = new Page<>(page, size);
        return baseMapper.selectCommentReplies(pageParam, commentId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean likeComment(Long commentId, Long userId) {
        // 检查评论是否存在
        Comment comment = this.getById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在或已删除");
        }

        // 检查是否已点赞
        LambdaQueryWrapper<CommentLike> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommentLike::getCommentId, commentId)
                .eq(CommentLike::getUserId, userId);
        if (commentLikeMapper.selectCount(queryWrapper) > 0) {
            return true;
        }

        // 创建点赞记录
        CommentLike commentLike = new CommentLike();
        commentLike.setCommentId(commentId);
        commentLike.setUserId(userId);
        commentLikeMapper.insert(commentLike);

        // 增加评论点赞数
        baseMapper.incrementLikeCount(commentId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlikeComment(Long commentId, Long userId) {
        // 检查评论是否存在
        Comment comment = this.getById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在或已删除");
        }

        // 检查是否已点赞
        LambdaQueryWrapper<CommentLike> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommentLike::getCommentId, commentId)
                .eq(CommentLike::getUserId, userId);
        if (commentLikeMapper.selectCount(queryWrapper) == 0) {
            return true;
        }

        // 删除点赞记录
        commentLikeMapper.delete(queryWrapper);

        // 减少评论点赞数
        baseMapper.decrementLikeCount(commentId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteComment(Long commentId, Long userId) {
        // 检查评论是否存在
        Comment comment = this.getById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在或已删除");
        }

        // 检查权限
        if (!comment.getUserId().equals(userId)) {
            throw new BusinessException("无权删除该评论");
        }

        // 删除评论
        this.removeById(commentId);

        // 如果是回复，减少父评论回复数
        if (comment.getParentId() != null) {
            baseMapper.decrementReplyCount(comment.getParentId());
        }

        // 减少照片评论数
        photoMapper.decrementCommentCount(comment.getPhotoId());

        return true;
    }

    @Override
    public IPage<CommentDTO> getCommentListForAdmin(CommentQueryRequest queryRequest) {
        Page<CommentDTO> page = new Page<>(queryRequest.getPage(), queryRequest.getSize());
        return baseMapper.selectCommentListForAdmin(page, queryRequest);
    }

    @Override
    public CommentDTO getCommentDetailForAdmin(Long commentId) {
        return baseMapper.selectCommentDetailForAdmin(commentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCommentStatusByAdmin(Long commentId, Integer status, String reason) {
        Comment comment = this.getById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }

        comment.setStatus(status);
        this.updateById(comment);

        // 记录操作日志
        log.info("管理员修改评论状态: commentId={}, status={}, reason={}", commentId, status, reason);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCommentStatusByAdmin(Long[] commentIds, Integer status, String reason) {
        for (Long commentId : commentIds) {
            updateCommentStatusByAdmin(commentId, status, reason);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReplyStatusByAdmin(Long replyId, Integer status, String reason) {
        Comment reply = this.getById(replyId);
        if (reply == null) {
            throw new BusinessException("回复不存在");
        }

        reply.setStatus(status);
        this.updateById(reply);

        // 记录操作日志
        log.info("管理员修改回复状态: replyId={}, status={}, reason={}", replyId, status, reason);

        return true;
    }

    @Override
    public Object getCommentStatistics() {
        return baseMapper.selectCommentStatistics();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCommentPermanentlyByAdmin(Long commentId, String reason) {
        Comment comment = this.getById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }

        // 删除所有回复
        LambdaQueryWrapper<Comment> replyWrapper = new LambdaQueryWrapper<>();
        replyWrapper.eq(Comment::getParentId, commentId);
        this.remove(replyWrapper);

        // 删除评论
        this.removeById(commentId);

        // 减少照片评论数
        photoMapper.decrementCommentCount(comment.getPhotoId());

        // 记录操作日志
        log.info("管理员永久删除评论: commentId={}, reason={}", commentId, reason);

        return true;
    }
}
