<template>
  <div>
    <el-form :model="configForm" label-width="180px">
      <el-form-item label="启用短信服务">
        <el-switch v-model="configForm.enabled" />
      </el-form-item>
      <el-form-item label="短信服务提供商">
        <el-select v-model="configForm.provider" placeholder="请选择短信服务提供商">
          <el-option label="阿里云" value="aliyun" />
          <el-option label="腾讯云" value="tencent" />
        </el-select>
      </el-form-item>

      <el-divider content-position="left">阿里云短信配置</el-divider>
      <el-form-item label="AccessKey ID">
        <el-input v-model="configForm.aliyunAccessKeyId" placeholder="请输入阿里云AccessKey ID" />
      </el-form-item>
      <el-form-item label="AccessKey Secret">
        <el-input v-model="configForm.aliyunAccessKeySecret" placeholder="请输入阿里云AccessKey Secret" show-password />
      </el-form-item>
      <el-form-item label="短信签名">
        <el-input v-model="configForm.aliyunSignName" placeholder="请输入短信签名" />
      </el-form-item>
      <el-form-item label="模板代码">
        <el-input v-model="configForm.aliyunTemplateCode" placeholder="请输入模板代码" />
      </el-form-item>

      <el-divider content-position="left">验证码配置</el-divider>
      <el-form-item label="验证码长度">
        <el-input-number v-model="configForm.verificationCodeLength" :min="4" :max="8" />
      </el-form-item>
      <el-form-item label="验证码有效期(秒)">
        <el-input-number v-model="configForm.verificationCodeExpiration" :min="60" :max="600" />
      </el-form-item>
      <el-form-item label="每日最大发送次数">
        <el-input-number v-model="configForm.verificationCodeDailyLimit" :min="1" :max="20" />
      </el-form-item>
      <el-form-item label="发送间隔(秒)">
        <el-input-number v-model="configForm.verificationCodeInterval" :min="30" :max="300" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
        <el-button type="success" @click="testSms">测试短信</el-button>
      </el-form-item>
    </el-form>

    <!-- 测试短信对话框 -->
    <el-dialog title="测试短信" v-model="dialogVisible" width="400px">
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="testForm.phone" placeholder="请输入手机号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="sendTestSms" :loading="testLoading">发送测试短信</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getSystemConfig, updateSystemConfig } from '@/api/system/config'

export default {
  name: 'SmsConfig',
  setup() {
    const loading = ref(false)
    const dialogVisible = ref(false)
    const testLoading = ref(false)
    
    const configForm = reactive({
      enabled: false,
      provider: 'aliyun',
      aliyunAccessKeyId: '',
      aliyunAccessKeySecret: '',
      aliyunSignName: '',
      aliyunTemplateCode: '',
      verificationCodeLength: 6,
      verificationCodeExpiration: 300,
      verificationCodeDailyLimit: 10,
      verificationCodeInterval: 60
    })
    
    const testForm = reactive({
      phone: ''
    })

    // 获取配置
    const fetchConfig = async () => {
      loading.value = true
      try {
        // 获取短信配置
        const config = await getSystemConfig([
          'sms.enabled',
          'sms.provider',
          'sms.aliyun.access-key-id',
          'sms.aliyun.access-key-secret',
          'sms.aliyun.sign-name',
          'sms.aliyun.template-code',
          'sms.verification-code.length',
          'sms.verification-code.expiration',
          'sms.verification-code.daily-limit',
          'sms.verification-code.interval'
        ])

        // 设置表单数据
        configForm.enabled = config['sms.enabled'] === 'true'
        configForm.provider = config['sms.provider'] || 'aliyun'
        configForm.aliyunAccessKeyId = config['sms.aliyun.access-key-id'] || ''
        configForm.aliyunAccessKeySecret = config['sms.aliyun.access-key-secret'] || ''
        configForm.aliyunSignName = config['sms.aliyun.sign-name'] || ''
        configForm.aliyunTemplateCode = config['sms.aliyun.template-code'] || ''
        configForm.verificationCodeLength = parseInt(config['sms.verification-code.length'] || '6')
        configForm.verificationCodeExpiration = parseInt(config['sms.verification-code.expiration'] || '300')
        configForm.verificationCodeDailyLimit = parseInt(config['sms.verification-code.daily-limit'] || '10')
        configForm.verificationCodeInterval = parseInt(config['sms.verification-code.interval'] || '60')

        loading.value = false
      } catch (error) {
        console.error('获取短信配置失败', error)
        ElMessage.error('获取配置失败，请稍后再试')
        loading.value = false
      }
    }

    // 保存配置
    const saveConfig = async () => {
      loading.value = true
      try {
        // 构建配置数据
        const configData = {
          'sms.enabled': configForm.enabled.toString(),
          'sms.provider': configForm.provider,
          'sms.aliyun.access-key-id': configForm.aliyunAccessKeyId,
          'sms.aliyun.access-key-secret': configForm.aliyunAccessKeySecret,
          'sms.aliyun.sign-name': configForm.aliyunSignName,
          'sms.aliyun.template-code': configForm.aliyunTemplateCode,
          'sms.verification-code.length': configForm.verificationCodeLength.toString(),
          'sms.verification-code.expiration': configForm.verificationCodeExpiration.toString(),
          'sms.verification-code.daily-limit': configForm.verificationCodeDailyLimit.toString(),
          'sms.verification-code.interval': configForm.verificationCodeInterval.toString()
        }

        // 更新配置
        const res = await updateSystemConfig(configData)
        if (res.code === 200) {
          ElMessage.success('保存成功')
        } else {
          ElMessage.error(res.message || '保存失败')
        }
        loading.value = false
      } catch (error) {
        console.error('保存短信配置失败', error)
        ElMessage.error('保存失败，请稍后再试')
        loading.value = false
      }
    }

    // 测试短信
    const testSms = () => {
      dialogVisible.value = true
    }

    // 发送测试短信
    const sendTestSms = async () => {
      if (!testForm.phone) {
        ElMessage.warning('请输入手机号')
        return
      }

      testLoading.value = true
      try {
        // 调用发送测试短信接口
        // 这里需要实现一个测试短信的接口
        ElMessage.success('测试短信发送成功')
        dialogVisible.value = false
      } catch (error) {
        console.error('发送测试短信失败', error)
        ElMessage.error('发送测试短信失败')
      } finally {
        testLoading.value = false
      }
    }

    onMounted(() => {
      fetchConfig()
    })

    return {
      loading,
      configForm,
      dialogVisible,
      testForm,
      testLoading,
      fetchConfig,
      saveConfig,
      testSms,
      sendTestSms
    }
  }
}
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}
</style>
