<template>
  <div class="file-manage-container">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入文件名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="文件类型">
          <el-select v-model="searchForm.fileType" placeholder="请选择" clearable style="width: 150px">
            <el-option label="图片" value="image" />
            <el-option label="视频" value="video" />
            <el-option label="音频" value="audio" />
            <el-option label="文档" value="document" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="上传时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="operation-card">
      <div class="operation-bar">
        <div class="left-operations">
          <el-button type="primary" @click="showUploadDialog = true">
            <el-icon><upload-filled /></el-icon>
            上传文件
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedFiles.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><delete /></el-icon>
            批量删除
          </el-button>
          <el-button 
            type="warning"
            :disabled="selectedFiles.length === 0"
            @click="handleBatchMoveToTrash"
          >
            <el-icon><folder-delete /></el-icon>
            移至回收站
          </el-button>
        </div>
        <div class="right-operations">
          <el-button @click="handleRefresh">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="showTrashDialog = true">
            <el-icon><delete /></el-icon>
            回收站
          </el-button>
          <el-button @click="showStatisticsDialog = true">
            <el-icon><data-analysis /></el-icon>
            统计信息
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 文件列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="fileList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="文件预览" width="100">
          <template #default="{ row }">
            <div class="file-preview">
              <el-image
                v-if="row.isImage"
                :src="row.fileUrl"
                :preview-src-list="[row.fileUrl]"
                fit="cover"
                style="width: 60px; height: 60px; border-radius: 4px;"
              />
              <div v-else class="file-icon">
                <el-icon size="40" :class="getFileIconClass(row.extension)">
                  <document />
                </el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="文件名" min-width="200">
          <template #default="{ row }">
            <div class="file-info">
              <div class="file-name" :title="row.originalName">{{ row.originalName }}</div>
              <div class="file-meta">
                <el-tag size="small" type="info">{{ row.extension?.toUpperCase() }}</el-tag>
                <span class="file-size">{{ row.fileSizeFormatted }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="文件类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getFileTypeTagType(row.category)">
              {{ getFileTypeLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="上传者" width="120">
          <template #default="{ row }">
            <div>
              <div>{{ row.uploaderName || '未知' }}</div>
              <el-tag size="small" :type="row.uploaderType === 'ADMIN' ? 'warning' : 'primary'">
                {{ row.uploaderType === 'ADMIN' ? '管理员' : '用户' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="存储类型" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="row.storageType === 'QINIU' ? 'success' : 'info'">
              {{ row.storageType === 'QINIU' ? '七牛云' : '本地' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="访问次数" width="100">
          <template #default="{ row }">
            {{ row.accessCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="上传时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handlePreview(row)">
              <el-icon><view /></el-icon>
              预览
            </el-button>
            <el-button size="small" @click="handleDownload(row)">
              <el-icon><download /></el-icon>
              下载
            </el-button>
            <el-dropdown @command="(command) => handleMoreAction(command, row)">
              <el-button size="small">
                更多<el-icon><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="rename">重命名</el-dropdown-item>
                  <el-dropdown-item command="move">移动</el-dropdown-item>
                  <el-dropdown-item command="copy">复制链接</el-dropdown-item>
                  <el-dropdown-item command="trash" divided>移至回收站</el-dropdown-item>
                  <el-dropdown-item command="delete" style="color: #f56c6c;">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传文件"
      width="600px"
      :close-on-click-modal="false"
    >
      <FileUpload
        ref="fileUploadRef"
        :file-type="uploadFileType"
        :multiple="true"
        :limit="10"
        :max-size="50"
        @success="handleUploadSuccess"
      />
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUploadConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 回收站对话框 -->
    <el-dialog
      v-model="showTrashDialog"
      title="回收站"
      width="800px"
      :close-on-click-modal="false"
    >
      <!-- 回收站内容 -->
      <TrashFiles @restore="handleRefresh" @clear="handleRefresh" />
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog
      v-model="showStatisticsDialog"
      title="文件统计信息"
      width="600px"
      :close-on-click-modal="false"
    >
      <FileStatistics />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  UploadFilled,
  Delete,
  FolderDelete,
  DataAnalysis,
  Document,
  View,
  Download,
  ArrowDown
} from '@element-plus/icons-vue'
import {
  getFileList,
  batchDeleteFiles,
  moveToTrash,
  generateAccessUrl,
  type FileInfo,
  type FileListParams
} from '@/api/file'
import FileUpload from '@/components/FileUpload/index.vue'
import TrashFiles from './components/TrashFiles.vue'
import FileStatistics from './components/FileStatistics.vue'
import { formatDateTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const fileList = ref<FileInfo[]>([])
const selectedFiles = ref<FileInfo[]>([])
const showUploadDialog = ref(false)
const showTrashDialog = ref(false)
const showStatisticsDialog = ref(false)
const uploadFileType = ref('general')
const fileUploadRef = ref()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  fileType: '',
  uploaderId: undefined
})

const dateRange = ref([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 计算属性
const searchParams = computed((): FileListParams => {
  const params: FileListParams = {
    page: pagination.page,
    size: pagination.size,
    ...searchForm
  }
  
  if (dateRange.value && dateRange.value.length === 2) {
    params.startDate = dateRange.value[0]
    params.endDate = dateRange.value[1]
  }
  
  return params
})

// 生命周期
onMounted(() => {
  loadFileList()
})

// 方法
const loadFileList = async () => {
  loading.value = true
  try {
    const response = await getFileList(searchParams.value)
    if ((response as any).code === 200) {
      fileList.value = (response as any).data.records
      pagination.total = (response as any).data.total
    }
  } catch (error) {
    console.error('加载文件列表失败:', error)
    ElMessage.error('加载文件列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadFileList()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    fileType: '',
    uploaderId: undefined
  })
  dateRange.value = []
  pagination.page = 1
  loadFileList()
}

const handleRefresh = () => {
  loadFileList()
}

const handleSelectionChange = (selection: FileInfo[]) => {
  selectedFiles.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadFileList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadFileList()
}

const handleUploadSuccess = (files: any[]) => {
  ElMessage.success('文件上传成功')
  loadFileList()
}

const handleUploadConfirm = () => {
  showUploadDialog.value = false
  loadFileList()
}

const handleBatchDelete = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请选择要删除的文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const fileIds = selectedFiles.value.map(file => file.id)
    const response = await batchDeleteFiles(fileIds)
    
    if ((response as any).code === 200) {
      ElMessage.success('删除成功')
      loadFileList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const handleBatchMoveToTrash = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请选择要移动的文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedFiles.value.length} 个文件移至回收站吗？`,
      '确认移动',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    for (const file of selectedFiles.value) {
      await moveToTrash(file.id)
    }
    
    ElMessage.success('移动成功')
    loadFileList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移动文件失败:', error)
      ElMessage.error('移动文件失败')
    }
  }
}

const handlePreview = (file: FileInfo) => {
  if (file.isImage) {
    // 图片预览
    window.open(file.fileUrl, '_blank')
  } else {
    // 其他文件类型的预览
    ElMessage.info('该文件类型不支持预览')
  }
}

const handleDownload = (file: FileInfo) => {
  const link = document.createElement('a')
  link.href = file.fileUrl
  link.download = file.originalName
  link.click()
}

const handleMoreAction = async (command: string, file: FileInfo) => {
  switch (command) {
    case 'rename':
      // 重命名逻辑
      break
    case 'move':
      // 移动逻辑
      break
    case 'copy':
      // 复制链接
      try {
        await navigator.clipboard.writeText(file.fileUrl)
        ElMessage.success('链接已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
      break
    case 'trash':
      await moveToTrash(file.id)
      ElMessage.success('已移至回收站')
      loadFileList()
      break
    case 'delete':
      // 删除逻辑
      break
  }
}

// 工具方法
const getFileIconClass = (extension: string) => {
  // 根据文件扩展名返回图标类名
  return 'file-icon'
}

const getFileTypeTagType = (category: string) => {
  const typeMap = {
    image: 'success',
    video: 'warning',
    audio: 'info',
    document: 'primary',
    other: 'info'
  }
  return typeMap[category] || 'info'
}

const getFileTypeLabel = (category: string) => {
  const labelMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档',
    other: '其他'
  }
  return labelMap[category] || '其他'
}
</script>

<style scoped>
.file-manage-container {
  padding: 20px;
}

.search-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-operations,
.right-operations {
  display: flex;
  gap: 8px;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
