package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.QiniuStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * 文件控制器
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Tag(name = "文件接口", description = "文件上传、下载接口")
public class FileController {

    @Value("${storage.local.path}")
    private String storagePath;

    @Autowired
    private QiniuStorageService qiniuStorageService;

    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "上传单个文件")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<String> uploadFile(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ApiResponse.failed("上传文件不能为空");
        }

        try {
            // 获取文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = UUID.randomUUID().toString().replace("-", "") + extension;

            // 获取当前用户ID
            Long userId = SecurityUtil.getCurrentUserId();

            // 生成存储路径
            String relativePath = "uploads/" + userId + "/" + filename;
            Path targetPath = Paths.get(storagePath, relativePath);

            // 创建目录
            Files.createDirectories(targetPath.getParent());

            // 保存文件
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);

            // 返回文件访问路径
            return ApiResponse.success("/api/file/" + relativePath);
        } catch (IOException e) {
            log.error("上传文件失败", e);
            return ApiResponse.failed("上传文件失败");
        }
    }

    @GetMapping("/{userId}/{filename:.+}")
    @Operation(summary = "获取文件", description = "根据用户ID和文件名获取文件")
    public ResponseEntity<Resource> getFile(
            @Parameter(description = "用户ID") @PathVariable String userId,
            @Parameter(description = "文件名") @PathVariable String filename) {
        try {
            // 构建文件路径
            Path filePath = Paths.get(storagePath, "uploads", userId, filename);
            Resource resource = new UrlResource(filePath.toUri());

            // 检查文件是否存在
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }

            // 获取文件类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // 返回文件
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
        } catch (MalformedURLException e) {
            log.error("文件路径错误", e);
            return ResponseEntity.badRequest().build();
        } catch (IOException e) {
            log.error("获取文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/photos/{userId}/{filename:.+}")
    @Operation(summary = "获取照片", description = "根据用户ID和文件名获取照片")
    public ResponseEntity<Resource> getPhoto(
            @Parameter(description = "用户ID") @PathVariable String userId,
            @Parameter(description = "文件名") @PathVariable String filename) {
        try {
            // 构建文件路径
            Path filePath = Paths.get(storagePath, "photos", userId, filename);
            Resource resource = new UrlResource(filePath.toUri());

            // 检查文件是否存在
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }

            // 获取文件类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "image/jpeg";
            }

            // 返回文件
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
        } catch (MalformedURLException e) {
            log.error("文件路径错误", e);
            return ResponseEntity.badRequest().build();
        } catch (IOException e) {
            log.error("获取文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/photos/{userId}/thumbnails/{filename:.+}")
    @Operation(summary = "获取照片缩略图", description = "根据用户ID和文件名获取照片缩略图")
    public ResponseEntity<Resource> getPhotoThumbnail(
            @Parameter(description = "用户ID") @PathVariable String userId,
            @Parameter(description = "文件名") @PathVariable String filename) {
        try {
            // 构建文件路径
            Path filePath = Paths.get(storagePath, "photos", userId, "thumbnails", filename);
            Resource resource = new UrlResource(filePath.toUri());

            // 检查文件是否存在
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }

            // 获取文件类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "image/jpeg";
            }

            // 返回文件
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
        } catch (MalformedURLException e) {
            log.error("文件路径错误", e);
            return ResponseEntity.badRequest().build();
        } catch (IOException e) {
            log.error("获取文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取文件访问URL
     * 用于前端获取带有下载凭证的文件URL
     */
    @GetMapping("/url")
    @Operation(summary = "获取文件访问URL", description = "获取带有下载凭证的文件URL，用于访问私有空间的文件")
    public ApiResponse<String> getFileUrl(
            @Parameter(description = "文件名") @RequestParam String fileName) {
        try {
            String fileUrl = qiniuStorageService.getFileUrl(fileName);
            if (fileUrl == null || fileUrl.trim().isEmpty()) {
                return ApiResponse.failed("获取文件访问URL失败");
            }
            return ApiResponse.success(fileUrl);
        } catch (Exception e) {
            log.error("获取文件访问URL失败", e);
            return ApiResponse.failed("获取文件访问URL失败: " + e.getMessage());
        }
    }

    /**
     * 获取私有URL（带下载凭证）
     * 用于前端获取带有下载凭证的私有URL
     */
    @GetMapping("/private-url")
    @Operation(summary = "获取私有URL", description = "获取带下载凭证的私有URL，用于访问私有空间的文件")
    public ApiResponse<String> getPrivateUrl(
            @Parameter(description = "文件路径") @RequestParam String path) {
        try {
            String url = qiniuStorageService.getFileUrl(path);
            if (url == null || url.trim().isEmpty()) {
                return ApiResponse.failed("获取私有URL失败");
            }
            return ApiResponse.success(url);
        } catch (Exception e) {
            log.error("获取私有URL失败", e);
            return ApiResponse.failed("获取私有URL失败: " + e.getMessage());
        }
    }
}
