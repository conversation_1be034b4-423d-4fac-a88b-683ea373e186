<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button type="primary" @click="handleCreate">
        <el-icon><Plus /></el-icon>
        新增权限
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="permissionList"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ children: 'children' }"
    >
      <el-table-column prop="name" label="权限名称" min-width="180" />
      <el-table-column prop="code" label="权限编码" min-width="180" />
      <el-table-column prop="type" label="权限类型" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.type === 1" type="success">菜单</el-tag>
          <el-tag v-else-if="scope.row.type === 2" type="warning">按钮</el-tag>
          <el-tag v-else-if="scope.row.type === 3" type="info">接口</el-tag>
          <el-tag v-else type="danger">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="path" label="路由路径" min-width="150" />
      <el-table-column prop="component" label="组件路径" min-width="150" />
      <el-table-column prop="sort" label="排序" width="80" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" min-width="150" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button
            v-if="scope.row.isSystem !== 1"
            type="primary"
            link
            @click="handleUpdate(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.isSystem !== 1"
            type="danger"
            link
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
          <el-button
            v-if="scope.row.isSystem === 1"
            type="info"
            link
            disabled
          >
            系统内置
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 权限表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入权限编码" />
        </el-form-item>
        <el-form-item label="权限类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择权限类型" style="width: 100%">
            <el-option label="菜单" :value="1" />
            <el-option label="按钮" :value="2" />
            <el-option label="接口" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="父权限" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="permissionOptions"
            :props="{ label: 'name', value: 'id', children: 'children' }"
            placeholder="请选择父权限"
            check-strictly
            style="width: 100%"
            clearable
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="9999" />
        </el-form-item>
        <el-form-item v-if="form.type === 1" label="图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请输入图标" />
        </el-form-item>
        <el-form-item v-if="form.type === 1" label="路由路径" prop="path">
          <el-input v-model="form.path" placeholder="请输入路由路径" />
        </el-form-item>
        <el-form-item v-if="form.type === 1" label="组件路径" prop="component">
          <el-input v-model="form.component" placeholder="请输入组件路径" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  getPermissionTree,
  getPermissionDetail,
  createPermission,
  updatePermission,
  deletePermission,
  updatePermissionStatus
} from '@/api/system/permission'

// 权限列表
const permissionList = ref([])
// 加载状态
const loading = ref(false)
// 对话框标题
const dialogTitle = ref('新增权限')
// 对话框可见性
const dialogVisible = ref(false)
// 表单引用
const formRef = ref()
// 表单数据
const form = reactive({
  id: undefined,
  name: '',
  code: '',
  description: '',
  type: 1,
  parentId: undefined,
  sort: 0,
  icon: '',
  path: '',
  component: '',
  status: 1
})
// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入权限编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择权限类型', trigger: 'change' }]
}

// 权限选项（用于父权限选择）
const permissionOptions = computed(() => {
  // 添加一个顶级节点
  return [{ id: 0, name: '顶级权限', children: permissionList.value }]
})

// 获取权限树
const fetchPermissionTree = async () => {
  loading.value = true
  try {
    const { data } = await getPermissionTree()
    permissionList.value = data
  } catch (error) {
    console.error('获取权限树失败', error)
    ElMessage.error('获取权限树失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.id = undefined
  form.name = ''
  form.code = ''
  form.description = ''
  form.type = 1
  form.parentId = undefined
  form.sort = 0
  form.icon = ''
  form.path = ''
  form.component = ''
  form.status = 1
  formRef.value?.resetFields()
}

// 处理新增权限
const handleCreate = () => {
  resetForm()
  dialogTitle.value = '新增权限'
  dialogVisible.value = true
}

// 处理编辑权限
const handleUpdate = async (row) => {
  resetForm()
  dialogTitle.value = '编辑权限'
  
  try {
    const { data } = await getPermissionDetail(row.id)
    Object.assign(form, data)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取权限详情失败', error)
    ElMessage.error('获取权限详情失败')
  }
}

// 处理删除权限
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该权限吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deletePermission(row.id)
      ElMessage.success('删除成功')
      fetchPermissionTree()
    } catch (error) {
      console.error('删除权限失败', error)
      ElMessage.error('删除权限失败')
    }
  }).catch(() => {})
}

// 处理状态变更
const handleStatusChange = async (row) => {
  try {
    await updatePermissionStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('更新权限状态失败', error)
    ElMessage.error('更新权限状态失败')
    // 恢复状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 提交表单
const submitForm = async () => {
  formRef.value?.validate(async (valid) => {
    if (!valid) return
    
    try {
      if (form.id) {
        // 更新权限
        await updatePermission(form.id, form)
        ElMessage.success('更新成功')
      } else {
        // 创建权限
        await createPermission(form)
        ElMessage.success('创建成功')
      }
      dialogVisible.value = false
      fetchPermissionTree()
    } catch (error) {
      console.error('保存权限失败', error)
      ElMessage.error('保存权限失败')
    }
  })
}

// 页面加载时获取权限树
onMounted(() => {
  fetchPermissionTree()
})
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
</style>
