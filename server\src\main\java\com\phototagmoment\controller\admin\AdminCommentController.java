package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.CommentDTO;
import com.phototagmoment.dto.CommentQueryRequest;
import com.phototagmoment.service.CommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 管理员评论管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/comment")
@Tag(name = "管理员评论管理", description = "管理员评论管理相关接口")
@Validated
@PreAuthorize("hasRole('ADMIN')")
public class AdminCommentController {

    @Autowired
    private CommentService commentService;

    @GetMapping("/list")
    @Operation(summary = "获取评论列表", description = "分页获取评论列表，支持筛选")
    public ApiResponse<IPage<CommentDTO>> getCommentList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "评论内容关键词") @RequestParam(required = false) String content,
            @Parameter(description = "用户昵称") @RequestParam(required = false) String username,
            @Parameter(description = "状态：1-正常，0-已删除") @RequestParam(required = false) Integer status,
            @Parameter(description = "照片ID") @RequestParam(required = false) Long photoId,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime) {
        
        CommentQueryRequest queryRequest = new CommentQueryRequest();
        queryRequest.setPage(page);
        queryRequest.setSize(size);
        queryRequest.setContent(content);
        queryRequest.setUsername(username);
        queryRequest.setStatus(status);
        queryRequest.setPhotoId(photoId);
        queryRequest.setStartTime(startTime);
        queryRequest.setEndTime(endTime);
        
        IPage<CommentDTO> commentPage = commentService.getCommentListForAdmin(queryRequest);
        return ApiResponse.success(commentPage);
    }

    @GetMapping("/{commentId}")
    @Operation(summary = "获取评论详情", description = "获取评论详情，包含回复列表")
    public ApiResponse<CommentDTO> getCommentDetail(
            @Parameter(description = "评论ID") @PathVariable Long commentId) {
        CommentDTO commentDTO = commentService.getCommentDetailForAdmin(commentId);
        return ApiResponse.success(commentDTO);
    }

    @PutMapping("/{commentId}/status")
    @Operation(summary = "修改评论状态", description = "删除或恢复评论")
    public ApiResponse<Boolean> updateCommentStatus(
            @Parameter(description = "评论ID") @PathVariable @NotNull Long commentId,
            @Parameter(description = "状态：1-正常，0-删除") @RequestParam @NotNull Integer status,
            @Parameter(description = "操作原因") @RequestParam(required = false) String reason) {
        
        boolean result = commentService.updateCommentStatusByAdmin(commentId, status, reason);
        return ApiResponse.success(result, status == 1 ? "评论恢复成功" : "评论删除成功");
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量修改评论状态", description = "批量删除或恢复评论")
    public ApiResponse<Boolean> batchUpdateCommentStatus(
            @Parameter(description = "评论ID列表") @RequestParam String commentIds,
            @Parameter(description = "状态：1-正常，0-删除") @RequestParam @NotNull Integer status,
            @Parameter(description = "操作原因") @RequestParam(required = false) String reason) {
        
        String[] idArray = commentIds.split(",");
        Long[] ids = new Long[idArray.length];
        for (int i = 0; i < idArray.length; i++) {
            ids[i] = Long.parseLong(idArray[i].trim());
        }
        
        boolean result = commentService.batchUpdateCommentStatusByAdmin(ids, status, reason);
        return ApiResponse.success(result, status == 1 ? "批量恢复成功" : "批量删除成功");
    }

    @PutMapping("/{commentId}/reply/{replyId}/status")
    @Operation(summary = "修改回复状态", description = "删除或恢复回复")
    public ApiResponse<Boolean> updateReplyStatus(
            @Parameter(description = "评论ID") @PathVariable @NotNull Long commentId,
            @Parameter(description = "回复ID") @PathVariable @NotNull Long replyId,
            @Parameter(description = "状态：1-正常，0-删除") @RequestParam @NotNull Integer status,
            @Parameter(description = "操作原因") @RequestParam(required = false) String reason) {
        
        boolean result = commentService.updateReplyStatusByAdmin(replyId, status, reason);
        return ApiResponse.success(result, status == 1 ? "回复恢复成功" : "回复删除成功");
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取评论统计", description = "获取评论统计信息")
    public ApiResponse<Object> getCommentStatistics() {
        Object statistics = commentService.getCommentStatistics();
        return ApiResponse.success(statistics);
    }

    @DeleteMapping("/{commentId}")
    @Operation(summary = "永久删除评论", description = "永久删除评论及其所有回复")
    public ApiResponse<Boolean> deleteCommentPermanently(
            @Parameter(description = "评论ID") @PathVariable @NotNull Long commentId,
            @Parameter(description = "删除原因") @RequestParam(required = false) String reason) {
        
        boolean result = commentService.deleteCommentPermanentlyByAdmin(commentId, reason);
        return ApiResponse.success(result, "评论永久删除成功");
    }
}
