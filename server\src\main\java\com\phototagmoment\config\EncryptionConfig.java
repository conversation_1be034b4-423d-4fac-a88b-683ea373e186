package com.phototagmoment.config;

import com.phototagmoment.util.EncryptionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 加密配置类
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "encryption")
public class EncryptionConfig {

    private final Environment environment;

    /**
     * 是否启用加密
     */
    private boolean enabled = true;

    /**
     * 用户数据加密密钥
     */
    private String userDataKey;

    /**
     * 照片加密密钥
     */
    private String photoKey;

    /**
     * 传输加密RSA公钥
     */
    private String rsaPublicKey;

    /**
     * 传输加密RSA私钥
     */
    private String rsaPrivateKey;

    /**
     * 需要加密的用户字段
     */
    private String[] encryptedUserFields = {"phone", "email", "realName", "idCard"};

    /**
     * 构造函数
     *
     * @param environment 环境变量
     */
    public EncryptionConfig(Environment environment) {
        this.environment = environment;
    }

    /**
     * 初始化加密配置
     */
    @PostConstruct
    public void init() {
        // 无论环境如何，如果启用了加密但密钥为空，则自动生成
        if (enabled) {
            try {
                // 尝试从系统属性或环境变量中获取密钥
                String envUserDataKey = System.getProperty("ENCRYPTION_USER_DATA_KEY");
                if (envUserDataKey == null) {
                    envUserDataKey = System.getenv("ENCRYPTION_USER_DATA_KEY");
                }

                String envPhotoKey = System.getProperty("ENCRYPTION_PHOTO_KEY");
                if (envPhotoKey == null) {
                    envPhotoKey = System.getenv("ENCRYPTION_PHOTO_KEY");
                }

                String envRsaPublicKey = System.getProperty("ENCRYPTION_RSA_PUBLIC_KEY");
                if (envRsaPublicKey == null) {
                    envRsaPublicKey = System.getenv("ENCRYPTION_RSA_PUBLIC_KEY");
                }

                String envRsaPrivateKey = System.getProperty("ENCRYPTION_RSA_PRIVATE_KEY");
                if (envRsaPrivateKey == null) {
                    envRsaPrivateKey = System.getenv("ENCRYPTION_RSA_PRIVATE_KEY");
                }

                // 使用环境变量中的密钥（如果存在）
                if (envUserDataKey != null && !envUserDataKey.isEmpty()) {
                    userDataKey = envUserDataKey;
                    log.info("从环境变量加载用户数据加密密钥");
                }

                if (envPhotoKey != null && !envPhotoKey.isEmpty()) {
                    photoKey = envPhotoKey;
                    log.info("从环境变量加载照片加密密钥");
                }

                if (envRsaPublicKey != null && !envRsaPublicKey.isEmpty()) {
                    rsaPublicKey = envRsaPublicKey;
                    log.info("从环境变量加载RSA公钥");
                }

                if (envRsaPrivateKey != null && !envRsaPrivateKey.isEmpty()) {
                    rsaPrivateKey = envRsaPrivateKey;
                    log.info("从环境变量加载RSA私钥");
                }

                // 如果密钥仍然为空，则生成新密钥
                if (userDataKey == null || userDataKey.isEmpty()) {
                    userDataKey = EncryptionUtil.generateAESKey();
//                    log.info("自动生成用户数据加密密钥: {}", userDataKey);
                    // 将生成的密钥设置为系统属性，以便在应用重启前保持一致
                    System.setProperty("ENCRYPTION_USER_DATA_KEY", userDataKey);
                }

                if (photoKey == null || photoKey.isEmpty()) {
                    photoKey = EncryptionUtil.generateAESKey();
//                    log.info("自动生成照片加密密钥: {}", photoKey);
                    System.setProperty("ENCRYPTION_PHOTO_KEY", photoKey);
                }

                if ((rsaPublicKey == null || rsaPublicKey.isEmpty()) ||
                    (rsaPrivateKey == null || rsaPrivateKey.isEmpty())) {
                    String[] keyPair = EncryptionUtil.generateRSAKeyPair();
                    rsaPublicKey = keyPair[0];
                    rsaPrivateKey = keyPair[1];
//                    log.info("自动生成RSA密钥对");
//                    log.info("RSA公钥: {}", rsaPublicKey);
//                    log.info("RSA私钥: {}", rsaPrivateKey);
                    System.setProperty("ENCRYPTION_RSA_PUBLIC_KEY", rsaPublicKey);
                    System.setProperty("ENCRYPTION_RSA_PRIVATE_KEY", rsaPrivateKey);
                }

                // 验证密钥有效性
                validateKeys();

            } catch (Exception e) {
                log.error("生成加密密钥失败", e);
                // 如果生成失败，禁用加密功能
                enabled = false;
                log.warn("由于密钥生成失败，加密功能已被禁用");
            }
        }
    }

    /**
     * 验证密钥有效性
     *
     * @throws Exception 验证失败时抛出异常
     */
    private void validateKeys() throws Exception {
        // 验证AES密钥
        if (userDataKey != null && !userDataKey.isEmpty()) {
            try {
                // 尝试使用密钥进行简单的加解密测试
                String testData = "test";
                String encrypted = EncryptionUtil.encryptAES(testData, userDataKey);
                String decrypted = EncryptionUtil.decryptAES(encrypted, userDataKey);

                if (!testData.equals(decrypted)) {
                    throw new Exception("用户数据加密密钥验证失败");
                }

                log.info("用户数据加密密钥验证成功");
            } catch (Exception e) {
                log.error("用户数据加密密钥验证失败", e);
                throw e;
            }
        }

        // 验证RSA密钥对
        if (rsaPublicKey != null && !rsaPublicKey.isEmpty() &&
            rsaPrivateKey != null && !rsaPrivateKey.isEmpty()) {
            try {
                // 尝试使用密钥对进行简单的加解密测试
                String testData = "test";
                String encrypted = EncryptionUtil.encryptRSA(testData, rsaPublicKey);
                String decrypted = EncryptionUtil.decryptRSA(encrypted, rsaPrivateKey);

                if (!testData.equals(decrypted)) {
                    throw new Exception("RSA密钥对验证失败");
                }

                log.info("RSA密钥对验证成功");
            } catch (Exception e) {
                log.error("RSA密钥对验证失败", e);
                throw e;
            }
        }
    }

    /**
     * 获取加密配置信息
     *
     * @return 加密配置信息
     */
    @Bean
    public Map<String, Object> encryptionInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("enabled", enabled);
        info.put("encryptedUserFields", encryptedUserFields);
        info.put("rsaPublicKey", rsaPublicKey);
        return info;
    }
}
