package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 照片笔记DTO
 */
@Data
@Schema(description = "照片笔记信息")
public class PhotoNoteDTO {

    @Schema(description = "照片笔记ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "标题（可选，最多100字符）")
    @Size(max = 100, message = "标题长度不能超过100个字符")
    private String title;

    @Schema(description = "正文内容（必填，最多2000字符）")
    @NotBlank(message = "正文内容不能为空")
    @Size(max = 2000, message = "正文内容长度不能超过2000个字符")
    private String content;

    @Schema(description = "处理后的正文内容（包含Tag和@用户的HTML标记）")
    private String processedContent;

    @Schema(description = "照片列表")
    @NotEmpty(message = "至少需要上传一张照片")
    @Size(min = 1, max = 9, message = "照片数量必须在1-9张之间")
    private List<PhotoNoteImageDTO> images;

    @Schema(description = "标签列表")
    @Size(max = 10, message = "最多支持10个标签")
    private List<String> tags;

    @Schema(description = "@用户列表")
    @Size(max = 10, message = "最多支持@10个用户")
    private List<PhotoNoteMentionDTO> mentions;

    @Schema(description = "照片数量")
    private Integer photoCount;

    @Schema(description = "浏览量")
    private Integer viewCount;

    @Schema(description = "点赞数")
    private Integer likeCount;

    @Schema(description = "评论数")
    private Integer commentCount;

    @Schema(description = "分享数")
    private Integer shareCount;

    @Schema(description = "可见性: 0-私密, 1-公开, 2-好友可见")
    @Min(value = 0, message = "可见性值无效")
    @Max(value = 2, message = "可见性值无效")
    private Integer visibility;

    @Schema(description = "是否允许评论: 0-不允许, 1-允许")
    @Min(value = 0, message = "评论设置值无效")
    @Max(value = 1, message = "评论设置值无效")
    private Integer allowComment;

    @Schema(description = "状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除")
    private Integer status;

    @Schema(description = "拒绝原因")
    private String rejectReason;

    @Schema(description = "地理位置")
    private String location;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "是否已点赞")
    private Boolean isLiked;

    @Schema(description = "是否已收藏")
    private Boolean isCollected;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    /**
     * 照片笔记图片DTO
     */
    @Data
    @Schema(description = "照片笔记图片信息")
    public static class PhotoNoteImageDTO {

        @Schema(description = "照片ID")
        private Long photoId;

        @Schema(description = "照片URL")
        private String url;

        @Schema(description = "缩略图URL")
        private String thumbnailUrl;

        @Schema(description = "照片宽度")
        private Integer width;

        @Schema(description = "照片高度")
        private Integer height;

        @Schema(description = "排序顺序")
        private Integer sortOrder;
    }

    /**
     * 照片笔记@用户DTO
     */
    @Data
    @Schema(description = "照片笔记@用户信息")
    public static class PhotoNoteMentionDTO {

        @Schema(description = "被@用户ID")
        private Long mentionedUserId;

        @Schema(description = "被@用户昵称")
        private String mentionedUserNickname;

        @Schema(description = "被@用户头像")
        private String mentionedUserAvatar;
    }

    /**
     * 点赞结果DTO
     */
    @Data
    public static class LikeResult {
        @Schema(description = "是否已点赞")
        private Boolean isLiked;

        @Schema(description = "点赞总数")
        private Integer likeCount;
    }

    /**
     * 收藏结果DTO
     */
    @Data
    public static class CollectResult {
        @Schema(description = "是否已收藏")
        private Boolean isCollected;

        @Schema(description = "收藏总数")
        private Integer collectCount;
    }

    /**
     * 统计信息DTO
     */
    @Data
    @Schema(description = "照片笔记统计信息")
    public static class Stats {
        @Schema(description = "浏览量")
        private Integer viewCount;

        @Schema(description = "点赞数")
        private Integer likeCount;

        @Schema(description = "评论数")
        private Integer commentCount;

        @Schema(description = "分享数")
        private Integer shareCount;

        @Schema(description = "收藏数")
        private Integer collectCount;
    }

    /**
     * 获取统计信息
     * @return 统计信息对象
     */
    public Stats getStats() {
        Stats stats = new Stats();
        stats.setViewCount(this.viewCount);
        stats.setLikeCount(this.likeCount);
        stats.setCommentCount(this.commentCount);
        stats.setShareCount(this.shareCount);
        // 收藏数需要从数据库查询，这里暂时设为0，后续可以优化
        stats.setCollectCount(0);
        return stats;
    }
}
