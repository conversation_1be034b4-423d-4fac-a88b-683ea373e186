<template>
  <div class="messages-container">
    <van-nav-bar
      title="消息"
      left-arrow
      @click-left="goBack"
      fixed
    />

    <div class="messages-content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div v-if="conversations.length > 0">
            <div
              v-for="conversation in conversations"
              :key="conversation.id"
              class="conversation-item"
              :class="{ 'unread': conversation.unreadCount > 0 }"
              @click="openConversation(conversation)"
            >
              <div class="avatar">
                <van-badge :content="conversation.unreadCount || ''" :max="99">
                  <van-image
                    round
                    width="50"
                    height="50"
                    :src="conversation.avatar"
                    :alt="conversation.name"
                  />
                </van-badge>
              </div>
              <div class="conversation-info">
                <div class="conversation-header">
                  <span class="name">{{ conversation.name }}</span>
                  <span class="time">{{ formatTime(conversation.lastMessageTime) }}</span>
                </div>
                <div class="last-message" :class="{ 'unread-text': conversation.unreadCount > 0 }">
                  {{ conversation.lastMessage }}
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="!loading" class="empty-state">
            <van-empty description="暂无消息" />
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <van-action-bar>
      <van-action-bar-button
        icon="chat-o"
        text="发起聊天"
        type="primary"
        @click="showNewMessagePopup = true"
      />
    </van-action-bar>

    <!-- 新建消息弹窗 -->
    <van-popup
      v-model:show="showNewMessagePopup"
      position="bottom"
      round
      closeable
      :style="{ height: '70%' }"
    >
      <div class="new-message-popup">
        <div class="popup-title">发起聊天</div>
        <van-search
          v-model="searchQuery"
          placeholder="搜索用户"
          @search="searchUsers"
        />

        <div class="user-list">
          <div
            v-for="user in searchResults"
            :key="user.id"
            class="user-item"
            @click="startConversation(user)"
          >
            <van-image
              round
              width="40"
              height="40"
              :src="user.avatar"
              :alt="user.nickname"
            />
            <div class="user-info">
              <div class="user-name">{{ user.nickname }}</div>
              <div class="user-username">@{{ user.username }}</div>
            </div>
          </div>

          <div v-if="searchQuery && searchResults.length === 0" class="empty-search">
            <van-empty description="未找到用户" />
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 检查登录状态
if (!userStore.isLoggedIn) {
  router.push('/auth/login?redirect=/messages');
}

// 会话列表
interface Conversation {
  id: number;
  userId: number;
  name: string;
  avatar: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
}

const conversations = ref<Conversation[]>([]);
const loading = ref(false);
const refreshing = ref(false);
const finished = ref(false);
const page = ref(1);
const pageSize = ref(20);

// 新建消息
const showNewMessagePopup = ref(false);
const searchQuery = ref('');
const searchResults = ref<any[]>([]);

// 返回上一页
const goBack = () => {
  router.back();
};

// 加载会话列表
const loadConversations = async () => {
  if (loading.value) return;

  loading.value = true;

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 生成模拟数据
    const mockConversations: Conversation[] = Array.from({ length: 10 }, (_, i) => {
      const id = (page.value - 1) * pageSize.value + i + 1;
      return {
        id,
        userId: id,
        name: `用户${id}`,
        avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
        lastMessage: `这是最后一条消息 ${id}`,
        lastMessageTime: new Date(Date.now() - Math.floor(Math.random() * 7) * 86400000).toISOString(),
        unreadCount: Math.random() > 0.7 ? Math.floor(Math.random() * 10) : 0
      };
    });

    // 更新会话列表
    if (page.value === 1) {
      conversations.value = mockConversations;
    } else {
      conversations.value = [...conversations.value, ...mockConversations];
    }

    // 更新分页参数
    finished.value = page.value >= 3; // 模拟只有3页数据
  } catch (error) {
    console.error('加载会话列表失败', error);
    showToast('加载失败，请稍后重试');
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  page.value = 1;
  finished.value = false;
  loadConversations();
};

// 加载更多
const onLoad = () => {
  if (!loading.value && !finished.value) {
    page.value++;
    loadConversations();
  }
};

// 打开会话
const openConversation = (conversation: Conversation) => {
  router.push(`/messages/chat/${conversation.userId}`);
};

// 搜索用户
const searchUsers = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = [];
    return;
  }

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    // 生成模拟数据
    searchResults.value = Array.from({ length: 5 }, (_, i) => ({
      id: i + 1,
      username: `user${i + 1}`,
      nickname: `${searchQuery.value}用户${i + 1}`,
      avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
    }));
  } catch (error) {
    console.error('搜索用户失败', error);
    showToast('搜索失败，请稍后重试');
  }
};

// 开始新会话
const startConversation = (user: any) => {
  showNewMessagePopup.value = false;
  router.push(`/messages/chat/${user.id}`);
};

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚';
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`;
  }

  // 小于24小时
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`;
  }

  // 今年内
  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  }

  // 更早
  return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
};

// 组件挂载时加载数据
onMounted(() => {
  loadConversations();

  // 如果有userId参数，直接打开对应的会话
  const userId = route.query.userId;
  if (userId) {
    router.push(`/messages/chat/${userId}`);
  }
});
</script>

<style lang="scss" scoped>
.messages-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px; // 导航栏高度
  padding-bottom: 50px; // 底部操作栏高度
}

.messages-content {
  height: calc(100vh - 96px); // 减去导航栏和底部操作栏的高度
  overflow-y: auto;

  @media (min-width: 768px) {
    max-width: 800px;
    margin: 0 auto;
    padding: 16px;
  }

  @media (min-width: 1200px) {
    max-width: 1000px;
  }
}

.conversation-item {
  display: flex;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;

  @media (min-width: 768px) {
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  &.unread {
    background-color: #f0f8ff;
  }

  &:active {
    background-color: #f9f9f9;
  }

  .avatar {
    margin-right: 12px;

    @media (min-width: 768px) {
      margin-right: 16px;

      .van-image {
        width: 60px !important;
        height: 60px !important;
      }
    }
  }

  .conversation-info {
    flex: 1;
    overflow: hidden;

    @media (min-width: 768px) {
      padding: 4px 0;
    }

    .conversation-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;

      .name {
        font-weight: 500;

        @media (min-width: 768px) {
          font-size: 16px;
        }
      }

      .time {
        font-size: 12px;
        color: #999;

        @media (min-width: 768px) {
          font-size: 14px;
        }
      }
    }

    .last-message {
      color: #666;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      @media (min-width: 768px) {
        font-size: 14px;
        margin-top: 4px;
      }

      &.unread-text {
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.empty-state {
  padding: 40px 0;
}

.new-message-popup {
  padding: 16px;

  @media (min-width: 768px) {
    padding: 24px;
  }

  .popup-title {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 16px;

    @media (min-width: 768px) {
      font-size: 20px;
      margin-bottom: 24px;
    }
  }

  .user-list {
    margin-top: 16px;
    max-height: calc(70vh - 120px);
    overflow-y: auto;

    @media (min-width: 768px) {
      margin-top: 24px;
    }

    .user-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      @media (min-width: 768px) {
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 8px;

        &:hover {
          background-color: #f9f9f9;
        }
      }

      &:active {
        background-color: #f9f9f9;
      }

      .user-info {
        margin-left: 12px;

        .user-name {
          font-weight: 500;

          @media (min-width: 768px) {
            font-size: 16px;
          }
        }

        .user-username {
          font-size: 12px;
          color: #999;

          @media (min-width: 768px) {
            font-size: 14px;
          }
        }
      }
    }

    .empty-search {
      padding: 40px 0;
    }
  }
}

// PC端底部操作栏样式
@media (min-width: 768px) {
  .van-action-bar {
    max-width: 800px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  @media (min-width: 1200px) {
    .van-action-bar {
      max-width: 1000px;
    }
  }
}
</style>
