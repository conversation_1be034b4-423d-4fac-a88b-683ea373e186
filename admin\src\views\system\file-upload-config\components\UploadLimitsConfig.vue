<template>
  <el-card class="form-section" header="上传限制配置">
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="最大文件大小">
          <el-input-number
            v-model="localConfig.maxFileSize"
            :min="1"
            :max="1024"
            style="width: 100%"
            @change="handleChange"
          />
          <span class="input-suffix">MB</span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="单次最大数量">
          <el-input-number
            v-model="localConfig.maxFileCount"
            :min="1"
            :max="100"
            style="width: 100%"
            @change="handleChange"
          />
          <span class="input-suffix">个</span>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="允许的文件类型">
      <div class="file-types-container">
        <el-checkbox-group v-model="localConfig.allowedFileTypes" @change="handleChange">
          <div class="file-type-group">
            <h5>图片类型</h5>
            <el-checkbox label="jpg">JPG</el-checkbox>
            <el-checkbox label="jpeg">JPEG</el-checkbox>
            <el-checkbox label="png">PNG</el-checkbox>
            <el-checkbox label="gif">GIF</el-checkbox>
            <el-checkbox label="bmp">BMP</el-checkbox>
            <el-checkbox label="webp">WebP</el-checkbox>
          </div>
          <div class="file-type-group">
            <h5>文档类型</h5>
            <el-checkbox label="pdf">PDF</el-checkbox>
            <el-checkbox label="doc">DOC</el-checkbox>
            <el-checkbox label="docx">DOCX</el-checkbox>
            <el-checkbox label="xls">XLS</el-checkbox>
            <el-checkbox label="xlsx">XLSX</el-checkbox>
            <el-checkbox label="ppt">PPT</el-checkbox>
            <el-checkbox label="pptx">PPTX</el-checkbox>
            <el-checkbox label="txt">TXT</el-checkbox>
          </div>
          <div class="file-type-group">
            <h5>视频类型</h5>
            <el-checkbox label="mp4">MP4</el-checkbox>
            <el-checkbox label="avi">AVI</el-checkbox>
            <el-checkbox label="mov">MOV</el-checkbox>
            <el-checkbox label="wmv">WMV</el-checkbox>
            <el-checkbox label="flv">FLV</el-checkbox>
            <el-checkbox label="mkv">MKV</el-checkbox>
          </div>
          <div class="file-type-group">
            <h5>音频类型</h5>
            <el-checkbox label="mp3">MP3</el-checkbox>
            <el-checkbox label="wav">WAV</el-checkbox>
            <el-checkbox label="flac">FLAC</el-checkbox>
            <el-checkbox label="aac">AAC</el-checkbox>
            <el-checkbox label="ogg">OGG</el-checkbox>
          </div>
          <div class="file-type-group">
            <h5>压缩类型</h5>
            <el-checkbox label="zip">ZIP</el-checkbox>
            <el-checkbox label="rar">RAR</el-checkbox>
            <el-checkbox label="7z">7Z</el-checkbox>
            <el-checkbox label="tar">TAR</el-checkbox>
            <el-checkbox label="gz">GZ</el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
    </el-form-item>

    <el-form-item label="禁止的文件类型">
      <el-input
        v-model="forbiddenTypesInput"
        type="textarea"
        :rows="2"
        placeholder="请输入禁止的文件类型，用逗号分隔，如：exe,bat,cmd"
        @blur="handleForbiddenTypesChange"
      />
      <div class="form-tip">
        输入禁止上传的文件扩展名，用逗号分隔。禁止类型优先级高于允许类型。
      </div>
    </el-form-item>

    <el-form-item label="图片尺寸限制">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="最大宽度" label-width="80px">
            <el-input-number
              v-model="localConfig.imageMaxDimensions.maxWidth"
              :min="1"
              :max="10000"
              placeholder="像素"
              style="width: 100%"
              @change="handleChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大高度" label-width="80px">
            <el-input-number
              v-model="localConfig.imageMaxDimensions.maxHeight"
              :min="1"
              :max="10000"
              placeholder="像素"
              style="width: 100%"
              @change="handleChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form-item>

    <el-form-item label="安全检查">
      <div class="security-options">
        <el-checkbox
          v-model="localConfig.enableFileTypeCheck"
          @change="handleChange"
        >
          启用文件类型检查
        </el-checkbox>
        <div class="option-description">
          检查文件扩展名是否在允许列表中
        </div>
      </div>
      <div class="security-options">
        <el-checkbox
          v-model="localConfig.enableContentCheck"
          @change="handleChange"
        >
          启用文件内容检查
        </el-checkbox>
        <div class="option-description">
          检查文件实际内容是否与扩展名匹配
        </div>
      </div>
      <div class="security-options">
        <el-checkbox
          v-model="localConfig.enableVirusScan"
          @change="handleChange"
        >
          启用病毒扫描
        </el-checkbox>
        <div class="option-description">
          对上传的文件进行病毒扫描（需要配置杀毒引擎）
        </div>
      </div>
    </el-form-item>

    <el-form-item label="快速配置">
      <el-button-group>
        <el-button size="small" @click="applyPreset('image')">
          仅图片
        </el-button>
        <el-button size="small" @click="applyPreset('document')">
          仅文档
        </el-button>
        <el-button size="small" @click="applyPreset('media')">
          图片+视频
        </el-button>
        <el-button size="small" @click="applyPreset('all')">
          全部类型
        </el-button>
        <el-button size="small" @click="applyPreset('strict')">
          严格模式
        </el-button>
      </el-button-group>
    </el-form-item>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import type { UploadLimitsConfig } from '@/api/fileUploadConfig'

// Props
interface Props {
  modelValue?: UploadLimitsConfig
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    maxFileSize: 50,
    maxFileCount: 10,
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    forbiddenFileTypes: [],
    imageMaxDimensions: {
      maxWidth: 4096,
      maxHeight: 4096
    },
    enableFileTypeCheck: true,
    enableContentCheck: false,
    enableVirusScan: false
  })
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: UploadLimitsConfig]
}>()

// 响应式数据
const localConfig = reactive<UploadLimitsConfig>({
  maxFileSize: 50,
  maxFileCount: 10,
  allowedFileTypes: [],
  forbiddenFileTypes: [],
  imageMaxDimensions: {
    maxWidth: 4096,
    maxHeight: 4096
  },
  enableFileTypeCheck: true,
  enableContentCheck: false,
  enableVirusScan: false
})

const forbiddenTypesInput = ref('')

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(localConfig, {
      ...newValue,
      imageMaxDimensions: {
        maxWidth: 4096,
        maxHeight: 4096,
        ...newValue.imageMaxDimensions
      }
    })
    
    // 更新禁止类型输入框
    if (newValue.forbiddenFileTypes) {
      forbiddenTypesInput.value = newValue.forbiddenFileTypes.join(', ')
    }
  }
}, { immediate: true, deep: true })

// 方法
const handleChange = () => {
  emit('update:modelValue', { ...localConfig })
}

const handleForbiddenTypesChange = () => {
  const types = forbiddenTypesInput.value
    .split(',')
    .map(type => type.trim().toLowerCase())
    .filter(type => type.length > 0)
  
  localConfig.forbiddenFileTypes = types
  handleChange()
}

const applyPreset = (preset: string) => {
  switch (preset) {
    case 'image':
      localConfig.allowedFileTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      localConfig.maxFileSize = 10
      localConfig.enableContentCheck = true
      break
    case 'document':
      localConfig.allowedFileTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
      localConfig.maxFileSize = 50
      localConfig.enableContentCheck = true
      break
    case 'media':
      localConfig.allowedFileTypes = [
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
        'mp4', 'avi', 'mov', 'wmv', 'mp3', 'wav'
      ]
      localConfig.maxFileSize = 100
      localConfig.enableContentCheck = true
      break
    case 'all':
      localConfig.allowedFileTypes = [
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt',
        'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv',
        'mp3', 'wav', 'flac', 'aac', 'ogg',
        'zip', 'rar', '7z'
      ]
      localConfig.maxFileSize = 200
      localConfig.enableContentCheck = false
      break
    case 'strict':
      localConfig.allowedFileTypes = ['jpg', 'jpeg', 'png', 'pdf']
      localConfig.maxFileSize = 5
      localConfig.maxFileCount = 3
      localConfig.enableFileTypeCheck = true
      localConfig.enableContentCheck = true
      localConfig.enableVirusScan = true
      localConfig.forbiddenFileTypes = ['exe', 'bat', 'cmd', 'scr', 'com', 'pif']
      forbiddenTypesInput.value = localConfig.forbiddenFileTypes.join(', ')
      break
  }
  
  handleChange()
}
</script>

<style scoped>
.file-types-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.file-type-group {
  margin-bottom: 16px;
}

.file-type-group:last-child {
  margin-bottom: 0;
}

.file-type-group h5 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 13px;
  font-weight: 600;
}

.file-type-group .el-checkbox {
  margin-right: 16px;
  margin-bottom: 8px;
}

.input-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.form-tip {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.security-options {
  margin-bottom: 12px;
}

.security-options:last-child {
  margin-bottom: 0;
}

.option-description {
  margin-top: 4px;
  margin-left: 24px;
  color: #909399;
  font-size: 12px;
}

.el-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
