# PhotoTagMoment 前端代码重构计划

## 当前问题

1. 文件结构不一致（有些文件在 views 根目录，有些在子目录）
2. 存在重复文件（Home.vue 和 home/index.vue）
3. 命名约定不一致
4. 混合使用 .js 和 .ts 文件
5. 组件缺乏适当的组织结构

## 重构目标

1. 创建一个更加组织化的目录结构，遵循 Vue 3 应用程序的最佳实践
2. 实现一致的命名约定
3. 提高代码的可维护性和可扩展性
4. 确保所有文件使用 TypeScript 以提供类型安全
5. 优化组件结构，提高复用性

## 目录结构重组

```
src/
├── api/                  # 按领域组织的 API 调用
├── assets/               # 静态资源（图片、字体等）
├── components/           # 共享组件
│   ├── common/           # 通用 UI 组件
│   ├── layout/           # 布局组件
│   ├── photo/            # 照片相关组件
│   ├── user/             # 用户相关组件
│   └── notification/     # 通知组件
├── composables/          # Vue 3 组合式函数
├── router/               # 路由配置
├── stores/               # Pinia 状态管理
├── utils/                # 工具函数
├── views/                # 页面组件
│   ├── home/             # 首页
│   ├── photo/            # 照片相关页面
│   ├── user/             # 用户相关页面
│   ├── search/           # 搜索页面
│   ├── upload/           # 上传页面
│   ├── notifications/    # 通知页面
│   ├── messages/         # 消息页面
│   ├── settings/         # 设置页面
│   ├── auth/             # 认证页面（登录、注册）
│   └── error/            # 错误页面
├── App.vue               # 根组件
└── main.ts               # 应用程序入口点
```

## 组件重组

1. 将所有布局相关组件移至 `components/layout/`
2. 在 `components/common/` 中创建通用 UI 组件
3. 将特定领域的组件分组到各自的文件夹中

## 视图重组

1. 每个主要功能都有自己的目录
2. 每个目录都有一个 `index.vue` 作为主入口点
3. 相关的子视图将位于同一目录中

## API 重组

1. 将所有 .js 文件转换为 .ts 以提供类型安全
2. 按领域分组 API 调用（用户、照片、通知等）
3. 确保一致的错误处理和响应格式化

## 状态管理重组

1. 为不同领域创建单独的 stores
2. 确保所有 store 状态和操作都有适当的类型定义
3. 在需要的地方实现适当的持久化

## 实施步骤

1. 创建新的目录结构
2. 重组组件和视图
3. 统一 API 调用格式
4. 重构状态管理
5. 更新路由配置
6. 测试所有功能

## 命名约定

1. 组件文件名：PascalCase（例如：PhotoCard.vue）
2. 视图文件名：index.vue 或特定功能名称（例如：Detail.vue）
3. API 和工具函数文件名：camelCase（例如：photoApi.ts, formatDate.ts）
4. 常量：UPPER_SNAKE_CASE
5. 变量和函数：camelCase
6. 类型和接口：PascalCase

## 代码风格

1. 使用 TypeScript 类型定义
2. 使用 Vue 3 组合式 API
3. 使用 ESLint 和 Prettier 保持代码风格一致
4. 添加适当的注释和文档
