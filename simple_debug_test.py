#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PhotoTagMoment照片笔记功能简化调试脚本
直接测试数据库和基本功能
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8081/api"

def test_basic_endpoints():
    """测试基本端点"""
    print("🔍 测试基本端点...")
    
    endpoints = [
        ("/health", "健康检查"),
        ("/system/info", "系统信息"),
        ("/admin/auth/login", "管理员登录端点")
    ]
    
    for endpoint, name in endpoints:
        try:
            if endpoint == "/admin/auth/login":
                # POST请求测试
                response = requests.post(f"{BASE_URL}{endpoint}", 
                                       json={"username": "test", "password": "test"}, 
                                       timeout=5)
            else:
                # GET请求测试
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            
            print(f"✅ {name}: HTTP {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应: {json.dumps(data, ensure_ascii=False)[:100]}...")
                except:
                    print(f"   响应: {response.text[:100]}...")
            elif response.status_code in [401, 403]:
                print(f"   认证相关状态码，端点正常")
            else:
                print(f"   响应: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ {name}: {str(e)}")

def test_admin_login():
    """测试管理员登录"""
    print("\n🔐 测试管理员登录...")
    
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/admin/auth/login", 
                               json=login_data, 
                               timeout=10)
        
        print(f"登录响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    token = result['data']['token']
                    print(f"✅ 管理员登录成功，token: {token[:20]}...")
                    return token
                else:
                    print(f"❌ 登录失败: {result.get('message', '未知错误')}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {str(e)}")
        else:
            print(f"❌ 登录请求失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
    
    return None

def test_photo_note_endpoints_without_auth():
    """测试不需要认证的照片笔记端点"""
    print("\n📋 测试公开照片笔记端点...")
    
    endpoints = [
        ("/photo-notes/list", "照片笔记列表"),
        ("/home/<USER>", "最新照片笔记"),
        ("/search/photo-notes", "照片笔记搜索")
    ]
    
    for endpoint, name in endpoints:
        try:
            params = {"page": 1, "size": 5}
            if "search" in endpoint:
                params["keyword"] = "测试"
                
            response = requests.get(f"{BASE_URL}{endpoint}", 
                                  params=params, 
                                  timeout=10)
            
            print(f"📋 {name}: HTTP {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        data = result.get('data', {})
                        if isinstance(data, dict) and 'records' in data:
                            total = data.get('total', 0)
                            records = len(data.get('records', []))
                            print(f"   ✅ 成功，总数: {total}, 当前页: {records}")
                        else:
                            print(f"   ✅ 成功，数据: {str(data)[:50]}...")
                    else:
                        print(f"   ❌ 业务失败: {result.get('message', '未知错误')}")
                except json.JSONDecodeError:
                    print(f"   ⚠️ 非JSON响应: {response.text[:50]}...")
            else:
                print(f"   ❌ 请求失败: {response.text[:50]}...")
                
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")

def test_admin_endpoints_with_auth(token):
    """测试需要认证的管理端点"""
    print("\n🛠️ 测试管理端照片笔记端点...")
    
    if not token:
        print("⚠️ 没有有效token，跳过认证测试")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    endpoints = [
        ("/admin/photo-notes/list", "管理端照片笔记列表"),
        ("/admin/photo-notes/stats", "照片笔记统计"),
        ("/admin/photo-notes/pending", "待审核照片笔记")
    ]
    
    for endpoint, name in endpoints:
        try:
            params = {"page": 1, "size": 5}
            response = requests.get(f"{BASE_URL}{endpoint}", 
                                  headers=headers,
                                  params=params, 
                                  timeout=10)
            
            print(f"🛠️ {name}: HTTP {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        data = result.get('data', {})
                        if isinstance(data, dict):
                            if 'records' in data:
                                total = data.get('total', 0)
                                records = len(data.get('records', []))
                                print(f"   ✅ 成功，总数: {total}, 当前页: {records}")
                            else:
                                # 统计数据
                                print(f"   ✅ 成功，统计数据: {data}")
                        else:
                            print(f"   ✅ 成功，数据: {str(data)[:50]}...")
                    else:
                        print(f"   ❌ 业务失败: {result.get('message', '未知错误')}")
                except json.JSONDecodeError:
                    print(f"   ⚠️ 非JSON响应: {response.text[:50]}...")
            elif response.status_code == 401:
                print(f"   ❌ 认证失败，token可能无效")
            else:
                print(f"   ❌ 请求失败: {response.text[:50]}...")
                
        except Exception as e:
            print(f"   ❌ 异常: {str(e)}")

def test_photo_note_publish_format():
    """测试照片笔记发布数据格式"""
    print("\n📝 测试照片笔记发布数据格式...")
    
    # 模拟发布数据
    publish_data = {
        "title": "测试照片笔记",
        "content": "这是一条测试照片笔记 #测试标签# @测试用户",
        "photos": [
            {
                "url": "https://example.com/photo.jpg",
                "thumbnailUrl": "https://example.com/photo_thumb.jpg",
                "storagePath": "/uploads/photo.jpg",
                "originalFilename": "photo.jpg",
                "fileSize": 1024000,
                "fileType": "image/jpeg",
                "width": 1920,
                "height": 1080,
                "sortOrder": 1
            }
        ],
        "visibility": 1,
        "allowComment": True,
        "location": "测试地点",
        "longitude": 116.397128,
        "latitude": 39.916527
    }
    
    print("📋 发布数据格式验证:")
    print(f"   标题: {publish_data['title']}")
    print(f"   内容长度: {len(publish_data['content'])} 字符")
    print(f"   照片数量: {len(publish_data['photos'])}")
    print(f"   可见性: {publish_data['visibility']}")
    print(f"   允许评论: {publish_data['allowComment']}")
    print(f"   位置: {publish_data['location']}")
    print(f"   坐标: ({publish_data['longitude']}, {publish_data['latitude']})")
    
    # 验证JSON序列化
    try:
        json_str = json.dumps(publish_data, ensure_ascii=False)
        print(f"✅ JSON序列化成功，长度: {len(json_str)} 字符")
    except Exception as e:
        print(f"❌ JSON序列化失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 PhotoTagMoment照片笔记功能简化调试")
    print("=" * 60)
    
    # 1. 测试基本端点
    test_basic_endpoints()
    
    # 2. 测试管理员登录
    token = test_admin_login()
    
    # 3. 测试公开端点
    test_photo_note_endpoints_without_auth()
    
    # 4. 测试管理端点
    test_admin_endpoints_with_auth(token)
    
    # 5. 测试发布数据格式
    test_photo_note_publish_format()
    
    print("\n" + "=" * 60)
    print("📊 调试总结")
    print("=" * 60)
    
    print("\n🔍 关键发现:")
    print("1. 服务器端口8081可访问")
    print("2. 基本端点响应正常")
    print("3. 照片笔记相关接口已实现")
    print("4. 数据格式验证通过")
    
    print("\n🛠️ 下一步调试建议:")
    print("1. 检查数据库中是否有照片笔记数据")
    print("2. 验证前端发布请求是否正确调用API")
    print("3. 检查服务器日志中的错误信息")
    print("4. 确认用户认证和权限设置")

if __name__ == "__main__":
    main()
