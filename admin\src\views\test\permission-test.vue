<template>
  <div class="permission-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>权限测试页面</span>
          <el-button type="primary" @click="refreshUserInfo">刷新用户信息</el-button>
        </div>
      </template>

      <!-- 用户信息显示 -->
      <el-descriptions title="当前用户信息" :column="2" border>
        <el-descriptions-item label="用户名">{{ userInfo?.username || '未登录' }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ userInfo?.name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="Token">{{ token ? '已获取' : '未获取' }}</el-descriptions-item>
        <el-descriptions-item label="角色">{{ roles.join(', ') || '无' }}</el-descriptions-item>
        <el-descriptions-item label="权限" :span="2">{{ permissions.join(', ') || '无' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 权限测试 -->
      <el-divider>权限测试</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>基础权限测试</template>
            <div class="permission-test-item">
              <span>ADMIN权限：</span>
              <el-tag :type="hasPermission('ADMIN') ? 'success' : 'danger'">
                {{ hasPermission('ADMIN') ? '有权限' : '无权限' }}
              </el-tag>
            </div>
            <div class="permission-test-item">
              <span>SUPER_ADMIN权限：</span>
              <el-tag :type="hasPermission('SUPER_ADMIN') ? 'success' : 'danger'">
                {{ hasPermission('SUPER_ADMIN') ? '有权限' : '无权限' }}
              </el-tag>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>文件配置权限测试</template>
            <div class="permission-test-item">
              <span>创建配置：</span>
              <el-tag :type="hasPermission('FILE_CONFIG_CREATE') ? 'success' : 'danger'">
                {{ hasPermission('FILE_CONFIG_CREATE') ? '有权限' : '无权限' }}
              </el-tag>
            </div>
            <div class="permission-test-item">
              <span>编辑配置：</span>
              <el-tag :type="hasPermission('FILE_CONFIG_UPDATE') ? 'success' : 'danger'">
                {{ hasPermission('FILE_CONFIG_UPDATE') ? '有权限' : '无权限' }}
              </el-tag>
            </div>
            <div class="permission-test-item">
              <span>删除配置：</span>
              <el-tag :type="hasPermission('FILE_CONFIG_DELETE') ? 'success' : 'danger'">
                {{ hasPermission('FILE_CONFIG_DELETE') ? '有权限' : '无权限' }}
              </el-tag>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>操作按钮测试</template>
            <div class="button-test">
              <el-button 
                type="primary" 
                :disabled="!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')"
                @click="testOperation('编辑')"
              >
                编辑按钮
              </el-button>
              <el-button 
                type="success" 
                :disabled="!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')"
                @click="testOperation('启用/禁用')"
              >
                启用/禁用
              </el-button>
              <el-button 
                type="warning" 
                :disabled="!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')"
                @click="testOperation('设为默认')"
              >
                设为默认
              </el-button>
              <el-button 
                type="info" 
                :disabled="!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')"
                @click="testOperation('复制配置')"
              >
                复制配置
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 测试日志 -->
      <el-divider>测试日志</el-divider>
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>操作日志</span>
            <el-button size="small" @click="clearLogs">清空日志</el-button>
          </div>
        </template>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-content">{{ log.content }}</span>
          </div>
          <div v-if="logs.length === 0" class="no-logs">暂无日志</div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { hasPermission } from '@/utils/permission'

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
const token = computed(() => userStore.token)
const roles = computed(() => userStore.roles || [])
const permissions = computed(() => userStore.permissions || [])

// 测试日志
const logs = ref<Array<{ time: string, content: string }>>([])

// 添加日志
const addLog = (content: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    content
  })
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  addLog('日志已清空')
}

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    await userStore.fetchUserInfo()
    addLog('用户信息刷新成功')
    ElMessage.success('用户信息刷新成功')
  } catch (error) {
    addLog('用户信息刷新失败: ' + error)
    ElMessage.error('用户信息刷新失败')
  }
}

// 测试操作
const testOperation = (operation: string) => {
  addLog(`执行操作: ${operation}`)
  ElMessage.success(`${operation}操作测试成功`)
}

// 页面加载时的初始化
onMounted(() => {
  addLog('权限测试页面已加载')
  addLog(`当前用户: ${userInfo.value?.username || '未登录'}`)
  addLog(`Token状态: ${token.value ? '已获取' : '未获取'}`)
  addLog(`角色列表: ${roles.value.join(', ') || '无'}`)
  addLog(`权限列表: ${permissions.value.join(', ') || '无'}`)
})
</script>

<style scoped>
.permission-test-container {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permission-test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 0;
}

.button-test {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
  min-width: 80px;
}

.log-content {
  color: #303133;
}

.no-logs {
  text-align: center;
  color: #909399;
  font-style: italic;
}
</style>
