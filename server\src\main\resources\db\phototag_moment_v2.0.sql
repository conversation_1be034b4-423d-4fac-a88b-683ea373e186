/*
 Navicat Premium Data Transfer

 Source Server         : local8.0
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : localhost:31160
 Source Schema         : phototag_moment

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 27/05/2025 13:33:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for flyway_schema_history
-- ----------------------------
DROP TABLE IF EXISTS `flyway_schema_history`;
CREATE TABLE `flyway_schema_history`  (
  `installed_rank` int NOT NULL,
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `script` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `checksum` int NULL DEFAULT NULL,
  `installed_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`) USING BTREE,
  INDEX `flyway_schema_history_s_idx`(`success`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of flyway_schema_history
-- ----------------------------
INSERT INTO `flyway_schema_history` VALUES (1, '1', '<< Flyway Baseline >>', 'BASELINE', '<< Flyway Baseline >>', NULL, 'root', '2025-05-11 19:54:23', 0, 1);
INSERT INTO `flyway_schema_history` VALUES (2, '1.1.1', 'add content moderation fixed', 'SQL', 'V1.1.1__add_content_moderation_fixed.sql', -1964168816, 'root', '2025-05-11 19:54:24', 156, 1);
INSERT INTO `flyway_schema_history` VALUES (3, '1.4', 'Create Admin Tables', 'SQL', 'V1.4__Create_Admin_Tables.sql', -1209958282, 'root', '2025-05-11 19:54:24', 205, 1);
INSERT INTO `flyway_schema_history` VALUES (4, '1.5', 'Add Test Admin', 'SQL', 'V1.5__Add_Test_Admin.sql', 1397466603, 'root', '2025-05-11 20:20:07', 8, 1);
INSERT INTO `flyway_schema_history` VALUES (5, '1.6', 'Fix Admin Password', 'SQL', 'V1.6__Fix_Admin_Password.sql', -1286321084, 'root', '2025-05-11 20:20:07', 3, 1);
INSERT INTO `flyway_schema_history` VALUES (6, '1.0.5', 'add wechat and realname fields', 'SQL', 'V1.0.5__add_wechat_and_realname_fields.sql', 1324673091, 'root', '2025-05-11 20:53:21', 156, 1);
INSERT INTO `flyway_schema_history` VALUES (7, '1.1', 'add content moderation', 'SQL', 'V1.1__add_content_moderation.sql', -1147033556, 'root', '2025-05-12 13:29:20', 56, 1);
INSERT INTO `flyway_schema_history` VALUES (8, '1.2', 'add identity verification', 'SQL', 'V1.2__add_identity_verification.sql', -1188731706, 'root', '2025-05-13 20:14:33', 63, 1);
INSERT INTO `flyway_schema_history` VALUES (9, '1.3', 'add admin system', 'SQL', 'V1.3__add_admin_system.sql', -1459594417, 'root', '2025-05-13 20:28:23', 125, 1);
INSERT INTO `flyway_schema_history` VALUES (10, '1.3.1', 'add photo draft', 'SQL', 'V1.3.1__add_photo_draft.sql', 1942222111, 'root', '2025-05-13 20:28:23', 52, 1);
INSERT INTO `flyway_schema_history` VALUES (11, '1.7', 'reset admin password', 'SQL', 'V1.7__reset_admin_password.sql', -123404800, 'root', '2025-05-13 20:28:23', 2, 1);
INSERT INTO `flyway_schema_history` VALUES (12, '1.8', 'Create System Settings Tables', 'SQL', 'V1.8__Create_System_Settings_Tables.sql', 587041745, 'root', '2025-05-13 20:33:40', 244, 1);
INSERT INTO `flyway_schema_history` VALUES (13, '1.9', 'Create Recommendation Tables', 'SQL', 'V1.9__Create_Recommendation_Tables.sql', 504066099, 'root', '2025-05-13 20:37:21', 85, 1);
INSERT INTO `flyway_schema_history` VALUES (14, '1.10', 'Create Photo Interaction Tables', 'SQL', 'V1.10__Create_Photo_Interaction_Tables.sql', 1278293747, 'root', '2025-05-14 17:04:52', 199, 1);
INSERT INTO `flyway_schema_history` VALUES (15, '1.11', 'Add Original Filename To Photo', 'SQL', 'V1.11__Add_Original_Filename_To_Photo.sql', 128164714, 'root', '2025-05-14 23:54:51', 75, 1);
INSERT INTO `flyway_schema_history` VALUES (16, '1.12', 'Add File Size And Type To Photo', 'SQL', 'V1.12__Add_File_Size_And_Type_To_Photo.sql', -1575895979, 'root', '2025-05-15 00:12:28', 136, 1);
INSERT INTO `flyway_schema_history` VALUES (17, '1.11', 'Add Original Filename To Photo', 'DELETE', 'V1.11__Add_Original_Filename_To_Photo.sql', 128164714, 'root', '2025-05-15 00:36:41', 0, 1);
INSERT INTO `flyway_schema_history` VALUES (18, '1.12', 'Add File Size And Type To Photo', 'DELETE', 'V1.12__Add_File_Size_And_Type_To_Photo.sql', -1575895979, 'root', '2025-05-15 00:36:41', 0, 1);
INSERT INTO `flyway_schema_history` VALUES (19, '1.14', 'Add Missing Fields To Photo Safe', 'SQL', 'V1.14__Add_Missing_Fields_To_Photo_Safe.sql', -136919407, 'root', '2025-05-15 00:36:41', 15, 1);
INSERT INTO `flyway_schema_history` VALUES (20, '1.16', 'Add Missing Fields To Photo Complete', 'SQL', 'V1.16__Add_Missing_Fields_To_Photo_Complete.sql', -1352386510, 'root', '2025-05-15 00:53:59', 95, 1);
INSERT INTO `flyway_schema_history` VALUES (21, '1.17', 'Create Photo Audit Table', 'SQL', 'V1.17__Create_Photo_Audit_Table.sql', 687707093, 'root', '2025-05-15 11:46:57', 50, 1);
INSERT INTO `flyway_schema_history` VALUES (22, '1.18', 'Add Missing Count Fields To Photo', 'SQL', 'V1.18__Add_Missing_Count_Fields_To_Photo.sql', -1366362278, 'root', '2025-05-15 12:11:46', 12, 1);
INSERT INTO `flyway_schema_history` VALUES (23, '1.19', 'Add Taken Time To Photo', 'SQL', 'V1.19__Add_Taken_Time_To_Photo.sql', -1473037449, 'root', '2025-05-15 12:25:27', 31, 1);
INSERT INTO `flyway_schema_history` VALUES (24, '1.20', 'Add File Size To Photo', 'SQL', 'V1.20__Add_File_Size_To_Photo.sql', -133726240, 'root', '2025-05-15 12:36:17', 9, 1);
INSERT INTO `flyway_schema_history` VALUES (25, '1.22', 'Add CollectCount To Photo', 'SQL', 'V1.22__Add_CollectCount_To_Photo.sql', -1716821753, 'root', '2025-05-15 16:44:17', 9, 1);
INSERT INTO `flyway_schema_history` VALUES (26, '1.23', 'Add Photo Fields And Group', 'SQL', 'V1.23__Add_Photo_Fields_And_Group.sql', -534563706, 'root', '2025-05-15 17:38:07', 3, 1);
INSERT INTO `flyway_schema_history` VALUES (27, '1.24', 'Add Photo Group ID', 'SQL', 'V1.24__Add_Photo_Group_ID.sql', 2146912811, 'root', '2025-05-15 17:38:07', 114, 1);

-- ----------------------------
-- Table structure for ptm_admin
-- ----------------------------
DROP TABLE IF EXISTS `ptm_admin`;
CREATE TABLE `ptm_admin`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `role_id` bigint NULL DEFAULT NULL COMMENT '角色ID',
  `creator_id` bigint NULL DEFAULT NULL COMMENT '创建者ID',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '管理员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_admin
-- ----------------------------
INSERT INTO `ptm_admin` VALUES (1, 'admin', '$2a$10$By5IixVSn7liNU3aOoJaZ.o9kn8pjqCcGPGNUVMiQBfHOgkVSVouu', '系统管理员', NULL, '<EMAIL>', '13800138000', 1, 1, NULL, '127.0.0.1', '2025-05-27 13:23:32', '2025-05-11 19:54:24', '2025-05-27 13:23:32', 0);
INSERT INTO `ptm_admin` VALUES (2, 'testadmin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iAt2s7JS', '测试管理员', NULL, '<EMAIL>', '13800138001', 1, 1, NULL, NULL, NULL, '2025-05-11 20:20:07', '2025-05-11 20:20:07', 0);
INSERT INTO `ptm_admin` VALUES (4, 'admin_test', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试管理员', NULL, '<EMAIL>', NULL, 1, NULL, NULL, '127.0.0.1', '2025-05-24 16:40:21', '2025-05-24 13:49:54', '2025-05-24 16:40:21', 0);
INSERT INTO `ptm_admin` VALUES (5, 'superadmin', '\\.zmdr9k7uOCQb376NoUnuTJ8iKyF.bCw6JvYLIqrOimNjCR6RDeO', '超级管理员', NULL, '<EMAIL>', NULL, 1, NULL, NULL, NULL, NULL, '2025-05-24 13:52:27', '2025-05-24 13:52:27', 0);

-- ----------------------------
-- Table structure for ptm_admin_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `ptm_admin_operation_log`;
CREATE TABLE `ptm_admin_operation_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` bigint NOT NULL COMMENT '管理员ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块',
  `operation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内容',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_admin_id`(`admin_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '管理员操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_admin_operation_log
-- ----------------------------
INSERT INTO `ptm_admin_operation_log` VALUES (1, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 00:38:50');
INSERT INTO `ptm_admin_operation_log` VALUES (2, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 00:41:05');
INSERT INTO `ptm_admin_operation_log` VALUES (3, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 00:42:51');
INSERT INTO `ptm_admin_operation_log` VALUES (4, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 00:43:36');
INSERT INTO `ptm_admin_operation_log` VALUES (5, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 00:44:44');
INSERT INTO `ptm_admin_operation_log` VALUES (6, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 00:45:19');
INSERT INTO `ptm_admin_operation_log` VALUES (7, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 01:02:40');
INSERT INTO `ptm_admin_operation_log` VALUES (8, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 01:02:47');
INSERT INTO `ptm_admin_operation_log` VALUES (9, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 01:05:15');
INSERT INTO `ptm_admin_operation_log` VALUES (10, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 01:21:33');
INSERT INTO `ptm_admin_operation_log` VALUES (11, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 01:22:06');
INSERT INTO `ptm_admin_operation_log` VALUES (12, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 01:37:07');
INSERT INTO `ptm_admin_operation_log` VALUES (13, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-05-12 14:08:32');
INSERT INTO `ptm_admin_operation_log` VALUES (14, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:08:53');
INSERT INTO `ptm_admin_operation_log` VALUES (15, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:09:46');
INSERT INTO `ptm_admin_operation_log` VALUES (16, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:10:47');
INSERT INTO `ptm_admin_operation_log` VALUES (17, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:11:19');
INSERT INTO `ptm_admin_operation_log` VALUES (18, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:15:29');
INSERT INTO `ptm_admin_operation_log` VALUES (19, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:39:30');
INSERT INTO `ptm_admin_operation_log` VALUES (20, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:40:31');
INSERT INTO `ptm_admin_operation_log` VALUES (21, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:43:33');
INSERT INTO `ptm_admin_operation_log` VALUES (22, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:43:43');
INSERT INTO `ptm_admin_operation_log` VALUES (23, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:46:22');
INSERT INTO `ptm_admin_operation_log` VALUES (24, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 19:46:52');
INSERT INTO `ptm_admin_operation_log` VALUES (25, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 20:10:27');
INSERT INTO `ptm_admin_operation_log` VALUES (26, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 20:14:00');
INSERT INTO `ptm_admin_operation_log` VALUES (27, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 20:39:54');
INSERT INTO `ptm_admin_operation_log` VALUES (28, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 21:10:52');
INSERT INTO `ptm_admin_operation_log` VALUES (29, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 21:10:59');
INSERT INTO `ptm_admin_operation_log` VALUES (30, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 21:24:15');
INSERT INTO `ptm_admin_operation_log` VALUES (31, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 21:25:25');
INSERT INTO `ptm_admin_operation_log` VALUES (32, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-19 21:25:50');
INSERT INTO `ptm_admin_operation_log` VALUES (33, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 17:54:18');
INSERT INTO `ptm_admin_operation_log` VALUES (34, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 17:55:21');
INSERT INTO `ptm_admin_operation_log` VALUES (35, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 18:22:44');
INSERT INTO `ptm_admin_operation_log` VALUES (36, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 18:23:23');
INSERT INTO `ptm_admin_operation_log` VALUES (37, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 18:24:24');
INSERT INTO `ptm_admin_operation_log` VALUES (38, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 18:30:41');
INSERT INTO `ptm_admin_operation_log` VALUES (39, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 18:57:41');
INSERT INTO `ptm_admin_operation_log` VALUES (40, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-20 21:51:30');
INSERT INTO `ptm_admin_operation_log` VALUES (41, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-21 16:32:30');
INSERT INTO `ptm_admin_operation_log` VALUES (42, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-23 18:17:59');
INSERT INTO `ptm_admin_operation_log` VALUES (43, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-23 19:17:06');
INSERT INTO `ptm_admin_operation_log` VALUES (44, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-24 11:50:28');
INSERT INTO `ptm_admin_operation_log` VALUES (45, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 14:11:52');
INSERT INTO `ptm_admin_operation_log` VALUES (46, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 14:20:06');
INSERT INTO `ptm_admin_operation_log` VALUES (47, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 14:20:22');
INSERT INTO `ptm_admin_operation_log` VALUES (48, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 14:22:56');
INSERT INTO `ptm_admin_operation_log` VALUES (49, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 14:23:49');
INSERT INTO `ptm_admin_operation_log` VALUES (50, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-24 14:44:21');
INSERT INTO `ptm_admin_operation_log` VALUES (51, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 15:00:14');
INSERT INTO `ptm_admin_operation_log` VALUES (52, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 15:00:24');
INSERT INTO `ptm_admin_operation_log` VALUES (53, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 15:13:57');
INSERT INTO `ptm_admin_operation_log` VALUES (54, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 15:32:27');
INSERT INTO `ptm_admin_operation_log` VALUES (55, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 15:33:17');
INSERT INTO `ptm_admin_operation_log` VALUES (56, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 16:32:45');
INSERT INTO `ptm_admin_operation_log` VALUES (57, 4, 'admin_test', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4061', '2025-05-24 16:40:21');
INSERT INTO `ptm_admin_operation_log` VALUES (58, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-27 11:26:26');
INSERT INTO `ptm_admin_operation_log` VALUES (59, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-27 11:26:30');
INSERT INTO `ptm_admin_operation_log` VALUES (60, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-27 11:26:53');
INSERT INTO `ptm_admin_operation_log` VALUES (61, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-27 11:27:02');
INSERT INTO `ptm_admin_operation_log` VALUES (62, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'python-requests/2.31.0', '2025-05-27 12:38:42');
INSERT INTO `ptm_admin_operation_log` VALUES (63, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'python-requests/2.31.0', '2025-05-27 12:40:58');
INSERT INTO `ptm_admin_operation_log` VALUES (64, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'python-requests/2.31.0', '2025-05-27 12:42:23');
INSERT INTO `ptm_admin_operation_log` VALUES (65, 1, 'admin', '系统', '登录', '管理员登录系统', '127.0.0.1', 'python-requests/2.31.0', '2025-05-27 13:23:33');

-- ----------------------------
-- Table structure for ptm_admin_permission
-- ----------------------------
DROP TABLE IF EXISTS `ptm_admin_permission`;
CREATE TABLE `ptm_admin_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限编码',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限描述',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '权限类型：1-菜单，2-按钮，3-接口',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父权限ID',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路径',
  `component` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统内置：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '管理员权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_admin_permission
-- ----------------------------
INSERT INTO `ptm_admin_permission` VALUES (1, '系统管理', 'system', '系统管理', 1, NULL, '/system', NULL, 'setting', 1, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (2, '管理员管理', 'system:admin', '管理员管理', 1, 1, '/system/admin', NULL, 'user', 1, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (3, '管理员列表', 'system:admin:list', '管理员列表', 2, 2, NULL, NULL, NULL, 1, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (4, '管理员创建', 'system:admin:create', '管理员创建', 2, 2, NULL, NULL, NULL, 2, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (5, '管理员编辑', 'system:admin:edit', '管理员编辑', 2, 2, NULL, NULL, NULL, 3, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (6, '管理员删除', 'system:admin:delete', '管理员删除', 2, 2, NULL, NULL, NULL, 4, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (7, '管理员状态修改', 'system:admin:status', '管理员状态修改', 2, 2, NULL, NULL, NULL, 5, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (8, '管理员密码重置', 'system:admin:reset-password', '管理员密码重置', 2, 2, NULL, NULL, NULL, 6, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (9, '角色管理', 'system:role', '角色管理', 1, 1, '/system/role', NULL, 'team', 2, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (10, '角色列表', 'system:role:list', '角色列表', 2, 10, NULL, NULL, NULL, 1, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (11, '角色创建', 'system:role:create', '角色创建', 2, 10, NULL, NULL, NULL, 2, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (12, '角色编辑', 'system:role:edit', '角色编辑', 2, 10, NULL, NULL, NULL, 3, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (13, '角色删除', 'system:role:delete', '角色删除', 2, 10, NULL, NULL, NULL, 4, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (14, '角色权限设置', 'system:role:permission', '角色权限设置', 2, 10, NULL, NULL, NULL, 5, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (15, '日志管理', 'system:log', '日志管理', 1, 1, '/system/log', NULL, 'file-text', 3, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (16, '操作日志', 'system:log:operation', '操作日志', 2, 16, NULL, NULL, NULL, 1, 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_permission` VALUES (17, '权限管理', 'system:permission', '权限管理', 1, 1, 'permission', 'system/permission/index', 'tree-table', 3, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_permission` VALUES (18, '用户管理', 'user', '用户管理', 1, NULL, '/user', 'Layout', 'peoples', 2, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_permission` VALUES (19, '用户列表', 'user:list', '用户列表', 1, 6, 'list', 'user/list/index', 'user', 1, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_permission` VALUES (20, '实名认证', 'user:verification', '实名认证', 1, 6, 'verification', 'user/verification/index', 'id-card', 2, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_permission` VALUES (21, '内容管理', 'content', '内容管理', 1, NULL, '/content', 'Layout', 'documentation', 3, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_permission` VALUES (22, '照片管理', 'content:photo', '照片管理', 1, 9, 'photo', 'content/photo/index', 'image', 1, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_permission` VALUES (23, '评论管理', 'content:comment', '评论管理', 1, 9, 'comment', 'content/comment/index', 'message', 2, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_permission` VALUES (24, '内容审核', 'content:moderation', '内容审核', 1, 9, 'moderation', 'content/moderation/index', 'check', 3, 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);

-- ----------------------------
-- Table structure for ptm_admin_role
-- ----------------------------
DROP TABLE IF EXISTS `ptm_admin_role`;
CREATE TABLE `ptm_admin_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色编码',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统内置：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '管理员角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_admin_role
-- ----------------------------
INSERT INTO `ptm_admin_role` VALUES (1, '超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1, 1, '2025-05-11 19:54:24', '2025-05-11 19:54:24', 0);
INSERT INTO `ptm_admin_role` VALUES (8, '内容管理员', 'content_admin', '负责内容管理', 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);
INSERT INTO `ptm_admin_role` VALUES (9, '用户管理员', 'user_admin', '负责用户管理', 1, 1, '2025-05-13 20:28:23', '2025-05-13 20:28:23', 0);

-- ----------------------------
-- Table structure for ptm_admin_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `ptm_admin_role_permission`;
CREATE TABLE `ptm_admin_role_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_role_permission`(`role_id`, `permission_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '管理员角色权限关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_admin_role_permission
-- ----------------------------
INSERT INTO `ptm_admin_role_permission` VALUES (1, 1, 1, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (2, 1, 2, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (3, 1, 4, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (4, 1, 6, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (5, 1, 5, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (6, 1, 3, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (7, 1, 8, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (8, 1, 7, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (9, 1, 15, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (10, 1, 16, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (11, 1, 9, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (12, 1, 11, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (13, 1, 13, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (14, 1, 12, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (15, 1, 10, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (16, 1, 14, '2025-05-11 19:54:24');
INSERT INTO `ptm_admin_role_permission` VALUES (18, 8, 9, '2025-05-13 20:28:23');
INSERT INTO `ptm_admin_role_permission` VALUES (19, 8, 10, '2025-05-13 20:28:23');
INSERT INTO `ptm_admin_role_permission` VALUES (20, 8, 11, '2025-05-13 20:28:23');
INSERT INTO `ptm_admin_role_permission` VALUES (21, 8, 12, '2025-05-13 20:28:23');
INSERT INTO `ptm_admin_role_permission` VALUES (25, 9, 6, '2025-05-13 20:28:23');
INSERT INTO `ptm_admin_role_permission` VALUES (26, 9, 7, '2025-05-13 20:28:23');
INSERT INTO `ptm_admin_role_permission` VALUES (27, 9, 8, '2025-05-13 20:28:23');

-- ----------------------------
-- Table structure for ptm_album
-- ----------------------------
DROP TABLE IF EXISTS `ptm_album`;
CREATE TABLE `ptm_album`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '相册ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '相册名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相册描述',
  `cover_photo_id` bigint NULL DEFAULT NULL COMMENT '封面照片ID',
  `photo_count` int NULL DEFAULT 0 COMMENT '照片数量',
  `visibility` tinyint(1) NULL DEFAULT 1 COMMENT '可见性：0私密，1公开，2好友可见',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '相册表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_album
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_album_photo
-- ----------------------------
DROP TABLE IF EXISTS `ptm_album_photo`;
CREATE TABLE `ptm_album_photo`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `album_id` bigint NOT NULL COMMENT '相册ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_album_photo`(`album_id`, `photo_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '相册照片关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_album_photo
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_collection
-- ----------------------------
DROP TABLE IF EXISTS `ptm_collection`;
CREATE TABLE `ptm_collection`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_photo`(`user_id`, `photo_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_collection
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_comment
-- ----------------------------
DROP TABLE IF EXISTS `ptm_comment`;
CREATE TABLE `ptm_comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父评论ID',
  `reply_to_user_id` bigint NULL DEFAULT NULL COMMENT '回复用户ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `reply_count` int NULL DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0待审核，1正常，2已拒绝',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_comment
-- ----------------------------
INSERT INTO `ptm_comment` VALUES (1, 1, 31, NULL, NULL, '看看', 0, 0, 1, '2025-05-15 19:14:33', '2025-05-15 19:14:33', 0);
INSERT INTO `ptm_comment` VALUES (2, 1, 31, NULL, NULL, '@undefined ', 0, 0, 1, '2025-05-15 19:34:15', '2025-05-15 19:34:15', 0);

-- ----------------------------
-- Table structure for ptm_comment_like
-- ----------------------------
DROP TABLE IF EXISTS `ptm_comment_like`;
CREATE TABLE `ptm_comment_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_comment_user`(`comment_id`, `user_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_comment_id`(`comment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_comment_like
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_content_moderation_record
-- ----------------------------
DROP TABLE IF EXISTS `ptm_content_moderation_record`;
CREATE TABLE `ptm_content_moderation_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `content_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容类型: photo, text, comment',
  `content_id` bigint NULL DEFAULT NULL COMMENT '内容ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核内容',
  `result` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核结果: 0-待审核, 1-通过, 2-拒绝',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `provider` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核提供商: local, aliyun, tencent, baidu',
  `admin_id` bigint NULL DEFAULT NULL COMMENT '管理员ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_content_type_id`(`content_type`, `content_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '内容审核记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_content_moderation_record
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `ptm_dict_data`;
CREATE TABLE `ptm_dict_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典数据ID',
  `dict_type_id` bigint NOT NULL COMMENT '字典类型ID',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典键值',
  `dict_sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dict_type_id`(`dict_type_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_dict_data
-- ----------------------------
INSERT INTO `ptm_dict_data` VALUES (1, 1, '正常', '1', 1, 1, '正常状态', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (2, 1, '禁用', '0', 2, 1, '禁用状态', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (3, 2, '男', 'male', 1, 1, '男性', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (4, 2, '女', 'female', 2, 1, '女性', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (5, 2, '未知', 'unknown', 3, 1, '未知性别', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (6, 3, '待审核', '0', 1, 1, '待审核状态', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (7, 3, '通过', '1', 2, 1, '审核通过', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (8, 3, '拒绝', '2', 3, 1, '审核拒绝', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (9, 4, '未认证', '0', 1, 1, '未进行实名认证', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (10, 4, '已认证', '1', 2, 1, '已通过实名认证', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (11, 4, '认证失败', '2', 3, 1, '实名认证失败', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (12, 5, '政治', 'political', 1, 1, '政治类敏感词', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (13, 5, '色情', 'porn', 2, 1, '色情类敏感词', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (14, 5, '暴力', 'violence', 3, 1, '暴力类敏感词', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (15, 5, '广告', 'ad', 4, 1, '广告类敏感词', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (16, 5, '其他', 'other', 5, 1, '其他类敏感词', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (17, 6, '一般', '1', 1, 1, '一般级别', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (18, 6, '中等', '2', 2, 1, '中等级别', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (19, 6, '严重', '3', 3, 1, '严重级别', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (20, 7, '系统通知', 'system', 1, 1, '系统通知', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (21, 7, '关注通知', 'follow', 2, 1, '关注通知', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (22, 7, '点赞通知', 'like', 3, 1, '点赞通知', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (23, 7, '评论通知', 'comment', 4, 1, '评论通知', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (24, 7, '私信通知', 'message', 5, 1, '私信通知', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_data` VALUES (25, 1, '正常', '1', 1, 1, '正常状态', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (26, 1, '禁用', '0', 2, 1, '禁用状态', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (27, 2, '男', 'male', 1, 1, '男性', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (28, 2, '女', 'female', 2, 1, '女性', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (29, 2, '未知', 'unknown', 3, 1, '未知性别', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (30, 3, '待审核', '0', 1, 1, '待审核状态', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (31, 3, '通过', '1', 2, 1, '审核通过', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (32, 3, '拒绝', '2', 3, 1, '审核拒绝', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (33, 4, '未认证', '0', 1, 1, '未进行实名认证', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (34, 4, '已认证', '1', 2, 1, '已通过实名认证', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (35, 4, '认证失败', '2', 3, 1, '实名认证失败', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (36, 5, '政治', 'political', 1, 1, '政治类敏感词', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (37, 5, '色情', 'porn', 2, 1, '色情类敏感词', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (38, 5, '暴力', 'violence', 3, 1, '暴力类敏感词', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (39, 5, '广告', 'ad', 4, 1, '广告类敏感词', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (40, 5, '其他', 'other', 5, 1, '其他类敏感词', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (41, 6, '一般', '1', 1, 1, '一般级别', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (42, 6, '中等', '2', 2, 1, '中等级别', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (43, 6, '严重', '3', 3, 1, '严重级别', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (44, 7, '系统通知', 'system', 1, 1, '系统通知', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (45, 7, '关注通知', 'follow', 2, 1, '关注通知', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (46, 7, '点赞通知', 'like', 3, 1, '点赞通知', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (47, 7, '评论通知', 'comment', 4, 1, '评论通知', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);
INSERT INTO `ptm_dict_data` VALUES (48, 7, '私信通知', 'message', 5, 1, '私信通知', '2025-05-19 18:31:06', '2025-05-19 18:31:06', 0);

-- ----------------------------
-- Table structure for ptm_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `ptm_dict_type`;
CREATE TABLE `ptm_dict_type`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典类型ID',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典类型',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '数据字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_dict_type
-- ----------------------------
INSERT INTO `ptm_dict_type` VALUES (1, '用户状态', 'user_status', 1, '用户状态', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_type` VALUES (2, '性别类型', 'gender_type', 1, '性别类型', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_type` VALUES (3, '内容审核状态', 'content_audit_status', 1, '内容审核状态', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_type` VALUES (4, '实名认证状态', 'identity_verify_status', 1, '实名认证状态', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_type` VALUES (5, '敏感词类型', 'sensitive_word_type', 1, '敏感词类型', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_type` VALUES (6, '敏感词级别', 'sensitive_word_level', 1, '敏感词级别', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);
INSERT INTO `ptm_dict_type` VALUES (7, '通知类型', 'notification_type', 1, '通知类型', '2025-05-13 20:33:40', '2025-05-13 20:33:40', 0);

-- ----------------------------
-- Table structure for ptm_file_record
-- ----------------------------
DROP TABLE IF EXISTS `ptm_file_record`;
CREATE TABLE `ptm_file_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件记录ID',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件URL',
  `file_size` bigint NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'MIME类型',
  `extension` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件扩展名',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件分类',
  `uploader_id` bigint NOT NULL COMMENT '上传者ID',
  `uploader_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'USER' COMMENT '上传者类型',
  `storage_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'LOCAL' COMMENT '存储类型',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '文件状态：0-正常，1-回收站，2-已删除',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件标签',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件描述',
  `access_count` int NOT NULL DEFAULT 0 COMMENT '访问次数',
  `last_access_time` datetime NULL DEFAULT NULL COMMENT '最后访问时间',
  `md5_hash` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件MD5值',
  `sha1_hash` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件SHA1值',
  `is_temp` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为临时文件',
  `temp_expire_time` datetime NULL DEFAULT NULL COMMENT '临时文件过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否逻辑删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_file_path`(`file_path`) USING BTREE,
  INDEX `idx_uploader`(`uploader_id`, `uploader_type`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_storage_type`(`storage_type`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_md5_hash`(`md5_hash`) USING BTREE,
  INDEX `idx_is_temp`(`is_temp`, `temp_expire_time`) USING BTREE,
  INDEX `idx_access_count`(`access_count`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of ptm_file_record
-- ----------------------------
INSERT INTO `ptm_file_record` VALUES (1, 'sample1.jpg', 'uuid1.jpg', '/uploads/2025/01/uuid1.jpg', 'http://localhost:8081/uploads/2025/01/uuid1.jpg', 1024000, 'image/jpeg', 'jpg', 'image', 1, 'ADMIN', 'LOCAL', 0, NULL, NULL, 5, NULL, NULL, NULL, 0, NULL, '2025-05-24 16:42:26', '2025-05-24 16:42:26', NULL, 0);
INSERT INTO `ptm_file_record` VALUES (2, 'sample2.png', 'uuid2.png', '/uploads/2025/01/uuid2.png', 'http://localhost:8081/uploads/2025/01/uuid2.png', 2048000, 'image/png', 'png', 'image', 2, 'USER', 'LOCAL', 0, NULL, NULL, 3, NULL, NULL, NULL, 0, NULL, '2025-05-24 16:42:26', '2025-05-24 16:42:26', NULL, 0);
INSERT INTO `ptm_file_record` VALUES (3, 'document.pdf', 'uuid3.pdf', '/uploads/2025/01/uuid3.pdf', 'http://localhost:8081/uploads/2025/01/uuid3.pdf', 5120000, 'application/pdf', 'pdf', 'document', 1, 'ADMIN', 'LOCAL', 0, NULL, NULL, 1, NULL, NULL, NULL, 0, NULL, '2025-05-24 16:42:26', '2025-05-24 16:42:26', NULL, 0);

-- ----------------------------
-- Table structure for ptm_file_upload_config
-- ----------------------------
DROP TABLE IF EXISTS `ptm_file_upload_config`;
CREATE TABLE `ptm_file_upload_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `storage_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `config_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `upload_limits` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `path_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT 1,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `sort_order` int NOT NULL DEFAULT 0,
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `last_test_time` datetime NULL DEFAULT NULL,
  `last_test_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `created_by` bigint NULL DEFAULT NULL COMMENT '创建者ID',
  `updated_by` bigint NULL DEFAULT NULL COMMENT '更新者ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_file_upload_config
-- ----------------------------
INSERT INTO `ptm_file_upload_config` VALUES (1, '默认本地存储', 'LOCAL', '{\"localPath\":\"uploads\",\"domain\":\"http://localhost:8081\",\"useHttps\":false,\"connectTimeout\":30,\"readTimeout\":60}', '{\"maxFileSize\":50,\"maxFileCount\":10,\"allowedFileTypes\":[\"jpg\",\"jpeg\",\"png\",\"gif\",\"pdf\",\"doc\",\"docx\"],\"forbiddenFileTypes\":[\"exe\",\"bat\",\"sh\",\"cmd\"],\"enableFileTypeCheck\":true,\"enableContentCheck\":false,\"enableVirusScan\":false}', '{\"rootPath\":\"uploads\",\"fileNamingRule\":\"UUID\",\"directoryStructure\":\"DATE_USER_TYPE\",\"enableDateDirectory\":true,\"enableUserDirectory\":true,\"enableTypeDirectory\":true,\"thumbnailDirectory\":\"thumbnails\",\"tempDirectory\":\"temp\"}', 0, 0, 0, 1, '系统默认的本地文件存储配置', NULL, NULL, 1, 3, 1, '2025-05-23 19:22:28', '2025-05-24 15:38:03', NULL);
INSERT INTO `ptm_file_upload_config` VALUES (2, '默认本地存储', 'LOCAL', '{\"localPath\":\"uploads\",\"domain\":\"http://localhost:8081\",\"useHttps\":false,\"connectTimeout\":30,\"readTimeout\":60}', '{\"maxFileSize\":50,\"maxFileCount\":10,\"allowedFileTypes\":[\"jpg\",\"jpeg\",\"png\",\"gif\",\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"zip\",\"rar\"],\"forbiddenFileTypes\":[\"exe\",\"bat\",\"sh\",\"cmd\"],\"enableFileTypeCheck\":true,\"enableContentCheck\":false,\"enableVirusScan\":false,\"imageMaxDimensions\":{\"maxWidth\":4096,\"maxHeight\":4096}}', '{\"rootPath\":\"uploads\",\"fileNamingRule\":\"UUID\",\"directoryStructure\":\"DATE_USER_TYPE\",\"enableDateDirectory\":true,\"enableUserDirectory\":true,\"enableTypeDirectory\":true,\"customPrefix\":\"\",\"thumbnailDirectory\":\"thumbnails\",\"tempDirectory\":\"temp\"}', 0, 0, 0, 1, '系统默认的本地文件存储配置', '2025-05-24 15:38:10', '{\"success\":true,\"message\":\"连接测试成功\",\"responseTime\":0,\"testTime\":\"2025-05-24 15:38:10\",\"errorDetails\":null}', 1, 3, 0, '2025-05-23 19:31:30', '2025-05-24 18:46:11', NULL);
INSERT INTO `ptm_file_upload_config` VALUES (3, '七牛云存储示例', 'QINIU', '{\"qiniuAccessKey\":\"your-access-key\",\"qiniuSecretKey\":\"your-secret-key\",\"qiniuBucket\":\"your-bucket\",\"qiniuRegion\":\"z0\",\"domain\":\"https://your-domain.com\",\"useHttps\":true,\"connectTimeout\":30,\"readTimeout\":60}', '{\"maxFileSize\":100,\"maxFileCount\":20,\"allowedFileTypes\":[\"jpg\",\"jpeg\",\"png\",\"gif\",\"webp\",\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"zip\",\"rar\",\"mp4\",\"avi\",\"mov\"],\"forbiddenFileTypes\":[\"exe\",\"bat\",\"sh\",\"cmd\",\"scr\"],\"enableFileTypeCheck\":true,\"enableContentCheck\":true,\"enableVirusScan\":false,\"imageMaxDimensions\":{\"maxWidth\":8192,\"maxHeight\":8192}}', '{\"rootPath\":\"phototagmoment\",\"fileNamingRule\":\"UUID\",\"directoryStructure\":\"DATE_TYPE\",\"enableDateDirectory\":true,\"enableUserDirectory\":false,\"enableTypeDirectory\":true,\"customPrefix\":\"ptm\",\"thumbnailDirectory\":\"thumbnails\",\"tempDirectory\":\"temp\"}', 0, 0, 1, 2, '七牛云对象存储配置示例，需要配置真实的AccessKey和SecretKey', NULL, NULL, 1, 1, 1, '2025-05-23 19:31:30', '2025-05-24 15:38:15', NULL);
INSERT INTO `ptm_file_upload_config` VALUES (4, '阿里云OSS示例', 'ALIYUN_OSS', '{\"aliyunAccessKeyId\":\"your-access-key-id\",\"aliyunAccessKeySecret\":\"your-access-key-secret\",\"aliyunBucket\":\"your-bucket\",\"aliyunEndpoint\":\"oss-cn-hangzhou.aliyuncs.com\",\"domain\":\"https://your-bucket.oss-cn-hangzhou.aliyuncs.com\",\"useHttps\":true,\"connectTimeout\":30,\"readTimeout\":60}', '{\"maxFileSize\":200,\"maxFileCount\":50,\"allowedFileTypes\":[\"jpg\",\"jpeg\",\"png\",\"gif\",\"webp\",\"bmp\",\"svg\",\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"csv\",\"zip\",\"rar\",\"7z\",\"mp4\",\"avi\",\"mov\",\"wmv\",\"flv\",\"mp3\",\"wav\",\"aac\"],\"forbiddenFileTypes\":[\"exe\",\"bat\",\"sh\",\"cmd\",\"scr\",\"msi\"],\"enableFileTypeCheck\":true,\"enableContentCheck\":true,\"enableVirusScan\":true,\"imageMaxDimensions\":{\"maxWidth\":10240,\"maxHeight\":10240}}', '{\"rootPath\":\"phototagmoment\",\"fileNamingRule\":\"UUID_ORIGINAL\",\"directoryStructure\":\"DATE_USER_TYPE\",\"enableDateDirectory\":true,\"enableUserDirectory\":true,\"enableTypeDirectory\":true,\"customPrefix\":\"ptm\",\"thumbnailDirectory\":\"thumbnails\",\"tempDirectory\":\"temp\"}', 0, 0, 1, 3, '阿里云对象存储服务配置示例，需要配置真实的AccessKey', NULL, NULL, 1, 1, 0, '2025-05-23 19:31:30', '2025-05-24 14:37:27', NULL);
INSERT INTO `ptm_file_upload_config` VALUES (5, '七牛云', 'QINIU', 'UZwCp5sP+G4TFF7w/z29cMwVN65GV/guYvnmx1Gh/EYd8jhmq2n7dxitf7S0smebgi0Ed2hafaps0BWt5TKMBA7ZIVqrIHioBFRs3NUIiP6SdaqZIWpH/i+lGAp66h9MZoZAjL8wwq9cbYL3J5AyldDwXrz5cAas6NC5ARMYtbTSZuo9Is1vbC1/WDe7PKGAbFakaOwo0zqSeyXgrUt77bPs4XjUGL4wn4gDDdoCXg2wruo5vuL7SO7CCzUuNPCy8FB/RDJ+9G2nYLFEZ1MZZhYNSqoouTLAxOBxpS1CqADLIBsWSqDbIF1eqsULebZZchasH6DPu8VSDBx4gQMlpn8h9XE5YrY2Rny6GNdJzYJTfbr1NeXg56Jpd3rUigSTbKAmuxJAyDsELInGrgkl+VdN6TGrH6LtTrVGTYj42GYqXLpntP/RguhrSmkJH9ksrejfZ/52MjCS3p4Ye6kKoB8t/7ogGx570HGqVuBJlRu/Ah2/howa9gDxVa/Ia0YPxTe1DQtf9vkmB13dWhuflg6G6fLHEXLzWzIv2+Na5HGh1oB+77slvgQNQnlwf1QhPdwcTlyHIxC7sVPcIIK+rNqWdsL2uwBkKlxqBOaH55cxNW21z8doUJ/axHUoFShRiCl9qrFkafcGD4QM5tihqFVtgG6V2JNGd3+ETf2FPsMKjt+dQbVACOrLSod5BKQHP9/w9E9czybNtAuOkXQAE2P1aSWxJ3hn8mTcKS7OmPGBPv2oYamseifZCZCe6knIxjbqmk1GRxPW9saigj/j3qqUNvyLFOHpfqMfX+m2PHkENQ6TtJQvpAXNl7ZCKdFexL+LLWdYlCNEcMLwyoMCQA==', '{\"maxFileSize\":50,\"maxFileCount\":10,\"allowedFileTypes\":[\"jpg\",\"jpeg\",\"png\",\"gif\",\"pdf\",\"doc\",\"docx\"],\"forbiddenFileTypes\":[],\"imageMaxDimensions\":{\"maxWidth\":4096,\"maxHeight\":4096},\"enableFileTypeCheck\":true,\"enableContentCheck\":true,\"enableVirusScan\":false}', '{\"rootPath\":\"uploads\",\"fileNamingRule\":\"UUID\",\"directoryStructure\":\"DATE_USER_TYPE\",\"enableDateDirectory\":true,\"enableUserDirectory\":true,\"enableTypeDirectory\":true,\"customPrefix\":\"\",\"thumbnailDirectory\":\"thumbnails\",\"tempDirectory\":\"temp\"}', 1, 1, 0, 0, '七牛云测试配置', '2025-05-24 15:38:04', '{\"success\":true,\"message\":\"连接测试成功\",\"responseTime\":0,\"testTime\":\"2025-05-24 15:38:04\",\"errorDetails\":null}', 1, NULL, 0, '2025-05-24 14:06:05', '2025-05-24 18:46:11', NULL);

-- ----------------------------
-- Table structure for ptm_file_upload_config_log
-- ----------------------------
DROP TABLE IF EXISTS `ptm_file_upload_config_log`;
CREATE TABLE `ptm_file_upload_config_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint NOT NULL COMMENT '配置ID',
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型（CREATE/UPDATE/DELETE/TEST/ENABLE/DISABLE）',
  `old_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '变更前的值（JSON格式）',
  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '变更后的值（JSON格式）',
  `operator_id` bigint NULL DEFAULT NULL COMMENT '操作者ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作者名称',
  `operator_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作者IP',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_config_id`(`config_id`) USING BTREE,
  INDEX `idx_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE,
  INDEX `idx_operation_time`(`operation_time`) USING BTREE,
  CONSTRAINT `fk_config_log_config_id` FOREIGN KEY (`config_id`) REFERENCES `ptm_file_upload_config` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件上传配置变更日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_file_upload_config_log
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_file_upload_config_stats
-- ----------------------------
DROP TABLE IF EXISTS `ptm_file_upload_config_stats`;
CREATE TABLE `ptm_file_upload_config_stats`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint NOT NULL COMMENT '配置ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `upload_count` int NOT NULL DEFAULT 0 COMMENT '上传次数',
  `upload_size` bigint NOT NULL DEFAULT 0 COMMENT '上传总大小（字节）',
  `success_count` int NOT NULL DEFAULT 0 COMMENT '成功次数',
  `fail_count` int NOT NULL DEFAULT 0 COMMENT '失败次数',
  `avg_response_time` int NOT NULL DEFAULT 0 COMMENT '平均响应时间（毫秒）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_date`(`config_id`, `stat_date`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE,
  CONSTRAINT `fk_config_stats_config_id` FOREIGN KEY (`config_id`) REFERENCES `ptm_file_upload_config` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件上传配置统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_file_upload_config_stats
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_identity_verification
-- ----------------------------
DROP TABLE IF EXISTS `ptm_identity_verification`;
CREATE TABLE `ptm_identity_verification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '真实姓名',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '身份证号',
  `id_card_front_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证正面照片URL',
  `id_card_back_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证背面照片URL',
  `id_card_holding_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手持身份证照片URL',
  `face_image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人脸照片URL',
  `verify_method` tinyint(1) NOT NULL DEFAULT 4 COMMENT '认证方式：1-支付宝，2-微信，3-人脸识别，4-人工审核',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '认证状态：0-待审核，1-已认证，2-认证失败',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失败原因',
  `verified_at` datetime NULL DEFAULT NULL COMMENT '认证时间',
  `reviewer_id` bigint NULL DEFAULT NULL COMMENT '审核人ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '实名认证表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_identity_verification
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_like
-- ----------------------------
DROP TABLE IF EXISTS `ptm_like`;
CREATE TABLE `ptm_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `target_id` bigint NOT NULL COMMENT '目标ID',
  `target_type` tinyint(1) NOT NULL COMMENT '目标类型：1照片，2评论',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_target`(`user_id`, `target_id`, `target_type`) USING BTREE,
  INDEX `idx_target`(`target_id`, `target_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_like
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_message
-- ----------------------------
DROP TABLE IF EXISTS `ptm_message`;
CREATE TABLE `ptm_message`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '私信ID',
  `from_user_id` bigint NOT NULL COMMENT '发送用户ID',
  `to_user_id` bigint NOT NULL COMMENT '接收用户ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '私信内容',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_from_user_id`(`from_user_id`) USING BTREE,
  INDEX `idx_to_user_id`(`to_user_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '私信表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_message
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_notification
-- ----------------------------
DROP TABLE IF EXISTS `ptm_notification`;
CREATE TABLE `ptm_notification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` bigint NOT NULL COMMENT '接收用户ID',
  `sender_id` bigint NULL DEFAULT NULL COMMENT '发送用户ID',
  `type` tinyint(1) NOT NULL COMMENT '通知类型：1关注，2点赞，3评论，4回复，5系统，6@用户',
  `target_id` bigint NULL DEFAULT NULL COMMENT '目标ID',
  `target_type` tinyint(1) NULL DEFAULT NULL COMMENT '目标类型：1照片，2评论，3用户',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通知内容',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_notification
-- ----------------------------
INSERT INTO `ptm_notification` VALUES (1, 1, NULL, 5, NULL, NULL, '您的照片《测试照片》已通过审核，现已公开显示。', 1, '2025-05-15 12:25:56');
INSERT INTO `ptm_notification` VALUES (2, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 测试分享》已通过审核，现已公开显示。', 1, '2025-05-15 16:49:18');
INSERT INTO `ptm_notification` VALUES (3, 1, NULL, 5, NULL, NULL, '您的照片《分享照片测试TAG》已通过审核，现已公开显示。', 1, '2025-05-15 17:39:24');
INSERT INTO `ptm_notification` VALUES (4, 1, NULL, 5, NULL, NULL, '您的照片《我的PhotoTag》已通过审核，现已公开显示。', 1, '2025-05-15 17:47:22');
INSERT INTO `ptm_notification` VALUES (5, 1, NULL, 5, NULL, NULL, '您的照片《我的PhotoTag》未通过审核，原因：文本包含违禁品内容', 1, '2025-05-15 17:47:22');
INSERT INTO `ptm_notification` VALUES (6, 1, NULL, 5, NULL, NULL, '您的照片《我的PhotoTag》已通过审核，现已公开显示。', 1, '2025-05-15 17:47:22');
INSERT INTO `ptm_notification` VALUES (7, 1, NULL, 5, NULL, NULL, '您的照片《我的PhotoTag》已通过审核，现已公开显示。', 1, '2025-05-15 17:47:22');
INSERT INTO `ptm_notification` VALUES (8, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:56');
INSERT INTO `ptm_notification` VALUES (9, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:56');
INSERT INTO `ptm_notification` VALUES (10, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:56');
INSERT INTO `ptm_notification` VALUES (11, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:57');
INSERT INTO `ptm_notification` VALUES (12, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:57');
INSERT INTO `ptm_notification` VALUES (13, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:57');
INSERT INTO `ptm_notification` VALUES (14, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:57');
INSERT INTO `ptm_notification` VALUES (15, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:57');
INSERT INTO `ptm_notification` VALUES (16, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTag 成都站》未通过审核，原因：文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', 0, '2025-05-24 19:00:58');
INSERT INTO `ptm_notification` VALUES (17, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:56');
INSERT INTO `ptm_notification` VALUES (18, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:57');
INSERT INTO `ptm_notification` VALUES (19, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:57');
INSERT INTO `ptm_notification` VALUES (20, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:57');
INSERT INTO `ptm_notification` VALUES (21, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:57');
INSERT INTO `ptm_notification` VALUES (22, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:57');
INSERT INTO `ptm_notification` VALUES (23, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:58');
INSERT INTO `ptm_notification` VALUES (24, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:58');
INSERT INTO `ptm_notification` VALUES (25, 1, NULL, 5, NULL, NULL, '您的照片《PhotoTAG 成都》未通过审核，原因：文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', 0, '2025-05-27 12:28:58');
INSERT INTO `ptm_notification` VALUES (26, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:18:59');
INSERT INTO `ptm_notification` VALUES (27, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:18:59');
INSERT INTO `ptm_notification` VALUES (28, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:18:59');
INSERT INTO `ptm_notification` VALUES (29, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:18:59');
INSERT INTO `ptm_notification` VALUES (30, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:19:00');
INSERT INTO `ptm_notification` VALUES (31, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:19:00');
INSERT INTO `ptm_notification` VALUES (32, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:19:00');
INSERT INTO `ptm_notification` VALUES (33, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:19:00');
INSERT INTO `ptm_notification` VALUES (34, 1, NULL, 5, NULL, NULL, '您的照片《测试PhotoTag》未通过审核，原因：文本包含敏感词: a, #, 测试, o', 0, '2025-05-27 13:19:00');

-- ----------------------------
-- Table structure for ptm_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `ptm_operation_log`;
CREATE TABLE `ptm_operation_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `operation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求方法',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '请求参数',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户代理',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '操作状态：0失败，1成功',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `execution_time` bigint NULL DEFAULT NULL COMMENT '执行时间(ms)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_operation`(`operation`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_operation_log
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo`;
CREATE TABLE `ptm_photo`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '照片分组ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述',
  `storage_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '存储路径',
  `original_filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原始文件名',
  `file_size` bigint NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件类型',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '访问URL',
  `thumbnail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '缩略图URL',
  `width` int NULL DEFAULT NULL COMMENT '宽度',
  `height` int NULL DEFAULT NULL COMMENT '高度',
  `size` bigint NULL DEFAULT NULL COMMENT '文件大小',
  `format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件格式',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地理位置',
  `longitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '纬度',
  `visibility` tinyint(1) NULL DEFAULT 1 COMMENT '可见性：0私密，1公开，2好友可见',
  `allow_comment` tinyint(1) NULL DEFAULT 1 COMMENT '是否允许评论',
  `allow_download` tinyint(1) NULL DEFAULT 1 COMMENT '是否允许下载',
  `view_count` int NULL DEFAULT 0 COMMENT '查看次数',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞次数',
  `comment_count` int NULL DEFAULT 0 COMMENT '评论次数',
  `collect_count` int NULL DEFAULT 0 COMMENT '收藏数',
  `share_count` int NULL DEFAULT 0 COMMENT '分享次数',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除',
  `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  `taken_time` datetime NULL DEFAULT NULL COMMENT '拍摄时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_visibility`(`visibility`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo
-- ----------------------------
INSERT INTO `ptm_photo` VALUES (1, 1, NULL, '这是一个测试', '测试照片发布#PhotoTAG', 'phototagmoment/photos/1/2025/05/15/3de12958295b4a62a571123e4ac57214', '38aa2ed5be9ebc8bae0a0c922a360c0ab3a545cb.jpg', 0, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/3de12958295b4a62a571123e4ac57214', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/3de12958295b4a62a571123e4ac57214?imageView2/2/w/400', 1092, 1279, NULL, NULL, '', NULL, NULL, 1, 1, 1, 3, 0, 0, 0, 0, 0, NULL, '2025-05-15 00:54:21', '2025-05-15 19:43:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (3, 1, NULL, '测试照片', '测试照片发布#PhotoTag', 'phototagmoment/photos/1/2025/05/15/60b1aac0df63415a8b0407e8e11f9467', '38aa2ed5be9ebc8bae0a0c922a360c0ab3a545cb.jpg', 0, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/60b1aac0df63415a8b0407e8e11f9467', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/60b1aac0df63415a8b0407e8e11f9467?imageView2/2/w/400', 1092, 1279, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 11:49:31', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (5, 1, NULL, '测试照片', '测试照片发布#PhotoTag', 'phototagmoment/photos/1/2025/05/15/bd9c29b961a046b7b6ab2d79fd0570f5', '38aa2ed5be9ebc8bae0a0c922a360c0ab3a545cb.jpg', 0, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/bd9c29b961a046b7b6ab2d79fd0570f5', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/bd9c29b961a046b7b6ab2d79fd0570f5?imageView2/2/w/400', 1092, 1279, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 12:14:11', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (7, 1, NULL, '测试照片', '测试照片发布#PhotoTag', 'phototagmoment/photos/1/2025/05/15/cd582b1adf36457dbc8373dc11f11508', '38aa2ed5be9ebc8bae0a0c922a360c0ab3a545cb.jpg', 0, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/cd582b1adf36457dbc8373dc11f11508', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/cd582b1adf36457dbc8373dc11f11508?imageView2/2/w/400', 1092, 1279, NULL, NULL, '', NULL, NULL, 1, 1, 1, 24, 0, 0, 0, 0, 0, NULL, '2025-05-15 12:25:56', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (8, 1, NULL, '测试照片', '测试照片发布#PhotoTag', 'phototagmoment/photos/1/2025/05/15/4a1a8e92264e453b961170aa6985693d', '18be18ad6d414ac5b85a6b54f4e7612ed9324f5d.jpg', 0, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/4a1a8e92264e453b961170aa6985693d', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/4a1a8e92264e453b961170aa6985693d?imageView2/2/w/400', 1276, 744, NULL, NULL, '', NULL, NULL, 1, 1, 1, 1, 0, 0, 0, 0, 0, NULL, '2025-05-15 12:25:56', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (9, 1, NULL, 'PhotoTag 分享', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag照片集\">#PhotoTag照片集 照片分享</span>', 'phototagmoment/photos/1/2025/05/15/9e5b597c5ef246c29ce08394beece384', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/9e5b597c5ef246c29ce08394beece384', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/9e5b597c5ef246c29ce08394beece384?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:21:22', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (10, 1, NULL, 'PhotoTag 分享', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag照片集\">#PhotoTag照片集 照片分享</span>', 'phototagmoment/photos/1/2025/05/15/70a263c4013d4003b543ce346e0b8696', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/70a263c4013d4003b543ce346e0b8696', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/70a263c4013d4003b543ce346e0b8696?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:21:22', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (11, 1, NULL, 'PhotoTag 分享', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag照片集\">#PhotoTag照片集 照片分享</span>', 'phototagmoment/photos/1/2025/05/15/4ee7f89275af46509960d97806e44030', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/4ee7f89275af46509960d97806e44030', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/4ee7f89275af46509960d97806e44030?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:21:23', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (12, 1, NULL, 'PhotoTag 分享', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag照片集\">#PhotoTag照片集 照片分享</span>', 'phototagmoment/photos/1/2025/05/15/62a0a62abe58495fae8d1919730f0749', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/62a0a62abe58495fae8d1919730f0749', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/62a0a62abe58495fae8d1919730f0749?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:21:23', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (13, 1, NULL, 'PhotoTag照片集', '照片分享#PhotoTag', 'phototagmoment/photos/1/2025/05/15/03cf3448c709416f9d2642aa9be9e0b2', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/03cf3448c709416f9d2642aa9be9e0b2', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/03cf3448c709416f9d2642aa9be9e0b2?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:23:33', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (14, 1, NULL, 'PhotoTag照片集', '照片分享#PhotoTag', 'phototagmoment/photos/1/2025/05/15/6fe8f921e9c34e36a79a88f765f98c19', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/6fe8f921e9c34e36a79a88f765f98c19', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/6fe8f921e9c34e36a79a88f765f98c19?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:23:34', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (15, 1, NULL, 'PhotoTag照片集', '照片分享#PhotoTag', 'phototagmoment/photos/1/2025/05/15/d822c8bbb74b43e7960ba03162af2a2e', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/d822c8bbb74b43e7960ba03162af2a2e', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/d822c8bbb74b43e7960ba03162af2a2e?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:23:34', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (16, 1, NULL, 'PhotoTag照片集', '照片分享#PhotoTag', 'phototagmoment/photos/1/2025/05/15/ab2ce86c5efa465381581db3d16a5290', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/ab2ce86c5efa465381581db3d16a5290', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/ab2ce86c5efa465381581db3d16a5290?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 1, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:23:34', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (17, 1, NULL, 'PhotoTag 测试分享', '照片分享测试#PhotoTag', 'phototagmoment/photos/1/2025/05/15/3c00e2ca0c1d40b990c3290fb3a0f78b', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/3c00e2ca0c1d40b990c3290fb3a0f78b', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/3c00e2ca0c1d40b990c3290fb3a0f78b?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 1, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:49:18', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (18, 1, NULL, 'PhotoTag 测试分享', '照片分享测试#PhotoTag', 'phototagmoment/photos/1/2025/05/15/8b482475bfa545bb84a6d047216d8348', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/8b482475bfa545bb84a6d047216d8348', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/8b482475bfa545bb84a6d047216d8348?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:49:18', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (19, 1, NULL, 'PhotoTag 测试分享', '照片分享测试#PhotoTag', 'phototagmoment/photos/1/2025/05/15/698f9f9e29ec4abf9d8e0877104395cf', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/698f9f9e29ec4abf9d8e0877104395cf', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/698f9f9e29ec4abf9d8e0877104395cf?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:49:18', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (20, 1, NULL, 'PhotoTag 测试分享', '照片分享测试#PhotoTag', 'phototagmoment/photos/1/2025/05/15/696abf1b05464d2db7c4e3bb6d2158ba', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/696abf1b05464d2db7c4e3bb6d2158ba', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/696abf1b05464d2db7c4e3bb6d2158ba?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:49:18', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (21, 1, NULL, '照片分享Tag', '测试照片分享 #PhotoTAG', 'phototagmoment/photos/1/2025/05/15/39f5aa7de7ab451f8723af8d4379c685', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/39f5aa7de7ab451f8723af8d4379c685', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/39f5aa7de7ab451f8723af8d4379c685?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:51:06', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (22, 1, NULL, '照片分享Tag', '测试照片分享 #PhotoTAG', 'phototagmoment/photos/1/2025/05/15/b1a52c56f90e4ce68c2c0fcb3cd5f7d6', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/b1a52c56f90e4ce68c2c0fcb3cd5f7d6', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/b1a52c56f90e4ce68c2c0fcb3cd5f7d6?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:51:06', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (23, 1, NULL, '照片分享Tag', '测试照片分享 #PhotoTAG', 'phototagmoment/photos/1/2025/05/15/432bc58a764944b392df0b5eb7ceae9e', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/432bc58a764944b392df0b5eb7ceae9e', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/432bc58a764944b392df0b5eb7ceae9e?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:51:07', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (24, 1, NULL, '照片分享Tag', '测试照片分享 #PhotoTAG', 'phototagmoment/photos/1/2025/05/15/df7889c26cea49718c646b56c6664967', NULL, NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/df7889c26cea49718c646b56c6664967', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/df7889c26cea49718c646b56c6664967?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 2, 0, 0, 0, 0, 0, NULL, '2025-05-15 16:51:07', '2025-05-16 13:23:56', 0, NULL);
INSERT INTO `ptm_photo` VALUES (25, 1, '1747301962270-wcwj414f6', '分享照片测试TAG', '照片分享PhotoTag #PhotoTag', 'phototagmoment/photos/1/2025/05/15/fd68e511fe274204bb9f1e873d834c86', '451df8743d210292ce73ed46fc14ced.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/fd68e511fe274204bb9f1e873d834c86', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/fd68e511fe274204bb9f1e873d834c86?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 17:39:24', '2025-05-15 17:47:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (26, 1, '1747301962270-wcwj414f6', '分享照片测试TAG', '照片分享PhotoTag #PhotoTag', 'phototagmoment/photos/1/2025/05/15/14a7c484a5b84ff8a0d9a1918a175783', 'a8f434cdf357bfaad93afe88cb3a0cc.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/14a7c484a5b84ff8a0d9a1918a175783', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/14a7c484a5b84ff8a0d9a1918a175783?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 17:39:24', '2025-05-15 17:39:24', 0, NULL);
INSERT INTO `ptm_photo` VALUES (27, 1, '1747301962270-wcwj414f6', '分享照片测试TAG', '照片分享PhotoTag #PhotoTag', 'phototagmoment/photos/1/2025/05/15/14e69f3e68474468a5109ac19e6a6025', 'b1ba9786b05414db28cdde1746f81b2.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/14e69f3e68474468a5109ac19e6a6025', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/14e69f3e68474468a5109ac19e6a6025?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 17:39:24', '2025-05-15 17:39:24', 0, NULL);
INSERT INTO `ptm_photo` VALUES (28, 1, '1747301962270-wcwj414f6', '分享照片测试TAG', '照片分享PhotoTag #PhotoTag', 'phototagmoment/photos/1/2025/05/15/9f63b3d0c44c411b8b1db5e34c5981eb', 'b6e41e7c2d144ab32f285fc7b32bb9e.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/9f63b3d0c44c411b8b1db5e34c5981eb', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/9f63b3d0c44c411b8b1db5e34c5981eb?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 0, NULL, '2025-05-15 17:39:24', '2025-05-15 17:39:24', 0, NULL);
INSERT INTO `ptm_photo` VALUES (29, 1, '1747302439925-yg8v0aprb', '我的PhotoTag', '<span class=\"topic-tag\" data-tag-name=\"我的PhotoTag\">#我的PhotoTag 在成都</span>', 'phototagmoment/photos/1/2025/05/15/fc79628c46844a5b82dd4eada1395646', '451df8743d210292ce73ed46fc14ced.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/fc79628c46844a5b82dd4eada1395646', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/fc79628c46844a5b82dd4eada1395646?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 10, 0, 0, 0, 0, 1, NULL, '2025-05-15 17:47:22', '2025-05-16 14:34:12', 0, NULL);
INSERT INTO `ptm_photo` VALUES (30, 1, '1747302439925-yg8v0aprb', '我的PhotoTag', '<span class=\"topic-tag\" data-tag-name=\"我的PhotoTag\">#我的PhotoTag 在成都</span>', 'phototagmoment/photos/1/2025/05/15/0e9151f18e0348a48d529ab8d60969c8', 'a8f434cdf357bfaad93afe88cb3a0cc.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/0e9151f18e0348a48d529ab8d60969c8', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/0e9151f18e0348a48d529ab8d60969c8?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 2, 0, 0, 0, 0, 1, '文本包含违禁品内容', '2025-05-15 17:47:22', '2025-05-21 12:49:19', 0, NULL);
INSERT INTO `ptm_photo` VALUES (31, 1, '1747302439925-yg8v0aprb', '我的PhotoTag', '<span class=\"topic-tag\" data-tag-name=\"我的PhotoTag\">#我的PhotoTag 在成都</span>', 'phototagmoment/photos/1/2025/05/15/5d0fc1a55a314053a3f6252de14053ce', 'b1ba9786b05414db28cdde1746f81b2.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/5d0fc1a55a314053a3f6252de14053ce', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/5d0fc1a55a314053a3f6252de14053ce?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 151, 1, 2, 0, 0, 1, NULL, '2025-05-15 17:47:22', '2025-05-26 14:06:59', 0, NULL);
INSERT INTO `ptm_photo` VALUES (32, 1, '1747302439925-yg8v0aprb', '我的PhotoTag', '<span class=\"topic-tag\" data-tag-name=\"我的PhotoTag\">#我的PhotoTag 在成都</span>', 'phototagmoment/photos/1/2025/05/15/22756b93a93644e1a3e6ef069a2eb49a', 'b6e41e7c2d144ab32f285fc7b32bb9e.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/22756b93a93644e1a3e6ef069a2eb49a', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/15/22756b93a93644e1a3e6ef069a2eb49a?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 6, 0, 0, 0, 0, 1, NULL, '2025-05-15 17:47:22', '2025-05-23 16:19:00', 0, NULL);
INSERT INTO `ptm_photo` VALUES (33, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/32f99bf9678846d19f82af115244316e', '1.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/32f99bf9678846d19f82af115244316e', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/32f99bf9678846d19f82af115244316e?imageView2/2/w/400', 2048, 1586, NULL, NULL, '', NULL, NULL, 1, 1, 1, 1, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:56', '2025-05-24 19:00:59', 0, NULL);
INSERT INTO `ptm_photo` VALUES (34, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/9d1cfe6b0d1c4fdab36a26800ce4e542', '3f206ae6bce75e0bdf5a32cb7e8a09c.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/9d1cfe6b0d1c4fdab36a26800ce4e542', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/9d1cfe6b0d1c4fdab36a26800ce4e542?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:56', '2025-05-24 19:00:56', 0, NULL);
INSERT INTO `ptm_photo` VALUES (35, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/1eb0c4de47594c79af74fe465d01408c', '12ff1da46d994e45a968ea1dff88caa.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/1eb0c4de47594c79af74fe465d01408c', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/1eb0c4de47594c79af74fe465d01408c?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:56', '2025-05-24 19:00:56', 0, NULL);
INSERT INTO `ptm_photo` VALUES (36, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/5e7b1a6d88bf480bbe0df14a2576a1f4', '388dc9c99659cd0aae55e1d8d84c7da.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/5e7b1a6d88bf480bbe0df14a2576a1f4', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/5e7b1a6d88bf480bbe0df14a2576a1f4?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:57', '2025-05-24 19:00:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (37, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/77dff281c5694b0aa3cfeb008ab5ed51', '451df8743d210292ce73ed46fc14ced.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/77dff281c5694b0aa3cfeb008ab5ed51', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/77dff281c5694b0aa3cfeb008ab5ed51?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:57', '2025-05-24 19:00:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (38, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/5fba9300074845fea39a357ab7fff34d', 'a8f434cdf357bfaad93afe88cb3a0cc.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/5fba9300074845fea39a357ab7fff34d', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/5fba9300074845fea39a357ab7fff34d?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:57', '2025-05-24 19:00:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (39, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/12bb677cd1b245c4a7d716a3fbe1a60b', 'b1ba9786b05414db28cdde1746f81b2.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/12bb677cd1b245c4a7d716a3fbe1a60b', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/12bb677cd1b245c4a7d716a3fbe1a60b?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:57', '2025-05-24 19:00:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (40, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/1f507635a4d54787806ed965433245e1', 'b6e41e7c2d144ab32f285fc7b32bb9e.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/1f507635a4d54787806ed965433245e1', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/1f507635a4d54787806ed965433245e1?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:57', '2025-05-24 19:00:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (41, 1, '1748084453371-fawsdpmpc', 'PhotoTag 成都站', '<span class=\"topic-tag\" data-tag-name=\"PhotoTag成都\">#PhotoTag成都 在成都东郊记忆有一个自拍馆叫PhotoTag</span>', 'phototagmoment/photos/1/2025/05/24/e345a84581eb4f62ae77fa60cb0b14c6', 'c6ae50c58f1cb7b8c8de53462150e61.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/e345a84581eb4f62ae77fa60cb0b14c6', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/24/e345a84581eb4f62ae77fa60cb0b14c6?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', '2025-05-24 19:00:57', '2025-05-24 19:00:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (42, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/4e57e3e97f3b42c5be784f7e1a6bd977', '1.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/4e57e3e97f3b42c5be784f7e1a6bd977', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/4e57e3e97f3b42c5be784f7e1a6bd977?imageView2/2/w/400', 2048, 1586, NULL, NULL, '', NULL, NULL, 1, 1, 1, 2, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:56', '2025-05-27 13:17:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (43, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/9549d890357942a4b47589363f1062cc', '3f206ae6bce75e0bdf5a32cb7e8a09c.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/9549d890357942a4b47589363f1062cc', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/9549d890357942a4b47589363f1062cc?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:57', '2025-05-27 12:28:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (44, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/c32d0b1e297643f5891f9f62f29b5f0e', '12ff1da46d994e45a968ea1dff88caa.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/c32d0b1e297643f5891f9f62f29b5f0e', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/c32d0b1e297643f5891f9f62f29b5f0e?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:57', '2025-05-27 12:28:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (45, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/bb462dc048bc44b9af5be7d37151df5b', '388dc9c99659cd0aae55e1d8d84c7da.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/bb462dc048bc44b9af5be7d37151df5b', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/bb462dc048bc44b9af5be7d37151df5b?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:57', '2025-05-27 12:28:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (46, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/fb3aee0474b64cd993cf1d1ca519029d', '451df8743d210292ce73ed46fc14ced.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/fb3aee0474b64cd993cf1d1ca519029d', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/fb3aee0474b64cd993cf1d1ca519029d?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:57', '2025-05-27 12:28:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (47, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/40a48a30448941768012307fa95fd759', 'a8f434cdf357bfaad93afe88cb3a0cc.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/40a48a30448941768012307fa95fd759', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/40a48a30448941768012307fa95fd759?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:57', '2025-05-27 12:28:57', 0, NULL);
INSERT INTO `ptm_photo` VALUES (48, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/1e64999098994e07a8db78e610051760', 'b1ba9786b05414db28cdde1746f81b2.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/1e64999098994e07a8db78e610051760', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/1e64999098994e07a8db78e610051760?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:58', '2025-05-27 12:28:58', 0, NULL);
INSERT INTO `ptm_photo` VALUES (49, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/5bbd0958c3814b0b9639b21d337bf094', 'b6e41e7c2d144ab32f285fc7b32bb9e.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/5bbd0958c3814b0b9639b21d337bf094', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/5bbd0958c3814b0b9639b21d337bf094?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:58', '2025-05-27 12:28:58', 0, NULL);
INSERT INTO `ptm_photo` VALUES (50, 1, '1748320134473-88zjv3wl0', 'PhotoTAG 成都', '<span class=\"topic-tag\" data-tag-name=\"PhotoTAG成都\">#PhotoTAG成都 成都东郊记忆有一个自拍馆，出片效果特别漂亮。</span>', 'phototagmoment/photos/1/2025/05/27/b8b5366ec06d41638c0abe0b671640e1', 'c6ae50c58f1cb7b8c8de53462150e61.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/b8b5366ec06d41638c0abe0b671640e1', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/b8b5366ec06d41638c0abe0b671640e1?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', '2025-05-27 12:28:58', '2025-05-27 12:28:58', 0, NULL);
INSERT INTO `ptm_photo` VALUES (51, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/7576288d50ef4174bd6419dd42ed04fc', '1.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/7576288d50ef4174bd6419dd42ed04fc', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/7576288d50ef4174bd6419dd42ed04fc?imageView2/2/w/400', 2048, 1586, NULL, NULL, '', NULL, NULL, 1, 1, 1, 1, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:18:59', '2025-05-27 13:19:02', 0, NULL);
INSERT INTO `ptm_photo` VALUES (52, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/d10622c6a2424b40a83750a982ff5842', '3f206ae6bce75e0bdf5a32cb7e8a09c.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d10622c6a2424b40a83750a982ff5842', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/d10622c6a2424b40a83750a982ff5842?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:18:59', '2025-05-27 13:18:59', 0, NULL);
INSERT INTO `ptm_photo` VALUES (53, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/b8319de2bd06486db21031667efcb885', '12ff1da46d994e45a968ea1dff88caa.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/b8319de2bd06486db21031667efcb885', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/b8319de2bd06486db21031667efcb885?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:18:59', '2025-05-27 13:18:59', 0, NULL);
INSERT INTO `ptm_photo` VALUES (54, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/0ee22aa70ed54a7b9507b4aff5455a22', '388dc9c99659cd0aae55e1d8d84c7da.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/0ee22aa70ed54a7b9507b4aff5455a22', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/0ee22aa70ed54a7b9507b4aff5455a22?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:18:59', '2025-05-27 13:18:59', 0, NULL);
INSERT INTO `ptm_photo` VALUES (55, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/b31ca15a8baf4176b194d55502af3ef3', '451df8743d210292ce73ed46fc14ced.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/b31ca15a8baf4176b194d55502af3ef3', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/b31ca15a8baf4176b194d55502af3ef3?imageView2/2/w/400', 4096, 2732, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:19:00', '2025-05-27 13:19:00', 0, NULL);
INSERT INTO `ptm_photo` VALUES (56, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/c59e653a060842b6a5261f4635ce6063', 'a8f434cdf357bfaad93afe88cb3a0cc.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/c59e653a060842b6a5261f4635ce6063', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/c59e653a060842b6a5261f4635ce6063?imageView2/2/w/400', 4433, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:19:00', '2025-05-27 13:19:00', 0, NULL);
INSERT INTO `ptm_photo` VALUES (57, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/f26741cec00d440fb80806640d789ba1', 'b1ba9786b05414db28cdde1746f81b2.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/f26741cec00d440fb80806640d789ba1', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/f26741cec00d440fb80806640d789ba1?imageView2/2/w/400', 1536, 1026, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:19:00', '2025-05-27 13:19:00', 0, NULL);
INSERT INTO `ptm_photo` VALUES (58, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/82e69a1530a6419da3fc1ed202d4a688', 'b6e41e7c2d144ab32f285fc7b32bb9e.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/82e69a1530a6419da3fc1ed202d4a688', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/82e69a1530a6419da3fc1ed202d4a688?imageView2/2/w/400', 4608, 3072, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:19:00', '2025-05-27 13:19:00', 0, NULL);
INSERT INTO `ptm_photo` VALUES (59, 1, '1748323137331-d1m1kchir', '测试PhotoTag', '#测试PhotoTAG', 'phototagmoment/photos/1/2025/05/27/6a964807a68246209990a142d43f6287', 'c6ae50c58f1cb7b8c8de53462150e61.jpg', NULL, 'image/jpeg', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/6a964807a68246209990a142d43f6287', 'http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/6a964807a68246209990a142d43f6287?imageView2/2/w/400', 1080, 1620, NULL, NULL, '', NULL, NULL, 1, 1, 1, 0, 0, 0, 0, 0, 2, '文本包含敏感词: a, #, 测试, o', '2025-05-27 13:19:00', '2025-05-27 13:19:00', 0, NULL);

-- ----------------------------
-- Table structure for ptm_photo_audit
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_audit`;
CREATE TABLE `ptm_photo_audit`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '审核记录ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `audit_type` tinyint NOT NULL COMMENT '审核类型: 0-自动审核, 1-人工审核',
  `audit_result` tinyint NOT NULL COMMENT '审核结果: 0-待审核, 1-通过, 2-拒绝',
  `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `auditor_id` bigint NULL DEFAULT NULL COMMENT '审核人ID（人工审核时）',
  `audit_time` datetime NOT NULL COMMENT '审核时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片审核记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_audit
-- ----------------------------
INSERT INTO `ptm_photo_audit` VALUES (1, 7, 0, 1, NULL, NULL, '2025-05-15 12:25:56', '2025-05-15 12:25:56', '2025-05-15 12:25:56');
INSERT INTO `ptm_photo_audit` VALUES (2, 17, 0, 1, NULL, NULL, '2025-05-15 16:49:18', '2025-05-15 16:49:18', '2025-05-15 16:49:18');
INSERT INTO `ptm_photo_audit` VALUES (3, 25, 0, 1, NULL, NULL, '2025-05-15 17:39:24', '2025-05-15 17:39:24', '2025-05-15 17:39:24');
INSERT INTO `ptm_photo_audit` VALUES (4, 29, 0, 1, NULL, NULL, '2025-05-15 17:47:22', '2025-05-15 17:47:22', '2025-05-15 17:47:22');
INSERT INTO `ptm_photo_audit` VALUES (5, 30, 0, 2, '文本包含违禁品内容', NULL, '2025-05-15 17:47:22', '2025-05-15 17:47:22', '2025-05-15 17:47:22');
INSERT INTO `ptm_photo_audit` VALUES (6, 31, 0, 1, NULL, NULL, '2025-05-15 17:47:22', '2025-05-15 17:47:22', '2025-05-15 17:47:22');
INSERT INTO `ptm_photo_audit` VALUES (7, 32, 0, 1, NULL, NULL, '2025-05-15 17:47:22', '2025-05-15 17:47:22', '2025-05-15 17:47:22');
INSERT INTO `ptm_photo_audit` VALUES (8, 33, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:56', '2025-05-24 19:00:56', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_audit` VALUES (9, 34, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:56', '2025-05-24 19:00:56', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_audit` VALUES (10, 35, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:56', '2025-05-24 19:00:56', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_audit` VALUES (11, 36, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:57', '2025-05-24 19:00:57', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_audit` VALUES (12, 37, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:57', '2025-05-24 19:00:57', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_audit` VALUES (13, 38, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:57', '2025-05-24 19:00:57', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_audit` VALUES (14, 39, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:57', '2025-05-24 19:00:57', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_audit` VALUES (15, 40, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:57', '2025-05-24 19:00:57', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_audit` VALUES (16, 41, 0, 2, '文本包含敏感词: 成, 一, a, c, #, e, 有, 个, 自, 东, 拍, o', NULL, '2025-05-24 19:00:58', '2025-05-24 19:00:58', '2025-05-24 19:00:58');
INSERT INTO `ptm_photo_audit` VALUES (17, 42, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:56', '2025-05-27 12:28:56', '2025-05-27 12:28:56');
INSERT INTO `ptm_photo_audit` VALUES (18, 43, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:57', '2025-05-27 12:28:57', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_audit` VALUES (19, 44, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:57', '2025-05-27 12:28:57', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_audit` VALUES (20, 45, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:57', '2025-05-27 12:28:57', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_audit` VALUES (21, 46, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:57', '2025-05-27 12:28:57', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_audit` VALUES (22, 47, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:57', '2025-05-27 12:28:57', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_audit` VALUES (23, 48, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:58', '2025-05-27 12:28:58', '2025-05-27 12:28:58');
INSERT INTO `ptm_photo_audit` VALUES (24, 49, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:58', '2025-05-27 12:28:58', '2025-05-27 12:28:58');
INSERT INTO `ptm_photo_audit` VALUES (25, 50, 0, 2, '文本包含敏感词: 一, a, c, #, e, 片, 有, 个, 自, 拍, o, 成, 特, 东', NULL, '2025-05-27 12:28:58', '2025-05-27 12:28:58', '2025-05-27 12:28:58');
INSERT INTO `ptm_photo_audit` VALUES (26, 51, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:18:59', '2025-05-27 13:18:59', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_audit` VALUES (27, 52, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:18:59', '2025-05-27 13:18:59', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_audit` VALUES (28, 53, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:18:59', '2025-05-27 13:18:59', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_audit` VALUES (29, 54, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:18:59', '2025-05-27 13:18:59', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_audit` VALUES (30, 55, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:19:00', '2025-05-27 13:19:00', '2025-05-27 13:19:00');
INSERT INTO `ptm_photo_audit` VALUES (31, 56, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:19:00', '2025-05-27 13:19:00', '2025-05-27 13:19:00');
INSERT INTO `ptm_photo_audit` VALUES (32, 57, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:19:00', '2025-05-27 13:19:00', '2025-05-27 13:19:00');
INSERT INTO `ptm_photo_audit` VALUES (33, 58, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:19:00', '2025-05-27 13:19:00', '2025-05-27 13:19:00');
INSERT INTO `ptm_photo_audit` VALUES (34, 59, 0, 2, '文本包含敏感词: a, #, 测试, o', NULL, '2025-05-27 13:19:00', '2025-05-27 13:19:00', '2025-05-27 13:19:00');

-- ----------------------------
-- Table structure for ptm_photo_collect
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_collect`;
CREATE TABLE `ptm_photo_collect`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_photo_user`(`photo_id`, `user_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_collect
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_comment
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_comment`;
CREATE TABLE `ptm_photo_comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父评论ID',
  `reply_to_id` bigint NULL DEFAULT NULL COMMENT '回复评论ID',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-待审核，1-正常，2-已删除',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_reply_to_id`(`reply_to_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_comment
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_draft
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_draft`;
CREATE TABLE `ptm_photo_draft`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '草稿ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '草稿标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '草稿描述',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '拍摄地点',
  `tags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '标签，JSON格式',
  `mentions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '提及用户，JSON格式',
  `visibility` tinyint(1) NULL DEFAULT 1 COMMENT '可见性: 0-私密, 1-公开, 2-好友可见',
  `allow_comment` tinyint(1) NULL DEFAULT 1 COMMENT '是否允许评论: 0-不允许, 1-允许',
  `allow_download` tinyint(1) NULL DEFAULT 1 COMMENT '是否允许下载: 0-不允许, 1-允许',
  `temp_file_paths` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '临时文件路径，JSON格式',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除, 1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片草稿表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_draft
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_hotness
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_hotness`;
CREATE TABLE `ptm_photo_hotness`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `hot_score` double NOT NULL DEFAULT 0 COMMENT '热度分数',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞数',
  `comment_count` int NOT NULL DEFAULT 0 COMMENT '评论数',
  `collect_count` int NOT NULL DEFAULT 0 COMMENT '收藏数',
  `view_count` int NOT NULL DEFAULT 0 COMMENT '浏览数',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_hot_score`(`hot_score`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片热度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_hotness
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_like
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_like`;
CREATE TABLE `ptm_photo_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_photo_user`(`photo_id`, `user_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_like
-- ----------------------------
INSERT INTO `ptm_photo_like` VALUES (1, 31, 1, '2025-05-15 19:14:52', '2025-05-15 19:14:51');

-- ----------------------------
-- Table structure for ptm_photo_note
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_note`;
CREATE TABLE `ptm_photo_note`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '照片笔记ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标题（可选，最多100字符）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '正文内容（必填，最多2000字符）',
  `photo_count` tinyint NOT NULL DEFAULT 1 COMMENT '照片数量（1-9张）',
  `view_count` int NULL DEFAULT 0 COMMENT '浏览量',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `comment_count` int NULL DEFAULT 0 COMMENT '评论数',
  `share_count` int NULL DEFAULT 0 COMMENT '分享数',
  `visibility` tinyint(1) NULL DEFAULT 1 COMMENT '可见性: 0-私密, 1-公开, 2-好友可见',
  `allow_comment` tinyint(1) NULL DEFAULT 1 COMMENT '是否允许评论: 0-不允许, 1-允许',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除',
  `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地理位置',
  `longitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10, 7) NULL DEFAULT NULL COMMENT '纬度',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_like_count`(`like_count`) USING BTREE,
  INDEX `idx_view_count`(`view_count`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_note
-- ----------------------------
INSERT INTO `ptm_photo_note` VALUES (1, 1, '测试照片笔记', '这是一个测试照片笔记的内容，用于验证照片笔记详情接口是否正常工作。', 1, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, NULL, NULL, '2025-05-23 16:33:37', '2025-05-23 16:33:37', 0);
INSERT INTO `ptm_photo_note` VALUES (31, 1, '照片笔记31', '这是ID为31的测试照片笔记，包含了丰富的内容和标签。#测试标签# #照片分享# 这里还有@用户提及功能的演示。', 3, 43, 5, 2, 1, 1, 1, 1, NULL, NULL, NULL, NULL, '2025-05-23 16:33:55', '2025-05-24 11:21:27', 0);

-- ----------------------------
-- Table structure for ptm_photo_note_collection
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_note_collection`;
CREATE TABLE `ptm_photo_note_collection`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint NOT NULL COMMENT '照片笔记ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_note_user`(`note_id`, `user_id`) USING BTREE,
  INDEX `idx_note_id`(`note_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_note_collection
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_note_comment
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_note_comment`;
CREATE TABLE `ptm_photo_note_comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `note_id` bigint NOT NULL COMMENT '照片笔记ID',
  `user_id` bigint NOT NULL COMMENT '评论用户ID',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父评论ID（回复评论时使用）',
  `reply_to_user_id` bigint NULL DEFAULT NULL COMMENT '回复的用户ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `reply_count` int NULL DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0-待审核，1-正常，2-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_note_id`(`note_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_note_comment
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_note_image
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_note_image`;
CREATE TABLE `ptm_photo_note_image`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint NOT NULL COMMENT '照片笔记ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `sort_order` tinyint NOT NULL DEFAULT 1 COMMENT '排序顺序（1-9）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_note_photo`(`note_id`, `photo_id`) USING BTREE,
  INDEX `idx_note_id`(`note_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记图片关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_note_image
-- ----------------------------
INSERT INTO `ptm_photo_note_image` VALUES (1, 31, 31, 1, '2025-05-23 17:05:07');

-- ----------------------------
-- Table structure for ptm_photo_note_like
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_note_like`;
CREATE TABLE `ptm_photo_note_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint NOT NULL COMMENT '照片笔记ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_note_user`(`note_id`, `user_id`) USING BTREE,
  INDEX `idx_note_id`(`note_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_note_like
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_note_mention
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_note_mention`;
CREATE TABLE `ptm_photo_note_mention`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint NOT NULL COMMENT '照片笔记ID',
  `mentioned_user_id` bigint NOT NULL COMMENT '被@用户ID',
  `mention_user_id` bigint NOT NULL COMMENT '@用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_note_mention`(`note_id`, `mentioned_user_id`) USING BTREE,
  INDEX `idx_note_id`(`note_id`) USING BTREE,
  INDEX `idx_mentioned_user_id`(`mentioned_user_id`) USING BTREE,
  INDEX `idx_mention_user_id`(`mention_user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记@用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_note_mention
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_note_tag
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_note_tag`;
CREATE TABLE `ptm_photo_note_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint NOT NULL COMMENT '照片笔记ID',
  `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标签名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_note_tag`(`note_id`, `tag_name`) USING BTREE,
  INDEX `idx_note_id`(`note_id`) USING BTREE,
  INDEX `idx_tag_name`(`tag_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_note_tag
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_person
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_person`;
CREATE TABLE `ptm_photo_person`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID（如果标记的是用户）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人物名称（如果不是用户）',
  `position_x` decimal(5, 2) NULL DEFAULT NULL COMMENT 'X坐标（百分比）',
  `position_y` decimal(5, 2) NULL DEFAULT NULL COMMENT 'Y坐标（百分比）',
  `width` decimal(5, 2) NULL DEFAULT NULL COMMENT '宽度（百分比）',
  `height` decimal(5, 2) NULL DEFAULT NULL COMMENT '高度（百分比）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片人物标记表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_person
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_photo_tag
-- ----------------------------
DROP TABLE IF EXISTS `ptm_photo_tag`;
CREATE TABLE `ptm_photo_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标签名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_tag_name`(`tag_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_photo_tag
-- ----------------------------
INSERT INTO `ptm_photo_tag` VALUES (1, 8, 'PhotoTag', '2025-05-15 12:25:55');
INSERT INTO `ptm_photo_tag` VALUES (2, 9, 'PhotoTag照片集', '2025-05-15 16:21:21');
INSERT INTO `ptm_photo_tag` VALUES (3, 10, 'PhotoTag照片集', '2025-05-15 16:21:22');
INSERT INTO `ptm_photo_tag` VALUES (4, 11, 'PhotoTag照片集', '2025-05-15 16:21:22');
INSERT INTO `ptm_photo_tag` VALUES (5, 12, 'PhotoTag照片集', '2025-05-15 16:21:22');
INSERT INTO `ptm_photo_tag` VALUES (6, 13, 'PhotoTag', '2025-05-15 16:23:33');
INSERT INTO `ptm_photo_tag` VALUES (7, 14, 'PhotoTag', '2025-05-15 16:23:33');
INSERT INTO `ptm_photo_tag` VALUES (8, 15, 'PhotoTag', '2025-05-15 16:23:33');
INSERT INTO `ptm_photo_tag` VALUES (9, 16, 'PhotoTag', '2025-05-15 16:23:33');
INSERT INTO `ptm_photo_tag` VALUES (10, 17, 'PhotoTag', '2025-05-15 16:49:17');
INSERT INTO `ptm_photo_tag` VALUES (11, 18, 'PhotoTag', '2025-05-15 16:49:17');
INSERT INTO `ptm_photo_tag` VALUES (12, 19, 'PhotoTag', '2025-05-15 16:49:18');
INSERT INTO `ptm_photo_tag` VALUES (13, 20, 'PhotoTag', '2025-05-15 16:49:18');
INSERT INTO `ptm_photo_tag` VALUES (14, 21, 'PhotoTAG', '2025-05-15 16:51:05');
INSERT INTO `ptm_photo_tag` VALUES (15, 22, 'PhotoTAG', '2025-05-15 16:51:06');
INSERT INTO `ptm_photo_tag` VALUES (16, 23, 'PhotoTAG', '2025-05-15 16:51:06');
INSERT INTO `ptm_photo_tag` VALUES (17, 24, 'PhotoTAG', '2025-05-15 16:51:06');
INSERT INTO `ptm_photo_tag` VALUES (18, 25, 'PhotoTag', '2025-05-15 17:39:23');
INSERT INTO `ptm_photo_tag` VALUES (19, 26, 'PhotoTag', '2025-05-15 17:39:24');
INSERT INTO `ptm_photo_tag` VALUES (20, 27, 'PhotoTag', '2025-05-15 17:39:24');
INSERT INTO `ptm_photo_tag` VALUES (21, 28, 'PhotoTag', '2025-05-15 17:39:24');
INSERT INTO `ptm_photo_tag` VALUES (22, 29, '我的PhotoTag', '2025-05-15 17:47:21');
INSERT INTO `ptm_photo_tag` VALUES (23, 30, '我的PhotoTag', '2025-05-15 17:47:21');
INSERT INTO `ptm_photo_tag` VALUES (24, 31, '我的PhotoTag', '2025-05-15 17:47:22');
INSERT INTO `ptm_photo_tag` VALUES (25, 32, '我的PhotoTag', '2025-05-15 17:47:22');
INSERT INTO `ptm_photo_tag` VALUES (26, 33, 'PhotoTag成都', '2025-05-24 19:00:55');
INSERT INTO `ptm_photo_tag` VALUES (27, 34, 'PhotoTag成都', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_tag` VALUES (28, 35, 'PhotoTag成都', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_tag` VALUES (29, 36, 'PhotoTag成都', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_tag` VALUES (30, 37, 'PhotoTag成都', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_tag` VALUES (31, 38, 'PhotoTag成都', '2025-05-24 19:00:56');
INSERT INTO `ptm_photo_tag` VALUES (32, 39, 'PhotoTag成都', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_tag` VALUES (33, 40, 'PhotoTag成都', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_tag` VALUES (34, 41, 'PhotoTag成都', '2025-05-24 19:00:57');
INSERT INTO `ptm_photo_tag` VALUES (35, 42, 'PhotoTAG成都', '2025-05-27 12:28:56');
INSERT INTO `ptm_photo_tag` VALUES (36, 43, 'PhotoTAG成都', '2025-05-27 12:28:56');
INSERT INTO `ptm_photo_tag` VALUES (37, 44, 'PhotoTAG成都', '2025-05-27 12:28:56');
INSERT INTO `ptm_photo_tag` VALUES (38, 45, 'PhotoTAG成都', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_tag` VALUES (39, 46, 'PhotoTAG成都', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_tag` VALUES (40, 47, 'PhotoTAG成都', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_tag` VALUES (41, 48, 'PhotoTAG成都', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_tag` VALUES (42, 49, 'PhotoTAG成都', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_tag` VALUES (43, 50, 'PhotoTAG成都', '2025-05-27 12:28:57');
INSERT INTO `ptm_photo_tag` VALUES (44, 51, '测试PhotoTAG', '2025-05-27 13:18:58');
INSERT INTO `ptm_photo_tag` VALUES (45, 52, '测试PhotoTAG', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_tag` VALUES (46, 53, '测试PhotoTAG', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_tag` VALUES (47, 54, '测试PhotoTAG', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_tag` VALUES (48, 55, '测试PhotoTAG', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_tag` VALUES (49, 56, '测试PhotoTAG', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_tag` VALUES (50, 57, '测试PhotoTAG', '2025-05-27 13:18:59');
INSERT INTO `ptm_photo_tag` VALUES (51, 58, '测试PhotoTAG', '2025-05-27 13:19:00');
INSERT INTO `ptm_photo_tag` VALUES (52, 59, '测试PhotoTAG', '2025-05-27 13:19:00');

-- ----------------------------
-- Table structure for ptm_recommendation_record
-- ----------------------------
DROP TABLE IF EXISTS `ptm_recommendation_record`;
CREATE TABLE `ptm_recommendation_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `recommendation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '推荐类型（interest, hot, following）',
  `recommendation_time` datetime NOT NULL COMMENT '推荐时间',
  `is_clicked` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被点击',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_photo_time`(`user_id`, `photo_id`, `recommendation_time`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_recommendation_type`(`recommendation_type`) USING BTREE,
  INDEX `idx_recommendation_time`(`recommendation_time`) USING BTREE,
  INDEX `idx_is_clicked`(`is_clicked`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '推荐记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_recommendation_record
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_sensitive_word
-- ----------------------------
DROP TABLE IF EXISTS `ptm_sensitive_word`;
CREATE TABLE `ptm_sensitive_word`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
  `word` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '敏感词',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型（政治、色情、暴力、广告等）',
  `level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '级别（1一般 2中等 3严重）',
  `replace_word` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '替换词',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0否 1是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_word`(`word`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 45226 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '敏感词表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_sensitive_word
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_share
-- ----------------------------
DROP TABLE IF EXISTS `ptm_share`;
CREATE TABLE `ptm_share`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '转发ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '转发内容',
  `share_type` tinyint(1) NULL DEFAULT 1 COMMENT '转发类型：1站内，2微信，3微博',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '转发表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_share
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_system_config
-- ----------------------------
DROP TABLE IF EXISTS `ptm_system_config`;
CREATE TABLE `ptm_system_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `config_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置值',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置名称',
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置类型（string, number, boolean, json）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `is_system` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否系统内置（0否 1是）',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_config_key`(`config_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 149 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_system_config
-- ----------------------------
INSERT INTO `ptm_system_config` VALUES (1, 'system.name', 'PhotoTagMoment', '系统名称', 'string', '系统名称', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (2, 'system.logo', '/logo.png', '系统Logo', 'string', '系统Logo路径', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (3, 'system.description', '照片社交分享平台', '系统描述', 'string', '系统描述', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (4, 'system.version', '1.0.0', '系统版本', 'string', '系统版本号', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (5, 'system.copyright', 'Copyright © 2023 PhotoTagMoment', '版权信息', 'string', '系统版权信息', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (6, 'system.record.number', '', '备案号', 'string', '网站备案号', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (7, 'upload.max.size', '10', '上传文件大小限制', 'number', '上传文件大小限制（MB）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (8, 'upload.allowed.types', 'jpg,jpeg,png,gif,webp', '允许上传的文件类型', 'string', '允许上传的文件类型，逗号分隔', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (9, 'user.default.avatar', '/default-avatar.png', '用户默认头像', 'string', '用户默认头像路径', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (10, 'user.register.verify', 'true', '注册是否需要验证', 'boolean', '注册是否需要验证（短信验证码）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (11, 'user.login.verify', 'true', '登录是否需要验证', 'boolean', '登录是否需要验证（短信验证码）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (12, 'user.identity.verify', 'true', '是否开启实名认证', 'boolean', '是否开启实名认证', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (13, 'content.audit.enabled', 'true', '是否开启内容审核', 'boolean', '是否开启内容审核', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (14, 'content.audit.provider', 'local', '内容审核服务提供商', 'string', '内容审核服务提供商（local, aliyun, tencent, baidu）', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (15, 'sensitive.word.filter', 'true', '是否开启敏感词过滤', 'boolean', '是否开启敏感词过滤', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (16, 'notification.email.enabled', 'false', '是否开启邮件通知', 'boolean', '是否开启邮件通知', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (17, 'notification.sms.enabled', 'true', '是否开启短信通知', 'boolean', '是否开启短信通知', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (18, 'notification.websocket.enabled', 'true', '是否开启WebSocket通知', 'boolean', '是否开启WebSocket通知', 1, NULL, '2025-05-13 20:33:40', '2025-05-13 20:33:40');
INSERT INTO `ptm_system_config` VALUES (19, 'content-moderation.enabled', 'true', '启用内容审核', 'boolean', '是否启用内容审核功能', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (20, 'content-moderation.image.provider', 'baidu', '图像审核服务提供商', 'string', '图像审核服务提供商：local, baidu', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (21, 'content-moderation.text.provider', 'local', '文本审核服务提供商', 'string', '文本审核服务提供商：local', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (22, 'content-moderation.auto-approve', 'false', '自动通过审核', 'boolean', '是否自动通过审核（用于测试）', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (23, 'content-moderation.mode', 'mixed', '审核模式', 'string', '审核模式：auto-自动审核, manual-人工审核, mixed-混合审核', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (24, 'content-moderation.sensitivity', '80', '自动审核敏感度', 'number', '自动审核敏感度：0-100，值越大越严格', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (25, 'content-moderation.contact-info.filter', 'true', '联系方式过滤', 'boolean', '是否过滤联系方式', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (26, 'content-moderation.baidu.app-id', '118929147', '百度AppID', 'string', '百度内容审核AppID', 1, NULL, '2025-05-19 18:24:54', '2025-05-20 20:39:20');
INSERT INTO `ptm_system_config` VALUES (27, 'content-moderation.baidu.api-key', 'xwHcCeFjXNwxk436aNc44bUh', '百度ApiKey', 'string', '百度内容审核ApiKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-20 20:39:24');
INSERT INTO `ptm_system_config` VALUES (28, 'content-moderation.baidu.secret-key', 'G6CJsS8fMoTcEe1aZ0dNGDLZcYudwTlY', '百度SecretKey', 'string', '百度内容审核SecretKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-20 20:39:27');
INSERT INTO `ptm_system_config` VALUES (29, 'storage.type', 'qiniu', '存储类型', 'string', '存储类型：local, qiniu, tencent, aliyun', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (30, 'storage.local.path', '/data/upload', '本地存储路径', 'string', '本地存储路径', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (31, 'storage.local.domain', 'http://localhost:8081/api/file', '本地存储域名', 'string', '本地存储域名', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (32, 'storage.qiniu.access-key', '', '七牛云AccessKey', 'string', '七牛云AccessKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (33, 'storage.qiniu.secret-key', '', '七牛云SecretKey', 'string', '七牛云SecretKey', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (34, 'storage.qiniu.bucket', 'phototagmoment', '七牛云存储空间', 'string', '七牛云存储空间', 1, NULL, '2025-05-19 18:24:54', '2025-05-23 17:22:32');
INSERT INTO `ptm_system_config` VALUES (35, 'storage.qiniu.domain', 'sw5eg63qc.hn-bkt.clouddn.com', '七牛云域名', 'string', '七牛云域名', 1, NULL, '2025-05-19 18:24:54', '2025-05-23 17:22:25');
INSERT INTO `ptm_system_config` VALUES (36, 'storage.qiniu.region', 'huanan', '七牛云区域', 'string', '七牛云区域', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (37, 'storage.qiniu.upload-dir', 'phototagmoment', '七牛云上传目录', 'string', '七牛云上传目录', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (38, 'storage.qiniu.is-private', 'true', '七牛云是否私有空间', 'boolean', '七牛云是否私有空间', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (39, 'storage.qiniu.download-expires', '3600', '七牛云下载凭证有效期', 'number', '七牛云下载凭证有效期（秒）', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (40, 'sensitive.word.replace.char', '*', '敏感词替换字符', 'string', '敏感词替换字符', 1, NULL, '2025-05-19 18:24:54', '2025-05-19 18:24:54');
INSERT INTO `ptm_system_config` VALUES (106, 'auth.wechat.client-secret', '', 'Auth Wechat Client-secret', 'string', '系统配置: auth.wechat.client-secret', 1, '系统配置项: auth.wechat.client-secret', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (107, 'auth.qq.enabled', 'true', 'Auth Qq Enabled', 'boolean', '系统配置: auth.qq.enabled', 1, '系统配置项: auth.qq.enabled', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (108, 'auth.qq.client-secret', 'qMDtQjqjMkuqZ5NR', 'Auth Qq Client-secret', 'string', '系统配置: auth.qq.client-secret', 1, '系统配置项: auth.qq.client-secret', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (109, 'auth.enabled', 'true', 'Auth Enabled', 'boolean', '系统配置: auth.enabled', 1, '系统配置项: auth.enabled', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (110, 'auth.wechat.client-id', '', 'Auth Wechat Client-id', 'string', '系统配置: auth.wechat.client-id', 1, '系统配置项: auth.wechat.client-id', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (111, 'auth.qq.redirect-uri', 'http://phototag.aitrpix.com/qq-callback', 'Auth Qq Redirect-uri', 'string', '系统配置: auth.qq.redirect-uri', 1, '系统配置项: auth.qq.redirect-uri', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (112, 'auth.wechat.enabled', 'true', 'Auth Wechat Enabled', 'boolean', '系统配置: auth.wechat.enabled', 1, '系统配置项: auth.wechat.enabled', '2025-05-21 16:47:14', '2025-05-21 16:47:14');
INSERT INTO `ptm_system_config` VALUES (113, 'auth.qq.client-id', '102792770', 'Auth Qq Client-id', 'number', '系统配置: auth.qq.client-id', 1, '系统配置项: auth.qq.client-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (114, 'auth.wechat.redirect-uri', '', 'Auth Wechat Redirect-uri', 'string', '系统配置: auth.wechat.redirect-uri', 1, '系统配置项: auth.wechat.redirect-uri', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (115, 'identity-verification.alipay.private-key', '', 'Identity-verification Alipay Private-key', 'string', '系统配置: identity-verification.alipay.private-key', 1, '系统配置项: identity-verification.alipay.private-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (116, 'identity-verification.wechat.app-id', '', 'Identity-verification Wechat App-id', 'string', '系统配置: identity-verification.wechat.app-id', 1, '系统配置项: identity-verification.wechat.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (117, 'identity-verification.enabled', 'false', 'Identity-verification Enabled', 'boolean', '系统配置: identity-verification.enabled', 1, '系统配置项: identity-verification.enabled', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (118, 'identity-verification.alipay.app-id', '', 'Identity-verification Alipay App-id', 'string', '系统配置: identity-verification.alipay.app-id', 1, '系统配置项: identity-verification.alipay.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (119, 'identity-verification.provider', 'local', 'Identity-verification Provider', 'string', '系统配置: identity-verification.provider', 1, '系统配置项: identity-verification.provider', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (120, 'identity-verification.wechat.app-secret', '', 'Identity-verification Wechat App-secret', 'string', '系统配置: identity-verification.wechat.app-secret', 1, '系统配置项: identity-verification.wechat.app-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (121, 'identity-verification.alipay.public-key', '', 'Identity-verification Alipay Public-key', 'string', '系统配置: identity-verification.alipay.public-key', 1, '系统配置项: identity-verification.alipay.public-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (122, 'sms.aliyun.access-key-id', '', 'Sms Aliyun Access-key-id', 'string', '系统配置: sms.aliyun.access-key-id', 1, '系统配置项: sms.aliyun.access-key-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (123, 'sms.verification-code.daily-limit', '10', 'Sms Verification-code Daily-limit', 'number', '系统配置: sms.verification-code.daily-limit', 1, '系统配置项: sms.verification-code.daily-limit', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (124, 'sms.aliyun.access-key-secret', '', 'Sms Aliyun Access-key-secret', 'string', '系统配置: sms.aliyun.access-key-secret', 1, '系统配置项: sms.aliyun.access-key-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (125, 'sms.verification-code.expiration', '300', 'Sms Verification-code Expiration', 'number', '系统配置: sms.verification-code.expiration', 1, '系统配置项: sms.verification-code.expiration', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (126, 'sms.aliyun.template-code', 'SMS_123456789', 'Sms Aliyun Template-code', 'string', '系统配置: sms.aliyun.template-code', 1, '系统配置项: sms.aliyun.template-code', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (127, 'sms.enabled', 'false', 'Sms Enabled', 'boolean', '系统配置: sms.enabled', 1, '系统配置项: sms.enabled', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (128, 'sms.aliyun.sign-name', 'PhotoTagMoment', 'Sms Aliyun Sign-name', 'string', '系统配置: sms.aliyun.sign-name', 1, '系统配置项: sms.aliyun.sign-name', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (129, 'sms.verification-code.length', '6', 'Sms Verification-code Length', 'number', '系统配置: sms.verification-code.length', 1, '系统配置项: sms.verification-code.length', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (130, 'sms.provider', 'aliyun', 'Sms Provider', 'string', '系统配置: sms.provider', 1, '系统配置项: sms.provider', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (131, 'wechat.mp.app-id', '', 'Wechat Mp App-id', 'string', '系统配置: wechat.mp.app-id', 1, '系统配置项: wechat.mp.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (132, 'wechat.mp.aes-key', '', 'Wechat Mp Aes-key', 'string', '系统配置: wechat.mp.aes-key', 1, '系统配置项: wechat.mp.aes-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (133, 'wechat.mini-app.app-id', '', 'Wechat Mini-app App-id', 'string', '系统配置: wechat.mini-app.app-id', 1, '系统配置项: wechat.mini-app.app-id', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (134, 'wechat.mini-app.aes-key', '', 'Wechat Mini-app Aes-key', 'string', '系统配置: wechat.mini-app.aes-key', 1, '系统配置项: wechat.mini-app.aes-key', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (135, 'wechat.mp.token', '', 'Wechat Mp Token', 'string', '系统配置: wechat.mp.token', 1, '系统配置项: wechat.mp.token', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (136, 'wechat.mini-app.app-secret', '', 'Wechat Mini-app App-secret', 'string', '系统配置: wechat.mini-app.app-secret', 1, '系统配置项: wechat.mini-app.app-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (137, 'wechat.enabled', 'false', 'Wechat Enabled', 'boolean', '系统配置: wechat.enabled', 1, '系统配置项: wechat.enabled', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (138, 'wechat.mini-app.token', '', 'Wechat Mini-app Token', 'string', '系统配置: wechat.mini-app.token', 1, '系统配置项: wechat.mini-app.token', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (139, 'wechat.mp.app-secret', '', 'Wechat Mp App-secret', 'string', '系统配置: wechat.mp.app-secret', 1, '系统配置项: wechat.mp.app-secret', '2025-05-21 16:47:15', '2025-05-21 16:47:15');
INSERT INTO `ptm_system_config` VALUES (140, 'file.upload.default.config', '1', '默认文件上传配置ID', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (141, 'file.upload.max.concurrent', '10', '最大并发上传数', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (142, 'file.upload.chunk.size', '2097152', '分片上传大小（字节）', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (143, 'file.upload.temp.expire.hours', '24', '临时文件过期时间（小时）', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (144, 'file.upload.auto.cleanup.enabled', 'true', '是否启用自动清理', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (145, 'file.upload.thumbnail.enabled', 'true', '是否启用缩略图生成', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (146, 'file.upload.thumbnail.quality', '0.8', '缩略图质量（0.1-1.0）', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (147, 'file.upload.thumbnail.max.width', '300', '缩略图最大宽度', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');
INSERT INTO `ptm_system_config` VALUES (148, 'file.upload.thumbnail.max.height', '300', '缩略图最大高度', 'SYSTEM', NULL, 1, NULL, '2025-05-23 19:32:51', '2025-05-23 19:32:51');

-- ----------------------------
-- Table structure for ptm_tag_stats
-- ----------------------------
DROP TABLE IF EXISTS `ptm_tag_stats`;
CREATE TABLE `ptm_tag_stats`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标签名称',
  `use_count` int NULL DEFAULT 0 COMMENT '使用次数',
  `note_count` int NULL DEFAULT 0 COMMENT '笔记数量',
  `total_view_count` bigint NULL DEFAULT 0 COMMENT '总浏览量',
  `total_like_count` bigint NULL DEFAULT 0 COMMENT '总点赞数',
  `hot_score` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '热度分数',
  `last_used_at` datetime NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tag_name`(`tag_name`) USING BTREE,
  INDEX `idx_hot_score`(`hot_score`) USING BTREE,
  INDEX `idx_use_count`(`use_count`) USING BTREE,
  INDEX `idx_last_used_at`(`last_used_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '标签统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_tag_stats
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_user
-- ----------------------------
DROP TABLE IF EXISTS `ptm_user`;
CREATE TABLE `ptm_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `gender` tinyint(1) NULL DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `bio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '个人简介',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0禁用，1正常',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否实名认证：0否，1是',
  `is_admin` tinyint(1) NULL DEFAULT 0 COMMENT '是否管理员：0否，1是',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `register_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册IP',
  `register_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '注册来源',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
  `wechat_open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信OpenID',
  `wechat_union_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信UnionID',
  `wechat_nickname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信昵称',
  `real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_username`(`username`) USING BTREE,
  UNIQUE INDEX `idx_email`(`email`) USING BTREE,
  UNIQUE INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_user_wechat_open_id`(`wechat_open_id`) USING BTREE,
  INDEX `idx_user_wechat_union_id`(`wechat_union_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_user
-- ----------------------------
INSERT INTO `ptm_user` VALUES (1, 'test', '$2a$10$n/EUYyPQ2jtf.7/hDRKKOOZ7TZp9LOG0LD90mx21xU6JlI8jbL9SK', '测试', NULL, 'FcMQXoUVvKdRRzxIqLHDLjE13Y1UFk0p7mA2muyCpJXAh+OAtubTe3kZHvc=', '*******0743', 0, NULL, NULL, 1, 0, 0, '2025-05-27 11:26:06', '127.0.0.1', '127.0.0.1', 'web', '2025-05-13 00:28:56', '2025-05-13 00:28:56', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ptm_user` VALUES (2, 'qq_test_ope', '$2a$10$7rVkTRnHGMmZGuX3gS93xuI12LJeSoybA8j24O03OcMqvGOkM4w/S', 'qq_test_ope', NULL, NULL, NULL, 0, NULL, NULL, 1, 0, 0, '2025-05-16 21:55:01', NULL, NULL, NULL, '2025-05-16 21:55:01', '2025-05-16 21:55:01', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ptm_user` VALUES (3, 'admin_test', '\\.zmdr9k7uOCQb376NoUnuTJ8iKyF.bCw6JvYLIqrOimNjCR6RDeO', '测试管理员', NULL, '<EMAIL>', NULL, 0, NULL, NULL, 1, 0, 1, NULL, NULL, NULL, NULL, '2025-05-24 12:04:41', '2025-05-24 12:04:41', 0, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `ptm_user` VALUES (4, 'superadmin', '\\.zmdr9k7uOCQb376NoUnuTJ8iKyF.bCw6JvYLIqrOimNjCR6RDeO', '超级管理员', NULL, '<EMAIL>', NULL, 0, NULL, NULL, 1, 0, 1, NULL, NULL, NULL, NULL, '2025-05-24 12:31:44', '2025-05-24 12:31:44', 0, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for ptm_user_auth
-- ----------------------------
DROP TABLE IF EXISTS `ptm_user_auth`;
CREATE TABLE `ptm_user_auth`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `identity_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '认证类型：wechat微信，alipay支付宝，email邮箱，phone手机',
  `identifier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '认证标识（微信openid、邮箱、手机号）',
  `credential` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '凭证（密码、token）',
  `verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已验证',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_identity`(`user_id`, `identity_type`) USING BTREE,
  UNIQUE INDEX `idx_identity`(`identity_type`, `identifier`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户认证信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_user_auth
-- ----------------------------
INSERT INTO `ptm_user_auth` VALUES (1, 1, 'username', 'test', '$2a$10$txAZKs8lqc2yUVjOgDIR7OCqQIkG5fXw823Ck3Hnj4x.N0V3wdfy2', 1, '2025-05-13 00:28:56', '2025-05-13 00:28:56');
INSERT INTO `ptm_user_auth` VALUES (2, 1, 'email', '<EMAIL>', NULL, 0, '2025-05-13 00:28:56', '2025-05-13 00:28:56');
INSERT INTO `ptm_user_auth` VALUES (3, 1, 'phone', '13668150743', NULL, 0, '2025-05-13 00:28:56', '2025-05-13 00:28:56');
INSERT INTO `ptm_user_auth` VALUES (4, 2, 'qq', 'test_openid', 'test_access_token', 1, '2025-05-16 21:55:01', '2025-05-16 21:55:01');

-- ----------------------------
-- Table structure for ptm_user_behavior
-- ----------------------------
DROP TABLE IF EXISTS `ptm_user_behavior`;
CREATE TABLE `ptm_user_behavior`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `photo_id` bigint NOT NULL COMMENT '照片ID',
  `behavior_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为类型（view, like, comment, collect）',
  `behavior_time` datetime NOT NULL COMMENT '行为时间',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_photo_id`(`photo_id`) USING BTREE,
  INDEX `idx_behavior_type`(`behavior_type`) USING BTREE,
  INDEX `idx_behavior_time`(`behavior_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户行为表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_user_behavior
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_user_follow
-- ----------------------------
DROP TABLE IF EXISTS `ptm_user_follow`;
CREATE TABLE `ptm_user_follow`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `follow_user_id` bigint NOT NULL COMMENT '被关注用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_follow`(`user_id`, `follow_user_id`) USING BTREE,
  INDEX `idx_follow_user_id`(`follow_user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户关注表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_user_follow
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_user_interest
-- ----------------------------
DROP TABLE IF EXISTS `ptm_user_interest`;
CREATE TABLE `ptm_user_interest`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标签名称',
  `interest_score` double NOT NULL DEFAULT 0 COMMENT '兴趣分数',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_tag`(`user_id`, `tag_name`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_tag_name`(`tag_name`) USING BTREE,
  INDEX `idx_interest_score`(`interest_score`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户兴趣标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_user_interest
-- ----------------------------

-- ----------------------------
-- Table structure for ptm_user_verification
-- ----------------------------
DROP TABLE IF EXISTS `ptm_user_verification`;
CREATE TABLE `ptm_user_verification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '真实姓名',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '身份证号',
  `front_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证正面照片',
  `back_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证背面照片',
  `face_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人脸照片',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态：0待审核，1已通过，2已拒绝',
  `reject_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `verified_at` datetime NULL DEFAULT NULL COMMENT '认证通过时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_id`(`user_id`) USING BTREE,
  UNIQUE INDEX `idx_id_card`(`id_card`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户实名认证表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ptm_user_verification
-- ----------------------------

-- ----------------------------
-- View structure for v_file_upload_config_overview
-- ----------------------------
DROP VIEW IF EXISTS `v_file_upload_config_overview`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_file_upload_config_overview` AS select `c`.`id` AS `id`,`c`.`config_name` AS `config_name`,`c`.`storage_type` AS `storage_type`,`c`.`enabled` AS `enabled`,`c`.`is_default` AS `is_default`,`c`.`status` AS `status`,`c`.`description` AS `description`,`c`.`last_test_time` AS `last_test_time`,`c`.`created_at` AS `created_at`,`c`.`updated_at` AS `updated_at`,coalesce(`s`.`upload_count`,0) AS `total_upload_count`,coalesce(`s`.`upload_size`,0) AS `total_upload_size`,coalesce(`s`.`success_count`,0) AS `total_success_count`,coalesce(`s`.`fail_count`,0) AS `total_fail_count`,(case when (coalesce(`s`.`upload_count`,0) = 0) then 0 else round(((coalesce(`s`.`success_count`,0) * 100.0) / coalesce(`s`.`upload_count`,0)),2) end) AS `success_rate` from (`ptm_file_upload_config` `c` left join (select `ptm_file_upload_config_stats`.`config_id` AS `config_id`,sum(`ptm_file_upload_config_stats`.`upload_count`) AS `upload_count`,sum(`ptm_file_upload_config_stats`.`upload_size`) AS `upload_size`,sum(`ptm_file_upload_config_stats`.`success_count`) AS `success_count`,sum(`ptm_file_upload_config_stats`.`fail_count`) AS `fail_count` from `ptm_file_upload_config_stats` group by `ptm_file_upload_config_stats`.`config_id`) `s` on((`c`.`id` = `s`.`config_id`))) where (`c`.`is_deleted` = 0);

-- ----------------------------
-- Procedure structure for sp_cleanup_file_upload_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_cleanup_file_upload_stats`;
delimiter ;;
CREATE PROCEDURE `sp_cleanup_file_upload_stats`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_config_id BIGINT;
    DECLARE cur CURSOR FOR 
        SELECT DISTINCT config_id 
        FROM ptm_file_upload_config_stats 
        WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO v_config_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        
        DELETE FROM ptm_file_upload_config_stats 
        WHERE config_id = v_config_id 
        AND stat_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
        
    END LOOP;
    CLOSE cur;
    
    COMMIT;
END
;;
delimiter ;

-- ----------------------------
-- Event structure for ev_cleanup_file_upload_stats
-- ----------------------------
DROP EVENT IF EXISTS `ev_cleanup_file_upload_stats`;
delimiter ;;
CREATE EVENT `ev_cleanup_file_upload_stats`
ON SCHEDULE
EVERY '1' DAY STARTS '2025-05-23 19:31:30'
DO CALL sp_cleanup_file_upload_stats()
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
