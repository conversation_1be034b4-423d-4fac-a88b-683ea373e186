package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.dto.NotificationDTO;
import com.phototagmoment.entity.Notification;

/**
 * 通知服务接口
 */
public interface NotificationService extends IService<Notification> {

    /**
     * 推送通知
     *
     * @param notification 通知对象
     * @return 是否推送成功
     */
    boolean pushNotification(Notification notification);

    /**
     * 创建关注通知
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean createFollowNotification(Long followerId, Long followingId);

    /**
     * 创建点赞通知
     *
     * @param userId 点赞用户ID
     * @param photoId 照片ID
     * @param photoUserId 照片所有者ID
     * @return 是否成功
     */
    boolean createLikeNotification(Long userId, Long photoId, Long photoUserId);

    /**
     * 创建评论通知
     *
     * @param userId 评论用户ID
     * @param photoId 照片ID
     * @param photoUserId 照片所有者ID
     * @param commentId 评论ID
     * @param content 评论内容
     * @return 是否成功
     */
    boolean createCommentNotification(Long userId, Long photoId, Long photoUserId, Long commentId, String content);

    /**
     * 创建回复通知
     *
     * @param userId 回复用户ID
     * @param commentId 评论ID
     * @param replyToUserId 被回复用户ID
     * @param content 回复内容
     * @return 是否成功
     */
    boolean createReplyNotification(Long userId, Long commentId, Long replyToUserId, String content);

    /**
     * 创建系统通知
     *
     * @param userId 接收用户ID
     * @param content 通知内容
     * @return 是否成功
     */
    boolean createSystemNotification(Long userId, String content);

    /**
     * 发送@用户通知
     *
     * @param mentionedUserId 被@用户ID
     * @param mentionUserId @用户ID
     * @param noteId 照片笔记ID
     * @param noteTitle 照片笔记标题
     * @return 是否成功
     */
    boolean sendMentionNotification(Long mentionedUserId, Long mentionUserId, Long noteId, String noteTitle);

    /**
     * 获取用户通知列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param type 通知类型，为null则查询所有类型
     * @return 通知列表
     */
    IPage<NotificationDTO> getUserNotifications(Long userId, int page, int size, Integer type);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int getUnreadCount(Long userId);

    /**
     * 标记通知为已读
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAsRead(Long notificationId, Long userId);

    /**
     * 标记所有通知为已读
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAllAsRead(Long userId);

    /**
     * 删除通知
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteNotification(Long notificationId, Long userId);
}
