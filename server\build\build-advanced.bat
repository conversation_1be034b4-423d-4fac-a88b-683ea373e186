@echo off
chcp 65001 >nul

REM 解析命令行参数
set PROFILE=%1
set RUN_TESTS=%2
if "%PROFILE%"=="" set PROFILE=dev
if "%RUN_TESTS%"=="" set RUN_TESTS=false

REM 验证环境参数
if not "%PROFILE%"=="dev" if not "%PROFILE%"=="test" if not "%PROFILE%"=="prod" (
    echo 错误: 无效的环境参数 "%PROFILE%"
    echo 支持的环境: dev, test, prod
    echo 用法: %0 [dev^|test^|prod] [true^|false]
    echo 示例: 
    echo   %0 prod false    - 生产环境打包，跳过测试
    echo   %0 test true     - 测试环境打包，运行测试
    echo   %0               - 默认开发环境，跳过测试
    pause
    exit /b 1
)

REM 验证测试参数
if not "%RUN_TESTS%"=="true" if not "%RUN_TESTS%"=="false" (
    echo 错误: 无效的测试参数 "%RUN_TESTS%"
    echo 支持的值: true, false
    pause
    exit /b 1
)

echo ==========================================
echo PhotoTagMoment 高级多环境打包脚本
echo ==========================================
echo 目标环境: %PROFILE%
echo 运行测试: %RUN_TESTS%
echo ==========================================

REM 设置Java 17环境
set JAVA_HOME=C:\Program Files\Java\jdk-17
echo 使用Java版本: %JAVA_HOME%

REM 检查Java环境
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: Java环境未正确配置
    echo 请确保Java 17已安装并配置在PATH中
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if errorlevel 1 (
    echo 错误: Maven环境未正确配置
    echo 请确保Maven已安装并配置在PATH中
    pause
    exit /b 1
)

REM 检查是否在项目根目录
if not exist "..\pom.xml" (
    echo 错误: 请在项目根目录运行此脚本
    echo 当前目录: %CD%
    echo 应该包含: backend\pom.xml
    pause
    exit /b 1
)

REM 检查环境配置文件是否存在
if not exist "..\src\main\resources\application-%PROFILE%.yml" (
    echo 警告: 环境配置文件不存在: application-%PROFILE%.yml
    echo 将使用默认配置文件
)

echo [1/5] 进入后端目录...
cd backend

echo [2/5] 清理之前的构建...
call mvn clean -q
if errorlevel 1 (
    echo 错误: Maven清理失败
    pause
    exit /b 1
)

echo [3/5] 编译项目...
call mvn compile -q
if errorlevel 1 (
    echo 错误: Maven编译失败
    pause
    exit /b 1
)

REM 根据参数决定是否运行测试
if "%RUN_TESTS%"=="true" (
    echo [4/5] 运行单元测试...
    call mvn test -Dspring.profiles.active=%PROFILE%
    if errorlevel 1 (
        echo 警告: 测试失败，但继续打包
        set /p choice="是否继续打包? (y/n): "
        if /i not "!choice!"=="y" (
            echo 构建已取消
            pause
            exit /b 1
        )
    ) else (
        echo 测试通过
    )
) else (
    echo [4/5] 跳过测试...
)

echo [5/5] 开始打包 (环境: %PROFILE%)...
if "%RUN_TESTS%"=="true" (
    call mvn package -Dspring.profiles.active=%PROFILE% -q
) else (
    call mvn package -DskipTests -Dspring.profiles.active=%PROFILE% -q
)
if errorlevel 1 (
    echo 错误: Maven打包失败
    pause
    exit /b 1
)

REM 获取JAR文件信息
set JAR_FILE=%CD%\target\phototagmoment-0.0.1-SNAPSHOT.jar
if not exist "%JAR_FILE%" (
    echo 错误: JAR文件未生成
    pause
    exit /b 1
)

REM 获取文件大小
for %%A in ("%JAR_FILE%") do set JAR_SIZE=%%~zA
set /a JAR_SIZE_MB=%JAR_SIZE%/1024/1024

REM 创建环境特定的JAR文件名
set ENV_JAR_FILE=%CD%\target\phototagmoment-%PROFILE%-0.0.1-SNAPSHOT.jar
copy "%JAR_FILE%" "%ENV_JAR_FILE%" >nul

echo.
echo ==========================================
echo 打包成功！
echo ==========================================
echo 环境配置: %PROFILE%
echo 运行测试: %RUN_TESTS%
echo JAR文件位置: %JAR_FILE%
echo 环境JAR文件: %ENV_JAR_FILE%
echo 文件大小: %JAR_SIZE_MB% MB
echo 生成时间: %date% %time%
echo.
echo 运行命令:
echo   java -jar target\phototagmoment-0.0.1-SNAPSHOT.jar
echo   或者使用环境特定JAR:
echo   java -jar target\phototagmoment-%PROFILE%-0.0.1-SNAPSHOT.jar
echo   或者指定环境:
echo   java -jar -Dspring.profiles.active=%PROFILE% target\phototagmoment-0.0.1-SNAPSHOT.jar
echo.
echo 配置文件: application-%PROFILE%.yml

REM 根据环境显示不同的信息
if "%PROFILE%"=="dev" (
    echo API文档: http://localhost:8081/api/doc.html
    echo 开发环境特性: 详细日志、API文档启用、简单缓存
) else if "%PROFILE%"=="test" (
    echo API文档: http://localhost:8082/api/doc.html
    echo 测试环境特性: 中等日志、API文档需认证、Redis缓存
) else if "%PROFILE%"=="prod" (
    echo API文档: http://localhost:8081/api/doc.html
    echo 生产环境特性: 精简日志、API文档关闭、Redis缓存、安全增强
)

echo ==========================================

pause
