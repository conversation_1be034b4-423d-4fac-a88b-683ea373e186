package com.phototagmoment.security;

import com.phototagmoment.entity.Admin;
import com.phototagmoment.entity.User;
import com.phototagmoment.mapper.AdminMapper;
import com.phototagmoment.service.AuthenticationService;
import com.phototagmoment.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义用户详情服务
 */
@Slf4j
@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    private UserService userService;

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private AuthenticationService authenticationService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.info("加载用户信息: 用户名={}", username);

        // 首先尝试查找管理员
        Admin admin = adminMapper.selectByUsername(username);
        if (admin != null) {
            log.info("找到管理员: id={}, 用户名={}", admin.getId(), admin.getUsername());
            return loadAdminUserDetails(admin);
        }
        log.info("未找到管理员: 用户名={}", username);

        // 如果不是管理员，尝试查找普通用户
        User user = userService.getUserByUsername(username);
        if (user != null) {
            log.info("找到普通用户: id={}, 用户名={}", user.getId(), user.getUsername());
            return loadUserUserDetails(user);
        }
        log.info("未找到普通用户: 用户名={}", username);

        // 如果都找不到，抛出异常
        log.error("用户名不存在: {}", username);
        throw new UsernameNotFoundException("用户名不存在: " + username);
    }

    /**
     * 加载管理员用户详情
     *
     * @param admin 管理员
     * @return 用户详情
     */
    private UserDetails loadAdminUserDetails(Admin admin) {
        log.info("加载管理员用户详情: id={}, 用户名={}, 密码哈希={}", admin.getId(), admin.getUsername(), admin.getPassword());

        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        log.info("管理员权限: {}", authorities);

        UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                admin.getUsername(),
                admin.getPassword(),
                admin.getStatus() == 1, // enabled
                true, // accountNonExpired
                true, // credentialsNonExpired
                true, // accountNonLocked
                authorities
        );
        log.info("管理员用户详情加载完成: 用户名={}, 是否启用={}", userDetails.getUsername(), userDetails.isEnabled());

        return userDetails;
    }

    /**
     * 加载普通用户用户详情
     *
     * @param user 用户
     * @return 用户详情
     */
    private UserDetails loadUserUserDetails(User user) {
        // 使用认证服务创建用户详情
        UserDetails userDetails = authenticationService.createUserDetails(user);

        // 如果用户详情为空，创建一个基本的用户详情
        if (userDetails == null) {
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

            return new org.springframework.security.core.userdetails.User(
                    user.getUsername(),
                    user.getPassword(),
                    user.getStatus() == 1, // enabled
                    true, // accountNonExpired
                    true, // credentialsNonExpired
                    true, // accountNonLocked
                    authorities
            );
        }

        return userDetails;
    }
}
