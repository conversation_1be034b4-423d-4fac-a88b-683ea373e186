<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.FileRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="FileInfoDTOMap" type="com.phototagmoment.dto.FileInfoDTO">
        <id column="id" property="id"/>
        <result column="original_name" property="originalName"/>
        <result column="file_name" property="fileName"/>
        <result column="file_path" property="filePath"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_size" property="fileSize"/>
        <result column="mime_type" property="mimeType"/>
        <result column="extension" property="extension"/>
        <result column="category" property="category"/>
        <result column="uploader_id" property="uploaderId"/>
        <result column="uploader_name" property="uploaderName"/>
        <result column="uploader_type" property="uploaderType"/>
        <result column="storage_type" property="storageType"/>
        <result column="status" property="status"/>
        <result column="tags" property="tags"/>
        <result column="description" property="description"/>
        <result column="access_count" property="accessCount"/>
        <result column="last_access_time" property="lastAccessTime"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
    </resultMap>

    <!-- 分页查询文件列表 -->
    <select id="selectFileList" resultMap="FileInfoDTOMap">
        SELECT
            fr.id,
            fr.original_name,
            fr.file_name,
            fr.file_path,
            fr.file_url,
            fr.file_size,
            fr.mime_type,
            fr.extension,
            fr.category,
            fr.uploader_id,
            CASE
                WHEN fr.uploader_type = 'ADMIN' THEN COALESCE(a.username, '管理员')
                WHEN fr.uploader_type = 'USER' THEN COALESCE(u.nickname, u.username, '用户')
                ELSE '系统'
            END as uploader_name,
            fr.uploader_type,
            fr.storage_type,
            fr.status,
            fr.tags,
            fr.description,
            fr.access_count,
            fr.last_access_time,
            fr.created_at,
            fr.updated_at,
            fr.deleted_at
        FROM ptm_file_record fr
        LEFT JOIN ptm_admin a ON fr.uploader_type = 'ADMIN' AND fr.uploader_id = a.id
        LEFT JOIN ptm_user u ON fr.uploader_type = 'USER' AND fr.uploader_id = u.id
        WHERE fr.is_deleted = 0
        <if test="status != null">
            AND fr.status = #{status}
        </if>
        <if test="fileType != null and fileType != ''">
            AND fr.category = #{fileType}
        </if>
        <if test="uploaderId != null">
            AND fr.uploader_id = #{uploaderId}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (fr.original_name LIKE CONCAT('%', #{keyword}, '%')
                 OR fr.description LIKE CONCAT('%', #{keyword}, '%')
                 OR fr.tags LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(fr.created_at) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(fr.created_at) &lt;= #{endDate}
        </if>
        ORDER BY fr.created_at DESC
    </select>

    <!-- 获取文件详情 -->
    <select id="selectFileDetail" resultMap="FileInfoDTOMap">
        SELECT
            fr.id,
            fr.original_name,
            fr.file_name,
            fr.file_path,
            fr.file_url,
            fr.file_size,
            fr.mime_type,
            fr.extension,
            fr.category,
            fr.uploader_id,
            CASE
                WHEN fr.uploader_type = 'ADMIN' THEN COALESCE(a.username, '管理员')
                WHEN fr.uploader_type = 'USER' THEN COALESCE(u.nickname, u.username, '用户')
                ELSE '系统'
            END as uploader_name,
            fr.uploader_type,
            fr.storage_type,
            fr.status,
            fr.tags,
            fr.description,
            fr.access_count,
            fr.last_access_time,
            fr.created_at,
            fr.updated_at,
            fr.deleted_at
        FROM ptm_file_record fr
        LEFT JOIN ptm_admin a ON fr.uploader_type = 'ADMIN' AND fr.uploader_id = a.id
        LEFT JOIN ptm_user u ON fr.uploader_type = 'USER' AND fr.uploader_id = u.id
        WHERE fr.id = #{fileId} AND fr.is_deleted = 0
    </select>

    <!-- 搜索文件 -->
    <select id="searchFiles" resultMap="FileInfoDTOMap">
        SELECT
            fr.id,
            fr.original_name,
            fr.file_name,
            fr.file_path,
            fr.file_url,
            fr.file_size,
            fr.mime_type,
            fr.extension,
            fr.category,
            fr.uploader_id,
            CASE
                WHEN fr.uploader_type = 'ADMIN' THEN COALESCE(a.username, '管理员')
                WHEN fr.uploader_type = 'USER' THEN COALESCE(u.nickname, u.username, '用户')
                ELSE '系统'
            END as uploader_name,
            fr.uploader_type,
            fr.storage_type,
            fr.status,
            fr.tags,
            fr.description,
            fr.access_count,
            fr.last_access_time,
            fr.created_at,
            fr.updated_at,
            fr.deleted_at
        FROM ptm_file_record fr
        LEFT JOIN ptm_admin a ON fr.uploader_type = 'ADMIN' AND fr.uploader_id = a.id
        LEFT JOIN ptm_user u ON fr.uploader_type = 'USER' AND fr.uploader_id = u.id
        WHERE fr.is_deleted = 0
        <if test="status != null">
            AND fr.status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (fr.original_name LIKE CONCAT('%', #{keyword}, '%')
                 OR fr.description LIKE CONCAT('%', #{keyword}, '%')
                 OR fr.tags LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="fileType != null and fileType != ''">
            AND fr.category = #{fileType}
        </if>
        <if test="minSize != null">
            AND fr.file_size &gt;= #{minSize}
        </if>
        <if test="maxSize != null">
            AND fr.file_size &lt;= #{maxSize}
        </if>
        ORDER BY fr.created_at DESC
    </select>

    <!-- 批量更新文件状态 -->
    <update id="batchUpdateStatus">
        UPDATE ptm_file_record
        SET status = #{status}, updated_at = #{updatedAt}
        WHERE id IN
        <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 批量物理删除文件记录 -->
    <delete id="batchPhysicalDelete">
        DELETE FROM ptm_file_record
        WHERE id IN
        <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>

    <!-- 更新文件访问记录 -->
    <update id="updateFileAccess">
        UPDATE ptm_file_record
        SET access_count = access_count + 1,
            last_access_time = #{accessTime}
        WHERE id = #{fileId} AND is_deleted = 0
    </update>

    <!-- 获取热门文件 -->
    <select id="selectPopularFiles" resultMap="FileInfoDTOMap">
        SELECT
            id, original_name, file_path, file_url, file_size,
            access_count, last_access_time, created_at
        FROM ptm_file_record
        WHERE is_deleted = 0 AND status = 0
        ORDER BY access_count DESC
        LIMIT #{limit}
    </select>

    <!-- 获取最近上传的文件 -->
    <select id="selectRecentFiles" resultMap="FileInfoDTOMap">
        SELECT
            id, original_name, file_path, file_url, file_size,
            uploader_id, uploader_type, created_at
        FROM ptm_file_record
        WHERE is_deleted = 0 AND status = 0
        ORDER BY created_at DESC
        LIMIT #{limit}
    </select>

    <!-- 获取过期的临时文件 -->
    <select id="selectExpiredTempFiles" resultType="com.phototagmoment.entity.FileRecord">
        SELECT id, file_path
        FROM ptm_file_record
        WHERE is_deleted = 0 AND is_temp = 1
        AND temp_expire_time &lt; #{currentTime}
    </select>

    <!-- 根据文件路径查询文件记录 -->
    <select id="selectByFilePath" resultType="com.phototagmoment.entity.FileRecord">
        SELECT * FROM ptm_file_record
        WHERE file_path = #{filePath} AND is_deleted = 0
    </select>

    <!-- 根据MD5查询重复文件 -->
    <select id="selectByMd5Hash" resultType="com.phototagmoment.entity.FileRecord">
        SELECT * FROM ptm_file_record
        WHERE md5_hash = #{md5Hash} AND is_deleted = 0
    </select>

    <!-- 获取用户文件统计 -->
    <select id="selectUserFileStatistics" resultType="java.util.Map">
        SELECT
            uploader_id,
            COUNT(*) as fileCount,
            SUM(file_size) as totalSize
        FROM ptm_file_record
        WHERE is_deleted = 0 AND status = 0 AND uploader_type = #{uploaderType}
        GROUP BY uploader_id
        ORDER BY totalSize DESC
        LIMIT #{limit}
    </select>

    <!-- 清理回收站文件 -->
    <select id="selectTrashFilesBeforeTime" resultType="com.phototagmoment.entity.FileRecord">
        SELECT id, file_path
        FROM ptm_file_record
        WHERE status = 1 AND deleted_at &lt; #{beforeTime}
    </select>

    <!-- 获取存储空间使用情况 -->
    <select id="selectStorageUsage" resultType="java.util.Map">
        SELECT
            storage_type,
            SUM(CASE WHEN status = 0 THEN file_size ELSE 0 END) as usedSize,
            COUNT(CASE WHEN status = 0 THEN 1 END) as fileCount
        FROM ptm_file_record
        WHERE is_deleted = 0
        GROUP BY storage_type
    </select>

</mapper>
