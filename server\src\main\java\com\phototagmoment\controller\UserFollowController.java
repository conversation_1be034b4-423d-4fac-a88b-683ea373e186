package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.UserFollowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 用户关注控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Tag(name = "用户关注接口", description = "用户关注相关接口")
public class UserFollowController {

    @Autowired
    private UserFollowService userFollowService;

    @PostMapping("/follow/{userId}")
    @Operation(summary = "关注用户", description = "关注指定用户")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> followUser(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        boolean result = userFollowService.followUser(currentUserId, userId);
        return ApiResponse.success(result);
    }

    @PostMapping("/unfollow/{userId}")
    @Operation(summary = "取消关注用户", description = "取消关注指定用户")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> unfollowUser(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        boolean result = userFollowService.unfollowUser(currentUserId, userId);
        return ApiResponse.success(result);
    }

    @GetMapping("/following/{userId}")
    @Operation(summary = "检查是否关注用户", description = "检查当前用户是否关注指定用户")
    public ApiResponse<Boolean> checkFollowing(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        boolean result = userFollowService.checkFollowing(currentUserId, userId);
        return ApiResponse.success(result);
    }

    @GetMapping("/following/list/{userId}")
    @Operation(summary = "获取用户关注列表", description = "获取指定用户的关注列表")
    public ApiResponse<IPage<UserDTO>> getFollowingList(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        IPage<UserDTO> followingList = userFollowService.getFollowingList(userId, page, size);
        return ApiResponse.success(followingList);
    }

    @GetMapping("/follower/list/{userId}")
    @Operation(summary = "获取用户粉丝列表", description = "获取指定用户的粉丝列表")
    public ApiResponse<IPage<UserDTO>> getFollowerList(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        IPage<UserDTO> followerList = userFollowService.getFollowerList(userId, page, size);
        return ApiResponse.success(followerList);
    }

    @GetMapping("/following/count/{userId}")
    @Operation(summary = "获取用户关注数量", description = "获取指定用户的关注数量")
    public ApiResponse<Integer> getFollowingCount(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        int count = userFollowService.getFollowingCount(userId);
        return ApiResponse.success(count);
    }

    @GetMapping("/follower/count/{userId}")
    @Operation(summary = "获取用户粉丝数量", description = "获取指定用户的粉丝数量")
    public ApiResponse<Integer> getFollowerCount(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        int count = userFollowService.getFollowerCount(userId);
        return ApiResponse.success(count);
    }
}
