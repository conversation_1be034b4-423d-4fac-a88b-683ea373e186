package com.phototagmoment.controller.admin;

import com.phototagmoment.common.Result;
import com.phototagmoment.config.ConfigInitializer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 配置迁移控制器
 * 用于手动触发配置从YAML文件迁移到数据库
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/config-migration")
@Tag(name = "配置迁移", description = "配置迁移相关接口")
public class AdminConfigMigrationController {

    @Autowired
    private ConfigInitializer configInitializer;

    /**
     * 手动触发配置迁移
     */
    @PostMapping("/trigger")
    @Operation(summary = "手动触发配置迁移", description = "手动触发配置从YAML文件迁移到数据库")
    public Result<Boolean> triggerConfigMigration() {
        log.info("手动触发配置迁移");
        try {
            configInitializer.run();
            return Result.success(true, "配置迁移成功");
        } catch (Exception e) {
            log.error("配置迁移失败: {}", e.getMessage(), e);
            return Result.fail("配置迁移失败: " + e.getMessage());
        }
    }
}
