package com.phototagmoment.config;

import lombok.Data;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wechat")
public class WechatConfig {

    /**
     * 是否启用微信登录
     */
    private boolean enabled = false;

    /**
     * 公众号配置
     */
    private Mp mp = new Mp();

    /**
     * 小程序配置
     */
    private MiniApp miniApp = new MiniApp();

    /**
     * 公众号配置
     */
    @Data
    public static class Mp {
        /**
         * 公众号appId
         */
        private String appId;

        /**
         * 公众号appSecret
         */
        private String appSecret;

        /**
         * 公众号token
         */
        private String token;

        /**
         * 公众号aesKey
         */
        private String aesKey;
    }

    /**
     * 小程序配置
     */
    @Data
    public static class MiniApp {
        /**
         * 小程序appId
         */
        private String appId;

        /**
         * 小程序appSecret
         */
        private String appSecret;

        /**
         * 小程序token
         */
        private String token;

        /**
         * 小程序aesKey
         */
        private String aesKey;
    }

    /**
     * 微信公众号服务
     */
    @Bean
    public WxMpService wxMpService() {
        if (!enabled || mp.getAppId() == null || mp.getAppSecret() == null) {
            return null;
        }

        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(mp.getAppId());
        config.setSecret(mp.getAppSecret());
        config.setToken(mp.getToken());
        config.setAesKey(mp.getAesKey());

        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(config);
        return wxMpService;
    }

    /**
     * 微信小程序服务
     */
    @Bean
    public cn.binarywang.wx.miniapp.api.WxMaService wxMaService() {
        if (!enabled || miniApp.getAppId() == null || miniApp.getAppSecret() == null) {
            return null;
        }

        cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl config = new cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl();
        config.setAppid(miniApp.getAppId());
        config.setSecret(miniApp.getAppSecret());
        config.setToken(miniApp.getToken());
        config.setAesKey(miniApp.getAesKey());

        cn.binarywang.wx.miniapp.api.WxMaService wxMaService = new cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl();
        wxMaService.setWxMaConfig(config);
        return wxMaService;
    }
}
