package com.phototagmoment.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.common.Result;
import com.phototagmoment.config.IdentityVerificationConfig;
import com.phototagmoment.entity.User;
import com.phototagmoment.service.IdentityVerificationService;
import com.phototagmoment.service.UserService;
import com.phototagmoment.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 实名认证拦截器
 * 用于限制未实名认证用户的功能
 */
@Slf4j
@Component
public class IdentityVerificationInterceptor implements HandlerInterceptor {

    @Autowired
    @Qualifier("extendedIdentityVerificationServiceImpl")
    private IdentityVerificationService identityVerificationService;

    @Autowired
    private UserService userService;

    @Autowired
    private IdentityVerificationConfig config;

    @Autowired
    private ObjectMapper objectMapper;

    // 需要实名认证的接口路径前缀
    private static final List<String> VERIFICATION_REQUIRED_PATHS = Arrays.asList(
            "/photo/upload",      // 上传照片
            "/photo/batch-upload", // 批量上传照片
            "/comment/add",       // 发表评论
            "/comment/reply"      // 回复评论
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果未启用实名认证，直接放行
        if (!config.isEnabled()) {
            return true;
        }

        // 如果不是处理器方法，直接放行
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // 检查请求路径是否需要实名认证
        String requestPath = request.getRequestURI();
        boolean requiresVerification = VERIFICATION_REQUIRED_PATHS.stream()
                .anyMatch(requestPath::startsWith);

        // 如果不需要实名认证，直接放行
        if (!requiresVerification) {
            return true;
        }

        // 获取当前用户ID
        Long userId = UserUtil.getCurrentUserId();
        if (userId == null) {
            // 未登录，返回错误信息
            handleUnauthorized(response, "请先登录");
            return false;
        }

        // 检查用户是否已实名认证
        User user = userService.getUserById(userId);
        if (user == null) {
            // 用户不存在，返回错误信息
            handleUnauthorized(response, "用户不存在");
            return false;
        }

        if (user.getIsVerified() != 1) {
            // 未实名认证，返回错误信息
            handleUnverified(response);
            return false;
        }

        return true;
    }

    /**
     * 处理未登录的情况
     *
     * @param response HttpServletResponse
     * @param message  错误信息
     * @throws IOException IO异常
     */
    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(objectMapper.writeValueAsString(Result.fail(401, message)));
    }

    /**
     * 处理未实名认证的情况
     *
     * @param response HttpServletResponse
     * @throws IOException IO异常
     */
    private void handleUnverified(HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.getWriter().write(objectMapper.writeValueAsString(Result.fail(403, "请先完成实名认证")));
    }
}
