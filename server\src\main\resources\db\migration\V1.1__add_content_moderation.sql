-- 修改照片表，添加审核相关字段
-- 首先修改状态字段
ALTER TABLE ptm_photo
    MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除';

-- 检查并添加 reject_reason 列（如果不存在）
SET @exist_reject_reason := (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'ptm_photo'
    AND COLUMN_NAME = 'reject_reason'
);

SET @sql_add_reject_reason := IF(
    @exist_reject_reason = 0,
    'ALTER TABLE ptm_photo ADD COLUMN `reject_reason` varchar(255) DEFAULT NULL COMMENT "拒绝原因" AFTER `status`',
    'SELECT "reject_reason column already exists" AS message'
);

PREPARE stmt FROM @sql_add_reject_reason;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 is_deleted 列（如果不存在）
SET @exist_is_deleted := (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'ptm_photo'
    AND COLUMN_NAME = 'is_deleted'
);

SET @sql_add_is_deleted := IF(
    @exist_is_deleted = 0,
    'ALTER TABLE ptm_photo ADD COLUMN `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT "是否删除: 0-未删除, 1-已删除" AFTER `reject_reason`',
    'SELECT "is_deleted column already exists" AS message'
);

PREPARE stmt FROM @sql_add_is_deleted;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有数据
UPDATE ptm_photo SET status = 1 WHERE status = 0;

-- 创建内容审核记录表
CREATE TABLE IF NOT EXISTS `ptm_content_moderation_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `content_type` varchar(20) NOT NULL COMMENT '内容类型: photo, text, comment',
    `content_id` bigint(20) DEFAULT NULL COMMENT '内容ID',
    `content` text DEFAULT NULL COMMENT '审核内容',
    `result` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核结果: 0-待审核, 1-通过, 2-拒绝',
    `reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
    `provider` varchar(20) DEFAULT NULL COMMENT '审核提供商: local, aliyun, tencent, baidu',
    `admin_id` bigint(20) DEFAULT NULL COMMENT '管理员ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_content_type_id` (`content_type`, `content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容审核记录表';

-- 创建敏感词表
CREATE TABLE IF NOT EXISTS `ptm_sensitive_word` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '敏感词ID',
    `word` varchar(100) NOT NULL COMMENT '敏感词',
    `category` varchar(20) NOT NULL COMMENT '分类: politics, porn, ad, violence, other',
    `level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '级别: 1-低, 2-中, 3-高',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_word` (`word`),
    KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词表';

-- 敏感词数据已移至 V1.8__Create_System_Settings_Tables.sql
