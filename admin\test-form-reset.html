<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传配置表单重置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 200px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: black;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>PhotoTagMoment 文件上传配置表单重置测试</h1>
    
    <div class="test-section">
        <h3>测试说明</h3>
        <p>此测试页面用于验证文件上传配置管理功能的数据残留问题修复效果：</p>
        <ul>
            <li><strong>问题1</strong>：新增配置对话框显示上一次的数据</li>
            <li><strong>问题2</strong>：数据覆盖风险（更新而非创建新记录）</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>模拟配置表单</h3>
        <form id="configForm">
            <div class="form-group">
                <label>配置名称:</label>
                <input type="text" id="configName" placeholder="请输入配置名称">
            </div>
            <div class="form-group">
                <label>存储类型:</label>
                <select id="storageType">
                    <option value="LOCAL">本地存储</option>
                    <option value="QINIU">七牛云</option>
                    <option value="ALIYUN_OSS">阿里云OSS</option>
                    <option value="TENCENT_COS">腾讯云COS</option>
                </select>
            </div>
            <div class="form-group">
                <label>启用状态:</label>
                <input type="checkbox" id="enabled" checked>
            </div>
            <div class="form-group">
                <label>设为默认:</label>
                <input type="checkbox" id="isDefault">
            </div>
            <div class="form-group">
                <label>配置描述:</label>
                <textarea id="description" rows="3" placeholder="请输入配置描述"></textarea>
            </div>
        </form>
        
        <div>
            <button class="btn-primary" onclick="simulateCreate()">新增配置</button>
            <button class="btn-warning" onclick="simulateEdit()">编辑配置</button>
            <button class="btn-secondary" onclick="resetForm()">重置表单</button>
            <button class="btn-success" onclick="runTest()">运行测试</button>
        </div>
    </div>

    <div class="test-section">
        <h3>测试结果</h3>
        <div id="testResults"></div>
    </div>

    <script>
        // 模拟配置数据
        const mockConfig = {
            id: 1,
            configName: '测试配置',
            storageType: 'QINIU',
            enabled: true,
            isDefault: false,
            description: '这是一个测试配置'
        };

        let currentConfig = null;
        let isEdit = false;

        // 模拟新增配置操作
        function simulateCreate() {
            // 修复前的错误行为：不清空currentConfig
            // currentConfig = mockConfig; // 这会导致数据残留
            
            // 修复后的正确行为：清空currentConfig
            currentConfig = null;
            isEdit = false;
            
            updateForm();
            showStatus('模拟新增配置操作 - currentConfig已清空', 'info');
        }

        // 模拟编辑配置操作
        function simulateEdit() {
            currentConfig = { ...mockConfig };
            isEdit = true;
            
            updateForm();
            showStatus('模拟编辑配置操作 - 加载现有配置数据', 'info');
        }

        // 更新表单数据
        function updateForm() {
            if (currentConfig) {
                // 编辑模式：加载现有配置数据
                document.getElementById('configName').value = currentConfig.configName || '';
                document.getElementById('storageType').value = currentConfig.storageType || 'LOCAL';
                document.getElementById('enabled').checked = currentConfig.enabled || false;
                document.getElementById('isDefault').checked = currentConfig.isDefault || false;
                document.getElementById('description').value = currentConfig.description || '';
            } else {
                // 新增模式：重置为默认值
                resetFormData();
            }
        }

        // 重置表单数据为默认值
        function resetFormData() {
            document.getElementById('configName').value = '';
            document.getElementById('storageType').value = 'LOCAL';
            document.getElementById('enabled').checked = true;
            document.getElementById('isDefault').checked = false;
            document.getElementById('description').value = '';
        }

        // 重置表单
        function resetForm() {
            resetFormData();
            showStatus('表单已重置为默认值', 'success');
        }

        // 运行完整测试
        function runTest() {
            const results = [];
            
            // 测试1：新增配置后表单应为空白
            simulateEdit(); // 先编辑一个配置
            const editData = getFormData();
            
            simulateCreate(); // 然后新增配置
            const createData = getFormData();
            
            if (createData.configName === '' && createData.storageType === 'LOCAL' && 
                createData.enabled === true && createData.isDefault === false && 
                createData.description === '') {
                results.push('✅ 测试1通过：新增配置时表单为空白状态');
            } else {
                results.push('❌ 测试1失败：新增配置时表单仍有旧数据');
            }
            
            // 测试2：编辑配置时应加载现有数据
            simulateEdit();
            const editData2 = getFormData();
            
            if (editData2.configName === mockConfig.configName && 
                editData2.storageType === mockConfig.storageType) {
                results.push('✅ 测试2通过：编辑配置时正确加载现有数据');
            } else {
                results.push('❌ 测试2失败：编辑配置时未正确加载数据');
            }
            
            // 测试3：数据隔离测试
            simulateEdit(); // 编辑
            simulateCreate(); // 新增
            const isolationData = getFormData();
            
            if (isolationData.configName === '') {
                results.push('✅ 测试3通过：新增和编辑操作数据隔离正常');
            } else {
                results.push('❌ 测试3失败：新增和编辑操作数据未隔离');
            }
            
            showTestResults(results);
        }

        // 获取表单数据
        function getFormData() {
            return {
                configName: document.getElementById('configName').value,
                storageType: document.getElementById('storageType').value,
                enabled: document.getElementById('enabled').checked,
                isDefault: document.getElementById('isDefault').checked,
                description: document.getElementById('description').value
            };
        }

        // 显示状态信息
        function showStatus(message, type) {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.appendChild(statusDiv);
            
            // 3秒后移除状态信息
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 3000);
        }

        // 显示测试结果
        function showTestResults(results) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h4>测试结果：</h4>';
            
            results.forEach(result => {
                const resultDiv = document.createElement('div');
                resultDiv.className = result.startsWith('✅') ? 'status success' : 'status error';
                resultDiv.textContent = result;
                resultsDiv.appendChild(resultDiv);
            });
        }

        // 页面加载时初始化
        window.onload = function() {
            resetForm();
            showStatus('测试页面已加载，可以开始测试', 'info');
        };
    </script>
</body>
</html>
