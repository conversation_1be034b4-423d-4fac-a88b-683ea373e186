<template>
  <div class="report-management-container">
    <el-card class="page-header">
      <div class="header-content">
        <h2>举报管理</h2>
        <p>处理用户举报的内容和行为，维护社区秩序</p>
      </div>
    </el-card>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="处理状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="待处理" value="pending" />
            <el-option label="已处理" value="processed" />
            <el-option label="已忽略" value="ignored" />
          </el-select>
        </el-form-item>
        <el-form-item label="举报类型">
          <el-select v-model="filterForm.reportType" placeholder="请选择类型" clearable>
            <el-option label="违法违规" value="illegal" />
            <el-option label="色情低俗" value="pornographic" />
            <el-option label="暴力血腥" value="violent" />
            <el-option label="垃圾广告" value="spam" />
            <el-option label="恶意骚扰" value="harassment" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="被举报内容">
          <el-select v-model="filterForm.contentType" placeholder="请选择内容类型" clearable>
            <el-option label="照片笔记" value="photo_note" />
            <el-option label="评论" value="comment" />
            <el-option label="用户" value="user" />
          </el-select>
        </el-form-item>
        <el-form-item label="举报时间">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="16" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.pending }}</div>
            <div class="stat-label">待处理</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.processed }}</div>
            <div class="stat-label">已处理</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.ignored }}</div>
            <div class="stat-label">已忽略</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总计</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 举报列表 -->
    <el-card class="report-list">
      <template #header>
        <div class="card-header">
          <span>举报列表</span>
          <div class="header-actions">
            <el-button size="small" @click="handleBatchProcess" :disabled="!selectedItems.length">
              批量处理
            </el-button>
            <el-button size="small" @click="handleBatchIgnore" :disabled="!selectedItems.length">
              批量忽略
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="reportList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="举报ID" width="80" />
        <el-table-column label="举报内容" width="200">
          <template #default="{ row }">
            <div class="report-content">
              <div class="content-type">{{ getContentTypeText(row.contentType) }}</div>
              <div class="content-preview">{{ row.contentPreview }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reportType" label="举报类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getReportTypeTag(row.reportType)" size="small">
              {{ getReportTypeText(row.reportType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reporter" label="举报人" width="120" />
        <el-table-column prop="reportedUser" label="被举报人" width="120" />
        <el-table-column prop="reportReason" label="举报原因" width="150">
          <template #default="{ row }">
            <el-tooltip :content="row.reportReason" placement="top">
              <div class="reason-text">{{ row.reportReason }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reportTime" label="举报时间" width="160" />
        <el-table-column prop="processTime" label="处理时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">
              <el-icon><view /></el-icon>
              查看
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="success"
              @click="handleProcess(row)"
            >
              处理
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="info"
              @click="handleIgnore(row)"
            >
              忽略
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 举报详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="举报详情"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentReport" class="report-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="举报ID">{{ currentReport.id }}</el-descriptions-item>
          <el-descriptions-item label="举报类型">
            <el-tag :type="getReportTypeTag(currentReport.reportType)" size="small">
              {{ getReportTypeText(currentReport.reportType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="举报人">{{ currentReport.reporter }}</el-descriptions-item>
          <el-descriptions-item label="被举报人">{{ currentReport.reportedUser }}</el-descriptions-item>
          <el-descriptions-item label="举报时间">{{ currentReport.reportTime }}</el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="getStatusTag(currentReport.status)">
              {{ getStatusText(currentReport.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="report-reason">
          <h4>举报原因</h4>
          <p>{{ currentReport.reportReason }}</p>
        </div>

        <div class="reported-content">
          <h4>被举报内容</h4>
          <div class="content-info">
            <p><strong>内容类型：</strong>{{ getContentTypeText(currentReport.contentType) }}</p>
            <p><strong>内容预览：</strong></p>
            <div class="content-preview-detail">
              <div v-if="currentReport.contentImages && currentReport.contentImages.length" class="content-images">
                <img
                  v-for="(image, index) in currentReport.contentImages"
                  :key="index"
                  :src="image"
                  class="content-image"
                />
              </div>
              <div class="content-text">{{ currentReport.contentPreview }}</div>
            </div>
          </div>
        </div>

        <div v-if="currentReport.processResult" class="process-result">
          <h4>处理结果</h4>
          <p><strong>处理时间：</strong>{{ currentReport.processTime }}</p>
          <p><strong>处理结果：</strong>{{ currentReport.processResult }}</p>
          <p><strong>处理说明：</strong>{{ currentReport.processNote || '-' }}</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentReport && currentReport.status === 'pending'"
            type="success"
            @click="handleProcess(currentReport)"
          >
            处理举报
          </el-button>
          <el-button
            v-if="currentReport && currentReport.status === 'pending'"
            type="info"
            @click="handleIgnore(currentReport)"
          >
            忽略举报
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, View } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const selectedItems = ref([])
const currentReport = ref(null)

const filterForm = reactive({
  status: '',
  reportType: '',
  contentType: '',
  dateRange: []
})

const stats = reactive({
  pending: 0,
  processed: 0,
  ignored: 0,
  total: 0
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const reportList = ref([])

// 方法
const loadReportList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    reportList.value = [
      {
        id: 1,
        contentType: 'photo_note',
        contentPreview: '这是一条可能包含不当内容的照片笔记...',
        reportType: 'spam',
        reporter: '用户A',
        reportedUser: '用户B',
        reportReason: '该内容包含大量广告信息，影响用户体验',
        status: 'pending',
        reportTime: '2025-05-23 10:30:00',
        processTime: null,
        contentImages: ['https://via.placeholder.com/300x200'],
        processResult: null,
        processNote: null
      },
      {
        id: 2,
        contentType: 'comment',
        contentPreview: '这是一条不当评论内容...',
        reportType: 'harassment',
        reporter: '用户C',
        reportedUser: '用户D',
        reportReason: '该用户在评论中使用不当言论，涉嫌恶意骚扰',
        status: 'processed',
        reportTime: '2025-05-23 09:15:00',
        processTime: '2025-05-23 11:20:00',
        contentImages: [],
        processResult: '已删除违规内容并警告用户',
        processNote: '确认违规，已处理'
      }
    ]
    
    stats.pending = 15
    stats.processed = 89
    stats.ignored = 23
    stats.total = 127
    pagination.total = 50
    
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadReportList()
}

const handleReset = () => {
  Object.assign(filterForm, {
    status: '',
    reportType: '',
    contentType: '',
    dateRange: []
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

const handleView = (row) => {
  currentReport.value = row
  detailDialogVisible.value = true
}

const handleProcess = async (row) => {
  try {
    const { value: processNote } = await ElMessageBox.prompt('请输入处理说明', '处理举报', {
      confirmButtonText: '确认处理',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入处理说明...'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    row.status = 'processed'
    row.processTime = new Date().toLocaleString()
    row.processResult = '已处理违规内容'
    row.processNote = processNote
    
    ElMessage.success('举报处理完成')
    
    if (detailDialogVisible.value) {
      detailDialogVisible.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleIgnore = async (row) => {
  try {
    await ElMessageBox.confirm('确认忽略此举报？', '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    row.status = 'ignored'
    row.processTime = new Date().toLocaleString()
    row.processResult = '已忽略举报'
    
    ElMessage.success('举报已忽略')
    
    if (detailDialogVisible.value) {
      detailDialogVisible.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleBatchProcess = async () => {
  try {
    const { value: processNote } = await ElMessageBox.prompt(
      `确认批量处理 ${selectedItems.value.length} 条举报？`,
      '批量处理',
      {
        confirmButtonText: '确认处理',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入处理说明...'
      }
    )
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    selectedItems.value.forEach(item => {
      item.status = 'processed'
      item.processTime = new Date().toLocaleString()
      item.processResult = '批量处理'
      item.processNote = processNote
    })
    
    ElMessage.success('批量处理完成')
    selectedItems.value = []
  } catch (error) {
    // 用户取消操作
  }
}

const handleBatchIgnore = async () => {
  try {
    await ElMessageBox.confirm(`确认批量忽略 ${selectedItems.value.length} 条举报？`, '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    selectedItems.value.forEach(item => {
      item.status = 'ignored'
      item.processTime = new Date().toLocaleString()
      item.processResult = '批量忽略'
    })
    
    ElMessage.success('批量忽略完成')
    selectedItems.value = []
  } catch (error) {
    // 用户取消操作
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadReportList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadReportList()
}

const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentReport.value = null
}

// 辅助方法
const getContentTypeText = (type) => {
  const map = {
    photo_note: '照片笔记',
    comment: '评论',
    user: '用户'
  }
  return map[type] || type
}

const getReportTypeText = (type) => {
  const map = {
    illegal: '违法违规',
    pornographic: '色情低俗',
    violent: '暴力血腥',
    spam: '垃圾广告',
    harassment: '恶意骚扰',
    other: '其他'
  }
  return map[type] || type
}

const getReportTypeTag = (type) => {
  const map = {
    illegal: 'danger',
    pornographic: 'danger',
    violent: 'danger',
    spam: 'warning',
    harassment: 'warning',
    other: 'info'
  }
  return map[type] || ''
}

const getStatusText = (status) => {
  const map = {
    pending: '待处理',
    processed: '已处理',
    ignored: '已忽略'
  }
  return map[status] || status
}

const getStatusTag = (status) => {
  const map = {
    pending: 'warning',
    processed: 'success',
    ignored: 'info'
  }
  return map[status] || ''
}

// 生命周期
onMounted(() => {
  loadReportList()
})
</script>

<style scoped>
.report-management-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.report-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.report-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-type {
  font-size: 12px;
  color: #909399;
}

.content-preview {
  font-size: 13px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reason-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.report-detail {
  max-height: 600px;
  overflow-y: auto;
}

.report-reason,
.reported-content,
.process-result {
  margin: 20px 0;
}

.report-reason h4,
.reported-content h4,
.process-result h4 {
  margin-bottom: 12px;
  color: #303133;
}

.content-info p {
  margin: 8px 0;
  color: #606266;
}

.content-preview-detail {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 8px;
}

.content-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.content-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.content-text {
  line-height: 1.6;
  color: #303133;
}

.process-result {
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.process-result p {
  margin: 8px 0;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
