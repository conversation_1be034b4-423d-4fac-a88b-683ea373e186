package com.phototagmoment.config;

import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.DependsOn;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户初始化器
 * 用于在应用启动时创建默认用户账户
 */
@Slf4j
@Component
@DependsOn("flyway")
public class UserInitializer implements CommandLineRunner {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserAuthMapper userAuthMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private Flyway flyway;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(String... args) {
        try {
            // 确保 Flyway 迁移已完成
            log.info("确保 Flyway 迁移已完成");

            // 检查数据库表是否存在
            boolean tableExists = isTableExists("ptm_user");
            if (!tableExists) {
                log.warn("用户表不存在，请先执行数据库迁移脚本");
                return;
            }

            log.info("用户表已存在，继续初始化");

            // 检查是否已存在测试用户
            User existingUser = userMapper.selectByUsername("test");
            if (existingUser != null) {
                log.info("默认测试用户账户已存在，跳过初始化");
                return;
            }

            // 创建默认测试用户账户
            User user = new User();
            user.setUsername("test");
            user.setNickname("测试用户");
            user.setEmail("<EMAIL>");
            user.setPhone("13800138001");
            user.setStatus(1);
            user.setIsVerified(1);
            user.setIsAdmin(0);
            user.setFollowingCount(0);
            user.setFollowerCount(0);
            user.setPhotoCount(0);
            user.setRegisterIp("127.0.0.1");
            user.setRegisterSource("system");
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.insert(user);

            // 创建用户认证信息
            UserAuth userAuth = new UserAuth();
            userAuth.setUserId(user.getId());
            userAuth.setIdentityType("username");
            userAuth.setIdentifier("test");
            userAuth.setCredential(passwordEncoder.encode("123456"));
            userAuth.setVerified(1);
            userAuth.setCreatedAt(LocalDateTime.now());
            userAuth.setUpdatedAt(LocalDateTime.now());
            userAuthMapper.insert(userAuth);

            log.info("创建默认测试用户账户成功，用户名：test，密码：123456");
        } catch (Exception e) {
            log.error("初始化测试用户账户失败", e);
        }
    }

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 是否存在
     */
    private boolean isTableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表是否存在失败", e);
            return false;
        }
    }
}
