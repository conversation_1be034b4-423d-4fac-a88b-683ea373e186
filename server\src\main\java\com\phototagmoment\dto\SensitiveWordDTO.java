package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 敏感词DTO
 */
@Data
@Schema(description = "敏感词信息")
public class SensitiveWordDTO {

    /**
     * 敏感词
     */
    @Schema(description = "敏感词")
    @NotBlank(message = "敏感词不能为空")
    @Size(max = 50, message = "敏感词长度不能超过50个字符")
    private String word;

    /**
     * 类型（政治、色情、暴力、广告等）
     */
    @Schema(description = "类型（政治、色情、暴力、广告等）")
    @NotBlank(message = "类型不能为空")
    @Size(max = 20, message = "类型长度不能超过20个字符")
    private String type;

    /**
     * 级别（1一般 2中等 3严重）
     */
    @Schema(description = "级别（1一般 2中等 3严重）")
    @NotNull(message = "级别不能为空")
    private Integer level;

    /**
     * 替换词
     */
    @Schema(description = "替换词")
    @Size(max = 50, message = "替换词长度不能超过50个字符")
    private String replaceWord;

    /**
     * 状态（0禁用 1启用）
     */
    @Schema(description = "状态（false禁用 true启用）")
    private Boolean status;
}
