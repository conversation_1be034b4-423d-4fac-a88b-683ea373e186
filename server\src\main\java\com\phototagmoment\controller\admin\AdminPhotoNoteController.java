package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.service.PhotoNoteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 管理端照片笔记控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/photo-notes")
@Tag(name = "管理端-照片笔记管理", description = "管理端照片笔记相关接口")
@Validated
public class AdminPhotoNoteController {

    @Autowired
    private PhotoNoteService photoNoteService;

    @GetMapping("/list")
    @Operation(summary = "获取照片笔记列表", description = "管理端分页获取照片笔记列表")
    public ApiResponse<IPage<PhotoNoteDTO>> getPhotoNoteList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size,
            @Parameter(description = "用户ID（可选）") @RequestParam(required = false) Long userId,
            @Parameter(description = "状态筛选") @RequestParam(required = false) Integer status) {

        IPage<PhotoNoteDTO> result = photoNoteService.getAdminPhotoNoteList(page, size, userId, status);
        return ApiResponse.success(result);
    }

    @GetMapping("/{noteId}")
    @Operation(summary = "获取照片笔记详情", description = "管理端获取照片笔记详情")
    public ApiResponse<PhotoNoteDTO> getPhotoNoteDetail(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId) {

        PhotoNoteDTO result = photoNoteService.getPhotoNoteDetail(noteId, null);
        if (result == null) {
            return ApiResponse.failed("照片笔记不存在");
        }

        return ApiResponse.success(result);
    }

    @PostMapping("/{noteId}/audit")
    @Operation(summary = "审核照片笔记", description = "管理员审核照片笔记")
    public ApiResponse<Void> auditPhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId,
            @Parameter(description = "审核状态") @RequestParam @NotNull Integer status,
            @Parameter(description = "拒绝原因") @RequestParam(required = false) String rejectReason) {

        if (status == 2 && (rejectReason == null || rejectReason.trim().isEmpty())) {
            return ApiResponse.failed("拒绝审核时必须提供拒绝原因");
        }

        boolean success = photoNoteService.auditPhotoNote(noteId, status, rejectReason);

        if (success) {
            return ApiResponse.success(null, "审核成功");
        } else {
            return ApiResponse.failed("审核失败，照片笔记不存在");
        }
    }

    @DeleteMapping("/{noteId}")
    @Operation(summary = "删除照片笔记", description = "管理员删除照片笔记")
    public ApiResponse<Void> deletePhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable @NotNull Long noteId) {

        // 管理员删除，直接设置为已删除状态
        boolean success = photoNoteService.auditPhotoNote(noteId, 3, "管理员删除");

        if (success) {
            return ApiResponse.success(null, "删除成功");
        } else {
            return ApiResponse.failed("删除失败，照片笔记不存在");
        }
    }

    @GetMapping("/pending")
    @Operation(summary = "获取待审核照片笔记", description = "获取待审核的照片笔记列表")
    public ApiResponse<IPage<PhotoNoteDTO>> getPendingPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size) {

        IPage<PhotoNoteDTO> result = photoNoteService.getAdminPhotoNoteList(page, size, null, 0);
        return ApiResponse.success(result);
    }

    @GetMapping("/rejected")
    @Operation(summary = "获取审核拒绝的照片笔记", description = "获取审核拒绝的照片笔记列表")
    public ApiResponse<IPage<PhotoNoteDTO>> getRejectedPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer size) {

        IPage<PhotoNoteDTO> result = photoNoteService.getAdminPhotoNoteList(page, size, null, 2);
        return ApiResponse.success(result);
    }

    @GetMapping("/stats")
    @Operation(summary = "获取照片笔记统计", description = "获取照片笔记的统计信息")
    public ApiResponse<PhotoNoteStatsDTO> getPhotoNoteStats() {
        PhotoNoteStatsDTO stats = photoNoteService.getPhotoNoteStats();
        return ApiResponse.success(stats);
    }

    /**
     * 照片笔记统计DTO
     */
    public static class PhotoNoteStatsDTO {
        private Long totalCount;        // 总数
        private Long pendingCount;      // 待审核数
        private Long approvedCount;     // 已通过数
        private Long rejectedCount;     // 已拒绝数
        private Long deletedCount;      // 已删除数
        private Long todayCount;        // 今日新增数
        private Long weekCount;         // 本周新增数
        private Long monthCount;        // 本月新增数

        // getter和setter方法
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }

        public Long getPendingCount() { return pendingCount; }
        public void setPendingCount(Long pendingCount) { this.pendingCount = pendingCount; }

        public Long getApprovedCount() { return approvedCount; }
        public void setApprovedCount(Long approvedCount) { this.approvedCount = approvedCount; }

        public Long getRejectedCount() { return rejectedCount; }
        public void setRejectedCount(Long rejectedCount) { this.rejectedCount = rejectedCount; }

        public Long getDeletedCount() { return deletedCount; }
        public void setDeletedCount(Long deletedCount) { this.deletedCount = deletedCount; }

        public Long getTodayCount() { return todayCount; }
        public void setTodayCount(Long todayCount) { this.todayCount = todayCount; }

        public Long getWeekCount() { return weekCount; }
        public void setWeekCount(Long weekCount) { this.weekCount = weekCount; }

        public Long getMonthCount() { return monthCount; }
        public void setMonthCount(Long monthCount) { this.monthCount = monthCount; }
    }
}
