<template>
  <div class="home-data-test">
    <van-nav-bar title="首页数据测试" left-arrow @click-left="$router.back()" />
    
    <div class="test-container">
      <van-cell-group>
        <van-cell title="后端状态" :value="backendStatusText" :label="currentTime" />
        <van-cell title="数据加载状态" :value="loading ? '加载中' : '已完成'" />
        <van-cell title="照片数量" :value="photoList.length.toString()" />
      </van-cell-group>

      <div class="test-section">
        <h3>原始API响应</h3>
        <van-button type="primary" block @click="loadApiData" :loading="apiLoading">
          重新加载API数据
        </van-button>
        
        <div v-if="apiResponse" class="api-response">
          <h4>API响应结构</h4>
          <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>处理后的照片数据</h3>
        <div v-if="photoList.length > 0" class="photo-list">
          <div v-for="(photo, index) in photoList.slice(0, 3)" :key="photo.id" class="photo-item">
            <div class="photo-info">
              <div><strong>ID:</strong> {{ photo.id }}</div>
              <div><strong>标题:</strong> {{ photo.title }}</div>
              <div><strong>图片URL:</strong> {{ photo.url ? '✅ 有' : '❌ 无' }}</div>
              <div><strong>缩略图URL:</strong> {{ photo.thumbnailUrl ? '✅ 有' : '❌ 无' }}</div>
              <div><strong>用户:</strong> {{ photo.userName }}</div>
              <div><strong>点赞数:</strong> {{ photo.likeCount }}</div>
              <div><strong>原始图片数组:</strong> {{ photo.images?.length || 0 }} 张</div>
            </div>
            
            <div v-if="photo.url" class="photo-preview">
              <img :src="photo.url" :alt="photo.title" style="max-width: 200px; max-height: 200px;" />
            </div>
            
            <van-button 
              type="success" 
              size="small" 
              @click="testPhotoClick(photo)"
              style="margin-top: 10px;"
            >
              测试点击跳转
            </van-button>
          </div>
        </div>
        <van-empty v-else description="暂无照片数据" />
      </div>

      <div class="test-section">
        <h3>图片URL测试</h3>
        <div v-if="photoList.length > 0">
          <div v-for="(photo, index) in photoList.slice(0, 2)" :key="`url-${photo.id}`" class="url-test">
            <h4>照片 {{ photo.id }}</h4>
            <div><strong>处理后URL:</strong> {{ photo.url }}</div>
            <div><strong>原始images数组:</strong></div>
            <pre v-if="photo.images">{{ JSON.stringify(photo.images, null, 2) }}</pre>
            <div v-else>无images数据</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getHomeRecommendations } from '@/api/recommendation'

const router = useRouter()

// 响应式数据
const backendStatusText = ref('检测中...')
const currentTime = ref('')
const loading = ref(false)
const apiLoading = ref(false)
const photoList = ref([])
const apiResponse = ref(null)

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 检查后端状态
const checkBackendStatus = async () => {
  try {
    const response = await fetch('http://localhost:8081/api/recommendation/home?page=1&size=1', { 
      method: 'GET',
      mode: 'cors'
    })
    if (response.ok) {
      backendStatusText.value = '在线'
    } else {
      backendStatusText.value = `错误 ${response.status}`
    }
  } catch (error) {
    backendStatusText.value = '离线'
  }
}

// 加载API数据
const loadApiData = async () => {
  try {
    apiLoading.value = true
    loading.value = true
    
    const params = {
      page: 1,
      size: 5
    }

    const res = await getHomeRecommendations(params)
    apiResponse.value = res
    
    if (res.code === 200) {
      // 适配数据结构 - 与首页相同的逻辑
      const photos = res.data.records.map((item) => {
        // 创建用户对象
        const user = {
          id: item.userId || 0,
          username: item.nickname || item.userName || 'Unknown',
          nickname: item.nickname || item.userName || 'Unknown',
          avatar: item.avatar || item.userAvatar || 'https://randomuser.me/api/portraits/men/1.jpg'
        };

        // 获取第一张图片作为封面图
        const firstImage = item.images && item.images.length > 0 ? item.images[0] : null;
        const imageUrl = firstImage ? (firstImage.thumbnailUrl || firstImage.url) : '';

        return {
          id: item.id,
          title: item.title,
          url: imageUrl,
          thumbnailUrl: imageUrl,
          userName: item.nickname || item.userName || 'Unknown',
          userNickname: item.nickname || '',
          userAvatar: item.avatar || item.userAvatar || 'https://randomuser.me/api/portraits/men/1.jpg',
          userId: item.userId || 0,
          viewCount: item.viewCount || item.stats?.viewCount || 0,
          likeCount: item.likeCount || item.stats?.likeCount || 0,
          commentCount: item.commentCount || item.stats?.commentCount || 0,
          uploadTime: item.uploadTime || item.createdAt || new Date().toISOString(),
          tags: item.tags || [],
          groupId: item.groupId || null,
          isGrouped: !!item.groupId,
          user: user,
          images: item.images || []
        };
      });

      photoList.value = photos
    }
  } catch (error) {
    console.error('加载API数据失败:', error)
  } finally {
    apiLoading.value = false
    loading.value = false
  }
}

// 测试照片点击
const testPhotoClick = (photo) => {
  console.log('测试照片点击:', photo)
  router.push(`/photo-note/${photo.id}`)
}

// 生命周期
onMounted(() => {
  updateTime()
  checkBackendStatus()
  loadApiData()
  
  // 每秒更新时间
  setInterval(() => {
    updateTime()
  }, 1000)
})
</script>

<style scoped>
.home-data-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-container {
  padding: 16px;
}

.test-section {
  margin: 20px 0;
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.test-section h3 {
  margin-bottom: 16px;
  color: #323233;
  font-size: 16px;
  font-weight: 600;
}

.test-section h4 {
  margin: 12px 0 8px 0;
  color: #646566;
  font-size: 14px;
  font-weight: 500;
}

.api-response {
  margin-top: 16px;
}

.api-response pre {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  color: #323233;
  max-height: 300px;
  overflow-y: auto;
}

.photo-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.photo-item {
  border: 1px solid #ebedf0;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
}

.photo-info {
  margin-bottom: 12px;
}

.photo-info div {
  margin-bottom: 4px;
  font-size: 14px;
}

.photo-preview {
  margin: 12px 0;
  text-align: center;
}

.photo-preview img {
  border-radius: 4px;
  border: 1px solid #ebedf0;
}

.url-test {
  margin-bottom: 20px;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 4px;
}

.url-test pre {
  background: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
