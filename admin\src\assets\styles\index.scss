/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
}

a {
  text-decoration: none;
  color: #409EFF;
}

/* Element Plus 样式覆盖 */
.el-menu {
  border-right: none !important;
}

.el-menu--collapse {
  width: 64px;
}

.el-menu-item.is-active {
  background-color: #ecf5ff;
}

.el-card {
  margin-bottom: 20px;
}

/* 通用样式类 */
.app-container {
  padding: 0px;
}

.search-container {
  margin-bottom: 20px;
  padding: 18px 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.table-container {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.pagination-container {
  padding: 15px;
  text-align: right;
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .app-container {
    padding: 0px;
  }
}

@media (max-width: 992px) {
  .search-container {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 0px;
  }
  
  .search-container {
    padding: 10px;
  }
}
