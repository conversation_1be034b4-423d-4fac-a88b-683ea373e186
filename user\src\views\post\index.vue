<template>
  <div class="post-list">
    <div class="filter-bar">
      <div class="filter-tabs">
        <div
          v-for="tab in filterTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: currentFilter === tab.value }"
          @click="setFilter(tab.value)"
        >
          {{ tab.label }}
        </div>
      </div>
      <div class="sort-options">
        <van-dropdown-menu>
          <van-dropdown-item v-model="sortBy" :options="sortOptions" />
        </van-dropdown-menu>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#3498db" />
      <p>加载中...</p>
    </div>

    <div v-else-if="posts.length === 0" class="empty-container">
      <van-empty description="暂无笔记" />
      <van-button v-if="isLoggedIn" type="primary" to="/upload">立即发布</van-button>
    </div>

    <div v-else class="posts-grid">
      <div v-for="post in posts" :key="post.id" class="post-card" @click="goToPostDetail(post.id)">
        <div class="post-image">
          <img
            v-if="post.photos && post.photos.length > 0"
            :src="post.photos[0].thumbnailUrl"
            :alt="post.title"
          />
          <div v-else class="no-image">
            <van-icon name="photo-o" />
          </div>
          <div class="photo-count" v-if="post.photos && post.photos.length > 1">
            <van-icon name="photo-o" />
            <span>{{ post.photos.length }}</span>
          </div>
        </div>
        <div class="post-info">
          <h3 class="post-title">{{ post.title }}</h3>
          <div class="post-meta">
            <div class="user-info">
              <img :src="post.userAvatar" :alt="post.userName" class="user-avatar" />
              <span class="user-name">{{ post.userName }}</span>
            </div>
            <div class="post-stats">
              <span class="stat-item">
                <van-icon name="like-o" />
                {{ post.likeCount || 0 }}
              </span>
              <span class="stat-item">
                <van-icon name="comment-o" />
                {{ post.commentCount || 0 }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="pagination-container" v-if="posts.length > 0">
      <van-pagination
        v-model="currentPage"
        :total-items="totalItems"
        :items-per-page="pageSize"
        :show-page-size="3"
        force-ellipses
        @change="loadPosts"
      />
    </div>

    <van-back-top />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import { getPostList } from '@/api/post'
import { useUserStore } from '@/stores/user'

// 路由和用户信息
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 用户状态
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 笔记列表数据
const posts = ref([] as any[])
const loading = ref(true)
const totalItems = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 筛选和排序
const filterTabs = [
  { label: '推荐', value: 'recommended' },
  { label: '最新', value: 'latest' },
  { label: '热门', value: 'popular' },
  { label: '关注', value: 'following' }
]
const currentFilter = ref('recommended')
const sortOptions = [
  { text: '最新发布', value: 'newest' },
  { text: '最多点赞', value: 'most_liked' },
  { text: '最多评论', value: 'most_commented' }
]
const sortBy = ref('newest')

// 设置筛选条件
const setFilter = (filter: string) => {
  currentFilter.value = filter
  currentPage.value = 1
  loadPosts()
}

// 加载笔记列表
const loadPosts = async () => {
  loading.value = true

  try {
    // 获取URL参数
    const userId = route.query.userId ? Number(route.query.userId) : undefined
    const tag = route.query.tag as string | undefined

    // 构建查询参数
    const params: any = {
      page: currentPage.value,
      size: pageSize.value
    }

    // 添加用户ID筛选
    if (userId) {
      params.userId = userId
    }

    // 添加标签筛选
    if (tag) {
      params.tag = tag
    }

    // 添加排序和筛选
    if (currentFilter.value === 'latest') {
      params.sort = 'newest'
    } else if (currentFilter.value === 'popular') {
      params.sort = 'most_liked'
    } else if (currentFilter.value === 'following') {
      params.following = true
    }

    // 覆盖排序（如果用户手动选择了排序方式）
    if (sortBy.value) {
      params.sort = sortBy.value
    }

    // 发送请求
    const res = await getPostList(params)

    if (res.code === 200) {
      posts.value = res.data.records || []
      totalItems.value = res.data.total || 0
    } else {
      posts.value = []
      totalItems.value = 0
    }
  } catch (error) {
    console.error('加载笔记列表失败', error)
    posts.value = []
    totalItems.value = 0
  } finally {
    loading.value = false
  }
}

// 跳转到笔记详情页
const goToPostDetail = (id: number) => {
  router.push(`/post/${id}`)
}

// 页面加载时获取笔记列表
onMounted(() => {
  // 从URL参数中获取筛选条件
  if (route.query.filter && typeof route.query.filter === 'string') {
    const filter = route.query.filter
    if (filterTabs.some(tab => tab.value === filter)) {
      currentFilter.value = filter
    }
  }

  // 从URL参数中获取排序方式
  if (route.query.sort && typeof route.query.sort === 'string') {
    const sort = route.query.sort
    if (sortOptions.some(option => option.value === sort)) {
      sortBy.value = sort
    }
  }

  // 从URL参数中获取页码
  if (route.query.page && typeof route.query.page === 'string') {
    const page = parseInt(route.query.page)
    if (!isNaN(page) && page > 0) {
      currentPage.value = page
    }
  }

  // 加载笔记列表
  loadPosts()
})
</script>

<style lang="scss" scoped>
.post-list {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;

  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .filter-tabs {
      display: flex;
      gap: 16px;

      .filter-tab {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #f0f7fc;
        }

        &.active {
          background-color: #3498db;
          color: white;
        }
      }
    }
  }

  .loading-container, .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;

    p {
      margin-top: 16px;
      color: #666;
    }

    .van-button {
      margin-top: 16px;
    }
  }

  .posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;

    .post-card {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: transform 0.3s, box-shadow 0.3s;
      cursor: pointer;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
      }

      .post-image {
        position: relative;
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s;
        }

        .no-image {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f5f5;

          .van-icon {
            font-size: 48px;
            color: #ccc;
          }
        }

        .photo-count {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          display: flex;
          align-items: center;

          .van-icon {
            margin-right: 4px;
          }
        }
      }

      .post-info {
        padding: 12px;
        background-color: white;

        .post-title {
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 10px;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .post-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .user-info {
            display: flex;
            align-items: center;

            .user-avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              object-fit: cover;
              margin-right: 8px;
            }

            .user-name {
              font-size: 13px;
              color: #666;
            }
          }

          .post-stats {
            display: flex;
            gap: 10px;

            .stat-item {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #999;

              .van-icon {
                margin-right: 4px;
              }
            }
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin: 30px 0;
  }
}
</style>
