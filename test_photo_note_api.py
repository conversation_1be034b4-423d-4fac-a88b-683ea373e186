#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PhotoTagMoment 照片笔记API测试脚本
用于测试修复后的照片笔记详情接口
"""

import requests
import json

def test_photo_note_detail():
    """测试照片笔记详情接口"""
    
    # 测试URL
    base_url = "http://127.0.0.1:8081/api"
    note_id = 31
    url = f"{base_url}/photo-notes/{note_id}"
    
    # 请求头（模拟前端请求）
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VySWQiOjEsInN1YiI6InRlc3QiLCJpYXQiOjE3NDc5MTgwNjUsImV4cCI6MTc0ODAwNDQ2NX0.1fPxVS3_Xrtti7Ky00hS28eCWP32g-emNUZp5_wdUcizmlczbtzfzcJ62eoQGZybFwskH6ceuW12QUrXZfctgg'
    }
    
    try:
        print(f"正在测试接口: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 接口请求成功!")
            try:
                data = response.json()
                print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError:
                print(f"响应内容: {response.text}")
        else:
            print(f"❌ 接口请求失败!")
            print(f"错误响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 服务器未启动或端口不可达")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    print("PhotoTagMoment 照片笔记API测试")
    print("=" * 50)
    test_photo_note_detail()
