package com.phototagmoment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 内容审核配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "content-moderation")
public class ContentModerationConfig {

    /**
     * 是否启用内容审核
     */
    private boolean enabled = false;

    /**
     * 内容审核服务提供商
     * 可选值：local, aliyun, tencent, baidu
     */
    private String provider = "local";

    /**
     * 阿里云内容安全配置
     */
    private AliyunConfig aliyun = new AliyunConfig();

    /**
     * 腾讯云内容安全配置
     */
    private TencentConfig tencent = new TencentConfig();

    /**
     * 百度云内容安全配置
     */
    private BaiduConfig baidu = new BaiduConfig();

    /**
     * 阿里云内容安全配置
     */
    @Data
    public static class AliyunConfig {
        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥密码
         */
        private String accessKeySecret;

        /**
         * 区域ID
         */
        private String regionId = "cn-shanghai";

        /**
         * 场景列表，多个场景用逗号分隔
         * porn: 鉴黄
         * terrorism: 暴恐
         * ad: 广告
         * live: 不良场景
         * qrcode: 二维码
         */
        private String scenes = "porn,terrorism,ad";
    }

    /**
     * 腾讯云内容安全配置
     */
    @Data
    public static class TencentConfig {
        /**
         * 访问密钥ID
         */
        private String secretId;

        /**
         * 访问密钥密码
         */
        private String secretKey;

        /**
         * 区域ID
         */
        private String region = "ap-guangzhou";
    }

    /**
     * 百度云内容安全配置
     */
    @Data
    public static class BaiduConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String apiKey;

        /**
         * 密钥
         */
        private String secretKey;
    }
}
