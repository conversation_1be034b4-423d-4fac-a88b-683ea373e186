package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.entity.SystemConfig;

import java.util.List;
import java.util.Map;

/**
 * 系统参数配置Service接口
 */
public interface SystemConfigService {

    /**
     * 获取所有系统配置
     *
     * @return 系统配置列表
     */
    List<SystemConfig> listAllConfigs();

    /**
     * 分页获取系统配置
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @param keyword  关键字
     * @return 分页结果
     */
    IPage<SystemConfig> pageConfigs(int page, int pageSize, String keyword);

    /**
     * 根据ID获取系统配置
     *
     * @param id 配置ID
     * @return 系统配置
     */
    SystemConfig getConfigById(Long id);

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     *
     * @param configKey   配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 根据配置键获取布尔类型配置值
     *
     * @param configKey 配置键
     * @return 布尔类型配置值
     */
    boolean getBooleanValue(String configKey);

    /**
     * 根据配置键获取布尔类型配置值，如果不存在则返回默认值
     *
     * @param configKey   配置键
     * @param defaultValue 默认值
     * @return 布尔类型配置值
     */
    boolean getBooleanValue(String configKey, boolean defaultValue);

    /**
     * 根据配置键获取整数类型配置值
     *
     * @param configKey 配置键
     * @return 整数类型配置值
     */
    int getIntValue(String configKey);

    /**
     * 根据配置键获取整数类型配置值，如果不存在则返回默认值
     *
     * @param configKey   配置键
     * @param defaultValue 默认值
     * @return 整数类型配置值
     */
    int getIntValue(String configKey, int defaultValue);

    /**
     * 根据配置键获取长整数类型配置值
     *
     * @param configKey 配置键
     * @return 长整数类型配置值
     */
    long getLongValue(String configKey);

    /**
     * 根据配置键获取长整数类型配置值，如果不存在则返回默认值
     *
     * @param configKey   配置键
     * @param defaultValue 默认值
     * @return 长整数类型配置值
     */
    long getLongValue(String configKey, long defaultValue);

    /**
     * 根据配置键获取浮点数类型配置值
     *
     * @param configKey 配置键
     * @return 浮点数类型配置值
     */
    double getDoubleValue(String configKey);

    /**
     * 根据配置键获取浮点数类型配置值，如果不存在则返回默认值
     *
     * @param configKey   配置键
     * @param defaultValue 默认值
     * @return 浮点数类型配置值
     */
    double getDoubleValue(String configKey, double defaultValue);

    /**
     * 根据配置键获取JSON类型配置值
     *
     * @param configKey 配置键
     * @param clazz     目标类型
     * @param <T>       目标类型
     * @return JSON类型配置值
     */
    <T> T getJsonValue(String configKey, Class<T> clazz);

    /**
     * 保存系统配置
     *
     * @param config 系统配置
     * @return 是否成功
     */
    boolean saveConfig(SystemConfig config);

    /**
     * 批量保存系统配置
     *
     * @param configs 系统配置列表
     * @return 是否成功
     */
    boolean batchSaveConfig(List<SystemConfig> configs);

    /**
     * 批量保存系统配置
     *
     * @param configMap 配置键值对
     * @return 是否成功
     */
    boolean batchSaveConfig(Map<String, String> configMap);

    /**
     * 更新系统配置
     *
     * @param config 系统配置
     * @return 是否成功
     */
    boolean updateConfig(SystemConfig config);

    /**
     * 根据配置键更新配置值
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @return 是否成功
     */
    boolean updateConfigValue(String configKey, String configValue);

    /**
     * 删除系统配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    boolean deleteConfig(Long id);

    /**
     * 刷新配置缓存
     */
    void refreshCache();

    /**
     * 批量获取系统配置值
     *
     * @param keys 配置键列表
     * @return 配置键值对
     */
    Map<String, String> batchGetConfigValues(List<String> keys);
}
