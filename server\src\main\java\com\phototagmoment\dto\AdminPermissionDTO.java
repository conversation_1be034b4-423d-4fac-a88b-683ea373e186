package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 管理员权限DTO
 */
@Data
@Schema(description = "管理员权限信息")
public class AdminPermissionDTO {

    @Schema(description = "权限名称")
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 50, message = "权限名称长度不能超过50个字符")
    private String name;

    @Schema(description = "权限编码")
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 50, message = "权限编码长度不能超过50个字符")
    private String code;

    @Schema(description = "权限描述")
    @Size(max = 200, message = "权限描述长度不能超过200个字符")
    private String description;

    @Schema(description = "权限类型：1-菜单，2-按钮，3-接口")
    @NotNull(message = "权限类型不能为空")
    private Integer type;

    @Schema(description = "父权限ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "路由路径")
    private String path;

    @Schema(description = "组件路径")
    private String component;

    @Schema(description = "状态：0-禁用，1-正常")
    private Integer status;
}
