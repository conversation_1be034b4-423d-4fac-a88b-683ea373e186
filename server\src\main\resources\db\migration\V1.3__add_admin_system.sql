-- 创建管理员表
CREATE TABLE IF NOT EXISTS `ptm_admin` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `name` varchar(50) NOT NULL COMMENT '姓名',
    `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 创建管理员角色表
CREATE TABLE IF NOT EXISTS `ptm_admin_role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name` varchar(50) NOT NULL COMMENT '角色名称',
    `code` varchar(50) NOT NULL COMMENT '角色编码',
    `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统内置：0-否，1-是',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色表';

-- 创建管理员权限表
CREATE TABLE IF NOT EXISTS `ptm_admin_permission` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `name` varchar(50) NOT NULL COMMENT '权限名称',
    `code` varchar(50) NOT NULL COMMENT '权限编码',
    `description` varchar(200) DEFAULT NULL COMMENT '权限描述',
    `type` tinyint(1) NOT NULL COMMENT '权限类型：1-菜单，2-按钮，3-接口',
    `parent_id` bigint(20) DEFAULT NULL COMMENT '父权限ID',
    `sort` int(11) DEFAULT 0 COMMENT '排序',
    `icon` varchar(50) DEFAULT NULL COMMENT '图标',
    `path` varchar(100) DEFAULT NULL COMMENT '路由路径',
    `component` varchar(100) DEFAULT NULL COMMENT '组件路径',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否系统内置：0-否，1-是',
    `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员权限表';

-- 检查并添加缺失的列
SET @column_exists = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_admin_permission' AND column_name = 'component');
SET @add_component_column = IF(@column_exists = 0, 'ALTER TABLE `ptm_admin_permission` ADD COLUMN `component` varchar(100) DEFAULT NULL COMMENT "组件路径" AFTER `path`', 'SELECT "Column component already exists"');

PREPARE stmt FROM @add_component_column;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建管理员角色权限关联表
CREATE TABLE IF NOT EXISTS `ptm_admin_role_permission` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色权限关联表';

-- 创建管理员操作日志表
CREATE TABLE IF NOT EXISTS `ptm_admin_operation_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `admin_id` bigint(20) NOT NULL COMMENT '管理员ID',
    `username` varchar(50) NOT NULL COMMENT '管理员用户名',
    `module` varchar(50) NOT NULL COMMENT '操作模块',
    `operation` varchar(50) NOT NULL COMMENT '操作类型',
    `content` varchar(500) DEFAULT NULL COMMENT '操作内容',
    `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- 插入默认角色（如果不存在）
INSERT IGNORE INTO `ptm_admin_role` (`name`, `code`, `description`, `status`, `is_system`) VALUES
('超级管理员', 'super_admin', '拥有所有权限', 1, 1),
('内容管理员', 'content_admin', '负责内容管理', 1, 1),
('用户管理员', 'user_admin', '负责用户管理', 1, 1);

-- 插入默认权限（如果不存在）
INSERT IGNORE INTO `ptm_admin_permission` (`name`, `code`, `description`, `type`, `parent_id`, `sort`, `icon`, `path`, `component`, `status`, `is_system`) VALUES
-- 系统管理
('系统管理', 'system', '系统管理', 1, NULL, 1, 'setting', '/system', 'Layout', 1, 1),
('管理员管理', 'system:admin', '管理员管理', 1, 1, 1, 'user', 'admin', 'system/admin/index', 1, 1),
('角色管理', 'system:role', '角色管理', 1, 1, 2, 'peoples', 'role', 'system/role/index', 1, 1),
('权限管理', 'system:permission', '权限管理', 1, 1, 3, 'tree-table', 'permission', 'system/permission/index', 1, 1),
('操作日志', 'system:log', '操作日志', 1, 1, 4, 'log', 'log', 'system/log/index', 1, 1),

-- 用户管理
('用户管理', 'user', '用户管理', 1, NULL, 2, 'peoples', '/user', 'Layout', 1, 1),
('用户列表', 'user:list', '用户列表', 1, 6, 1, 'user', 'list', 'user/list/index', 1, 1),
('实名认证', 'user:verification', '实名认证', 1, 6, 2, 'id-card', 'verification', 'user/verification/index', 1, 1),

-- 内容管理
('内容管理', 'content', '内容管理', 1, NULL, 3, 'documentation', '/content', 'Layout', 1, 1),
('照片管理', 'content:photo', '照片管理', 1, 9, 1, 'image', 'photo', 'content/photo/index', 1, 1),
('评论管理', 'content:comment', '评论管理', 1, 9, 2, 'message', 'comment', 'content/comment/index', 1, 1),
('内容审核', 'content:moderation', '内容审核', 1, 9, 3, 'check', 'moderation', 'content/moderation/index', 1, 1);

-- 插入角色权限关联（如果不存在）
-- 获取角色ID
SET @super_admin_id = (SELECT id FROM `ptm_admin_role` WHERE code = 'super_admin' LIMIT 1);
SET @content_admin_id = (SELECT id FROM `ptm_admin_role` WHERE code = 'content_admin' LIMIT 1);
SET @user_admin_id = (SELECT id FROM `ptm_admin_role` WHERE code = 'user_admin' LIMIT 1);

-- 超级管理员拥有所有权限
INSERT IGNORE INTO `ptm_admin_role_permission` (`role_id`, `permission_id`)
SELECT @super_admin_id, id FROM `ptm_admin_permission` WHERE id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12);

-- 内容管理员拥有内容管理权限
INSERT IGNORE INTO `ptm_admin_role_permission` (`role_id`, `permission_id`)
SELECT @content_admin_id, id FROM `ptm_admin_permission` WHERE id IN (9, 10, 11, 12);

-- 用户管理员拥有用户管理权限
INSERT IGNORE INTO `ptm_admin_role_permission` (`role_id`, `permission_id`)
SELECT @user_admin_id, id FROM `ptm_admin_permission` WHERE id IN (6, 7, 8);

-- 插入默认超级管理员账号，密码为 admin123（如果不存在）
INSERT IGNORE INTO `ptm_admin` (`username`, `password`, `name`, `role_id`, `status`, `is_deleted`) VALUES
('admin', '$2a$10$uXRGYXKj9Vw1Ckj6PRXoquuZGXqWYYnHXbf5yGLws.M8n0QWz6UHi', '超级管理员', @super_admin_id, 1, 0);
