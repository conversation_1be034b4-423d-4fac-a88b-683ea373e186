<template>
  <div class="safe-qrcode-container">
    <div v-if="loading" class="qrcode-loading">
      <van-loading type="spinner" size="24px" />
      <p class="loading-text">{{ loadingText }}</p>
    </div>
    <div v-else-if="error" class="qrcode-error">
      <van-icon name="warning-o" size="24px" color="#ee0a24" />
      <p class="error-text">{{ errorText }}</p>
      <van-button v-if="showRetry" size="small" type="primary" @click="generateQRCode">重试</van-button>
    </div>
    <div v-else ref="qrcodeRef" class="qrcode-content" :style="contentStyle"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { showToast } from 'vant'

// 尝试导入QRCode库
let QRCode: any = null
try {
  QRCode = (await import('qrcodejs2')).default
} catch (e) {
  console.error('Failed to import QRCode library', e)
}

const props = defineProps({
  text: {
    type: String,
    required: true
  },
  width: {
    type: Number,
    default: 200
  },
  height: {
    type: Number,
    default: 200
  },
  colorDark: {
    type: String,
    default: '#000000'
  },
  colorLight: {
    type: String,
    default: '#ffffff'
  },
  correctLevel: {
    type: Number,
    default: 0 // 0: L, 1: M, 2: Q, 3: H
  },
  loadingText: {
    type: String,
    default: '生成二维码中...'
  },
  errorText: {
    type: String,
    default: '生成二维码失败'
  },
  showRetry: {
    type: Boolean,
    default: true
  },
  autoRetry: {
    type: Boolean,
    default: true
  }
})

const qrcodeRef = ref<HTMLElement | null>(null)
const loading = ref(true)
const error = ref(false)
const qrcodeInstance = ref<any>(null)

// 计算内容样式
const contentStyle = computed(() => {
  return {
    width: `${props.width}px`,
    height: `${props.height}px`
  }
})

// 生成二维码
const generateQRCode = async () => {
  if (!qrcodeRef.value) return
  
  loading.value = true
  error.value = false
  
  try {
    // 检查QRCode库是否加载成功
    if (!QRCode) {
      // 如果第一次加载失败，尝试再次导入
      try {
        QRCode = (await import('qrcodejs2')).default
      } catch (e) {
        throw new Error('QRCode library not available')
      }
    }
    
    // 清空容器
    if (qrcodeRef.value) {
      qrcodeRef.value.innerHTML = ''
    }
    
    // 创建QRCode实例
    qrcodeInstance.value = new QRCode(qrcodeRef.value, {
      text: props.text,
      width: props.width,
      height: props.height,
      colorDark: props.colorDark,
      colorLight: props.colorLight,
      correctLevel: props.correctLevel
    })
    
    loading.value = false
  } catch (e) {
    console.error('Failed to generate QR code', e)
    error.value = true
    loading.value = false
    
    // 如果设置了自动重试，则在1秒后重试
    if (props.autoRetry) {
      setTimeout(() => {
        generateQRCode()
      }, 1000)
    }
  }
}

// 清除二维码
const clearQRCode = () => {
  if (qrcodeInstance.value && typeof qrcodeInstance.value.clear === 'function') {
    qrcodeInstance.value.clear()
  }
  
  if (qrcodeRef.value) {
    qrcodeRef.value.innerHTML = ''
  }
}

// 监听文本变化，重新生成二维码
watch(() => props.text, () => {
  if (qrcodeInstance.value && typeof qrcodeInstance.value.makeCode === 'function') {
    try {
      qrcodeInstance.value.makeCode(props.text)
    } catch (e) {
      console.error('Failed to update QR code', e)
      // 如果更新失败，尝试重新生成
      generateQRCode()
    }
  } else {
    generateQRCode()
  }
})

// 组件挂载时生成二维码
onMounted(() => {
  generateQRCode()
})

// 暴露方法
defineExpose({
  generateQRCode,
  clearQRCode
})
</script>

<style scoped>
.safe-qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-loading, .qrcode-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.loading-text, .error-text {
  margin-top: 10px;
  font-size: 14px;
  color: #969799;
}

.qrcode-content {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
