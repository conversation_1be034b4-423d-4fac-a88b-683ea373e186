package com.phototagmoment.config;

import com.phototagmoment.service.SystemConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 七牛云配置测试类
 */
public class QiniuConfigTest {

    @Mock
    private SystemConfigService systemConfigService;

    @InjectMocks
    private QiniuConfig qiniuConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testRefreshConfig_WhenQiniuEnabled() {
        // 模拟配置数据
        when(systemConfigService.getConfigValue("storage.type", "local")).thenReturn("qiniu");
        when(systemConfigService.getConfigValue("storage.qiniu.access-key", "")).thenReturn("test-access-key");
        when(systemConfigService.getConfigValue("storage.qiniu.secret-key", "")).thenReturn("test-secret-key");
        when(systemConfigService.getConfigValue("storage.qiniu.bucket", "")).thenReturn("test-bucket");
        when(systemConfigService.getConfigValue("storage.qiniu.region", "")).thenReturn("huanan");
        when(systemConfigService.getConfigValue("storage.qiniu.domain", "")).thenReturn("test.domain.com");
        when(systemConfigService.getConfigValue("storage.qiniu.upload-dir", "uploads")).thenReturn("phototagmoment");
        when(systemConfigService.getBooleanValue("storage.qiniu.is-private", true)).thenReturn(true);
        when(systemConfigService.getLongValue("storage.qiniu.download-expires", 3600L)).thenReturn(3600L);

        // 执行刷新配置
        qiniuConfig.refreshConfig();

        // 验证配置是否正确设置
        assertTrue(qiniuConfig.isEnabled());
        assertEquals("test-access-key", qiniuConfig.getAccessKey());
        assertEquals("test-secret-key", qiniuConfig.getSecretKey());
        assertEquals("test-bucket", qiniuConfig.getBucket());
        assertEquals("huanan", qiniuConfig.getRegion());
        assertEquals("test.domain.com", qiniuConfig.getDomain());
        assertEquals("phototagmoment", qiniuConfig.getUploadDir());
        assertTrue(qiniuConfig.isPrivate());
        assertEquals(3600L, qiniuConfig.getDownloadExpires());
    }

    @Test
    void testRefreshConfig_WhenQiniuDisabled() {
        // 模拟配置数据 - 七牛云未启用
        when(systemConfigService.getConfigValue("storage.type", "local")).thenReturn("local");

        // 执行刷新配置
        qiniuConfig.refreshConfig();

        // 验证七牛云未启用
        assertFalse(qiniuConfig.isEnabled());
    }

    @Test
    void testIsConfigComplete_WhenAllConfigPresent() {
        // 设置完整配置
        qiniuConfig.setEnabled(true);
        qiniuConfig.setAccessKey("test-access-key");
        qiniuConfig.setSecretKey("test-secret-key");
        qiniuConfig.setBucket("test-bucket");
        qiniuConfig.setRegion("huanan");
        qiniuConfig.setDomain("test.domain.com");

        // 验证配置完整
        assertTrue(qiniuConfig.isConfigComplete());
    }

    @Test
    void testIsConfigComplete_WhenConfigIncomplete() {
        // 设置不完整配置
        qiniuConfig.setEnabled(true);
        qiniuConfig.setAccessKey("test-access-key");
        // 缺少其他必要配置

        // 验证配置不完整
        assertFalse(qiniuConfig.isConfigComplete());
    }

    @Test
    void testGetConfigValidationError_WhenDisabled() {
        qiniuConfig.setEnabled(false);

        String error = qiniuConfig.getConfigValidationError();
        assertEquals("七牛云存储未启用", error);
    }

    @Test
    void testGetConfigValidationError_WhenMissingAccessKey() {
        qiniuConfig.setEnabled(true);
        qiniuConfig.setAccessKey("");

        String error = qiniuConfig.getConfigValidationError();
        assertEquals("七牛云AccessKey未配置", error);
    }

    @Test
    void testGetConfigValidationError_WhenConfigComplete() {
        // 设置完整配置
        qiniuConfig.setEnabled(true);
        qiniuConfig.setAccessKey("test-access-key");
        qiniuConfig.setSecretKey("test-secret-key");
        qiniuConfig.setBucket("test-bucket");
        qiniuConfig.setRegion("huanan");
        qiniuConfig.setDomain("test.domain.com");

        String error = qiniuConfig.getConfigValidationError();
        assertNull(error);
    }
}
