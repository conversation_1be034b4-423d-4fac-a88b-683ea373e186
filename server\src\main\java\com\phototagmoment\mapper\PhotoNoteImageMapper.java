package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.PhotoNoteImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 照片笔记图片关联Mapper接口
 */
@Mapper
public interface PhotoNoteImageMapper extends BaseMapper<PhotoNoteImage> {

    /**
     * 根据照片笔记ID查询图片列表
     *
     * @param noteId 照片笔记ID
     * @return 图片列表
     */
    @Select("SELECT pni.*, p.url, p.thumbnail_url, p.width, p.height " +
            "FROM ptm_photo_note_image pni " +
            "LEFT JOIN ptm_photo p ON pni.photo_id = p.id " +
            "WHERE pni.note_id = #{noteId} " +
            "ORDER BY pni.sort_order ASC")
    List<PhotoNoteImage> selectImagesByNoteId(@Param("noteId") Long noteId);

    /**
     * 批量插入照片笔记图片关联
     *
     * @param images 图片关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("images") List<PhotoNoteImage> images);

    /**
     * 删除照片笔记的所有图片关联
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    int deleteByNoteId(@Param("noteId") Long noteId);
}
