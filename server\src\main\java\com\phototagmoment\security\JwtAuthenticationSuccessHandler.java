package com.phototagmoment.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.entity.User;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.vo.TokenVO;
import com.phototagmoment.vo.UserVO;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * JWT认证成功处理器
 */
@Slf4j
@Component
public class JwtAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    // 移除 JwtTokenProvider 依赖

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        log.info("认证成功: {}", authentication.getName());

        // 获取认证用户信息
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String username = userDetails.getUsername();

        // 生成JWT
        // 使用内联方式生成JWT，不依赖JwtTokenProvider
        Map<String, Object> claims = new HashMap<>();

        // 尝试从用户名中提取用户ID
        Long userId = null;
        User user = userMapper.selectByUsername(username);
        if (user != null) {
            userId = user.getId();
        }

        if (userId != null) {
            claims.put("userId", userId);
        }

        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration);

        SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));

        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();

        log.info("生成JWT成功: {}", username);

        // 更新用户最后登录时间和IP
        if (user != null) {
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(getClientIp(request));
            userMapper.updateById(user);
            log.info("更新用户登录信息成功: {}", username);
        }

        // 将认证信息保存到会话中
        HttpSession session = request.getSession(true);
        session.setAttribute("SPRING_SECURITY_CONTEXT", authentication);
        log.info("认证信息已保存到会话: {}", session.getId());

        // 构建返回对象
        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(token);
        tokenVO.setTokenType(tokenPrefix);
        tokenVO.setExpiresIn(jwtExpiration / 1000);

        // 转换用户信息
        UserVO userVO = new UserVO();
        if (user != null) {
            userVO.setId(user.getId());
            userVO.setUsername(user.getUsername());
            userVO.setNickname(user.getNickname());
            userVO.setAvatar(user.getAvatar());
            userVO.setEmail(user.getEmail());
            userVO.setPhone(user.getPhone());
            userVO.setGender(user.getGender());
            userVO.setBio(user.getBio());
            // 移除不存在的字段
            // userVO.setLocation(user.getLocation());
            // userVO.setWebsite(user.getWebsite());
            userVO.setBirthday(user.getBirthday());
            userVO.setFollowerCount(user.getFollowerCount());
            userVO.setFollowingCount(user.getFollowingCount());
            userVO.setPhotoCount(user.getPhotoCount());
        }
        tokenVO.setUser(userVO);

        // 设置响应
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(objectMapper.writeValueAsString(ApiResponse.success(tokenVO)));
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
