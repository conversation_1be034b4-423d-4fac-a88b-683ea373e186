package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.TagDTO;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.entity.PhotoTag;
import com.phototagmoment.mapper.PhotoMapper;
import com.phototagmoment.mapper.PhotoTagMapper;
import com.phototagmoment.service.PhotoService;
import com.phototagmoment.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签服务实现类
 */
@Slf4j
@Service
public class TagServiceImpl implements TagService {

    @Autowired
    private PhotoTagMapper photoTagMapper;

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private PhotoService photoService;

    @Override
    public TagDTO getTagDetail(String tagName) {
        // 查询标签下的照片数量
        LambdaQueryWrapper<PhotoTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoTag::getName, tagName);
        long count = photoTagMapper.selectCount(queryWrapper);

        // 构建标签DTO
        TagDTO tagDTO = new TagDTO();
        tagDTO.setName(tagName);
        tagDTO.setCount(count); // 直接使用 long 类型

        return tagDTO;
    }

    @Override
    public IPage<PhotoDTO> getTagContent(String tagName, Integer page, Integer size, Long currentUserId) {
        // 查询标签下的照片ID列表
        LambdaQueryWrapper<PhotoTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoTag::getName, tagName);
        List<PhotoTag> photoTags = photoTagMapper.selectList(queryWrapper);
        List<Long> photoIds = photoTags.stream().map(PhotoTag::getPhotoId).collect(Collectors.toList());

        if (photoIds.isEmpty()) {
            // 如果没有照片，返回空结果
            return new Page<>(page, size);
        }

        // 查询照片列表
        LambdaQueryWrapper<Photo> photoQueryWrapper = new LambdaQueryWrapper<>();
        photoQueryWrapper.in(Photo::getId, photoIds);
        photoQueryWrapper.eq(Photo::getStatus, 1); // 只查询已发布的照片
        photoQueryWrapper.orderByDesc(Photo::getCreatedAt);

        Page<Photo> photoPage = new Page<>(page, size);
        photoPage = photoMapper.selectPage(photoPage, photoQueryWrapper);

        // 转换为PhotoDTO
        return photoService.convertToPhotoDTO(photoPage, currentUserId);
    }

    @Override
    public List<TagDTO> getHotTags(Integer limit) {
        // 查询热门标签
        List<Map<String, Object>> hotTags = photoTagMapper.selectHotTags(limit);

        // 转换为TagDTO
        List<TagDTO> tagDTOs = new ArrayList<>();
        for (Map<String, Object> tag : hotTags) {
            TagDTO tagDTO = new TagDTO();
            tagDTO.setName((String) tag.get("tagName"));
            tagDTO.setCount(((Number) tag.get("count")).longValue());
            tagDTOs.add(tagDTO);
        }

        return tagDTOs;
    }
}
