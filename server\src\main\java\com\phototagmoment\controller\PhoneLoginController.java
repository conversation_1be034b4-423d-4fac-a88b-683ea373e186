package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.config.SmsConfig;
import com.phototagmoment.service.SmsService;
import com.phototagmoment.service.UserService;
import com.phototagmoment.vo.TokenVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.regex.Pattern;

/**
 * 手机号登录控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth/phone")
@Tag(name = "手机号登录接口", description = "手机号登录相关接口")
public class PhoneLoginController {

    @Autowired
    private UserService userService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private SmsConfig smsConfig;

    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 手机号登录
     */
    @PostMapping("/login")
    @Operation(summary = "手机号登录", description = "使用手机号和验证码登录")
    public ApiResponse<TokenVO> login(
            @Parameter(description = "手机号") @RequestParam String phone,
            @Parameter(description = "验证码") @RequestParam String code) {
        if (!smsConfig.isEnabled()) {
            return ApiResponse.failed("短信服务未启用");
        }

        // 验证手机号格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return ApiResponse.failed("手机号格式不正确");
        }

        try {
            // 登录
            TokenVO tokenVO = userService.loginByPhone(phone, code);
            return ApiResponse.success(tokenVO);
        } catch (Exception e) {
            log.error("手机号登录失败: {}", e.getMessage(), e);
            return ApiResponse.failed(e.getMessage());
        }
    }

    /**
     * 发送登录验证码
     */
    @PostMapping("/send-code")
    @Operation(summary = "发送登录验证码", description = "发送手机号登录验证码")
    public ApiResponse<?> sendLoginCode(
            @Parameter(description = "手机号") @RequestParam String phone) {
        if (!smsConfig.isEnabled()) {
            return ApiResponse.failed("短信服务未启用");
        }

        // 验证手机号格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return ApiResponse.failed("手机号格式不正确");
        }

        // 检查发送频率
        long cooldown = smsService.getCooldownTime(phone);
        if (cooldown > 0) {
            return ApiResponse.failed("发送过于频繁，请" + cooldown + "秒后再试");
        }

        // 检查每日发送次数
        int remaining = smsService.getRemainingAttempts(phone);
        if (remaining <= 0) {
            return ApiResponse.failed("今日发送次数已达上限，请明天再试");
        }

        // 发送验证码
        boolean success = smsService.sendVerificationCode(phone);
        if (success) {
            return ApiResponse.success(null, "验证码发送成功");
        } else {
            return ApiResponse.failed("验证码发送失败，请稍后再试");
        }
    }
}
