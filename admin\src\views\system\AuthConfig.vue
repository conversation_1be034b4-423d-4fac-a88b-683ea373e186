<template>
  <div>
    <el-form :model="configForm" label-width="180px">
      <el-form-item label="启用第三方登录">
        <el-switch v-model="configForm.authEnabled" />
      </el-form-item>

      <el-divider content-position="left">QQ登录配置</el-divider>
      <el-form-item label="启用QQ登录">
        <el-switch v-model="configForm.qqEnabled" />
      </el-form-item>
      <el-form-item label="应用ID">
        <el-input v-model="configForm.qqClientId" placeholder="请输入QQ应用ID" />
      </el-form-item>
      <el-form-item label="应用密钥">
        <el-input v-model="configForm.qqClientSecret" placeholder="请输入QQ应用密钥" show-password />
      </el-form-item>
      <el-form-item label="回调地址">
        <el-input v-model="configForm.qqRedirectUri" placeholder="请输入QQ登录回调地址" />
      </el-form-item>

      <el-divider content-position="left">微信登录配置</el-divider>
      <el-form-item label="启用微信登录">
        <el-switch v-model="configForm.wechatEnabled" />
      </el-form-item>
      <el-form-item label="应用ID">
        <el-input v-model="configForm.wechatClientId" placeholder="请输入微信应用ID" />
      </el-form-item>
      <el-form-item label="应用密钥">
        <el-input v-model="configForm.wechatClientSecret" placeholder="请输入微信应用密钥" show-password />
      </el-form-item>
      <el-form-item label="回调地址">
        <el-input v-model="configForm.wechatRedirectUri" placeholder="请输入微信登录回调地址" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getSystemConfig, updateSystemConfig } from '@/api/system/config'

export default {
  name: 'AuthConfig',
  setup() {
    const loading = ref(false)
    const configForm = reactive({
      authEnabled: true,
      qqEnabled: false,
      qqClientId: '',
      qqClientSecret: '',
      qqRedirectUri: '',
      wechatEnabled: false,
      wechatClientId: '',
      wechatClientSecret: '',
      wechatRedirectUri: ''
    })

    // 获取配置
    const fetchConfig = async () => {
      loading.value = true
      try {
        // 获取第三方登录配置
        const authConfig = await getSystemConfig([
          'auth.enabled',
          'auth.qq.enabled',
          'auth.qq.client-id',
          'auth.qq.client-secret',
          'auth.qq.redirect-uri',
          'auth.wechat.enabled',
          'auth.wechat.client-id',
          'auth.wechat.client-secret',
          'auth.wechat.redirect-uri'
        ])

        // 设置表单数据
        configForm.authEnabled = authConfig['auth.enabled'] === 'true'
        configForm.qqEnabled = authConfig['auth.qq.enabled'] === 'true'
        configForm.qqClientId = authConfig['auth.qq.client-id'] || ''
        configForm.qqClientSecret = authConfig['auth.qq.client-secret'] || ''
        configForm.qqRedirectUri = authConfig['auth.qq.redirect-uri'] || ''
        configForm.wechatEnabled = authConfig['auth.wechat.enabled'] === 'true'
        configForm.wechatClientId = authConfig['auth.wechat.client-id'] || ''
        configForm.wechatClientSecret = authConfig['auth.wechat.client-secret'] || ''
        configForm.wechatRedirectUri = authConfig['auth.wechat.redirect-uri'] || ''

        loading.value = false
      } catch (error) {
        console.error('获取第三方登录配置失败', error)
        ElMessage.error('获取配置失败，请稍后再试')
        loading.value = false
      }
    }

    // 保存配置
    const saveConfig = async () => {
      loading.value = true
      try {
        // 构建配置数据
        const configData = {
          'auth.enabled': configForm.authEnabled.toString(),
          'auth.qq.enabled': configForm.qqEnabled.toString(),
          'auth.qq.client-id': configForm.qqClientId,
          'auth.qq.client-secret': configForm.qqClientSecret,
          'auth.qq.redirect-uri': configForm.qqRedirectUri,
          'auth.wechat.enabled': configForm.wechatEnabled.toString(),
          'auth.wechat.client-id': configForm.wechatClientId,
          'auth.wechat.client-secret': configForm.wechatClientSecret,
          'auth.wechat.redirect-uri': configForm.wechatRedirectUri
        }

        // 更新配置
        const res = await updateSystemConfig(configData)
        if (res.code === 200) {
          ElMessage.success('保存成功')
        } else {
          ElMessage.error(res.message || '保存失败')
        }
        loading.value = false
      } catch (error) {
        console.error('保存第三方登录配置失败', error)
        ElMessage.error('保存失败，请稍后再试')
        loading.value = false
      }
    }

    onMounted(() => {
      fetchConfig()
    })

    return {
      loading,
      configForm,
      fetchConfig,
      saveConfig
    }
  }
}
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}
</style>
