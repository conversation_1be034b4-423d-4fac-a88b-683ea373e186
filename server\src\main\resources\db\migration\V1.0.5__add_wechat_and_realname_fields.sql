-- 添加微信和实名认证相关字段
ALTER TABLE ptm_user
    ADD COLUMN wechat_open_id VARCHAR(64) COMMENT '微信OpenID',
    ADD COLUMN wechat_union_id VARCHAR(64) COMMENT '微信UnionID',
    ADD COLUMN wechat_nickname VARCHAR(64) COMMENT '微信昵称',
    ADD COLUMN real_name VARCHAR(64) COMMENT '真实姓名',
    ADD COLUMN id_card VARCHAR(64) COMMENT '身份证号';

-- 添加索引
CREATE INDEX idx_user_wechat_open_id ON ptm_user (wechat_open_id);
CREATE INDEX idx_user_wechat_union_id ON ptm_user (wechat_union_id);
