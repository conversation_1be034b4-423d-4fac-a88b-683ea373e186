package com.phototagmoment.service.parser;

import com.phototagmoment.entity.SensitiveWord;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Excel/CSV敏感词解析器
 */
@Slf4j
@Component
public class ExcelSensitiveWordParser implements SensitiveWordFileParser {

    private static final List<String> SUPPORTED_EXTENSIONS = Arrays.asList("xlsx", "xls", "csv");

    @Override
    public List<SensitiveWord> parse(MultipartFile file) throws Exception {
        log.info("解析Excel/CSV文件: {}", file.getOriginalFilename());
        List<SensitiveWord> sensitiveWords = new ArrayList<>();

        try (InputStream is = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(is)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            
            // 从第二行开始读取数据（第一行为表头）
            boolean isFirstRow = true;
            for (Row row : sheet) {
                if (isFirstRow) {
                    isFirstRow = false;
                    continue;
                }
                
                try {
                    SensitiveWord sensitiveWord = new SensitiveWord();
                    
                    // 读取敏感词
                    Cell wordCell = row.getCell(0);
                    if (wordCell == null) {
                        continue;
                    }
                    String word = getCellValueAsString(wordCell);
                    if (word == null || word.trim().isEmpty()) {
                        continue;
                    }
                    sensitiveWord.setWord(word.trim());
                    
                    // 读取类型
                    Cell typeCell = row.getCell(1);
                    if (typeCell != null) {
                        String type = getCellValueAsString(typeCell);
                        sensitiveWord.setType(type != null && !type.trim().isEmpty() ? type.trim() : "其他");
                    } else {
                        sensitiveWord.setType("其他");
                    }
                    
                    // 读取级别
                    Cell levelCell = row.getCell(2);
                    if (levelCell != null) {
                        String levelStr = getCellValueAsString(levelCell);
                        int level = 1;
                        if (levelStr != null && !levelStr.trim().isEmpty()) {
                            try {
                                level = Integer.parseInt(levelStr.trim());
                            } catch (NumberFormatException e) {
                                // 尝试解析文本级别
                                if (levelStr.contains("高") || levelStr.contains("严重")) {
                                    level = 3;
                                } else if (levelStr.contains("中") || levelStr.contains("中等")) {
                                    level = 2;
                                }
                            }
                        }
                        sensitiveWord.setLevel(level);
                    } else {
                        sensitiveWord.setLevel(1);
                    }
                    
                    // 读取替换词
                    Cell replaceWordCell = row.getCell(3);
                    if (replaceWordCell != null) {
                        String replaceWord = getCellValueAsString(replaceWordCell);
                        sensitiveWord.setReplaceWord(replaceWord != null ? replaceWord.trim() : "");
                    } else {
                        sensitiveWord.setReplaceWord("");
                    }
                    
                    // 设置状态和时间
                    sensitiveWord.setStatus(true);
                    sensitiveWord.setCreatedAt(LocalDateTime.now());
                    sensitiveWord.setUpdatedAt(LocalDateTime.now());
                    
                    sensitiveWords.add(sensitiveWord);
                } catch (Exception e) {
                    log.error("解析Excel/CSV行数据失败: {}", e.getMessage(), e);
                }
            }
        }
        
        log.info("Excel/CSV文件解析完成，共解析出{}个敏感词", sensitiveWords.size());
        return sensitiveWords;
    }

    @Override
    public boolean supports(String fileExtension) {
        return SUPPORTED_EXTENSIONS.contains(fileExtension.toLowerCase());
    }
    
    /**
     * 获取单元格的值（字符串形式）
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                } else {
                    // 避免科学计数法
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        return "";
                    }
                }
            default:
                return "";
        }
    }
}
