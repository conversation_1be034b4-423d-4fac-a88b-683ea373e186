<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.UserMapper">

    <!-- 查询月度用户增长数据 -->
    <select id="selectMonthlyUserGrowth" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m-%d') as date,
            COUNT(*) as count
        FROM ptm_user 
        WHERE is_deleted = 0 
        AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m-%d')
        ORDER BY date ASC
    </select>

    <!-- 查询月度活跃用户数据 -->
    <select id="selectMonthlyActiveUsers" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(last_login_time, '%Y-%m-%d') as date,
            COUNT(DISTINCT id) as count
        FROM ptm_user 
        WHERE is_deleted = 0 
        AND last_login_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE_FORMAT(last_login_time, '%Y-%m-%d')
        ORDER BY date ASC
    </select>

    <!-- 查询年度用户增长数据 -->
    <select id="selectYearlyUserGrowth" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as date,
            COUNT(*) as count
        FROM ptm_user 
        WHERE is_deleted = 0 
        AND created_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY date ASC
    </select>

    <!-- 查询年度活跃用户数据 -->
    <select id="selectYearlyActiveUsers" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(last_login_time, '%Y-%m') as date,
            COUNT(DISTINCT id) as count
        FROM ptm_user 
        WHERE is_deleted = 0 
        AND last_login_time >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(last_login_time, '%Y-%m')
        ORDER BY date ASC
    </select>

    <!-- 查询周度用户增长数据 -->
    <select id="selectWeeklyUserGrowth" resultType="java.util.Map">
        SELECT 
            DAYNAME(created_at) as dayName,
            WEEKDAY(created_at) as dayIndex,
            COUNT(*) as count
        FROM ptm_user 
        WHERE is_deleted = 0 
        AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DAYNAME(created_at), WEEKDAY(created_at)
        ORDER BY dayIndex ASC
    </select>

    <!-- 查询周度活跃用户数据 -->
    <select id="selectWeeklyActiveUsers" resultType="java.util.Map">
        SELECT 
            DAYNAME(last_login_time) as dayName,
            WEEKDAY(last_login_time) as dayIndex,
            COUNT(DISTINCT id) as count
        FROM ptm_user 
        WHERE is_deleted = 0 
        AND last_login_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DAYNAME(last_login_time), WEEKDAY(last_login_time)
        ORDER BY dayIndex ASC
    </select>

</mapper>
