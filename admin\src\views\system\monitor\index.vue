<template>
  <div class="monitor-container">
    <el-card class="page-header">
      <div class="header-content">
        <h2>系统监控</h2>
        <p>实时监控系统运行状态和性能指标</p>
      </div>
    </el-card>

    <!-- 系统概览 -->
    <el-row :gutter="16" class="overview-cards">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#409eff"><cpu /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.cpuUsage }}%</div>
              <div class="metric-label">CPU使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#67c23a"><monitor /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.memoryUsage }}%</div>
              <div class="metric-label">内存使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#e6a23c"><folder-opened /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.diskUsage }}%</div>
              <div class="metric-label">磁盘使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#f56c6c"><connection /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.activeConnections }}</div>
              <div class="metric-label">活跃连接数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务状态 -->
    <el-card class="service-status">
      <template #header>
        <div class="card-header">
          <span>服务状态</span>
          <el-button size="small" @click="refreshServiceStatus">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <el-table :data="services" style="width: 100%">
        <el-table-column prop="name" label="服务名称" width="200" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="row.status === 'running' ? 'success' : 'danger'">
              {{ row.status === 'running' ? '运行中' : '已停止' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="uptime" label="运行时间" width="150" />
        <el-table-column prop="memory" label="内存占用" width="120" />
        <el-table-column prop="cpu" label="CPU占用" width="120" />
        <el-table-column prop="description" label="描述" />
      </el-table>
    </el-card>

    <!-- 实时日志 -->
    <el-card class="real-time-logs">
      <template #header>
        <div class="card-header">
          <span>实时日志</span>
          <div class="log-controls">
            <el-select v-model="logLevel" size="small" style="width: 120px; margin-right: 8px;">
              <el-option label="全部" value="ALL" />
              <el-option label="错误" value="ERROR" />
              <el-option label="警告" value="WARN" />
              <el-option label="信息" value="INFO" />
              <el-option label="调试" value="DEBUG" />
            </el-select>
            <el-button size="small" @click="clearLogs">清空</el-button>
            <el-button size="small" @click="toggleAutoRefresh">
              {{ autoRefresh ? '停止' : '开始' }}自动刷新
            </el-button>
          </div>
        </div>
      </template>
      <div class="log-container">
        <div v-for="(log, index) in filteredLogs" :key="index" class="log-item" :class="log.level.toLowerCase()">
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Cpu, Monitor, FolderOpened, Connection, Refresh } from '@element-plus/icons-vue'

// 响应式数据
const systemInfo = reactive({
  cpuUsage: 0,
  memoryUsage: 0,
  diskUsage: 0,
  activeConnections: 0
})

const services = ref([
  {
    name: 'PhotoTagMoment API',
    status: 'running',
    uptime: '2天3小时',
    memory: '256MB',
    cpu: '5.2%',
    description: '主要API服务'
  },
  {
    name: 'MySQL数据库',
    status: 'running',
    uptime: '15天12小时',
    memory: '512MB',
    cpu: '2.1%',
    description: '主数据库服务'
  },
  {
    name: 'Redis缓存',
    status: 'running',
    uptime: '15天12小时',
    memory: '128MB',
    cpu: '0.8%',
    description: '缓存服务'
  },
  {
    name: '文件存储服务',
    status: 'running',
    uptime: '2天3小时',
    memory: '64MB',
    cpu: '1.2%',
    description: '文件上传下载服务'
  }
])

const logs = ref([
  {
    timestamp: new Date(),
    level: 'INFO',
    message: '系统启动完成'
  },
  {
    timestamp: new Date(Date.now() - 60000),
    level: 'INFO',
    message: '用户登录成功 - admin'
  },
  {
    timestamp: new Date(Date.now() - 120000),
    level: 'WARN',
    message: '磁盘使用率超过80%'
  },
  {
    timestamp: new Date(Date.now() - 180000),
    level: 'ERROR',
    message: '数据库连接超时'
  }
])

const logLevel = ref('ALL')
const autoRefresh = ref(false)
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const filteredLogs = computed(() => {
  if (logLevel.value === 'ALL') {
    return logs.value
  }
  return logs.value.filter(log => log.level === logLevel.value)
})

// 方法
const loadSystemInfo = () => {
  // 模拟获取系统信息
  systemInfo.cpuUsage = Math.floor(Math.random() * 100)
  systemInfo.memoryUsage = Math.floor(Math.random() * 100)
  systemInfo.diskUsage = Math.floor(Math.random() * 100)
  systemInfo.activeConnections = Math.floor(Math.random() * 1000)
}

const refreshServiceStatus = () => {
  ElMessage.success('服务状态已刷新')
  // 这里可以调用API获取真实的服务状态
}

const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value

  if (autoRefresh.value) {
    refreshTimer = setInterval(() => {
      loadSystemInfo()
      // 模拟新日志
      if (Math.random() > 0.7) {
        const levels = ['INFO', 'WARN', 'ERROR', 'DEBUG']
        const messages = [
          '用户操作记录',
          '系统性能检查',
          '数据库查询执行',
          '文件上传完成',
          '缓存更新'
        ]
        logs.value.unshift({
          timestamp: new Date(),
          level: levels[Math.floor(Math.random() * levels.length)],
          message: messages[Math.floor(Math.random() * messages.length)]
        })

        // 保持最多100条日志
        if (logs.value.length > 100) {
          logs.value = logs.value.slice(0, 100)
        }
      }
    }, 5000)
    ElMessage.success('已开启自动刷新')
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    ElMessage.info('已停止自动刷新')
  }
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  loadSystemInfo()
  // 每30秒更新一次系统信息
  refreshTimer = setInterval(loadSystemInfo, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.monitor-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.overview-cards {
  margin-bottom: 20px;
}

.metric-card {
  height: 120px;
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.metric-icon {
  margin-right: 16px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.service-status {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.real-time-logs {
  margin-bottom: 20px;
}

.log-controls {
  display: flex;
  align-items: center;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  background-color: #1e1e1e;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.log-time {
  color: #888;
  margin-right: 8px;
  min-width: 80px;
}

.log-level {
  margin-right: 8px;
  min-width: 50px;
  font-weight: bold;
}

.log-message {
  flex: 1;
}

.log-item.info .log-level {
  color: #409eff;
}

.log-item.warn .log-level {
  color: #e6a23c;
}

.log-item.error .log-level {
  color: #f56c6c;
}

.log-item.debug .log-level {
  color: #909399;
}

.log-item.info .log-message {
  color: #e8e8e8;
}

.log-item.warn .log-message {
  color: #e8e8e8;
}

.log-item.error .log-message {
  color: #e8e8e8;
}

.log-item.debug .log-message {
  color: #ccc;
}
</style>
