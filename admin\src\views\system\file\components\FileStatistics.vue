<template>
  <div class="file-statistics-container">
    <!-- 基础统计 -->
    <el-row :gutter="16" class="statistics-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.totalFiles || 0 }}</div>
            <div class="stat-label">总文件数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ formatFileSize(statistics.totalSize || 0) }}</div>
            <div class="stat-label">总存储大小</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.normalFiles || 0 }}</div>
            <div class="stat-label">正常文件</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ statistics.trashFiles || 0 }}</div>
            <div class="stat-label">回收站文件</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 存储使用情况 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>存储使用情况</span>
          <el-button size="small" @click="loadStorageUsage">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div class="storage-usage">
        <div v-for="storage in storageUsage.storageDetails" :key="storage.storage_type" class="storage-item">
          <div class="storage-info">
            <div class="storage-type">
              <el-tag :type="storage.storage_type === 'QINIU' ? 'success' : 'info'">
                {{ storage.storage_type === 'QINIU' ? '七牛云' : '本地存储' }}
              </el-tag>
            </div>
            <div class="storage-stats">
              <div class="stat-row">
                <span class="label">文件数量：</span>
                <span class="value">{{ storage.fileCount || 0 }} 个</span>
              </div>
              <div class="stat-row">
                <span class="label">使用空间：</span>
                <span class="value">{{ formatFileSize(storage.usedSize || 0) }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="total-usage">
          <el-divider />
          <div class="total-info">
            <div class="total-item">
              <span class="label">总文件数：</span>
              <span class="value">{{ storageUsage.totalFileCount || 0 }} 个</span>
            </div>
            <div class="total-item">
              <span class="label">总使用空间：</span>
              <span class="value">{{ storageUsage.usedSizeFormatted || '0 B' }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 文件类型分布 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>文件类型分布</span>
          <el-button size="small" @click="loadFileTypeDistribution">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div class="type-distribution">
        <div v-for="type in fileTypeDistribution" :key="type.category" class="type-item">
          <div class="type-info">
            <div class="type-header">
              <el-tag :type="getFileTypeTagType(type.category)">
                {{ getFileTypeLabel(type.category) }}
              </el-tag>
              <span class="type-count">{{ type.count || 0 }} 个文件</span>
            </div>
            <div class="type-stats">
              <div class="stat-row">
                <span class="label">总大小：</span>
                <span class="value">{{ formatFileSize(type.totalSize || 0) }}</span>
              </div>
              <div class="progress-bar">
                <el-progress
                  :percentage="getTypePercentage(type.count)"
                  :stroke-width="8"
                  :show-text="false"
                  :color="getProgressColor(type.category)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 最近上传 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>最近上传文件</span>
          <el-button size="small" @click="loadRecentFiles">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      <div class="recent-files">
        <div v-if="recentFiles.length === 0" class="empty-state">
          <el-empty description="暂无最近上传的文件" />
        </div>
        <div v-else class="file-list">
          <div v-for="file in recentFiles" :key="file.id" class="file-item">
            <div class="file-icon">
              <el-icon size="24" :class="getFileIconClass(file.extension)">
                <document />
              </el-icon>
            </div>
            <div class="file-details">
              <div class="file-name" :title="file.originalName">{{ file.originalName }}</div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
                <span class="upload-time">{{ formatDateTime(file.createdAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Document } from '@element-plus/icons-vue'
import { 
  getFileStatistics, 
  getStorageUsage, 
  getFileTypeDistribution,
  type FileInfo 
} from '@/api/file'
import { formatDateTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const statistics = reactive({
  totalFiles: 0,
  totalSize: 0,
  normalFiles: 0,
  trashFiles: 0,
  adminFiles: 0,
  userFiles: 0
})

const storageUsage = reactive({
  storageDetails: [],
  totalFileCount: 0,
  totalUsedSize: 0,
  usedSizeFormatted: '0 B'
})

const fileTypeDistribution = ref([])
const recentFiles = ref<FileInfo[]>([])

// 生命周期
onMounted(() => {
  loadStatistics()
  loadStorageUsage()
  loadFileTypeDistribution()
  loadRecentFiles()
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await getFileStatistics()
    if ((response as any).code === 200) {
      Object.assign(statistics, (response as any).data)
    }
  } catch (error) {
    console.error('加载文件统计失败:', error)
    ElMessage.error('加载文件统计失败')
  }
}

const loadStorageUsage = async () => {
  try {
    const response = await getStorageUsage()
    if ((response as any).code === 200) {
      Object.assign(storageUsage, (response as any).data)
    }
  } catch (error) {
    console.error('加载存储使用情况失败:', error)
    ElMessage.error('加载存储使用情况失败')
  }
}

const loadFileTypeDistribution = async () => {
  try {
    const response = await getFileTypeDistribution()
    if ((response as any).code === 200) {
      fileTypeDistribution.value = (response as any).data
    }
  } catch (error) {
    console.error('加载文件类型分布失败:', error)
    ElMessage.error('加载文件类型分布失败')
  }
}

const loadRecentFiles = async () => {
  try {
    // 这里应该调用获取最近文件的API，暂时使用空数组
    recentFiles.value = []
  } catch (error) {
    console.error('加载最近文件失败:', error)
    ElMessage.error('加载最近文件失败')
  }
}

// 工具方法
const formatFileSize = (size: number) => {
  if (!size) return '0 B'
  
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
  }
}

const getFileTypeTagType = (category: string) => {
  const typeMap = {
    image: 'success',
    video: 'warning',
    audio: 'info',
    document: 'primary',
    other: 'info'
  }
  return typeMap[category] || 'info'
}

const getFileTypeLabel = (category: string) => {
  const labelMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档',
    other: '其他'
  }
  return labelMap[category] || '其他'
}

const getTypePercentage = (count: number) => {
  const total = statistics.totalFiles || 1
  return Math.round((count / total) * 100)
}

const getProgressColor = (category: string) => {
  const colorMap = {
    image: '#67c23a',
    video: '#e6a23c',
    audio: '#409eff',
    document: '#909399',
    other: '#f56c6c'
  }
  return colorMap[category] || '#909399'
}

const getFileIconClass = (extension: string) => {
  return 'file-icon'
}
</script>

<style scoped>
.file-statistics-container {
  padding: 16px;
}

.statistics-row {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 16px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.chart-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.storage-usage {
  padding: 16px;
}

.storage-item {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.storage-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.storage-stats {
  text-align: right;
}

.stat-row {
  margin-bottom: 4px;
}

.label {
  color: #606266;
  margin-right: 8px;
}

.value {
  color: #303133;
  font-weight: 500;
}

.total-usage {
  margin-top: 16px;
}

.total-info {
  display: flex;
  justify-content: space-around;
}

.total-item {
  text-align: center;
}

.type-distribution {
  padding: 16px;
}

.type-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.type-count {
  color: #606266;
  font-size: 14px;
}

.progress-bar {
  margin-top: 8px;
}

.recent-files {
  padding: 16px;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  margin-right: 12px;
  color: #909399;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 40px;
}
</style>
