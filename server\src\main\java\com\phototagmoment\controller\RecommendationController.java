package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.service.RecommendationService;
import com.phototagmoment.util.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 推荐控制器
 */
@Slf4j
@RestController
@RequestMapping("/recommendation")
@Tag(name = "推荐接口", description = "照片笔记推荐相关接口")
public class RecommendationController {

    @Autowired
    private RecommendationService recommendationService;

    /**
     * 获取首页推荐照片笔记
     */
    @GetMapping("/home")
    @Operation(summary = "获取首页推荐照片笔记", description = "获取首页推荐照片笔记")
    public Result<IPage<PhotoNoteDTO>> getHomeRecommendations(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Long userId = UserUtil.getCurrentUserId();
        // 如果用户未登录，使用热门推荐
        IPage<PhotoNoteDTO> photoNotePage;
        if (userId == null) {
            photoNotePage = recommendationService.getHotPhotoNotes(page, size);
        } else {
            photoNotePage = recommendationService.getHomePhotoNoteRecommendations(userId, page, size);
        }
        return Result.success(photoNotePage);
    }

    /**
     * 获取关注的人发布的照片笔记
     */
    @GetMapping("/following")
    @Operation(summary = "获取关注的人发布的照片笔记", description = "获取关注的人发布的照片笔记")
    public Result<IPage<PhotoNoteDTO>> getFollowingPhotos(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Long userId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> photoNotePage = recommendationService.getFollowingPhotoNotes(userId, page, size);
        return Result.success(photoNotePage);
    }

    /**
     * 获取热门照片笔记
     */
    @GetMapping("/hot")
    @Operation(summary = "获取热门照片笔记", description = "获取热门照片笔记")
    public Result<IPage<PhotoNoteDTO>> getHotPhotos(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "天数") @RequestParam(required = false) Integer days) {
        IPage<PhotoNoteDTO> photoNotePage;
        if (days != null) {
            photoNotePage = recommendationService.getHotPhotoNotes(days, page, size);
        } else {
            photoNotePage = recommendationService.getHotPhotoNotes(page, size);
        }
        return Result.success(photoNotePage);
    }

    /**
     * 获取推荐照片笔记
     */
    @GetMapping("/recommended")
    @Operation(summary = "获取推荐照片笔记", description = "基于用户兴趣获取推荐照片笔记")
    public Result<IPage<PhotoNoteDTO>> getRecommendedPhotos(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        Long userId = UserUtil.getCurrentUserId();
        // 如果用户未登录，使用热门推荐
        IPage<PhotoNoteDTO> photoNotePage;
        if (userId == null) {
            photoNotePage = recommendationService.getHotPhotoNotes(page, size);
        } else {
            photoNotePage = recommendationService.getRecommendedPhotoNotes(userId, page, size);
        }
        return Result.success(photoNotePage);
    }

    /**
     * 获取用户兴趣标签
     */
    @GetMapping("/interest-tags")
    @Operation(summary = "获取用户兴趣标签", description = "获取用户可能感兴趣的标签")
    public Result<List<String>> getUserInterestTags(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") int limit) {
        Long userId = UserUtil.getCurrentUserId();
        // 如果用户未登录，返回热门标签
        List<String> tags;
        if (userId == null) {
            // 返回一些默认的热门标签
            tags = Arrays.asList("风景", "美食", "旅行", "人像", "宠物", "城市", "自然", "建筑", "艺术", "生活");
        } else {
            tags = recommendationService.getUserInterestTags(userId, limit);
        }
        return Result.success(tags);
    }

    /**
     * 记录用户行为
     */
    @PostMapping("/record-behavior")
    @Operation(summary = "记录用户行为", description = "记录用户对照片笔记的行为")
    public Result<Boolean> recordUserBehavior(
            @Parameter(description = "照片笔记ID") @RequestParam(required = false) Long noteId,
            @Parameter(description = "行为类型") @RequestParam(required = false) String behavior,
            @Parameter(description = "请求体") @RequestBody(required = false) BehaviorRequest request) {

        Long noteIdValue = noteId;
        String behaviorValue = behavior;

        // 如果参数从请求体中获取
        if (request != null) {
            if (noteIdValue == null) {
                // 优先使用noteId，如果为空则使用photoId
                noteIdValue = request.noteId != null ? request.noteId : request.photoId;
            }
            if (behaviorValue == null) {
                behaviorValue = request.getBehavior();
            }
        }

        // 参数验证
        if (noteIdValue == null) {
            log.warn("记录用户行为失败：照片笔记ID为空");
            return Result.fail("照片笔记ID不能为空");
        }

        if (behaviorValue == null || behaviorValue.isEmpty()) {
            log.warn("记录用户行为失败：行为类型为空");
            return Result.fail("行为类型不能为空");
        }

        Long userId = UserUtil.getCurrentUserId();
        // 只有登录用户才记录行为
        if (userId != null) {
            // 根据行为类型设置权重
            Double weight = getActionWeight(behaviorValue);
            recommendationService.recordUserBehavior(userId, noteIdValue, behaviorValue, weight);
        } else {
            log.info("未登录用户，不记录行为");
        }
        return Result.success(true);
    }

    /**
     * 根据行为类型获取权重
     */
    private Double getActionWeight(String actionType) {
        switch (actionType.toLowerCase()) {
            case "view":
                return 1.0;
            case "like":
                return 3.0;
            case "comment":
                return 5.0;
            case "collect":
                return 7.0;
            case "share":
                return 10.0;
            default:
                return 1.0;
        }
    }

    /**
     * 用于接收行为记录请求的内部类
     */
    @lombok.Data
    public static class BehaviorRequest {
        private Long noteId;
        private Long photoId; // 兼容前端发送的photoId字段
        private String behavior;
    }

    /**
     * 更新用户兴趣模型
     */
    @PostMapping("/update-interest-model")
    @Operation(summary = "更新用户兴趣模型", description = "手动更新用户兴趣模型")
    public Result<Boolean> updateUserInterestModel() {
        Long userId = UserUtil.getCurrentUserId();
        recommendationService.updateUserInterestModel(userId);
        return Result.success(true);
    }
}
