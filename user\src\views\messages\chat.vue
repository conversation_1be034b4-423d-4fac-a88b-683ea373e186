<template>
  <div class="chat-container">
    <van-nav-bar
      :title="chatUser.nickname || '聊天'"
      left-arrow
      @click-left="goBack"
      fixed
    >
      <template #right>
        <van-icon name="more-o" size="18" @click="showActionSheet = true" />
      </template>
    </van-nav-bar>

    <div class="chat-content" ref="chatContent">
      <van-pull-refresh v-model="refreshing" @refresh="loadMoreMessages">
        <div class="messages-wrapper">
          <div v-if="messages.length > 0">
            <div
              v-for="(message, index) in messages"
              :key="message.id"
              class="message-item"
              :class="{ 'self': message.isSelf }"
            >
              <!-- 日期分割线 -->
              <div v-if="showDateDivider(message, index)" class="date-divider">
                {{ formatDate(message.time) }}
              </div>

              <!-- 消息内容 -->
              <div class="message-content">
                <div v-if="!message.isSelf" class="avatar">
                  <van-image
                    round
                    width="40"
                    height="40"
                    :src="chatUser.avatar"
                    :alt="chatUser.nickname"
                  />
                </div>
                <div class="message-bubble">
                  <div class="message-text">{{ message.content }}</div>
                  <div class="message-time">{{ formatTime(message.time) }}</div>
                </div>
                <div v-if="message.isSelf" class="avatar">
                  <van-image
                    round
                    width="40"
                    height="40"
                    :src="userStore.avatar || 'https://randomuser.me/api/portraits/men/1.jpg'"
                    :alt="userStore.nickname || userStore.username"
                  />
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <van-empty description="暂无消息" />
          </div>
        </div>
      </van-pull-refresh>
    </div>

    <div class="chat-input">
      <van-field
        v-model="messageText"
        placeholder="输入消息..."
        :border="false"
        :autofocus="true"
        @keypress.enter.prevent="sendMessage"
      >
        <template #button>
          <van-button
            type="primary"
            size="small"
            :disabled="!messageText.trim()"
            @click="sendMessage"
          >
            发送
          </van-button>
        </template>
      </van-field>
    </div>

    <!-- 操作菜单 -->
    <van-action-sheet
      v-model:show="showActionSheet"
      :actions="actions"
      cancel-text="取消"
      @select="onActionSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast, showDialog } from 'vant';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 检查登录状态
if (!userStore.isLoggedIn) {
  router.push('/auth/login?redirect=' + route.fullPath);
}

// 聊天对象ID
const userId = computed(() => Number(route.params.id));

// 聊天对象信息
const chatUser = ref({
  id: 0,
  username: '',
  nickname: '',
  avatar: ''
});

// 消息列表
interface Message {
  id: number;
  content: string;
  time: string;
  isSelf: boolean;
  status: 'sending' | 'sent' | 'failed';
}

const messages = ref<Message[]>([]);
const messageText = ref('');
const refreshing = ref(false);
const chatContent = ref<HTMLElement | null>(null);
const showActionSheet = ref(false);

// 操作菜单
const actions = [
  { name: '查看用户资料', value: 'profile' },
  { name: '清空聊天记录', value: 'clear' },
  { name: '举报', value: 'report' },
  { name: '拉黑', value: 'block' }
];

// 返回上一页
const goBack = () => {
  router.back();
};

// 加载聊天对象信息
const loadChatUser = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    // 生成模拟数据
    chatUser.value = {
      id: userId.value,
      username: `user${userId.value}`,
      nickname: `用户${userId.value}`,
      avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${userId.value}.jpg`
    };
  } catch (error) {
    console.error('加载用户信息失败', error);
    showToast('加载用户信息失败');
  }
};

// 加载消息列表
const loadMessages = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 生成模拟数据
    const mockMessages: Message[] = Array.from({ length: 20 }, (_, i) => {
      const id = i + 1;
      const isSelf = Math.random() > 0.5;
      const time = new Date(Date.now() - (20 - i) * 3600000).toISOString();

      return {
        id,
        content: `这是一条${isSelf ? '发送' : '接收'}的消息 ${id}`,
        time,
        isSelf,
        status: 'sent'
      };
    });

    messages.value = mockMessages;

    // 滚动到底部
    await nextTick();
    scrollToBottom();
  } catch (error) {
    console.error('加载消息失败', error);
    showToast('加载消息失败');
  } finally {
    refreshing.value = false;
  }
};

// 加载更多消息
const loadMoreMessages = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 生成模拟数据
    const oldestMessageTime = messages.value.length > 0
      ? new Date(messages.value[0].time).getTime()
      : Date.now();

    const mockMessages: Message[] = Array.from({ length: 10 }, (_, i) => {
      const id = messages.value.length + i + 1;
      const isSelf = Math.random() > 0.5;
      const time = new Date(oldestMessageTime - (i + 1) * 3600000).toISOString();

      return {
        id,
        content: `这是更早的${isSelf ? '发送' : '接收'}消息 ${id}`,
        time,
        isSelf,
        status: 'sent'
      };
    });

    messages.value = [...mockMessages.reverse(), ...messages.value];
  } catch (error) {
    console.error('加载更多消息失败', error);
    showToast('加载更多消息失败');
  } finally {
    refreshing.value = false;
  }
};

// 发送消息
const sendMessage = async () => {
  if (!messageText.value.trim()) return;

  const content = messageText.value;
  messageText.value = '';

  // 创建临时消息
  const tempId = Date.now();
  const newMessage: Message = {
    id: tempId,
    content,
    time: new Date().toISOString(),
    isSelf: true,
    status: 'sending'
  };

  // 添加到消息列表
  messages.value.push(newMessage);

  // 滚动到底部
  await nextTick();
  scrollToBottom();

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    // 更新消息状态
    const index = messages.value.findIndex(msg => msg.id === tempId);
    if (index !== -1) {
      messages.value[index].status = 'sent';
    }

    // 模拟对方回复
    setTimeout(() => {
      const replyMessage: Message = {
        id: Date.now(),
        content: `这是对"${content}"的回复`,
        time: new Date().toISOString(),
        isSelf: false,
        status: 'sent'
      };

      messages.value.push(replyMessage);

      // 滚动到底部
      nextTick().then(() => {
        scrollToBottom();
      });
    }, 1000);
  } catch (error) {
    console.error('发送消息失败', error);

    // 更新消息状态为失败
    const index = messages.value.findIndex(msg => msg.id === tempId);
    if (index !== -1) {
      messages.value[index].status = 'failed';
    }

    showToast('发送失败，请重试');
  }
};

// 滚动到底部
const scrollToBottom = () => {
  if (chatContent.value) {
    chatContent.value.scrollTop = chatContent.value.scrollHeight;
  }
};

// 显示日期分割线
const showDateDivider = (message: Message, index: number) => {
  if (index === 0) return true;

  const prevMessage = messages.value[index - 1];
  const prevDate = new Date(prevMessage.time).toDateString();
  const currentDate = new Date(message.time).toDateString();

  return prevDate !== currentDate;
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);

  if (date.toDateString() === now.toDateString()) {
    return '今天';
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天';
  } else {
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  }
};

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${hours}:${minutes}`;
};

// 处理操作菜单选择
const onActionSelect = (action: { name: string, value: string }) => {
  switch (action.value) {
    case 'profile':
      router.push(`/user/${userId.value}`);
      break;
    case 'clear':
      showDialog({
        title: '清空聊天记录',
        message: '确定要清空与该用户的聊天记录吗？此操作不可撤销。',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonColor: '#ee0a24',
      }).then(() => {
        messages.value = [];
        showToast('聊天记录已清空');
      });
      break;
    case 'report':
      showToast('举报功能开发中');
      break;
    case 'block':
      showDialog({
        title: '拉黑用户',
        message: '确定要将该用户加入黑名单吗？加入黑名单后，您将不再收到该用户的消息。',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonColor: '#ee0a24',
      }).then(() => {
        showToast('已将用户加入黑名单');
        router.push('/messages');
      });
      break;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadChatUser();
  loadMessages();
});
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  margin-top: 46px; // 导航栏高度
  margin-bottom: 50px; // 输入框高度

  @media (min-width: 768px) {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding: 20px;
  }

  @media (min-width: 1200px) {
    max-width: 1000px;
  }

  .messages-wrapper {
    padding-bottom: 10px;

    @media (min-width: 768px) {
      padding-bottom: 20px;
    }
  }

  .date-divider {
    text-align: center;
    margin: 10px 0;
    color: #999;
    font-size: 12px;

    @media (min-width: 768px) {
      font-size: 14px;
      margin: 16px 0;
    }
  }

  .message-item {
    margin-bottom: 16px;

    @media (min-width: 768px) {
      margin-bottom: 24px;
    }

    .message-content {
      display: flex;
      align-items: flex-start;

      .avatar {
        margin: 0 8px;

        @media (min-width: 768px) {
          margin: 0 12px;

          .van-image {
            width: 48px !important;
            height: 48px !important;
          }
        }
      }

      .message-bubble {
        max-width: 70%;
        padding: 10px;
        border-radius: 8px;
        background-color: white;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        @media (min-width: 768px) {
          max-width: 60%;
          padding: 12px 16px;
          border-radius: 12px;
        }

        .message-text {
          word-break: break-word;

          @media (min-width: 768px) {
            font-size: 16px;
          }
        }

        .message-time {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
          text-align: right;
        }
      }
    }

    &.self {
      .message-content {
        flex-direction: row-reverse;

        .message-bubble {
          background-color: #d1f0ff;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 40px 0;
  }
}

.chat-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 8px;
  border-top: 1px solid #eee;

  @media (min-width: 768px) {
    max-width: 800px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 16px;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

    .van-field {
      border-radius: 20px;
      background-color: #f5f5f5;
      padding: 0 12px;

      &__control {
        min-height: 40px;
      }
    }

    .van-button {
      height: 40px;
      border-radius: 20px;
      padding: 0 20px;
    }
  }

  @media (min-width: 1200px) {
    max-width: 1000px;
  }
}
</style>
