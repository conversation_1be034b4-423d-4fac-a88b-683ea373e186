package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.common.Result;
import com.phototagmoment.dto.FileInfoDTO;
import com.phototagmoment.service.AdminFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 后台文件管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/file-manage")
@Tag(name = "后台文件管理", description = "后台管理系统文件管理相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminFileManageController {

    @Autowired
    private AdminFileService adminFileService;

    /**
     * 获取文件列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取文件列表", description = "分页获取文件列表")
    public Result<IPage<FileInfoDTO>> getFileList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            @Parameter(description = "文件类型") @RequestParam(required = false) String fileType,
            @Parameter(description = "上传者ID") @RequestParam(required = false) Long uploaderId,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {

        Page<FileInfoDTO> pageParam = new Page<>(page, size);
        IPage<FileInfoDTO> result = adminFileService.getFileList(pageParam, fileType, uploaderId, keyword, startDate, endDate);
        return Result.success(result);
    }

    /**
     * 获取文件详情
     */
    @GetMapping("/{fileId}")
    @Operation(summary = "获取文件详情", description = "根据文件ID获取文件详情")
    public Result<FileInfoDTO> getFileDetail(
            @Parameter(description = "文件ID") @PathVariable Long fileId) {

        FileInfoDTO fileInfo = adminFileService.getFileDetail(fileId);
        if (fileInfo != null) {
            return Result.success(fileInfo);
        } else {
            return Result.fail("文件不存在");
        }
    }

    /**
     * 批量删除文件
     */
    @DeleteMapping("/batch-delete")
    @Operation(summary = "批量删除文件", description = "批量删除指定的文件")
    public Result<Map<String, Object>> batchDeleteFiles(
            @Parameter(description = "文件ID列表") @RequestBody List<Long> fileIds) {

        if (fileIds == null || fileIds.isEmpty()) {
            return Result.fail("请选择要删除的文件");
        }

        if (fileIds.size() > 50) {
            return Result.fail("单次最多删除50个文件");
        }

        Map<String, Object> result = adminFileService.batchDeleteFiles(fileIds);
        return Result.success(result, "批量删除完成");
    }

    /**
     * 重命名文件
     */
    @PutMapping("/{fileId}/rename")
    @Operation(summary = "重命名文件", description = "重命名指定文件")
    public Result<Boolean> renameFile(
            @Parameter(description = "文件ID") @PathVariable Long fileId,
            @Parameter(description = "新文件名") @RequestParam String newName) {

        if (newName == null || newName.trim().isEmpty()) {
            return Result.fail("文件名不能为空");
        }

        boolean success = adminFileService.renameFile(fileId, newName.trim());
        if (success) {
            return Result.success(true, "文件重命名成功");
        } else {
            return Result.fail("文件重命名失败");
        }
    }

    /**
     * 移动文件到回收站
     */
    @PutMapping("/{fileId}/trash")
    @Operation(summary = "移动到回收站", description = "将文件移动到回收站")
    public Result<Boolean> moveToTrash(
            @Parameter(description = "文件ID") @PathVariable Long fileId) {

        boolean success = adminFileService.moveToTrash(fileId);
        if (success) {
            return Result.success(true, "文件已移动到回收站");
        } else {
            return Result.fail("操作失败");
        }
    }

    /**
     * 从回收站恢复文件
     */
    @PutMapping("/{fileId}/restore")
    @Operation(summary = "恢复文件", description = "从回收站恢复文件")
    public Result<Boolean> restoreFile(
            @Parameter(description = "文件ID") @PathVariable Long fileId) {

        boolean success = adminFileService.restoreFile(fileId);
        if (success) {
            return Result.success(true, "文件恢复成功");
        } else {
            return Result.fail("文件恢复失败");
        }
    }

    /**
     * 获取回收站文件列表
     */
    @GetMapping("/trash")
    @Operation(summary = "获取回收站文件", description = "获取回收站中的文件列表")
    public Result<IPage<FileInfoDTO>> getTrashFiles(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {

        Page<FileInfoDTO> pageParam = new Page<>(page, size);
        IPage<FileInfoDTO> result = adminFileService.getTrashFiles(pageParam);
        return Result.success(result);
    }

    /**
     * 清空回收站
     */
    @DeleteMapping("/trash/clear")
    @Operation(summary = "清空回收站", description = "永久删除回收站中的所有文件")
    public Result<Map<String, Object>> clearTrash() {
        Map<String, Object> result = adminFileService.clearTrash();
        return Result.success(result, "回收站清空完成");
    }

    /**
     * 获取文件统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取文件统计", description = "获取文件存储统计信息")
    public Result<Map<String, Object>> getFileStatistics() {
        Map<String, Object> statistics = adminFileService.getFileStatistics();
        return Result.success(statistics);
    }

    /**
     * 获取存储空间使用情况
     */
    @GetMapping("/storage-usage")
    @Operation(summary = "获取存储使用情况", description = "获取存储空间使用情况")
    public Result<Map<String, Object>> getStorageUsage() {
        Map<String, Object> usage = adminFileService.getStorageUsage();
        return Result.success(usage);
    }

    /**
     * 文件类型分布统计
     */
    @GetMapping("/type-distribution")
    @Operation(summary = "文件类型分布", description = "获取文件类型分布统计")
    public Result<List<Map<String, Object>>> getFileTypeDistribution() {
        List<Map<String, Object>> distribution = adminFileService.getFileTypeDistribution();
        return Result.success(distribution);
    }

    /**
     * 检查文件完整性
     */
    @PostMapping("/check-integrity")
    @Operation(summary = "检查文件完整性", description = "检查文件存储完整性")
    public Result<Map<String, Object>> checkFileIntegrity(
            @Parameter(description = "文件ID列表") @RequestBody(required = false) List<Long> fileIds) {

        Map<String, Object> result = adminFileService.checkFileIntegrity(fileIds);
        return Result.success(result, "文件完整性检查完成");
    }

    /**
     * 生成文件访问链接
     */
    @PostMapping("/{fileId}/access-url")
    @Operation(summary = "生成访问链接", description = "为文件生成临时访问链接")
    public Result<Map<String, Object>> generateAccessUrl(
            @Parameter(description = "文件ID") @PathVariable Long fileId,
            @Parameter(description = "有效期（秒）") @RequestParam(defaultValue = "3600") Integer expireSeconds) {

        if (expireSeconds <= 0 || expireSeconds > 86400) { // 最长24小时
            return Result.fail("有效期必须在1秒到24小时之间");
        }

        Map<String, Object> result = adminFileService.generateAccessUrl(fileId, expireSeconds);
        if (result != null) {
            return Result.success(result, "访问链接生成成功");
        } else {
            return Result.fail("生成访问链接失败");
        }
    }

    /**
     * 批量移动文件
     */
    @PutMapping("/batch-move")
    @Operation(summary = "批量移动文件", description = "批量移动文件到指定目录")
    public Result<Map<String, Object>> batchMoveFiles(
            @Parameter(description = "文件ID列表") @RequestParam List<Long> fileIds,
            @Parameter(description = "目标目录") @RequestParam String targetPath) {

        if (fileIds == null || fileIds.isEmpty()) {
            return Result.fail("请选择要移动的文件");
        }

        if (targetPath == null || targetPath.trim().isEmpty()) {
            return Result.fail("目标路径不能为空");
        }

        Map<String, Object> result = adminFileService.batchMoveFiles(fileIds, targetPath.trim());
        return Result.success(result, "批量移动完成");
    }

    /**
     * 文件搜索
     */
    @GetMapping("/search")
    @Operation(summary = "搜索文件", description = "根据条件搜索文件")
    public Result<IPage<FileInfoDTO>> searchFiles(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "文件类型") @RequestParam(required = false) String fileType,
            @Parameter(description = "最小文件大小") @RequestParam(required = false) Long minSize,
            @Parameter(description = "最大文件大小") @RequestParam(required = false) Long maxSize) {

        if (keyword == null || keyword.trim().isEmpty()) {
            return Result.fail("搜索关键词不能为空");
        }

        Page<FileInfoDTO> pageParam = new Page<>(page, size);
        IPage<FileInfoDTO> result = adminFileService.searchFiles(pageParam, keyword.trim(), fileType, minSize, maxSize);
        return Result.success(result);
    }
}
