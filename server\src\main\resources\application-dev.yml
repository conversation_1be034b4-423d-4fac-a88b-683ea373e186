server:
  port: 8081
  servlet:
    context-path: /api
  tomcat:
    uri-encoding: UTF-8

spring:
  application:
    name: PhotoTagMoment
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    lazy-initialization: false

  # 开发环境数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000

  # 开发环境Redis配置
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password: 123456
      database: 6
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms

  # 缓存配置
  cache:
    type: simple # 开发环境使用简单内存缓存

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 20MB

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

  # Flyway配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    out-of-order: true
    clean-disabled: true
    table: flyway_schema_history

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.phototagmoment.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: AUTO
      table-prefix: ptm_
      logic-delete-field: is_deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 开发环境日志配置
logging:
  level:
    root: info
    com.phototagmoment: debug
    org.apache.ibatis: warn
    com.baomidou.mybatisplus: warn
    com.phototagmoment.service.impl.SensitiveWordServiceImpl: info
    org.springframework.security: debug
    org.springframework.web: debug
  file:
    name: logs/phototagmoment-dev.log

# JWT配置
jwt:
  secret: phototagmoment_dev_secret_key_for_jwt_token_generation_and_validation
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer

# 加密配置
encryption:
  enabled: true
  user-data-key: ${ENCRYPTION_USER_DATA_KEY:dev_user_data_key}
  photo-key: ${ENCRYPTION_PHOTO_KEY:dev_photo_key}
  rsa-public-key: ${ENCRYPTION_RSA_PUBLIC_KEY:dev_rsa_public_key}
  rsa-private-key: ${ENCRYPTION_RSA_PRIVATE_KEY:dev_rsa_private_key}
  encrypted-user-fields: phone,email,realName,idCard

# Knife4j配置 - 开发环境启用
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-version: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Copyright © 2023 PhotoTagMoment
  documents:
    - group: 默认分组
      name: 接口说明文档
      locations: classpath:markdown/*
  production: false
  basic:
    enable: false
    username: admin
    password: 123456
  openapi:
    title: PhotoTagMoment API (开发环境)
    description: PhotoTagMoment照片社交网站API文档 - 开发环境
    email: <EMAIL>
    concat: PhotoTagMoment Dev Team
    url: http://localhost:8081
    version: v1.0.0-dev
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html

# OpenAPI配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.phototagmoment.controller
  group-configs:
    - group: 用户接口
      paths-to-match: /user/**, /auth/**, /photo/**, /tag/**, /search/**, /recommendation/**
      packages-to-scan: com.phototagmoment.controller
    - group: 管理接口
      paths-to-match: /admin/**
      packages-to-scan: com.phototagmoment.controller.admin

# 存储配置
storage:
  local:
    path: ./uploads/dev
