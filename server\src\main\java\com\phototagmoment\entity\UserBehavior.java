package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户行为实体类
 */
@Data
@TableName("ptm_user_behavior")
public class UserBehavior {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 照片ID
     */
    private Long photoId;

    /**
     * 行为类型（view, like, comment, collect）
     */
    private String behaviorType;

    /**
     * 行为权重（用于推荐算法计算）
     */
    private Double weight;

    /**
     * 行为时间
     */
    private LocalDateTime behaviorTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
