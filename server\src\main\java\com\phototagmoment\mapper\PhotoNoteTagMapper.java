package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.PhotoNoteTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 照片笔记标签Mapper接口
 */
@Mapper
public interface PhotoNoteTagMapper extends BaseMapper<PhotoNoteTag> {

    /**
     * 根据照片笔记ID查询标签列表
     *
     * @param noteId 照片笔记ID
     * @return 标签列表
     */
    @Select("SELECT tag_name FROM ptm_photo_note_tag WHERE note_id = #{noteId}")
    List<String> selectTagsByNoteId(@Param("noteId") Long noteId);

    /**
     * 批量插入照片笔记标签
     *
     * @param tags 标签列表
     * @return 影响行数
     */
    int batchInsert(@Param("tags") List<PhotoNoteTag> tags);

    /**
     * 删除照片笔记的所有标签
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    int deleteByNoteId(@Param("noteId") Long noteId);

    /**
     * 查询热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    @Select("SELECT ts.tag_name, ts.use_count, ts.hot_score " +
            "FROM ptm_tag_stats ts " +
            "ORDER BY ts.hot_score DESC, ts.use_count DESC " +
            "LIMIT #{limit}")
    List<String> selectHotTags(@Param("limit") Integer limit);

    /**
     * 根据标签名称模糊查询
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 标签列表
     */
    @Select("SELECT DISTINCT tag_name FROM ptm_photo_note_tag " +
            "WHERE tag_name LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY tag_name " +
            "LIMIT #{limit}")
    List<String> searchTags(@Param("keyword") String keyword, @Param("limit") Integer limit);
}
