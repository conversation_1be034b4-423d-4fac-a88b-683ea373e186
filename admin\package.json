{"name": "phototagmoment-admin", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.5.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "element-plus": "^2.3.12", "lodash-es": "^4.17.21", "phototagmoment-admin": "file:", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/lodash-es": "^4.17.9", "@types/node": "^20.6.0", "@types/path-browserify": "^1.0.3", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "sass": "^1.69.5", "sass-loader": "^13.3.2", "typescript": "^5.2.2", "vite": "^4.4.9", "vue-tsc": "^2.2.10"}}