package com.phototagmoment.controller.admin;

import com.phototagmoment.common.Result;
import com.phototagmoment.dto.AdminRoleDTO;
import com.phototagmoment.entity.AdminPermission;
import com.phototagmoment.entity.AdminRole;
import com.phototagmoment.service.NewAdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统管理员角色控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/system-role")
@Tag(name = "系统管理员角色", description = "系统管理员角色相关接口")
public class AdminSystemRoleController {

    @Autowired
    private NewAdminService adminService;

    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 角色ID
     */
    @PostMapping("/create")
    @Operation(summary = "创建角色", description = "创建新角色")
    public Result<Long> createRole(@RequestBody @Valid AdminRoleDTO roleDTO) {
        Long roleId = adminService.createRole(roleDTO);
        return Result.success(roleId);
    }

    /**
     * 更新角色
     *
     * @param id      角色ID
     * @param roleDTO 角色信息
     * @return 是否成功
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新角色", description = "更新角色信息")
    public Result<Boolean> updateRole(@Parameter(description = "角色ID") @PathVariable Long id,
                                     @RequestBody @Valid AdminRoleDTO roleDTO) {
        boolean result = adminService.updateRole(id, roleDTO);
        return Result.success(result);
    }

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色", description = "删除角色")
    public Result<Boolean> deleteRole(@Parameter(description = "角色ID") @PathVariable Long id) {
        boolean result = adminService.deleteRole(id);
        return Result.success(result);
    }

    /**
     * 获取角色列表
     *
     * @return 角色列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取角色列表", description = "获取所有角色列表")
    public Result<List<AdminRole>> getRoleList() {
        List<AdminRole> roleList = adminService.getRoleList();
        return Result.success(roleList);
    }

    /**
     * 获取权限列表
     *
     * @return 权限列表
     */
    @GetMapping("/permission/list")
    @Operation(summary = "获取权限列表", description = "获取所有权限列表")
    public Result<List<AdminPermission>> getPermissionList() {
        List<AdminPermission> permissionList = adminService.getPermissionList();
        return Result.success(permissionList);
    }

    /**
     * 获取角色权限
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @GetMapping("/{roleId}/permissions")
    @Operation(summary = "获取角色权限", description = "获取角色拥有的权限ID列表")
    public Result<List<Long>> getRolePermissions(@Parameter(description = "角色ID") @PathVariable Long roleId) {
        List<Long> permissionIds = adminService.getRolePermissions(roleId);
        return Result.success(permissionIds);
    }

    /**
     * 设置角色权限
     *
     * @param roleId        角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    @PostMapping("/{roleId}/permissions")
    @Operation(summary = "设置角色权限", description = "设置角色拥有的权限")
    public Result<Boolean> setRolePermissions(@Parameter(description = "角色ID") @PathVariable Long roleId,
                                             @RequestBody List<Long> permissionIds) {
        boolean result = adminService.setRolePermissions(roleId, permissionIds);
        return Result.success(result);
    }
}
