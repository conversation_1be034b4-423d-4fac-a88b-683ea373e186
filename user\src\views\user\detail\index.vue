<template>
  <div class="user-detail-container">
    <div class="user-header">
      <div class="user-info">
        <div class="avatar-container">
          <van-image
            round
            width="80"
            height="80"
            :src="userInfo.avatar || 'https://randomuser.me/api/portraits/men/1.jpg'"
            fit="cover"
          />
        </div>
        <div class="user-details">
          <h2 class="username">{{ userInfo.nickname || userInfo.username || '用户名' }}</h2>
          <p class="user-bio">{{ userInfo.bio || '这个人很懒，什么都没有留下' }}</p>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-value">{{ stats.photoCount }}</span>
              <span class="stat-label">照片</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ stats.followingCount }}</span>
              <span class="stat-label">关注</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ stats.followerCount }}</span>
              <span class="stat-label">粉丝</span>
            </div>
          </div>
        </div>
      </div>
      <div class="action-buttons">
        <van-button 
          :type="isFollowing ? 'default' : 'primary'" 
          size="small" 
          :icon="isFollowing ? 'cross' : 'plus'" 
          @click="toggleFollow"
        >
          {{ isFollowing ? '取消关注' : '关注' }}
        </van-button>
        <van-button type="default" size="small" icon="chat-o" @click="sendMessage">
          发消息
        </van-button>
      </div>
    </div>

    <div class="user-tabs">
      <van-tabs v-model:active="activeTab" animated swipeable>
        <van-tab title="照片">
          <div class="photo-grid" v-if="photos.length > 0">
            <div v-for="photo in photos" :key="photo.id" class="photo-item" @click="viewPhoto(photo.id)">
              <van-image
                :src="photo.thumbnailUrl"
                fit="cover"
                lazy-load
              />
            </div>
          </div>
          <div v-else class="empty-state">
            <van-empty description="暂无照片" />
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { showToast, showSuccessToast } from 'vant';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 用户ID
const userId = ref(route.params.id);

// 用户信息
const userInfo = reactive({
  id: 0,
  username: '',
  nickname: '',
  avatar: '',
  bio: ''
});

// 统计数据
const stats = reactive({
  photoCount: 0,
  followingCount: 0,
  followerCount: 0
});

// 当前标签页
const activeTab = ref(0);

// 照片列表
const photos = ref<any[]>([]);

// 是否已关注
const isFollowing = ref(false);

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    // 模拟API调用
    setTimeout(() => {
      Object.assign(userInfo, {
        id: Number(userId.value),
        username: `user${userId.value}`,
        nickname: `用户${userId.value}`,
        avatar: `https://randomuser.me/api/portraits/men/${Number(userId.value) % 100}.jpg`,
        bio: '热爱摄影，记录生活中的美好瞬间'
      });
    }, 500);
  } catch (error) {
    console.error('获取用户信息失败', error);
    showToast('获取用户信息失败');
  }
};

// 获取用户统计数据
const fetchUserStats = async () => {
  try {
    // 模拟API调用
    setTimeout(() => {
      stats.photoCount = Math.floor(Math.random() * 100);
      stats.followingCount = Math.floor(Math.random() * 200);
      stats.followerCount = Math.floor(Math.random() * 300);
    }, 500);
  } catch (error) {
    console.error('获取用户统计数据失败', error);
  }
};

// 获取用户照片
const fetchUserPhotos = async () => {
  try {
    // 模拟API调用
    setTimeout(() => {
      photos.value = Array.from({ length: 9 }, (_, i) => ({
        id: i + 1,
        thumbnailUrl: `https://picsum.photos/300/300?random=${i + Number(userId.value)}`,
        title: `照片 ${i + 1}`
      }));
    }, 800);
  } catch (error) {
    console.error('获取用户照片失败', error);
  }
};

// 检查是否已关注
const checkIsFollowing = async () => {
  try {
    // 模拟API调用
    setTimeout(() => {
      isFollowing.value = Math.random() > 0.5;
    }, 600);
  } catch (error) {
    console.error('检查关注状态失败', error);
  }
};

// 切换关注状态
const toggleFollow = async () => {
  if (!userStore.isLoggedIn) {
    router.push('/auth/login');
    return;
  }

  try {
    // 模拟API调用
    setTimeout(() => {
      isFollowing.value = !isFollowing.value;
      if (isFollowing.value) {
        stats.followerCount++;
        showSuccessToast('关注成功');
      } else {
        stats.followerCount--;
        showSuccessToast('已取消关注');
      }
    }, 300);
  } catch (error) {
    console.error('操作失败', error);
    showToast('操作失败，请稍后重试');
  }
};

// 发送消息
const sendMessage = () => {
  if (!userStore.isLoggedIn) {
    router.push('/auth/login');
    return;
  }
  
  router.push(`/messages?userId=${userId.value}`);
};

// 查看照片详情
const viewPhoto = (id: number) => {
  router.push(`/photo/${id}`);
};

// 生命周期钩子
onMounted(async () => {
  // 获取用户数据
  fetchUserInfo();
  fetchUserStats();
  fetchUserPhotos();
  
  if (userStore.isLoggedIn) {
    checkIsFollowing();
  }
});
</script>

<style lang="scss" scoped>
.user-detail-container {
  padding: 20px;
}

.user-header {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  margin-right: 20px;
}

.user-details {
  flex: 1;
}

.username {
  margin: 0 0 5px;
  font-size: 18px;
}

.user-bio {
  margin: 0 0 10px;
  font-size: 14px;
  color: #666;
}

.user-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-weight: bold;
  font-size: 16px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.user-tabs {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  padding: 4px;
}

.photo-item {
  aspect-ratio: 1;
  overflow: hidden;
  cursor: pointer;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

@media (min-width: 768px) {
  .user-detail-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .photo-grid {
    gap: 10px;
    padding: 10px;
  }
}
</style>
