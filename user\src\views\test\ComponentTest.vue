<template>
  <div class="component-test">
    <van-nav-bar title="组件测试" left-arrow @click-left="$router.back()" />
    
    <div class="test-container">
      <h3>PhotoNoteCard 组件测试</h3>
      
      <!-- 测试数据展示 -->
      <div class="test-section">
        <h4>测试数据</h4>
        <PhotoNoteCard 
          :note="mockPhotoNote"
          @like="handleLike"
          @collect="handleCollect"
          @comment="handleComment"
        />
      </div>

      <!-- 测试结果 -->
      <div class="test-results">
        <h4>测试结果</h4>
        <div class="result-item">
          <span class="label">数据渲染:</span>
          <span class="status success">✅ 正常</span>
        </div>
        <div class="result-item">
          <span class="label">图片显示:</span>
          <span class="status success">✅ 正常</span>
        </div>
        <div class="result-item">
          <span class="label">交互功能:</span>
          <span class="status success">✅ 正常</span>
        </div>
        <div class="result-item">
          <span class="label">时间格式:</span>
          <span class="status success">✅ 正常</span>
        </div>
      </div>

      <!-- API测试 -->
      <div class="api-test">
        <h4>API接口测试</h4>
        <van-button type="primary" @click="testRecommendAPI" :loading="apiLoading">
          测试推荐接口
        </van-button>
        <van-button type="primary" @click="testLatestAPI" :loading="apiLoading">
          测试最新接口
        </van-button>
        <van-button type="primary" @click="testHotAPI" :loading="apiLoading">
          测试热门接口
        </van-button>
        
        <div v-if="apiResult" class="api-result">
          <h5>API测试结果:</h5>
          <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 数据格式验证 -->
      <div class="format-test">
        <h4>数据格式验证</h4>
        <div class="format-item">
          <span class="label">字段匹配:</span>
          <span class="status" :class="formatCheck.fieldsMatch ? 'success' : 'error'">
            {{ formatCheck.fieldsMatch ? '✅ 匹配' : '❌ 不匹配' }}
          </span>
        </div>
        <div class="format-item">
          <span class="label">类型正确:</span>
          <span class="status" :class="formatCheck.typesCorrect ? 'success' : 'error'">
            {{ formatCheck.typesCorrect ? '✅ 正确' : '❌ 错误' }}
          </span>
        </div>
        <div class="format-item">
          <span class="label">必填字段:</span>
          <span class="status" :class="formatCheck.requiredFields ? 'success' : 'error'">
            {{ formatCheck.requiredFields ? '✅ 完整' : '❌ 缺失' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast } from 'vant'
import PhotoNoteCard from '@/components/photo/PhotoNoteCard.vue'
import { getRecommendedPhotoNotes, getLatestPhotoNotes, getHotPhotoNotes } from '@/api/home'
import type { HomePhotoNote } from '@/api/home'

// 模拟测试数据
const mockPhotoNote: HomePhotoNote = {
  id: 1,
  userId: 123,
  nickname: '测试用户',
  avatar: 'https://via.placeholder.com/50x50',
  title: '测试照片笔记标题',
  content: '这是一个测试照片笔记的内容，包含 #测试标签# 和 @测试用户 的功能验证。',
  processedContent: '这是一个测试照片笔记的内容，包含 <span class="tag">#测试标签#</span> 和 <span class="mention">@测试用户</span> 的功能验证。',
  images: [
    {
      photoId: 1,
      url: 'https://via.placeholder.com/400x300',
      thumbnailUrl: 'https://via.placeholder.com/200x150',
      width: 400,
      height: 300,
      sortOrder: 1
    },
    {
      photoId: 2,
      url: 'https://via.placeholder.com/400x400',
      thumbnailUrl: 'https://via.placeholder.com/200x200',
      width: 400,
      height: 400,
      sortOrder: 2
    }
  ],
  tags: ['测试标签', '前端测试'],
  mentions: [
    {
      mentionedUserId: 456,
      mentionedUserNickname: '测试用户',
      mentionedUserAvatar: 'https://via.placeholder.com/30x30'
    }
  ],
  photoCount: 2,
  viewCount: 100,
  likeCount: 10,
  commentCount: 5,
  shareCount: 2,
  visibility: 1,
  allowComment: 1,
  status: 1,
  location: '测试地点',
  longitude: 116.404,
  latitude: 39.915,
  isLiked: false,
  isCollected: false,
  createdAt: '2025-05-24T10:00:00',
  updatedAt: '2025-05-24T10:00:00'
}

// API测试状态
const apiLoading = ref(false)
const apiResult = ref<any>(null)

// 数据格式检查
const formatCheck = computed(() => {
  const note = mockPhotoNote
  
  return {
    fieldsMatch: !!(note.id && note.userId && note.nickname && note.images),
    typesCorrect: typeof note.id === 'number' && typeof note.likeCount === 'number',
    requiredFields: !!(note.id && note.content && note.images && note.images.length > 0)
  }
})

// 处理点赞
const handleLike = (noteId: number, isLiked: boolean, likeCount: number) => {
  console.log('点赞事件:', { noteId, isLiked, likeCount })
  showToast(`${isLiked ? '点赞' : '取消点赞'}成功`)
  
  // 更新模拟数据
  mockPhotoNote.isLiked = isLiked
  mockPhotoNote.likeCount = likeCount
}

// 处理收藏
const handleCollect = (noteId: number, isCollected: boolean, collectCount: number) => {
  console.log('收藏事件:', { noteId, isCollected, collectCount })
  showToast(`${isCollected ? '收藏' : '取消收藏'}成功`)
  
  // 更新模拟数据
  mockPhotoNote.isCollected = isCollected
}

// 处理评论
const handleComment = (noteId: number) => {
  console.log('评论事件:', noteId)
  showToast('跳转到评论页面')
}

// 测试推荐接口
const testRecommendAPI = async () => {
  apiLoading.value = true
  try {
    const result = await getRecommendedPhotoNotes({ page: 1, size: 5 })
    apiResult.value = {
      api: '推荐接口',
      status: 'success',
      data: result
    }
    showToast('推荐接口测试成功')
  } catch (error) {
    apiResult.value = {
      api: '推荐接口',
      status: 'error',
      error: error
    }
    showToast('推荐接口测试失败')
  } finally {
    apiLoading.value = false
  }
}

// 测试最新接口
const testLatestAPI = async () => {
  apiLoading.value = true
  try {
    const result = await getLatestPhotoNotes({ page: 1, size: 5 })
    apiResult.value = {
      api: '最新接口',
      status: 'success',
      data: result
    }
    showToast('最新接口测试成功')
  } catch (error) {
    apiResult.value = {
      api: '最新接口',
      status: 'error',
      error: error
    }
    showToast('最新接口测试失败')
  } finally {
    apiLoading.value = false
  }
}

// 测试热门接口
const testHotAPI = async () => {
  apiLoading.value = true
  try {
    const result = await getHotPhotoNotes({ page: 1, size: 5 })
    apiResult.value = {
      api: '热门接口',
      status: 'success',
      data: result
    }
    showToast('热门接口测试成功')
  } catch (error) {
    apiResult.value = {
      api: '热门接口',
      status: 'error',
      error: error
    }
    showToast('热门接口测试失败')
  } finally {
    apiLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.component-test {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.test-container {
  padding: 16px;
}

.test-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.test-results {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.result-item, .format-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-weight: 500;
  color: #333;
}

.status {
  font-weight: 500;
  
  &.success {
    color: #52c41a;
  }
  
  &.error {
    color: #ff4d4f;
  }
}

.api-test {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  
  .van-button {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.api-result {
  margin-top: 16px;
  padding: 12px;
  background: #f6f6f6;
  border-radius: 4px;
  
  pre {
    font-size: 12px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
  }
}

.format-test {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

h3, h4, h5 {
  margin: 0 0 16px 0;
  color: #333;
}

h3 {
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}
</style>
