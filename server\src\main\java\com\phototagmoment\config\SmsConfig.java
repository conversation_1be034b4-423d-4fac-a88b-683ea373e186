package com.phototagmoment.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.phototagmoment.service.SystemConfigService;

import javax.annotation.PostConstruct;

/**
 * 短信服务配置类
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {

    /**
     * 系统配置服务
     */
    @Autowired
    private SystemConfigService configService;

    /**
     * 是否启用短信服务
     */
    private boolean enabled = false;

    /**
     * 短信服务提供商（aliyun, tencent, etc.）
     */
    private String provider = "aliyun";

    /**
     * 阿里云短信配置
     */
    private Aliyun aliyun = new Aliyun();

    /**
     * 腾讯云短信配置
     */
    private Tencent tencent = new Tencent();

    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        log.info("初始化短信服务配置");

        try {
            // 从数据库读取配置
            this.enabled = configService.getBooleanValue("sms.enabled", false);
            this.provider = configService.getConfigValue("sms.provider", "aliyun");

            // 阿里云短信配置
            this.aliyun.setAccessKeyId(configService.getConfigValue("sms.aliyun.access-key-id", ""));
            this.aliyun.setAccessKeySecret(configService.getConfigValue("sms.aliyun.access-key-secret", ""));
            this.aliyun.setSignName(configService.getConfigValue("sms.aliyun.sign-name", ""));
            this.aliyun.setTemplateCode(configService.getConfigValue("sms.aliyun.template-code", ""));

            // 验证码配置
            this.verificationCode.setLength(configService.getIntValue("sms.verification-code.length", 6));
            this.verificationCode.setExpiration(configService.getIntValue("sms.verification-code.expiration", 300));
            this.verificationCode.setDailyLimit(configService.getIntValue("sms.verification-code.daily-limit", 10));
            this.verificationCode.setInterval(configService.getIntValue("sms.verification-code.interval", 60));

            log.info("从数据库读取短信服务配置成功");
        } catch (Exception e) {
            log.error("从数据库读取短信服务配置失败，将使用默认配置: {}", e.getMessage());
        }

        log.info("短信服务配置: enabled={}, provider={}", this.enabled, this.provider);
    }

    /**
     * 验证码配置
     */
    private VerificationCode verificationCode = new VerificationCode();

    /**
     * 阿里云短信配置
     */
    @Data
    public static class Aliyun {
        /**
         * 访问密钥ID
         */
        private String accessKeyId;

        /**
         * 访问密钥密码
         */
        private String accessKeySecret;

        /**
         * 短信签名
         */
        private String signName;

        /**
         * 短信模板代码
         */
        private String templateCode;

        /**
         * 地域ID
         */
        private String regionId = "cn-hangzhou";
    }

    /**
     * 腾讯云短信配置
     */
    @Data
    public static class Tencent {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appKey;

        /**
         * 短信签名
         */
        private String signName;

        /**
         * 短信模板ID
         */
        private String templateId;

        /**
         * SDK AppID
         */
        private String sdkAppId;
    }

    /**
     * 验证码配置
     */
    @Data
    public static class VerificationCode {
        /**
         * 验证码长度
         */
        private int length = 6;

        /**
         * 验证码有效期（秒）
         */
        private int expiration = 300;

        /**
         * 同一手机号每日最大发送次数
         */
        private int dailyLimit = 10;

        /**
         * 同一手机号发送间隔（秒）
         */
        private int interval = 60;

        /**
         * 验证码类型（纯数字：number，数字字母混合：mixed）
         */
        private String type = "number";
    }
}
