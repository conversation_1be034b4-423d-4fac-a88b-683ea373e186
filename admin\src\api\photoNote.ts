import request from '@/utils/request'

// 照片笔记查询参数接口
interface PhotoNoteListParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: number
  startDate?: string
  endDate?: string
  userId?: number
  [key: string]: any
}

// 照片笔记数据接口
interface PhotoNoteData {
  id: number
  title?: string
  description?: string
  status?: number
  userId?: number
  userName?: string
  createTime?: string
  updateTime?: string
  auditTime?: string
  rejectReason?: string
  [key: string]: any
}

// 审核数据接口
interface AuditData {
  status: number
  rejectReason?: string
}

// 统计数据接口
interface PhotoNoteStatsData {
  totalCount: number
  pendingCount: number
  approvedCount: number
  rejectedCount: number
  todayCount: number
  [key: string]: any
}

/**
 * 获取照片笔记列表（管理端）
 * @param params 查询参数
 * @returns Promise
 */
export function getPhotoNoteList(params: PhotoNoteListParams) {
  return request({
    url: '/admin/photo-notes/list',
    method: 'get',
    params
  })
}

/**
 * 获取照片笔记详情（管理端）
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function getPhotoNoteDetail(noteId: number) {
  return request({
    url: `/admin/photo-notes/${noteId}`,
    method: 'get'
  })
}

/**
 * 审核照片笔记
 * @param noteId 照片笔记ID
 * @param status 审核状态：1-通过，2-拒绝
 * @param rejectReason 拒绝原因（状态为拒绝时必填）
 * @returns Promise
 */
export function auditPhotoNote(noteId: number, status: number, rejectReason?: string) {
  return request({
    url: `/admin/photo-notes/${noteId}/audit`,
    method: 'post',
    params: {
      status,
      rejectReason
    }
  })
}

/**
 * 删除照片笔记（管理端）
 * @param noteId 照片笔记ID
 * @returns Promise
 */
export function deletePhotoNote(noteId: number) {
  return request({
    url: `/admin/photo-notes/${noteId}`,
    method: 'delete'
  })
}

/**
 * 获取待审核照片笔记
 * @param params 查询参数
 * @returns Promise
 */
export function getPendingPhotoNotes(params: PhotoNoteListParams) {
  return request({
    url: '/admin/photo-notes/pending',
    method: 'get',
    params
  })
}

/**
 * 获取审核拒绝的照片笔记
 * @param params 查询参数
 * @returns Promise
 */
export function getRejectedPhotoNotes(params: PhotoNoteListParams) {
  return request({
    url: '/admin/photo-notes/rejected',
    method: 'get',
    params
  })
}

/**
 * 获取照片笔记统计信息
 * @returns Promise
 */
export function getPhotoNoteStats() {
  return request({
    url: '/admin/photo-notes/stats',
    method: 'get'
  })
}

// 导出类型定义
export type {
  PhotoNoteListParams,
  PhotoNoteData,
  AuditData,
  PhotoNoteStatsData
}
