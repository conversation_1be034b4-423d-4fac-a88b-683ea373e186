package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.phototagmoment.dto.AuthLoginDTO;
import com.phototagmoment.dto.AuthUserInfoDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.security.JwtTokenProvider;
import com.phototagmoment.service.AuthService;
import com.phototagmoment.service.ThirdPartyLoginService;
import com.phototagmoment.vo.TokenVO;
import com.phototagmoment.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 第三方登录服务实现类
 */
@Slf4j
@Service
public class ThirdPartyLoginServiceImpl implements ThirdPartyLoginService {

    @Autowired
    private AuthService authService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserAuthMapper userAuthMapper;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TokenVO login(String source, AuthCallback callback) {
        // 处理登录
        AuthLoginDTO loginResult = authService.login(source, callback);
        if (!loginResult.isSuccess()) {
            throw new BusinessException(source + "登录失败: " + loginResult.getErrorMsg());
        }

        // 获取用户信息
        AuthUserInfoDTO userInfo = authService.getUserInfo(source, loginResult.getAccessToken(), loginResult.getOpenId());
        if (userInfo == null) {
            throw new BusinessException("获取" + source + "用户信息失败");
        }

        // 查询用户是否已存在
        UserAuth userAuth = userAuthMapper.selectByTypeAndIdentifier(source, userInfo.getOpenId());
        User user;

        if (userAuth == null) {
            // 用户不存在，创建新用户
            user = createUserFromAuthInfo(userInfo);
            userMapper.insert(user);
            // 创建用户认证信息
            createUserAuth(user.getId(), source, userInfo.getOpenId(), loginResult.getAccessToken());
        } else {
            // 用户已存在，更新用户信息
            user = userMapper.selectById(userAuth.getUserId());
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            // 更新用户认证信息
            updateUserAuth(userAuth, loginResult.getAccessToken());
        }

        // 生成JWT
        String token = jwtTokenProvider.generateToken(user.getUsername(), user.getId());

        // 更新用户最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userMapper.updateById(user);

        // 构建返回对象
        return buildTokenVO(token, user);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bind(Long userId, String source, String openId, String accessToken, AuthUserInfoDTO userInfo) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查第三方账号是否已被绑定
        UserAuth existingAuth = userAuthMapper.selectByTypeAndIdentifier(source, openId);
        if (existingAuth != null && !existingAuth.getUserId().equals(userId)) {
            throw new BusinessException("该" + source + "账号已被其他用户绑定");
        }

        // 检查用户是否已绑定该类型的第三方账号
        UserAuth userAuth = userAuthMapper.selectByUserIdAndType(userId, source);
        if (userAuth != null) {
            // 更新绑定信息
            userAuth.setIdentifier(openId);
            userAuth.setCredential(accessToken);
            userAuth.setUpdatedAt(LocalDateTime.now());
            userAuthMapper.updateById(userAuth);
        } else {
            // 创建新的绑定信息
            createUserAuth(userId, source, openId, accessToken);
        }

        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbind(Long userId, String source) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查用户是否已绑定该类型的第三方账号
        UserAuth userAuth = userAuthMapper.selectByUserIdAndType(userId, source);
        if (userAuth == null) {
            throw new BusinessException("用户未绑定" + source + "账号");
        }

        // 删除绑定信息
        userAuthMapper.deleteById(userAuth.getId());

        return true;
    }



    @Override
    public List<String> getBindingList(Long userId) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 查询用户绑定的第三方账号
        LambdaQueryWrapper<UserAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAuth::getUserId, userId)
                .in(UserAuth::getIdentityType, "qq", "wechat", "wechat_mini");
        List<UserAuth> userAuths = userAuthMapper.selectList(queryWrapper);

        // 构建返回结果
        List<String> bindingList = new ArrayList<>();
        for (UserAuth userAuth : userAuths) {
            bindingList.add(userAuth.getIdentityType());
        }

        return bindingList;
    }

    /**
     * 从第三方用户信息创建用户
     */
    private User createUserFromAuthInfo(AuthUserInfoDTO userInfo) {
        User user = new User();

        // 生成随机用户名
        String username = userInfo.getSource() + "_" + userInfo.getOpenId().substring(0, 8);
        user.setUsername(username);

        // 设置昵称
        if (StringUtils.hasText(userInfo.getNickname())) {
            user.setNickname(userInfo.getNickname());
        } else {
            user.setNickname(username);
        }

        // 设置头像
        if (StringUtils.hasText(userInfo.getAvatarLarge())) {
            user.setAvatar(userInfo.getAvatarLarge());
        } else if (StringUtils.hasText(userInfo.getAvatarMedium())) {
            user.setAvatar(userInfo.getAvatarMedium());
        } else if (StringUtils.hasText(userInfo.getAvatarSmall())) {
            user.setAvatar(userInfo.getAvatarSmall());
        }

        // 设置性别
        if (StringUtils.hasText(userInfo.getGender())) {
            user.setGender("男".equals(userInfo.getGender()) ? 1 : ("女".equals(userInfo.getGender()) ? 2 : 0));
        } else {
            user.setGender(0);
        }

        user.setStatus(1);
        user.setIsVerified(0);
        user.setIsAdmin(0);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        user.setLastLoginTime(LocalDateTime.now());
        return user;
    }



    /**
     * 创建用户认证信息
     */
    private void createUserAuth(Long userId, String identityType, String identifier, String credential) {
        UserAuth userAuth = new UserAuth();
        userAuth.setUserId(userId);
        userAuth.setIdentityType(identityType);
        userAuth.setIdentifier(identifier);
        userAuth.setCredential(credential);
        userAuth.setVerified(1);
        userAuth.setCreatedAt(LocalDateTime.now());
        userAuth.setUpdatedAt(LocalDateTime.now());
        userAuthMapper.insert(userAuth);
        log.info("创建用户认证信息成功: userId={}, identityType={}, identifier={}", userId, identityType, identifier);
    }

    /**
     * 更新用户认证信息
     */
    private void updateUserAuth(UserAuth userAuth, String credential) {
        userAuth.setCredential(credential);
        userAuth.setUpdatedAt(LocalDateTime.now());
        userAuthMapper.updateById(userAuth);
    }

    /**
     * 构建TokenVO
     */
    private TokenVO buildTokenVO(String token, User user) {
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);

        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(tokenPrefix + " " + token);
        tokenVO.setExpiresIn(jwtExpiration / 1000);
        tokenVO.setUser(userVO);

        return tokenVO;
    }
}
