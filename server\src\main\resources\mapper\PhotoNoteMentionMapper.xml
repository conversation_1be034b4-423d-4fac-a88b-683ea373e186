<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.PhotoNoteMentionMapper">

    <!-- 批量插入照片笔记@用户 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ptm_photo_note_mention (note_id, mentioned_user_id, mention_user_id, created_at)
        VALUES
        <foreach collection="mentions" item="item" separator=",">
            (#{item.noteId}, #{item.mentionedUserId}, #{item.mentionUserId}, #{item.createdAt})
        </foreach>
    </insert>

    <!-- 删除照片笔记的所有@用户 -->
    <delete id="deleteByNoteId">
        DELETE FROM ptm_photo_note_mention WHERE note_id = #{noteId}
    </delete>

</mapper>
