package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.dto.SensitiveWordDTO;
import com.phototagmoment.entity.SensitiveWord;
import com.phototagmoment.vo.SensitiveWordStatVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 敏感词Service接口
 */
public interface SensitiveWordService {

    /**
     * 获取所有敏感词
     *
     * @return 敏感词列表
     */
    List<SensitiveWord> listAllWords();

    /**
     * 分页获取敏感词
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @param keyword  关键字
     * @param type     类型
     * @return 分页结果
     */
    IPage<SensitiveWord> pageWords(int page, int pageSize, String keyword, String type);

    /**
     * 根据ID获取敏感词
     *
     * @param id 敏感词ID
     * @return 敏感词
     */
    SensitiveWord getWordById(Long id);

    /**
     * 保存敏感词
     *
     * @param word 敏感词
     * @return 是否成功
     */
    boolean saveWord(SensitiveWord word);

    /**
     * 批量保存敏感词
     *
     * @param words 敏感词列表
     * @return 是否成功
     */
    boolean batchSaveWord(List<SensitiveWord> words);

    /**
     * 更新敏感词
     *
     * @param word 敏感词
     * @return 是否成功
     */
    boolean updateWord(SensitiveWord word);

    /**
     * 删除敏感词
     *
     * @param id 敏感词ID
     * @return 是否成功
     */
    boolean deleteWord(Long id);

    /**
     * 批量删除敏感词
     *
     * @param ids 敏感词ID列表
     * @return 是否成功
     */
    boolean batchDeleteWord(List<Long> ids);

    /**
     * 检查文本是否包含敏感词
     *
     * @param text 文本
     * @return 是否包含敏感词
     */
    boolean containsSensitiveWord(String text);

    /**
     * 获取文本中的敏感词
     *
     * @param text 文本
     * @return 敏感词集合
     */
    Set<String> getSensitiveWords(String text);

    /**
     * 替换文本中的敏感词
     *
     * @param text        文本
     * @param replaceChar 替换字符
     * @return 替换后的文本
     */
    String replaceSensitiveWords(String text, char replaceChar);

    /**
     * 替换文本中的敏感词
     *
     * @param text 文本
     * @return 替换后的文本
     */
    String replaceSensitiveWords(String text);

    /**
     * 初始化敏感词库
     */
    void initSensitiveWordMap();

    /**
     * 刷新敏感词库
     */
    void refreshSensitiveWordMap();

    /**
     * 获取敏感词列表
     *
     * @param page     页码
     * @param pageSize 每页条数
     * @param keyword  关键字
     * @param type     类型
     * @param level    级别
     * @return 敏感词列表
     */
    IPage<SensitiveWord> getSensitiveWordList(Integer page, Integer pageSize, String keyword, String type, Integer level);

    /**
     * 获取敏感词详情
     *
     * @param id 敏感词ID
     * @return 敏感词详情
     */
    SensitiveWord getSensitiveWordById(Long id);

    /**
     * 创建敏感词
     *
     * @param sensitiveWordDTO 敏感词信息
     * @return 敏感词ID
     */
    Long createSensitiveWord(SensitiveWordDTO sensitiveWordDTO);

    /**
     * 更新敏感词
     *
     * @param id              敏感词ID
     * @param sensitiveWordDTO 敏感词信息
     * @return 是否成功
     */
    boolean updateSensitiveWord(Long id, SensitiveWordDTO sensitiveWordDTO);

    /**
     * 删除敏感词
     *
     * @param id 敏感词ID
     * @return 是否成功
     */
    boolean deleteSensitiveWord(Long id);

    /**
     * 更新敏感词状态
     *
     * @param id     敏感词ID
     * @param status 状态：false-禁用，true-启用
     * @return 是否成功
     */
    boolean updateSensitiveWordStatus(Long id, Boolean status);

    /**
     * 获取敏感词类型列表
     *
     * @return 敏感词类型列表
     */
    List<String> getSensitiveWordTypes();

    /**
     * 获取敏感词统计信息
     *
     * @return 敏感词统计信息
     */
    SensitiveWordStatVO getSensitiveWordStats();

    /**
     * 批量导入敏感词
     *
     * @param file 敏感词文件
     * @return 导入结果
     */
    Map<String, Object> importSensitiveWords(MultipartFile file);

    /**
     * 导出敏感词
     *
     * @param type     类型
     * @param level    级别
     * @param response HTTP响应
     */
    void exportSensitiveWords(String type, Integer level, HttpServletResponse response);

    /**
     * 测试敏感词过滤
     *
     * @param content 待测试内容
     * @return 过滤结果
     */
    Map<String, Object> testSensitiveWordFilter(String content);
}
