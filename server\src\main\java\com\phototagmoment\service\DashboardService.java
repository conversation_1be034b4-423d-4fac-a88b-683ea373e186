package com.phototagmoment.service;

import com.phototagmoment.dto.DashboardStatsDTO;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.UserDTO;

import java.util.List;
import java.util.Map;

/**
 * 控制台服务接口
 */
public interface DashboardService {

    /**
     * 获取控制台统计数据
     *
     * @return 统计数据
     */
    DashboardStatsDTO getDashboardStats();

    /**
     * 获取用户增长趋势
     *
     * @param period 周期类型：week/month/year
     * @return 趋势数据
     */
    Map<String, Object> getUserGrowthTrend(String period);

    /**
     * 获取内容分布
     *
     * @return 内容分布数据
     */
    List<Map<String, Object>> getContentDistribution();

    /**
     * 获取最新用户
     *
     * @param limit 数量限制
     * @return 最新用户列表
     */
    List<UserDTO> getLatestUsers(Integer limit);

    /**
     * 获取最新照片
     *
     * @param limit 数量限制
     * @return 最新照片列表
     */
    List<PhotoNoteDTO> getLatestPhotos(Integer limit);

    /**
     * 获取系统信息
     *
     * @return 系统信息
     */
    Map<String, Object> getSystemInfo();
}
