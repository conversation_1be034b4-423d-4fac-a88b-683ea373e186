package com.phototagmoment.common;

/**
 * 常用API返回码
 */
public enum ResultCode implements IErrorCode {
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    VALIDATE_FAILED(400, "参数检验失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),
    NOT_FOUND(404, "资源不存在"),

    // 用户相关错误码 1000-1999
    USER_NOT_EXIST(1001, "用户不存在"),
    USERNAME_OR_PASSWORD_ERROR(1002, "用户名或密码错误"),
    USER_ACCOUNT_EXPIRED(1003, "账号已过期"),
    USER_ACCOUNT_LOCKED(1004, "账号已被锁定"),
    USER_ACCOUNT_DISABLED(1005, "账号已被禁用"),
    DUPLICATE_USERNAME(1006, "用户名已存在"),
    DUPLICATE_PHONE(1007, "手机号已存在"),
    DUPLICATE_EMAIL(1008, "邮箱已存在"),
    VERIFICATION_CODE_ERROR(1009, "验证码错误或已过期"),
    PERMISSION_DENIED(1010, "权限不足"),
    PARAM_ERROR(1011, "参数错误"),

    // 照片相关错误码 2000-2999
    PHOTO_NOT_EXIST(2001, "照片不存在"),
    PHOTO_UPLOAD_FAILED(2002, "照片上传失败"),
    PHOTO_DELETE_FAILED(2003, "照片删除失败"),
    PHOTO_UPDATE_FAILED(2004, "照片更新失败"),
    PHOTO_PERMISSION_DENIED(2005, "没有权限操作该照片"),

    // 评论相关错误码 3000-3999
    COMMENT_NOT_EXIST(3001, "评论不存在"),
    COMMENT_CREATE_FAILED(3002, "评论创建失败"),
    COMMENT_DELETE_FAILED(3003, "评论删除失败"),
    COMMENT_PERMISSION_DENIED(3004, "没有权限操作该评论"),

    // 系统相关错误码 9000-9999
    SYSTEM_ERROR(9001, "系统错误"),
    FILE_UPLOAD_ERROR(9002, "文件上传错误"),
    FILE_TYPE_ERROR(9003, "文件类型错误"),
    FILE_SIZE_LIMIT(9004, "文件大小超出限制");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
