package com.phototagmoment.service;

import com.phototagmoment.dto.IdentityVerificationDTO;
import com.phototagmoment.entity.IdentityVerification;

/**
 * 实名认证服务接口
 */
public interface IdentityVerificationService {

    /**
     * 提交实名认证信息
     *
     * @param userId 用户ID
     * @param dto    实名认证信息
     * @return 认证记录ID
     */
    Long submitVerification(Long userId, IdentityVerificationDTO dto);

    /**
     * 获取用户实名认证信息
     *
     * @param userId 用户ID
     * @return 实名认证信息
     */
    IdentityVerification getUserVerification(Long userId);

    /**
     * 检查用户是否已实名认证
     *
     * @param userId 用户ID
     * @return 是否已实名认证
     */
    boolean isUserVerified(Long userId);

    /**
     * 通过支付宝实名认证
     *
     * @param userId 用户ID
     * @param name   真实姓名
     * @param idCard 身份证号
     * @return 认证结果
     */
    boolean verifyByAlipay(Long userId, String name, String idCard);

    /**
     * 通过微信实名认证
     *
     * @param userId 用户ID
     * @param name   真实姓名
     * @param idCard 身份证号
     * @return 认证结果
     */
    boolean verifyByWechat(Long userId, String name, String idCard);

    /**
     * 通过人脸识别实名认证
     *
     * @param userId 用户ID
     * @param name   真实姓名
     * @param idCard 身份证号
     * @param faceImageUrl 人脸图片URL
     * @return 认证结果
     */
    boolean verifyByFaceRecognition(Long userId, String name, String idCard, String faceImageUrl);

    /**
     * 管理员审核实名认证
     *
     * @param verificationId 认证记录ID
     * @param status         审核状态（1：通过，2：拒绝）
     * @param reason         拒绝原因
     * @return 审核结果
     */
    boolean reviewVerification(Long verificationId, Integer status, String reason);
}
