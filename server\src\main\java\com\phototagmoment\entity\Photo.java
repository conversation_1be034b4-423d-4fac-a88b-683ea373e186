package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 照片实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ptm_photo")
public class Photo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 照片ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 照片分组ID
     */
    private String groupId;

    /**
     * 照片标题
     */
    private String title;

    /**
     * 照片描述
     */
    private String description;

    /**
     * 照片URL
     */
    private String url;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 照片存储路径
     */
    private String storagePath;

    /**
     * 原始文件名
     */
    private String originalFilename;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 照片宽度
     */
    private Integer width;

    /**
     * 照片高度
     */
    private Integer height;

    /**
     * 拍摄地点
     */
    private String location;

    /**
     * 拍摄时间
     */
    private LocalDateTime takenTime;

    /**
     * 可见性: 0-私密, 1-公开, 2-好友可见
     */
    private Integer visibility;

    /**
     * 是否允许评论: 0-不允许, 1-允许
     */
    private Integer allowComment;

    /**
     * 是否允许下载: 0-不允许, 1-允许
     */
    private Integer allowDownload;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 浏览数
     */
    private Integer viewCount;

    /**
     * 状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除
     */
    private Integer status;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
