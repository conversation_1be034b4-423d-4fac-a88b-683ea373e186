<template>
  <div class="user-detail-container">
    <van-nav-bar
      title="用户主页"
      left-arrow
      @click-left="goBack"
      fixed
    />

    <div class="user-detail-content">
      <div class="user-header">
        <div class="user-info">
          <div class="avatar">
            <van-image
              round
              width="80"
              height="80"
              :src="user.avatar || 'https://randomuser.me/api/portraits/men/1.jpg'"
              fit="cover"
            />
          </div>
          <div class="user-details">
            <h2 class="username">{{ user.nickname || user.username }}</h2>
            <p class="user-bio" v-if="user.bio">{{ user.bio }}</p>
            <div class="user-stats">
              <div class="stat-item" @click="activeTab = 0">
                <span class="stat-value">{{ stats.photoCount }}</span>
                <span class="stat-label">照片</span>
              </div>
              <div class="stat-item" @click="showFollowing = true">
                <span class="stat-value">{{ stats.followingCount }}</span>
                <span class="stat-label">关注</span>
              </div>
              <div class="stat-item" @click="showFollowers = true">
                <span class="stat-value">{{ stats.followerCount }}</span>
                <span class="stat-label">粉丝</span>
              </div>
            </div>
          </div>
        </div>
        <div class="action-buttons">
          <van-button
            v-if="!isCurrentUser && !isFollowing"
            type="primary"
            size="small"
            icon="plus"
            @click="followUser"
          >
            关注
          </van-button>
          <van-button
            v-if="!isCurrentUser && isFollowing"
            plain
            size="small"
            @click="unfollowUser"
          >
            已关注
          </van-button>
          <van-button
            v-if="!isCurrentUser"
            plain
            size="small"
            icon="chat-o"
            @click="sendMessage"
          >
            发消息
          </van-button>
          <van-button
            v-if="isCurrentUser"
            type="primary"
            size="small"
            icon="setting-o"
            to="/settings/profile"
          >
            编辑资料
          </van-button>
        </div>
      </div>

      <div class="user-tabs">
        <van-tabs v-model="activeTab" animated swipeable>
          <van-tab title="照片">
            <div class="photo-grid" v-if="photos.length > 0">
              <div
                v-for="photo in photos"
                :key="photo.id"
                class="photo-item"
                @click="viewPhoto(photo.id)"
              >
                <van-image
                  :src="photo.thumbnailUrl"
                  fit="cover"
                  lazy-load
                />
                <div class="photo-info">
                  <div class="photo-title">{{ photo.title }}</div>
                  <div class="photo-stats">
                    <span class="stat-item">
                      <van-icon name="like-o" />
                      {{ photo.likeCount }}
                    </span>
                    <span class="stat-item">
                      <van-icon name="chat-o" />
                      {{ photo.commentCount }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else-if="!loading" class="empty-state">
              <van-empty description="暂无照片" />
            </div>
            <div v-else class="loading-state">
              <van-loading type="spinner" color="#3498db" />
            </div>
          </van-tab>
          <van-tab title="收藏">
            <div class="photo-grid" v-if="collections.length > 0">
              <div
                v-for="photo in collections"
                :key="photo.id"
                class="photo-item"
                @click="viewPhoto(photo.id)"
              >
                <van-image
                  :src="photo.thumbnailUrl"
                  fit="cover"
                  lazy-load
                />
                <div class="photo-info">
                  <div class="photo-title">{{ photo.title }}</div>
                </div>
              </div>
            </div>
            <div v-else-if="!loading" class="empty-state">
              <van-empty description="暂无收藏" />
            </div>
            <div v-else class="loading-state">
              <van-loading type="spinner" color="#3498db" />
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>

    <!-- 关注列表弹窗 -->
    <van-popup
      v-model:show="showFollowing"
      position="bottom"
      round
      :style="{ height: '70%' }"
    >
      <div class="user-list-popup">
        <div class="popup-header">
          <div class="popup-title">关注列表</div>
          <van-icon name="cross" @click="showFollowing = false" />
        </div>
        <div class="user-list">
          <div
            v-for="user in followingList"
            :key="user.id"
            class="user-item"
            @click="goToUserProfile(user.id)"
          >
            <van-image
              round
              width="40"
              height="40"
              :src="user.avatar"
              fit="cover"
            />
            <div class="user-info">
              <div class="user-name">{{ user.nickname || user.username }}</div>
              <div class="user-followers">{{ user.followerCount }} 粉丝</div>
            </div>
            <van-button
              v-if="user.id !== currentUserId && !user.isFollowing"
              type="primary"
              size="small"
              @click.stop="followUserInList(user)"
            >
              关注
            </van-button>
            <van-button
              v-if="user.id !== currentUserId && user.isFollowing"
              plain
              size="small"
              @click.stop="unfollowUserInList(user)"
            >
              已关注
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 粉丝列表弹窗 -->
    <van-popup
      v-model:show="showFollowers"
      position="bottom"
      round
      :style="{ height: '70%' }"
    >
      <div class="user-list-popup">
        <div class="popup-header">
          <div class="popup-title">粉丝列表</div>
          <van-icon name="cross" @click="showFollowers = false" />
        </div>
        <div class="user-list">
          <div
            v-for="user in followerList"
            :key="user.id"
            class="user-item"
            @click="goToUserProfile(user.id)"
          >
            <van-image
              round
              width="40"
              height="40"
              :src="user.avatar"
              fit="cover"
            />
            <div class="user-info">
              <div class="user-name">{{ user.nickname || user.username }}</div>
              <div class="user-followers">{{ user.followerCount }} 粉丝</div>
            </div>
            <van-button
              v-if="user.id !== currentUserId && !user.isFollowing"
              type="primary"
              size="small"
              @click.stop="followUserInList(user)"
            >
              关注
            </van-button>
            <van-button
              v-if="user.id !== currentUserId && user.isFollowing"
              plain
              size="small"
              @click.stop="unfollowUserInList(user)"
            >
              已关注
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { showToast, showSuccessToast } from 'vant';
import { useUserStore } from '@/stores/user';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 用户ID
const userId = computed(() => Number(route.params.id));

// 当前用户ID
const currentUserId = computed(() => userStore.userId || 0);

// 是否是当前用户
const isCurrentUser = computed(() => userId.value === currentUserId.value);

// 用户信息
const user = reactive({
  id: 0,
  username: '',
  nickname: '',
  avatar: '',
  bio: '',
  location: '',
  website: ''
});

// 统计数据
const stats = reactive({
  photoCount: 0,
  followingCount: 0,
  followerCount: 0
});

// 关注状态
const isFollowing = ref(false);

// 当前标签页
const activeTab = ref(0);

// 照片列表
const photos = ref<any[]>([]);
const collections = ref<any[]>([]);

// 加载状态
const loading = ref(true);

// 关注/粉丝列表
const followingList = ref<any[]>([]);
const followerList = ref<any[]>([]);
const showFollowing = ref(false);
const showFollowers = ref(false);

// 返回上一页
const goBack = () => {
  router.back();
};

// 查看照片详情
const viewPhoto = (id: number) => {
  router.push(`/photo/detail/${id}`);
};

// 跳转到用户主页
const goToUserProfile = (id: number) => {
  if (id === userId.value) return;
  router.push(`/user/${id}`);
  showFollowing.value = false;
  showFollowers.value = false;
};

// 发送消息
const sendMessage = () => {
  router.push(`/messages/chat/${userId.value}`);
};

// 关注用户
const followUser = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    isFollowing.value = true;
    stats.followerCount++;

    showSuccessToast('关注成功');
  } catch (error) {
    console.error('关注用户失败', error);
    showToast('关注失败，请稍后重试');
  }
};

// 取消关注
const unfollowUser = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    isFollowing.value = false;
    stats.followerCount--;

    showSuccessToast('已取消关注');
  } catch (error) {
    console.error('取消关注失败', error);
    showToast('操作失败，请稍后重试');
  }
};

// 关注列表中的用户
const followUserInList = async (user: any) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    user.isFollowing = true;

    showSuccessToast('关注成功');
  } catch (error) {
    console.error('关注用户失败', error);
    showToast('关注失败，请稍后重试');
  }
};

// 取消关注列表中的用户
const unfollowUserInList = async (user: any) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    user.isFollowing = false;

    showSuccessToast('已取消关注');
  } catch (error) {
    console.error('取消关注失败', error);
    showToast('操作失败，请稍后重试');
  }
};
</script>

<style lang="scss" scoped>
.user-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px; // 导航栏高度

  .user-detail-content {
    padding: 16px;
  }

  .user-header {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      margin-right: 16px;
    }

    .user-details {
      flex: 1;

      .username {
        margin: 0 0 4px;
        font-size: 18px;
      }

      .user-bio {
        margin: 0 0 8px;
        font-size: 14px;
        color: #666;
      }

      .user-stats {
        display: flex;
        gap: 16px;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;

          .stat-value {
            font-weight: bold;
            font-size: 16px;
          }

          .stat-label {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }

  .action-buttons {
    margin-top: 16px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }

  .user-tabs {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    padding: 4px;

    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      padding: 8px;
    }

    .photo-item {
      position: relative;
      aspect-ratio: 1;
      overflow: hidden;
      cursor: pointer;

      .photo-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        color: white;
        padding: 8px;
        opacity: 0;
        transition: opacity 0.3s;

        .photo-title {
          font-size: 12px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 4px;
        }

        .photo-stats {
          display: flex;
          gap: 8px;
          font-size: 12px;

          .stat-item {
            display: flex;
            align-items: center;

            .van-icon {
              margin-right: 4px;
            }
          }
        }
      }

      &:hover .photo-info {
        opacity: 1;
      }
    }
  }

  .empty-state, .loading-state {
    padding: 40px 0;
    text-align: center;
  }

  .user-list-popup {
    height: 100%;
    display: flex;
    flex-direction: column;

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #eee;

      .popup-title {
        font-size: 16px;
        font-weight: 500;
      }

      .van-icon {
        cursor: pointer;
      }
    }

    .user-list {
      flex: 1;
      overflow-y: auto;
      padding: 16px;

      .user-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f5f5f5;

        .user-info {
          flex: 1;
          margin-left: 12px;

          .user-name {
            font-size: 14px;
            font-weight: 500;
          }

          .user-followers {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}
</style>
