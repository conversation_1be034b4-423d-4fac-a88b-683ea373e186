package com.phototagmoment.service;

import com.phototagmoment.service.impl.BaiduContentModerationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 百度内容审核服务测试类
 */
@ExtendWith(MockitoExtension.class)
public class BaiduContentModerationServiceTest {

    @InjectMocks
    private BaiduContentModerationServiceImpl contentModerationService;

    @Mock
    private MultipartFile mockFile;

    @BeforeEach
    public void setUp() throws IOException {
        // 模拟文件内容
        byte[] content = "test image content".getBytes(StandardCharsets.UTF_8);
        when(mockFile.getBytes()).thenReturn(content);
        when(mockFile.getInputStream()).thenReturn(new ByteArrayInputStream(content));
        when(mockFile.getContentType()).thenReturn("image/jpeg");
        when(mockFile.isEmpty()).thenReturn(false);
        
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field clientField = BaiduContentModerationServiceImpl.class.getDeclaredField("client");
            clientField.setAccessible(true);
            clientField.set(contentModerationService, null); // 设置为null，模拟未初始化状态
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testModerateImage_NullFile() {
        // 测试空文件
        when(mockFile.isEmpty()).thenReturn(true);
        
        boolean result = contentModerationService.moderateImage(mockFile);
        
        assertFalse(result);
        assertEquals("文件为空", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateImage_InvalidContentType() throws IOException {
        // 测试无效的内容类型
        when(mockFile.getContentType()).thenReturn("text/plain");
        
        InputStream inputStream = new ByteArrayInputStream("test".getBytes(StandardCharsets.UTF_8));
        boolean result = contentModerationService.moderateImage(inputStream, "text/plain");
        
        assertFalse(result);
        assertEquals("不支持的文件类型", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateImage_ClientNotInitialized() throws IOException {
        // 测试客户端未初始化
        InputStream inputStream = new ByteArrayInputStream("test".getBytes(StandardCharsets.UTF_8));
        boolean result = contentModerationService.moderateImage(inputStream, "image/jpeg");
        
        assertFalse(result);
        assertEquals("内容审核服务未初始化", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateImageByUrl_NullUrl() {
        // 测试空URL
        boolean result = contentModerationService.moderateImageByUrl(null);
        
        assertFalse(result);
        assertEquals("图片URL为空", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateImageByUrl_EmptyUrl() {
        // 测试空URL
        boolean result = contentModerationService.moderateImageByUrl("");
        
        assertFalse(result);
        assertEquals("图片URL为空", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateImageByUrl_ClientNotInitialized() {
        // 测试客户端未初始化
        boolean result = contentModerationService.moderateImageByUrl("http://example.com/image.jpg");
        
        assertFalse(result);
        assertEquals("内容审核服务未初始化", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateText_NullText() {
        // 测试空文本
        boolean result = contentModerationService.moderateText(null);
        
        assertFalse(result);
        assertEquals("文本内容为空", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateText_EmptyText() {
        // 测试空文本
        boolean result = contentModerationService.moderateText("");
        
        assertFalse(result);
        assertEquals("文本内容为空", contentModerationService.getFailReason());
    }

    @Test
    public void testModerateText_ClientNotInitialized() {
        // 测试客户端未初始化
        boolean result = contentModerationService.moderateText("测试文本");
        
        assertFalse(result);
        assertEquals("内容审核服务未初始化", contentModerationService.getFailReason());
    }
}
