@echo off
chcp 65001 >nul
echo ==========================================
echo PhotoTagMoment 完整构建脚本 (包含测试)
echo ==========================================

REM 设置Java 17环境
set JAVA_HOME=C:\Program Files\Java\jdk-17
echo 使用Java版本: %JAVA_HOME%

REM 检查是否在项目根目录
if not exist "..\pom.xml" (
    echo 错误: 请在项目根目录运行此脚本
    echo 当前目录: %CD%
    echo 应该包含: ..\pom.xml
    pause
    exit /b 1
)

echo [1/4] 进入后端目录...
cd ..

echo [2/4] 清理之前的构建...
call mvn clean -q
if errorlevel 1 (
    echo 错误: Maven清理失败
    pause
    exit /b 1
)

echo [3/4] 运行测试...
call mvn test
if errorlevel 1 (
    echo 警告: 测试失败，但继续构建
    echo 如果需要修复测试，请检查测试报告
    echo.
    set /p choice="是否继续打包? (y/n): "
    if /i not "%choice%"=="y" (
        echo 构建已取消
        pause
        exit /b 1
    )
)

echo [4/4] 开始打包...
call mvn package -DskipTests -q
if errorlevel 1 (
    echo 错误: Maven打包失败
    pause
    exit /b 1
)

echo.
echo ==========================================
echo 构建完成！
echo ==========================================
echo JAR文件位置: %CD%\target\phototagmoment-0.0.1-SNAPSHOT.jar
echo 文件大小:
dir target\phototagmoment-0.0.1-SNAPSHOT.jar | findstr "phototagmoment"
echo.
echo 测试报告位置: %CD%\target\surefire-reports
echo 运行命令: java -jar target\phototagmoment-0.0.1-SNAPSHOT.jar
echo ==========================================

pause
