package com.phototagmoment.security;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT Token提供者
 */
@Component
public class JwtTokenProvider {

    // 手动添加日志对象
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(JwtTokenProvider.class);

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    /**
     * 从token中获取用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            if (token == null || token.isEmpty()) {
                return null;
            }
            String username = getClaimFromToken(token, Claims::getSubject);
            log.debug("从token中获取用户名: {}", username);
            return username;
        } catch (Exception e) {
            log.error("从token中获取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从token中获取过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    /**
     * 从token中获取指定的声明
     */
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        try {
            final Claims claims = getAllClaimsFromToken(token);
            return claimsResolver.apply(claims);
        } catch (Exception e) {
            log.error("从token中获取声明失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从token中获取所有声明
     */
    private Claims getAllClaimsFromToken(String token) {
        try {
            if (token == null || token.isEmpty()) {
                throw new IllegalArgumentException("Token不能为空");
            }
            SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("解析token失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查token是否过期
     */
    private Boolean isTokenExpired(String token) {
        try {
            final Date expiration = getExpirationDateFromToken(token);
            return expiration == null || expiration.before(new Date());
        } catch (Exception e) {
            log.error("检查token是否过期失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 检查日期是否过期
     */
    private Boolean isTokenExpired(Date expiration) {
        return expiration == null || expiration.before(new Date());
    }

    /**
     * 生成token
     */
    public String generateToken(String username) {
        return generateToken(username, null);
    }

    /**
     * 生成带用户ID的token
     */
    public String generateToken(String username, Long userId) {
        Map<String, Object> claims = new HashMap<>();
        if (userId != null) {
            claims.put("userId", userId);
        }
        return doGenerateToken(claims, username);
    }

    /**
     * 生成token的具体实现
     */
    private String doGenerateToken(Map<String, Object> claims, String subject) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration);

        SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从token中获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            Claims claims = getAllClaimsFromToken(token);
            // 尝试从claims中获取用户ID
            // 如果存储在claims中，可以直接获取
            if (claims.get("userId") != null) {
                return Long.parseLong(claims.get("userId").toString());
            }

            // 如果没有存储userId，则从用户名中提取
            // 假设用户名格式为 "user_123"，其中123是用户ID
            String username = claims.getSubject();
            if (username != null && username.contains("_")) {
                String[] parts = username.split("_");
                if (parts.length > 1) {
                    try {
                        return Long.parseLong(parts[1]);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 验证token（简化版）
     */
    public Boolean validateToken(String token) {
        try {
            // 检查token是否为空
            if (token == null || token.isEmpty()) {
                log.info("Token为空");
                return false;
            }

            // 解析token，如果解析失败会抛出异常
            Claims claims = getAllClaimsFromToken(token);
            if (claims == null) {
                log.warn("解析token失败");
                return false;
            }

            // 检查token是否过期
            Date expiration = claims.getExpiration();
            if (isTokenExpired(expiration)) {
                log.warn("Token已过期，过期时间: {}", expiration);
                return false;
            }

            // 检查token中是否包含用户名
            String username = claims.getSubject();
            if (username == null || username.isEmpty()) {
                log.warn("Token不包含有效的用户名");
                return false;
            }

            // 打印token的详细信息，帮助调试
            log.info("Token验证成功，用户: {}, 过期时间: {}, 发行时间: {}",
                username, expiration, claims.getIssuedAt());

            return true;
        } catch (io.jsonwebtoken.ExpiredJwtException e) {
            log.warn("Token已过期: {}", e.getMessage());
            return false;
        } catch (io.jsonwebtoken.security.SignatureException e) {
            log.warn("Token签名验证失败: {}", e.getMessage());
            return false;
        } catch (io.jsonwebtoken.MalformedJwtException e) {
            log.warn("Token格式错误: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("Token验证错误: {}, 类型: {}", e.getMessage(), e.getClass().getName());
            return false;
        }
    }

    /**
     * 获取JWT过期时间（毫秒）
     */
    public long getExpiration() {
        return jwtExpiration;
    }
}
