package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.entity.PhotoAudit;
import com.phototagmoment.vo.PhotoAuditVO;

import java.util.List;

/**
 * 照片审核服务接口
 */
public interface PhotoAuditService extends IService<PhotoAudit> {

    /**
     * 创建审核记录
     *
     * @param photoId 照片ID
     * @param auditType 审核类型: 0-自动审核, 1-人工审核
     * @param auditResult 审核结果: 0-待审核, 1-通过, 2-拒绝
     * @param rejectReason 拒绝原因
     * @param auditorId 审核人ID（人工审核时）
     * @return 审核记录ID
     */
    Long createAuditRecord(Long photoId, Integer auditType, Integer auditResult, String rejectReason, Long auditorId);

    /**
     * 提交照片进行自动审核
     *
     * @param photoId 照片ID
     * @return 是否提交成功
     */
    boolean submitForAutoAudit(Long photoId);

    /**
     * 提交照片进行人工审核
     *
     * @param photoId 照片ID
     * @return 是否提交成功
     */
    boolean submitForManualAudit(Long photoId);

    /**
     * 人工审核照片
     *
     * @param photoId 照片ID
     * @param approved 是否通过
     * @param rejectReason 拒绝原因（如果不通过）
     * @param auditorId 审核人ID
     * @return 是否审核成功
     */
    boolean auditPhoto(Long photoId, boolean approved, String rejectReason, Long auditorId);

    /**
     * 批量审核照片
     *
     * @param photoIds 照片ID列表
     * @param approved 是否通过
     * @param rejectReason 拒绝原因（如果不通过）
     * @param auditorId 审核人ID
     * @return 是否审核成功
     */
    boolean batchAuditPhotos(List<Long> photoIds, boolean approved, String rejectReason, Long auditorId);

    /**
     * 获取待审核照片列表
     *
     * @param page 页码
     * @param size 每页大小
     * @return 待审核照片分页列表
     */
    IPage<PhotoAuditVO> getPendingAuditPhotos(int page, int size);

    /**
     * 获取照片审核历史
     *
     * @param photoId 照片ID
     * @return 审核历史列表
     */
    List<PhotoAudit> getPhotoAuditHistory(Long photoId);
}
