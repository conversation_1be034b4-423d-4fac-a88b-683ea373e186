# PhotoTagMoment 评论功能测试验证脚本

## 📋 **测试概述**

本文档提供了完整的评论功能测试验证脚本，用于验证修复后的评论功能是否正常工作。

## 🧪 **测试环境准备**

### **1. 启动后端服务**
```bash
cd server
java -jar target/phototagmoment-0.0.1-SNAPSHOT.jar
```

### **2. 启动前端服务**
```bash
cd user
npm run dev
```

### **3. 验证服务状态**
- 后端服务：http://localhost:8080
- 前端服务：http://localhost:3001
- API文档：http://localhost:8080/swagger-ui/index.html

## 🔍 **数据库验证测试**

### **1. 验证新表是否创建成功**
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'ptm_comment_%';

-- 预期结果：
-- ptm_comment
-- ptm_comment_like
-- ptm_comment_mention
-- ptm_comment_tag

-- 验证表结构
DESCRIBE ptm_comment_tag;
DESCRIBE ptm_comment_mention;

-- 验证索引
SHOW INDEX FROM ptm_comment_tag;
SHOW INDEX FROM ptm_comment_mention;
```

### **2. 验证外键约束**
```sql
-- 检查外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'phototagmoment' 
AND TABLE_NAME IN ('ptm_comment_tag', 'ptm_comment_mention')
AND REFERENCED_TABLE_NAME IS NOT NULL;
```

## 🌐 **API接口测试**

### **1. 测试基础评论提交**
```bash
# 获取登录Token（替换为实际的用户凭据）
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password"
  }'

# 使用返回的token进行后续测试
export TOKEN="your_jwt_token_here"

# 测试基础评论提交
curl -X POST http://localhost:8080/comment/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "photoId": 37,
    "content": "这是一个基础测试评论"
  }'
```

### **2. 测试带标签的评论提交**
```bash
curl -X POST http://localhost:8080/comment/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "photoId": 37,
    "content": "这是带标签的评论 #测试标签# #风景# #摄影#",
    "tags": ["测试标签", "风景", "摄影"]
  }'
```

### **3. 测试带用户提及的评论提交**
```bash
curl -X POST http://localhost:8080/comment/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "photoId": 37,
    "content": "这是带用户提及的评论 @testuser @admin",
    "mentions": ["testuser", "admin"]
  }'
```

### **4. 测试完整功能评论提交**
```bash
curl -X POST http://localhost:8080/comment/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "photoId": 37,
    "content": "这是完整功能测试评论 #测试# @testuser 包含标签和用户提及",
    "tags": ["测试"],
    "mentions": ["testuser"]
  }'
```

### **5. 测试回复评论功能**
```bash
# 首先获取一个评论ID（从上面的测试中获取）
export COMMENT_ID="123"

curl -X POST http://localhost:8080/comment/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "photoId": 37,
    "content": "这是回复评论 @原评论用户",
    "parentId": '$COMMENT_ID',
    "replyToUserId": 1,
    "replyToUsername": "原评论用户",
    "mentions": ["原评论用户"]
  }'
```

### **6. 验证评论数据查询**
```bash
# 获取照片评论列表
curl -X GET "http://localhost:8080/comment/photo/37?page=1&size=10" \
  -H "Authorization: Bearer $TOKEN"

# 获取评论详情
curl -X GET "http://localhost:8080/comment/$COMMENT_ID" \
  -H "Authorization: Bearer $TOKEN"
```

## 🖥️ **前端功能测试**

### **1. 访问照片详情页面**
```
URL: http://localhost:3001/photo-note/37
```

### **2. 测试评论输入功能**
1. **标签输入测试**：
   - 在评论框中输入：`这是测试评论 #测试标签#`
   - 验证标签是否高亮显示为蓝色
   - 验证实时预览是否正确

2. **用户提及输入测试**：
   - 在评论框中输入：`这是测试评论 @testuser`
   - 验证用户提及是否高亮显示为橙色
   - 验证实时预览是否正确

3. **混合功能测试**：
   - 输入：`测试评论 #标签# @用户 混合功能`
   - 验证两种高亮是否都正确显示

### **3. 测试评论提交**
1. **提交普通评论**：
   - 输入评论内容并点击发布
   - 检查浏览器开发者工具的Network标签
   - 验证API请求参数是否包含完整数据

2. **提交带标签评论**：
   - 输入包含标签的评论
   - 验证请求中是否包含tags数组

3. **提交带用户提及评论**：
   - 输入包含@用户的评论
   - 验证请求中是否包含mentions数组

### **4. 测试回复功能**
1. **点击回复按钮**：
   - 点击某个评论的回复按钮
   - 验证回复目标是否正确设置
   - 验证输入框是否自动添加@用户名

2. **提交回复**：
   - 输入回复内容并提交
   - 验证API请求是否包含parentId、replyToUserId等字段

## 📊 **数据验证测试**

### **1. 验证评论数据存储**
```sql
-- 查看最新的评论记录
SELECT * FROM ptm_comment ORDER BY created_at DESC LIMIT 5;

-- 验证评论内容是否正确存储
SELECT id, content, parent_id, reply_to_user_id FROM ptm_comment 
WHERE photo_id = 37 ORDER BY created_at DESC;
```

### **2. 验证标签数据存储**
```sql
-- 查看评论标签关联数据
SELECT ct.*, c.content 
FROM ptm_comment_tag ct
LEFT JOIN ptm_comment c ON ct.comment_id = c.id
ORDER BY ct.created_at DESC LIMIT 10;

-- 验证特定评论的标签
SELECT tag_name FROM ptm_comment_tag 
WHERE comment_id = (SELECT id FROM ptm_comment ORDER BY created_at DESC LIMIT 1);
```

### **3. 验证用户提及数据存储**
```sql
-- 查看评论用户提及关联数据
SELECT cm.*, c.content 
FROM ptm_comment_mention cm
LEFT JOIN ptm_comment c ON cm.comment_id = c.id
ORDER BY cm.created_at DESC LIMIT 10;

-- 验证特定评论的用户提及
SELECT mentioned_username FROM ptm_comment_mention 
WHERE comment_id = (SELECT id FROM ptm_comment ORDER BY created_at DESC LIMIT 1);
```

### **4. 验证回复关系数据**
```sql
-- 查看回复关系
SELECT 
    c1.id as parent_id,
    c1.content as parent_content,
    c2.id as reply_id,
    c2.content as reply_content,
    c2.reply_to_user_id
FROM ptm_comment c1
LEFT JOIN ptm_comment c2 ON c1.id = c2.parent_id
WHERE c1.parent_id IS NULL AND c2.id IS NOT NULL
ORDER BY c1.created_at DESC;
```

## ✅ **预期测试结果**

### **1. 数据库层面**
- ✅ 新表创建成功
- ✅ 外键约束正确建立
- ✅ 索引创建成功
- ✅ 数据正确存储到各个表中

### **2. API层面**
- ✅ 评论提交接口返回成功（200状态码）
- ✅ 返回新创建的评论ID
- ✅ 标签和用户提及数据正确处理
- ✅ 回复关系正确建立

### **3. 前端层面**
- ✅ 标签高亮显示正确（蓝色）
- ✅ 用户提及高亮显示正确（橙色）
- ✅ 实时预览功能正常
- ✅ API请求参数完整

### **4. 数据完整性**
- ✅ 评论内容正确存储
- ✅ 标签关联数据正确存储
- ✅ 用户提及关联数据正确存储
- ✅ 回复关系正确建立

## 🚨 **常见问题排查**

### **1. 数据库连接问题**
```bash
# 检查数据库连接
mysql -u root -p -h localhost phototagmoment
```

### **2. 表创建失败**
```sql
-- 手动执行迁移脚本
SOURCE /path/to/V2.1__Create_Comment_Tag_And_Mention_Tables.sql;
```

### **3. API调用失败**
- 检查JWT Token是否有效
- 检查用户权限是否正确
- 检查请求参数格式是否正确

### **4. 前端功能异常**
- 检查浏览器控制台是否有JavaScript错误
- 检查Network标签中的API请求是否正确
- 验证前端代码是否正确调用新的API接口

## 📝 **测试报告模板**

### **测试执行记录**
```
测试时间：____年__月__日 __:__
测试人员：________
测试环境：开发环境

数据库测试：
□ 表创建成功
□ 外键约束正确
□ 索引创建成功

API测试：
□ 基础评论提交成功
□ 标签评论提交成功
□ 用户提及评论提交成功
□ 回复评论提交成功

前端测试：
□ 标签高亮显示正确
□ 用户提及高亮显示正确
□ 实时预览功能正常
□ 评论提交功能正常

数据验证：
□ 评论数据正确存储
□ 标签数据正确存储
□ 用户提及数据正确存储
□ 回复关系正确建立

测试结论：
□ 全部通过
□ 部分通过（需要说明问题）
□ 测试失败（需要说明原因）

备注：
_________________________________
```

通过执行以上测试脚本，可以全面验证PhotoTagMoment项目评论功能修复的完整性和正确性。
