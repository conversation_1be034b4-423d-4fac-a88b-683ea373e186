package com.phototagmoment.service.impl;

import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 本地存储服务实现类
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "storage.local.enabled", havingValue = "true", matchIfMissing = true)
public class LocalStorageServiceImpl implements StorageService {

    @Value("${storage.local.path}")
    private String storagePath;

    @Value("${storage.local.domain:#{null}}")
    private String domain;

    @Override
    public String uploadFile(InputStream inputStream, String path, String contentType) {
        try {
            // 创建目录
            Path targetPath = Paths.get(storagePath, path);
            Files.createDirectories(targetPath.getParent());

            // 保存文件
            Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);

            // 返回文件URL
            return getFileUrl(path);
        } catch (IOException e) {
            log.error("上传文件失败", e);
            throw new BusinessException("上传文件失败");
        }
    }

    @Override
    public String uploadFile(byte[] bytes, String path, String contentType) {
        return uploadFile(new ByteArrayInputStream(bytes), path, contentType);
    }

    @Override
    public boolean deleteFile(String path) {
        try {
            Path targetPath = Paths.get(storagePath, path);
            return Files.deleteIfExists(targetPath);
        } catch (IOException e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    @Override
    public String getFileUrl(String path) {
        if (domain != null && !domain.isEmpty()) {
            return domain + "/" + path;
        } else {
            // 如果没有配置域名，则返回相对路径
            return "/api/file/" + path;
        }
    }
}
