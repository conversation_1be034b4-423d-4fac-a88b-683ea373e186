package com.phototagmoment.security;

import com.phototagmoment.service.AuthenticationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 安全工具类
 *
 * 注意：这个类现在是一个组件，不再是静态工具类
 * 所有方法都委托给 AuthenticationService 处理
 */
@Component
public class SecurityUtil {

    private static AuthenticationService authenticationService;

    @Autowired
    public SecurityUtil(AuthenticationService authenticationService) {
        SecurityUtil.authenticationService = authenticationService;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        return authenticationService.getCurrentUserId();
    }

    /**
     * 获取当前登录用户名
     *
     * @return 用户名
     */
    public static String getCurrentUsername() {
        return authenticationService.getCurrentUsername();
    }

    /**
     * 判断当前用户是否已认证
     *
     * @return 是否已认证
     */
    public static boolean isAuthenticated() {
        return authenticationService.isAuthenticated();
    }

    /**
     * 判断当前用户是否具有指定角色
     *
     * @param role 角色
     * @return 是否具有指定角色
     */
    public static boolean hasRole(String role) {
        return authenticationService.hasRole(role);
    }
}
