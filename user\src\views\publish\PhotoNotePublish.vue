<template>
  <div class="photo-note-publish">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="发布照片笔记"
      left-text="取消"
      right-text="发布"
      left-arrow
      @click-left="onCancel"
      @click-right="onPublish"
      :right-disabled="!canPublish"
      class="publish-navbar"
    />

    <!-- 发布表单 -->
    <div class="publish-form">
      <!-- 照片上传区域 -->
      <div class="photo-upload-section">
        <div class="photo-grid" :class="getPhotoGridClass()">
          <!-- 已上传的照片 -->
          <div
            v-for="(photo, index) in photos"
            :key="index"
            class="photo-item"
            @click="previewPhoto(index)"
          >
            <img :src="photo.thumbnailUrl || photo.url" :alt="`照片${index + 1}`" />
            <div class="photo-actions">
              <van-icon name="cross" @click.stop="removePhoto(index)" />
            </div>
            <div class="photo-order">{{ index + 1 }}</div>
          </div>

          <!-- 上传按钮 -->
          <div
            v-if="photos.length < 9"
            class="photo-upload-btn"
            @click="selectPhotos"
          >
            <van-icon name="plus" size="24" />
            <div class="upload-text">添加照片</div>
          </div>
        </div>

        <!-- 照片上传提示 -->
        <div class="upload-tips">
          <span>{{ photos.length }}/9</span>
          <span>支持JPG、PNG格式，单张不超过10MB</span>
        </div>
      </div>

      <!-- 标题输入 -->
      <div class="form-section">
        <van-field
          v-model="form.title"
          placeholder="添加标题（可选）"
          maxlength="100"
          show-word-limit
          clearable
        />
      </div>

      <!-- 正文输入 -->
      <div class="form-section">
        <van-field
          v-model="form.content"
          type="textarea"
          placeholder="分享你的照片故事...&#10;&#10;使用 #标签# 来标记主题&#10;使用 @用户名 来提及朋友"
          rows="6"
          maxlength="2000"
          show-word-limit
          autosize
          required
        />
      </div>

      <!-- 标签和@用户预览 -->
      <div v-if="extractedTags.length > 0 || extractedMentions.length > 0" class="content-preview">
        <!-- 标签预览 -->
        <div v-if="extractedTags.length > 0" class="tags-preview">
          <div class="preview-label">标签：</div>
          <div class="tags-list">
            <span
              v-for="tag in extractedTags"
              :key="tag"
              class="tag-item"
              @click="searchTag(tag)"
            >
              #{{ tag }}#
            </span>
          </div>
        </div>

        <!-- @用户预览 -->
        <div v-if="extractedMentions.length > 0" class="mentions-preview">
          <div class="preview-label">提及：</div>
          <div class="mentions-list">
            <span
              v-for="mention in extractedMentions"
              :key="mention"
              class="mention-item"
            >
              @{{ mention }}
            </span>
          </div>
        </div>
      </div>

      <!-- 发布设置 -->
      <div class="publish-settings">
        <van-cell-group>
          <van-cell title="谁可以看" :value="visibilityText" is-link @click="showVisibilityPicker = true" />
          <van-cell title="允许评论" :value="form.allowComment ? '是' : '否'" is-link @click="showCommentPicker = true" />
          <van-cell
            v-if="location"
            title="位置"
            :value="location"
            is-link
            @click="selectLocation"
          />
          <van-cell
            v-else
            title="添加位置"
            is-link
            @click="selectLocation"
          />
        </van-cell-group>
      </div>
    </div>

    <!-- 照片选择器 -->
    <input
      ref="photoInput"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="onPhotoSelect"
    />

    <!-- 可见性选择器 -->
    <van-popup v-model:show="showVisibilityPicker" position="bottom">
      <van-picker
        :columns="visibilityOptions"
        @confirm="onVisibilityConfirm"
        @cancel="showVisibilityPicker = false"
      />
    </van-popup>

    <!-- 评论设置选择器 -->
    <van-popup v-model:show="showCommentPicker" position="bottom">
      <van-picker
        :columns="commentOptions"
        @confirm="onCommentConfirm"
        @cancel="showCommentPicker = false"
      />
    </van-popup>

    <!-- 照片预览 -->
    <van-image-preview
      v-model:show="showPreview"
      :images="previewImages"
      :start-position="previewIndex"
      @change="onPreviewChange"
    />

    <!-- 发布中加载 -->
    <van-loading v-if="publishing" type="spinner" color="#1989fa" vertical>
      发布中...
    </van-loading>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog, showLoadingToast, closeToast } from 'vant'
import { uploadPhotoForNote, publishPhotoNote } from '@/api/photo'

const router = useRouter()

// 响应式数据
const photos = ref([])
const form = ref({
  title: '',
  content: '',
  visibility: 1, // 0-私密, 1-公开, 2-好友可见
  allowComment: 1, // 0-不允许, 1-允许
  location: '',
  longitude: null,
  latitude: null
})

const showVisibilityPicker = ref(false)
const showCommentPicker = ref(false)
const showPreview = ref(false)
const previewIndex = ref(0)
const publishing = ref(false)
const location = ref('')

// 照片输入引用
const photoInput = ref(null)

// 可见性选项
const visibilityOptions = [
  { text: '公开', value: 1 },
  { text: '好友可见', value: 2 },
  { text: '私密', value: 0 }
]

// 评论选项
const commentOptions = [
  { text: '允许', value: 1 },
  { text: '不允许', value: 0 }
]

// 计算属性
const canPublish = computed(() => {
  return photos.value.length > 0 && form.value.content.trim().length > 0 && !publishing.value
})

const visibilityText = computed(() => {
  const option = visibilityOptions.find(item => item.value === form.value.visibility)
  return option ? option.text : '公开'
})

const previewImages = computed(() => {
  return photos.value.map(photo => photo.url)
})

const getPhotoGridClass = computed(() => {
  const count = photos.value.length
  if (count === 1) return 'grid-1'
  if (count <= 4) return 'grid-2x2'
  return 'grid-3x3'
})

// 提取标签和@用户（与详情页面保持一致的正则表达式）
const extractedTags = computed(() => {
  // 使用与详情页面相同的正则表达式：/#([^#]+)#/g
  const tagRegex = /#([^#]+)#/g
  const matches = form.value.content.match(tagRegex)
  if (!matches) return []
  // 提取标签名称（去掉#号）并去重
  return [...new Set(matches.map(match => match.slice(1, -1)))]
})

const extractedMentions = computed(() => {
  // 使用与详情页面相同的正则表达式：/@([^\s@]+)/g
  const mentionRegex = /@([^\s@]+)/g
  const matches = form.value.content.match(mentionRegex)
  if (!matches) return []
  // 提取用户名（去掉@号）并去重
  return [...new Set(matches.map(match => match.slice(1)))]
})

// 方法
const selectPhotos = () => {
  photoInput.value.click()
}

const onPhotoSelect = async (event) => {
  const files = Array.from(event.target.files)
  const remainingSlots = 9 - photos.value.length

  if (files.length > remainingSlots) {
    showToast(`最多只能选择${remainingSlots}张照片`)
    return
  }

  const loadingToast = showLoadingToast({
    message: '上传中...',
    forbidClick: true,
    duration: 0
  })

  try {
    for (const file of files) {
      // 验证文件
      if (!file.type.startsWith('image/')) {
        showToast('请选择图片文件')
        continue
      }

      if (file.size > 10 * 1024 * 1024) {
        showToast('图片大小不能超过10MB')
        continue
      }

      // 上传照片到七牛云（仅获取URL，不保存到数据库）
      const uploadResult = await uploadPhotoForNote(file)

      photos.value.push({
        url: uploadResult.url,
        thumbnailUrl: uploadResult.thumbnailUrl,
        width: uploadResult.width,
        height: uploadResult.height,
        key: uploadResult.key,
        originalFilename: uploadResult.originalFilename,
        sortOrder: photos.value.length + 1
      })
    }
  } catch (error) {
    console.error('照片上传失败:', error)
    showToast('照片上传失败')
  } finally {
    closeToast()
    // 清空input
    event.target.value = ''
  }
}

const removePhoto = (index) => {
  photos.value.splice(index, 1)
  // 重新排序
  photos.value.forEach((photo, i) => {
    photo.sortOrder = i + 1
  })
}

const previewPhoto = (index) => {
  previewIndex.value = index
  showPreview.value = true
}

const onPreviewChange = (index) => {
  previewIndex.value = index
}

const onVisibilityConfirm = ({ selectedOptions }) => {
  form.value.visibility = selectedOptions[0].value
  showVisibilityPicker.value = false
}

const onCommentConfirm = ({ selectedOptions }) => {
  form.value.allowComment = selectedOptions[0].value
  showCommentPicker.value = false
}

const selectLocation = () => {
  // TODO: 实现位置选择功能
  showToast('位置选择功能开发中')
}

const searchTag = (tag) => {
  router.push(`/tag/${encodeURIComponent(tag)}`)
}

const onCancel = async () => {
  if (photos.value.length > 0 || form.value.content.trim()) {
    const result = await showConfirmDialog({
      title: '确认离开',
      message: '离开后内容将不会保存，确认离开吗？'
    })

    if (result === 'confirm') {
      router.back()
    }
  } else {
    router.back()
  }
}

const onPublish = async () => {
  if (!canPublish.value) return

  publishing.value = true
  const loadingToast = showLoadingToast({
    message: '发布中...',
    forbidClick: true,
    duration: 0
  })

  try {
    const publishData = {
      title: form.value.title,
      content: form.value.content,
      photos: photos.value.map(photo => ({
        url: photo.url,
        thumbnailUrl: photo.thumbnailUrl,
        width: photo.width,
        height: photo.height,
        sortOrder: photo.sortOrder,
        storagePath: photo.key,
        originalFilename: photo.originalFilename,
        fileSize: null, // 七牛云上传无法直接获取文件大小
        fileType: 'image/jpeg' // 默认类型，实际应该根据文件类型判断
      })),
      visibility: form.value.visibility,
      allowComment: form.value.allowComment,
      location: form.value.location,
      longitude: form.value.longitude,
      latitude: form.value.latitude
    }

    const result = await publishPhotoNote(publishData)

    showToast('发布成功')

    // 跳转到照片笔记详情页
    router.replace(`/photo-note/${result.data}`)
  } catch (error) {
    console.error('发布失败:', error)
    showToast('发布失败，请重试')
  } finally {
    publishing.value = false
    closeToast()
  }
}
</script>

<style scoped>
.photo-note-publish {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.publish-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.publish-form {
  padding: 16px;
}

.photo-upload-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.photo-grid {
  display: grid;
  gap: 8px;
  margin-bottom: 12px;
}

.grid-1 {
  grid-template-columns: 1fr;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.photo-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-actions {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.photo-order {
  position: absolute;
  bottom: 4px;
  left: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.photo-upload-btn {
  aspect-ratio: 1;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  cursor: pointer;
  transition: all 0.3s;
}

.photo-upload-btn:hover {
  border-color: #1989fa;
  color: #1989fa;
}

.upload-text {
  font-size: 12px;
  margin-top: 4px;
}

.upload-tips {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.content-preview {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.preview-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.tags-list, .mentions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  background-color: #e8f4fd;
  color: #1989fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.mention-item {
  background-color: #fff2e8;
  color: #ff6a00;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.publish-settings {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}
</style>
