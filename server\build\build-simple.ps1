param(
    [Parameter(Position=0)]
    [ValidateSet("dev", "test", "prod")]
    [string]$Environment = "dev"
)

Write-Host "==========================================" -ForegroundColor Green
Write-Host "PhotoTagMoment Multi-Environment Build Script" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "Target Environment: $Environment" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Green

# Set Java 17 environment
$env:JAVA_HOME = "C:\Program Files\Java\jdk-17"
Write-Host "Using Java: $env:JAVA_HOME" -ForegroundColor Yellow

# Check if in project root directory
if (-not (Test-Path "..\pom.xml")) {
    Write-Host "Error: Please run this script from project root directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Red
    Write-Host "Should contain: ..\pom.xml" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

# Check environment config file
$configFile = "..\src\main\resources\application-$Environment.yml"
if (-not (Test-Path $configFile)) {
    Write-Host "Warning: Environment config file not found: application-$Environment.yml" -ForegroundColor Yellow
    Write-Host "Will use default configuration" -ForegroundColor Yellow
} else {
    Write-Host "Using config file: application-$Environment.yml" -ForegroundColor Green
}

Write-Host "[1/4] Entering server directory..." -ForegroundColor Yellow
Set-Location ..\

Write-Host "[2/4] Cleaning previous build..." -ForegroundColor Yellow
try {
    & mvn clean -q
    if ($LASTEXITCODE -ne 0) {
        throw "Maven clean failed"
    }
    Write-Host "Clean completed" -ForegroundColor Green
} catch {
    Write-Host "Error: Maven clean failed - $_" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "[3/4] Compiling project..." -ForegroundColor Yellow
try {
    & mvn compile -q
    if ($LASTEXITCODE -ne 0) {
        throw "Maven compile failed"
    }
    Write-Host "Compile completed" -ForegroundColor Green
} catch {
    Write-Host "Error: Maven compile failed - $_" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "[4/4] Packaging (Environment: $Environment)..." -ForegroundColor Yellow
try {
    & mvn package -DskipTests "-Dspring.profiles.active=$Environment" -q
    if ($LASTEXITCODE -ne 0) {
        throw "Maven package failed"
    }
    Write-Host "Package completed" -ForegroundColor Green
} catch {
    Write-Host "Error: Maven package failed - $_" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

# Get JAR file information
$jarFile = "target\phototagmoment-0.0.1-SNAPSHOT.jar"
if (-not (Test-Path $jarFile)) {
    Write-Host "Error: JAR file not generated" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

$fileInfo = Get-Item $jarFile
$fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
$buildTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

Write-Host ""
Write-Host "==========================================" -ForegroundColor Green
Write-Host "Build Successful!" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Cyan
Write-Host "JAR Location: $(Get-Location)\$jarFile" -ForegroundColor Cyan
Write-Host "File Size: $fileSizeMB MB" -ForegroundColor Cyan
Write-Host "Build Time: $buildTime" -ForegroundColor Cyan
Write-Host ""
Write-Host "Run Commands:" -ForegroundColor Yellow
Write-Host "  java -jar $jarFile" -ForegroundColor White
Write-Host "  java -jar -Dspring.profiles.active=$Environment $jarFile" -ForegroundColor White
Write-Host ""
Write-Host "Config File: application-$Environment.yml" -ForegroundColor Cyan

$port = if ($Environment -eq "test") { "8082" } else { "8081" }
Write-Host "API Docs: http://localhost:$port/api/doc.html" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Green

Read-Host "Press any key to exit"
