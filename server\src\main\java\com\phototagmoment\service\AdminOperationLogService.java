package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.entity.AdminOperationLog;

import javax.servlet.http.HttpServletResponse;

/**
 * 管理员操作日志服务接口
 */
public interface AdminOperationLogService {

    /**
     * 获取操作日志列表
     *
     * @param page      页码
     * @param pageSize  每页条数
     * @param keyword   关键字
     * @param module    模块
     * @param operation 操作类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 操作日志列表
     */
    IPage<AdminOperationLog> getOperationLogList(Integer page, Integer pageSize, String keyword,
                                                String module, String operation, String startDate, String endDate);

    /**
     * 导出操作日志
     *
     * @param keyword   关键字
     * @param module    模块
     * @param operation 操作类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     */
    void exportOperationLog(String keyword, String module, String operation,
                           String startDate, String endDate, HttpServletResponse response);

    /**
     * 清空操作日志
     *
     * @return 是否成功
     */
    boolean clearOperationLog();

    /**
     * 记录操作日志
     *
     * @param adminId   管理员ID
     * @param username  用户名
     * @param module    模块
     * @param operation 操作类型
     * @param content   操作内容
     * @param ip        IP地址
     * @param userAgent 用户代理
     */
    void recordOperationLog(Long adminId, String username, String module,
                           String operation, String content, String ip, String userAgent);
}
