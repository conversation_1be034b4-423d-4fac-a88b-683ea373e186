<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.PhotoNoteTagMapper">

    <!-- 批量插入照片笔记标签 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ptm_photo_note_tag (note_id, tag_name, created_at)
        VALUES
        <foreach collection="tags" item="item" separator=",">
            (#{item.noteId}, #{item.tagName}, #{item.createdAt})
        </foreach>
    </insert>

    <!-- 删除照片笔记的所有标签 -->
    <delete id="deleteByNoteId">
        DELETE FROM ptm_photo_note_tag WHERE note_id = #{noteId}
    </delete>

</mapper>
