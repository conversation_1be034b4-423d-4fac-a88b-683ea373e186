# PhotoTagMoment 照片社交网站项目需求与开发计划

## 项目概述

PhotoTagMoment 是一个照片分享社交网站，用户可以上传照片、添加标签、关注其他用户、点赞评论等。项目包括用户前端和管理后台两部分。

## 技术栈

- 后端：Java SpringBoot 17, MySQL 8, Redis, S3 存储, Mybatis-plus, Hutool
- 前端：Vue3 (响应式设计) 与 TypeScript (ts)
- 管理后台：Vue3 与 Element-UI

## 功能需求

### 用户系统
- 用户注册与登录（手机号+短信验证码、微信扫码）
- 用户资料管理（头像、昵称、个人简介等）
- 用户关注与粉丝管理
- 第三方身份认证，未认证用户发帖受限

### 照片管理
- 照片上传（支持多图上传，PC端拖拽，移动端相册选择）
- 照片编辑（标题、描述、标签、位置信息等）
- 照片浏览（瀑布流布局）
- 照片详情页（大图展示、评论区等）
- 照片审核机制（先保存后审核，支持自动审核和人工审核）

### 社交功能
- 点赞、评论、收藏
- 用户关注
- 消息通知（点赞、评论、关注等）
- @用户功能

### 内容发现
- 首页推荐
- 热门话题
- 搜索功能（照片、用户、标签等）

### 管理后台
- 用户管理
- 照片管理
- 内容审核（人工审核照片内容）
- 系统设置

## 照片审核机制详细设计

### 现状问题
目前系统在照片发布时会先进行内容审核，只有审核通过才能保存到数据库。这种方式存在以下问题：
1. 用户体验差，发布照片后需要等待审核完成
2. 系统脆弱，审核服务不可用时用户无法发布照片
3. 审核机制不灵活，只支持自动审核

### 改进方案
将照片审核流程从"先审核后保存"改为"先保存后审核"，并实现两种审核方式：

#### 1. 自动审核
- 照片发布后立即保存到数据库，状态设为"待审核"
- 通过异步任务队列进行内容审核
- 审核通过后，更新照片状态为"正常"
- 审核不通过，更新照片状态为"审核拒绝"，并记录拒绝原因

#### 2. 人工审核
- 管理员可在后台查看所有待审核的照片
- 管理员可以批准或拒绝照片，并提供拒绝理由
- 支持批量审核操作

#### 3. 混合审核模式
- 系统先进行自动审核
- 自动审核不确定的情况下，转入人工审核队列
- 管理员可设置审核敏感度，调整自动审核与人工审核的比例

### 照片状态流转
- 待审核(0)：照片刚上传，等待审核
- 正常(1)：审核通过，可正常显示
- 审核拒绝(2)：审核不通过，仅创建者可见
- 已删除(3)：照片已被删除

## 开发计划

### 第一阶段：照片审核机制改造（已完成）
1. 数据库设计调整（已完成）
   - 确保 ptm_photo 表包含必要的状态字段和审核相关字段
   - 创建审核记录表，记录审核历史

2. 后端开发（已完成）
   - 修改照片上传流程，实现"先保存后审核"
   - 实现异步审核任务队列
   - 开发审核相关API接口

3. 管理后台开发（已完成）
   - 开发照片审核页面
   - 实现批量审核功能
   - 开发审核详情功能

4. 前端适配（已完成）
   - 调整照片展示逻辑，根据照片状态显示不同内容
   - 优化用户体验，提供审核状态反馈

5. 用户通知系统完善（已完成）
   - 审核结果通知
   - 系统消息推送

### 第二阶段：其他功能完善（进行中）
1. 性能优化
   - 审核队列性能优化
   - 大规模照片处理优化
   - 缓存优化

2. 统计分析功能
   - 审核统计报表
   - 用户行为分析
   - 内容热度分析

3. 安全性增强
   - 敏感内容过滤优化
   - 用户权限管理完善
   - 数据加密与隐私保护

## 已完成功能

### 照片审核机制改造
1. 数据库设计调整
   - 创建了 `ptm_photo_audit` 表，用于记录审核历史
   - 确保 `ptm_photo` 表包含状态字段和审核相关字段

2. 后端开发
   - 修改了照片上传流程，实现"先保存后审核"
   - 实现了异步审核任务队列 `PhotoAuditTask`
   - 开发了审核相关API接口 `PhotoAuditController`
   - 实现了审核服务 `PhotoAuditService`

3. 管理后台开发
   - 开发了照片审核列表页面
   - 实现了照片审核详情页面
   - 开发了批量审核功能

4. 前端适配
   - 创建了照片状态展示组件 `PhotoStatus`
   - 修改了照片卡片组件，显示审核状态
   - 修改了照片详情页，显示审核状态和拒绝原因

5. 用户通知系统完善
   - 实现了审核结果通知功能
   - 集成了系统消息推送

## 下一步计划

1. 性能优化
   - 优化审核队列，提高处理效率
   - 实现分布式审核任务处理
   - 添加缓存机制，减少数据库访问

2. 统计分析功能
   - 开发审核统计报表
   - 实现用户行为分析
   - 开发内容热度分析

3. 安全性增强
   - 优化敏感内容过滤算法
   - 完善用户权限管理
   - 增强数据加密与隐私保护
