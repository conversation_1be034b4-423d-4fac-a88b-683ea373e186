#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PhotoTagMoment 照片笔记数据验证脚本
用于诊断用户端发布和管理端查询的数据同步问题
"""

import requests
import json
import time
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8081/api"
ADMIN_TOKEN = None  # 需要先获取管理员token

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_result(name, data):
    """打印结果"""
    print(f"\n{name}:")
    if isinstance(data, dict):
        print(json.dumps(data, indent=2, ensure_ascii=False))
    elif isinstance(data, list):
        print(f"数组长度: {len(data)}")
        for i, item in enumerate(data[:3]):  # 只显示前3个
            print(f"  [{i}]: {json.dumps(item, indent=4, ensure_ascii=False)}")
        if len(data) > 3:
            print(f"  ... 还有 {len(data) - 3} 个项目")
    else:
        print(data)

def test_admin_login():
    """测试管理员登录"""
    print_section("1. 管理员登录测试")
    
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/admin/system/login", json=login_data)
        result = response.json()
        print_result("登录响应", result)
        
        if result.get("code") == 200 and result.get("data"):
            global ADMIN_TOKEN
            ADMIN_TOKEN = result["data"].get("token")
            print(f"✅ 管理员登录成功，Token: {ADMIN_TOKEN[:20]}...")
            return True
        else:
            print("❌ 管理员登录失败")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False

def test_admin_photo_notes():
    """测试管理端照片笔记查询"""
    print_section("2. 管理端照片笔记查询测试")
    
    if not ADMIN_TOKEN:
        print("❌ 没有管理员Token，跳过测试")
        return
    
    headers = {
        "Authorization": f"Bearer {ADMIN_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # 查询所有状态的照片笔记
        response = requests.get(f"{BASE_URL}/admin/photo-notes/list?page=1&size=10", headers=headers)
        result = response.json()
        print_result("管理端查询结果", result)
        
        if result.get("code") == 200:
            data = result.get("data", {})
            records = data.get("records", [])
            total = data.get("total", 0)
            print(f"✅ 管理端查询成功，总数: {total}，当前页记录数: {len(records)}")
            
            # 显示最近的几条记录
            if records:
                print("\n最近的照片笔记:")
                for i, record in enumerate(records[:3]):
                    print(f"  {i+1}. ID: {record.get('id')}, 标题: {record.get('title', '无标题')}, 状态: {record.get('status')}, 创建时间: {record.get('createdAt')}")
            else:
                print("⚠️  没有找到照片笔记记录")
        else:
            print(f"❌ 管理端查询失败: {result.get('message')}")
    except Exception as e:
        print(f"❌ 管理端查询请求失败: {e}")

def test_user_photo_notes():
    """测试用户端照片笔记查询"""
    print_section("3. 用户端照片笔记查询测试")
    
    try:
        # 查询公开的照片笔记
        response = requests.get(f"{BASE_URL}/photo-notes/list?page=1&size=10")
        result = response.json()
        print_result("用户端查询结果", result)
        
        if result.get("code") == 200:
            data = result.get("data", {})
            records = data.get("records", [])
            total = data.get("total", 0)
            print(f"✅ 用户端查询成功，总数: {total}，当前页记录数: {len(records)}")
            
            # 显示最近的几条记录
            if records:
                print("\n最近的照片笔记:")
                for i, record in enumerate(records[:3]):
                    print(f"  {i+1}. ID: {record.get('id')}, 标题: {record.get('title', '无标题')}, 状态: {record.get('status')}, 创建时间: {record.get('createdAt')}")
            else:
                print("⚠️  没有找到照片笔记记录")
        else:
            print(f"❌ 用户端查询失败: {result.get('message')}")
    except Exception as e:
        print(f"❌ 用户端查询请求失败: {e}")

def test_server_status():
    """测试服务器状态"""
    print_section("0. 服务器状态测试")
    
    try:
        response = requests.get(f"{BASE_URL}/admin/system/info", timeout=5)
        result = response.json()
        print_result("服务器信息", result)
        
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def main():
    """主函数"""
    print("PhotoTagMoment 照片笔记数据验证脚本")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试服务器状态
    if not test_server_status():
        print("\n❌ 服务器不可用，退出测试")
        return
    
    # 测试管理员登录
    if test_admin_login():
        # 测试管理端查询
        test_admin_photo_notes()
    
    # 测试用户端查询
    test_user_photo_notes()
    
    print_section("测试总结")
    print("1. 如果管理端查询到的数据为空，但用户端有数据，说明状态筛选有问题")
    print("2. 如果两端都没有数据，说明数据库中确实没有照片笔记数据")
    print("3. 如果管理端查询失败，可能是权限或接口问题")
    print("4. 建议检查数据库中的实际数据和状态字段")

if __name__ == "__main__":
    main()
