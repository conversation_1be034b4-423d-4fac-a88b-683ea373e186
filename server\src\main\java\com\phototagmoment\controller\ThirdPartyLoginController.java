package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.AuthLoginDTO;
import com.phototagmoment.dto.AuthUserInfoDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.AuthService;
import com.phototagmoment.service.ThirdPartyLoginService;
import com.phototagmoment.vo.TokenVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * 第三方登录控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth/third-party")
@Tag(name = "第三方登录接口", description = "第三方登录相关接口")
public class ThirdPartyLoginController {

    @Autowired
    private ThirdPartyLoginService thirdPartyLoginService;

    @Autowired
    private AuthService authService;





    /**
     * 绑定第三方账号
     */
    @PostMapping("/bind/{source}")
    @Operation(summary = "绑定第三方账号", description = "绑定第三方平台账号")
    public ApiResponse<Boolean> bind(
            @Parameter(description = "第三方平台来源") @PathVariable String source,
            @Parameter(description = "第三方平台唯一标识") @RequestParam String openId,
            @Parameter(description = "访问令牌") @RequestParam String accessToken) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = thirdPartyLoginService.bind(userId, source, openId, accessToken, null);
        return ApiResponse.success(result);
    }



    /**
     * 解绑第三方账号
     */
    @PostMapping("/unbind/{source}")
    @Operation(summary = "解绑第三方账号", description = "解绑第三方平台账号")
    public ApiResponse<Boolean> unbind(
            @Parameter(description = "第三方平台来源") @PathVariable String source) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = thirdPartyLoginService.unbind(userId, source);
        return ApiResponse.success(result);
    }



    /**
     * 获取绑定的第三方账号列表
     */
    @GetMapping("/binding-list")
    @Operation(summary = "获取绑定的第三方账号列表", description = "获取绑定的第三方账号列表")
    public ApiResponse<List<String>> getBindingList() {
        Long userId = SecurityUtil.getCurrentUserId();
        List<String> bindingList = thirdPartyLoginService.getBindingList(userId);
        return ApiResponse.success(bindingList);
    }
}
