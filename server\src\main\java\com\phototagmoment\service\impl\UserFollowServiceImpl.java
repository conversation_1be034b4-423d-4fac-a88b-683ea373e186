package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserFollow;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.UserFollowMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.NotificationService;
import com.phototagmoment.service.UserFollowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户关注服务实现类
 */
@Slf4j
@Service
public class UserFollowServiceImpl extends ServiceImpl<UserFollowMapper, UserFollow> implements UserFollowService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private NotificationService notificationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean followUser(Long followerId, Long followingId) {
        // 不能关注自己
        if (followerId.equals(followingId)) {
            throw new BusinessException("不能关注自己");
        }

        // 检查被关注用户是否存在
        User followingUser = userMapper.selectById(followingId);
        if (followingUser == null) {
            throw new BusinessException("被关注用户不存在");
        }

        // 检查是否已关注
        LambdaQueryWrapper<UserFollow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowingId, followingId);
        if (this.count(queryWrapper) > 0) {
            return true;
        }

        // 创建关注记录
        UserFollow userFollow = new UserFollow();
        userFollow.setFollowerId(followerId);
        userFollow.setFollowingId(followingId);
        this.save(userFollow);

        // 更新关注数和粉丝数
        userMapper.incrementFollowingCount(followerId);
        userMapper.incrementFollowerCount(followingId);

        // 创建关注通知
        notificationService.createFollowNotification(followerId, followingId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfollowUser(Long followerId, Long followingId) {
        // 检查是否已关注
        LambdaQueryWrapper<UserFollow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowingId, followingId);
        if (this.count(queryWrapper) == 0) {
            return true;
        }

        // 删除关注记录
        this.remove(queryWrapper);

        // 更新关注数和粉丝数
        userMapper.decrementFollowingCount(followerId);
        userMapper.decrementFollowerCount(followingId);

        return true;
    }

    @Override
    public boolean checkFollowing(Long followerId, Long followingId) {
        if (followerId == null || followingId == null) {
            return false;
        }

        LambdaQueryWrapper<UserFollow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFollow::getFollowerId, followerId)
                .eq(UserFollow::getFollowingId, followingId);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public IPage<UserDTO> getFollowingList(Long userId, int page, int size) {
        Page<UserDTO> pageParam = new Page<>(page, size);
        return userMapper.selectFollowingList(pageParam, userId);
    }

    @Override
    public IPage<UserDTO> getFollowerList(Long userId, int page, int size) {
        Page<UserDTO> pageParam = new Page<>(page, size);
        return userMapper.selectFollowerList(pageParam, userId);
    }

    @Override
    public int getFollowingCount(Long userId) {
        LambdaQueryWrapper<UserFollow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFollow::getFollowerId, userId);
        return Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public int getFollowerCount(Long userId) {
        LambdaQueryWrapper<UserFollow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFollow::getFollowingId, userId);
        return Math.toIntExact(this.count(queryWrapper));
    }
}
