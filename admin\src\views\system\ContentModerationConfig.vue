<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>内容审核配置</span>
          <el-button type="primary" @click="saveConfig">保存配置</el-button>
        </div>
      </template>
      <el-form :model="configForm" label-width="180px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本配置" name="basic">
            <el-form-item label="启用内容审核">
              <el-switch v-model="configForm.contentModerationEnabled" />
            </el-form-item>
            <el-form-item label="图像审核服务提供商">
              <el-select v-model="configForm.imageProvider" placeholder="请选择图像审核服务提供商">
                <el-option label="本地审核" value="local" />
                <el-option label="百度内容审核" value="baidu" />
              </el-select>
            </el-form-item>
            <el-form-item label="文本审核服务提供商">
              <el-select v-model="configForm.textProvider" placeholder="请选择文本审核服务提供商">
                <el-option label="本地审核" value="local" />
              </el-select>
            </el-form-item>
            <el-form-item label="自动通过审核">
              <el-switch v-model="configForm.autoApprove" />
              <span class="form-tip">仅用于测试环境，开启后所有内容将自动通过审核</span>
            </el-form-item>
            <el-form-item label="审核模式">
              <el-select v-model="configForm.mode" placeholder="请选择审核模式">
                <el-option label="自动审核" value="auto" />
                <el-option label="人工审核" value="manual" />
                <el-option label="混合审核" value="mixed" />
              </el-select>
            </el-form-item>
            <el-form-item label="自动审核敏感度">
              <el-slider v-model="configForm.sensitivity" :min="0" :max="100" :step="1" show-input />
              <span class="form-tip">值越大越严格，建议设置为80</span>
            </el-form-item>
            <el-form-item label="联系方式过滤">
              <el-switch v-model="configForm.contactInfoFilter" />
              <span class="form-tip">是否过滤联系方式（手机号、邮箱、QQ、微信等）</span>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="百度内容审核配置" name="baidu">
            <el-form-item label="百度AppID">
              <el-input v-model="configForm.baiduAppId" placeholder="请输入百度AppID" />
            </el-form-item>
            <el-form-item label="百度ApiKey">
              <el-input v-model="configForm.baiduApiKey" placeholder="请输入百度ApiKey" />
            </el-form-item>
            <el-form-item label="百度SecretKey">
              <el-input v-model="configForm.baiduSecretKey" placeholder="请输入百度SecretKey" show-password />
            </el-form-item>
            <el-alert
              title="百度内容审核配置说明"
              type="info"
              description="请前往百度AI开放平台（https://ai.baidu.com/）创建应用，获取AppID、ApiKey和SecretKey。"
              :closable="false"
              show-icon
            />
          </el-tab-pane>
          <el-tab-pane label="敏感词配置" name="sensitive">
            <el-form-item label="启用敏感词过滤">
              <el-switch v-model="configForm.sensitiveWordFilter" />
            </el-form-item>
            <el-form-item label="敏感词替换字符">
              <el-input v-model="configForm.sensitiveWordReplaceChar" placeholder="请输入敏感词替换字符" maxlength="1" />
            </el-form-item>
            <el-alert
              title="敏感词配置说明"
              type="info"
              description="敏感词列表请在敏感词管理页面进行配置。"
              :closable="false"
              show-icon
            />
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getSystemConfig, updateSystemConfig } from '@/api/system/config'

export default {
  name: 'ContentModerationConfig',
  data() {
    return {
      activeTab: 'basic',
      configForm: {
        contentModerationEnabled: true,
        imageProvider: 'baidu',
        textProvider: 'local',
        autoApprove: false,
        mode: 'mixed',
        sensitivity: 80,
        contactInfoFilter: true,
        baiduAppId: '',
        baiduApiKey: '',
        baiduSecretKey: '',
        sensitiveWordFilter: true,
        sensitiveWordReplaceChar: '*'
      },
      loading: false
    }
  },
  created() {
    this.fetchConfig()
  },
  methods: {
    async fetchConfig() {
      this.loading = true
      try {
        // 获取基本配置
        const basicConfig = await getSystemConfig([
          'content-moderation.enabled',
          'content-moderation.image.provider',
          'content-moderation.text.provider',
          'content-moderation.auto-approve',
          'content-moderation.mode',
          'content-moderation.sensitivity',
          'content-moderation.contact-info.filter'
        ])

        // 获取百度内容审核配置
        const baiduConfig = await getSystemConfig([
          'content-moderation.baidu.app-id',
          'content-moderation.baidu.api-key',
          'content-moderation.baidu.secret-key'
        ])

        // 获取敏感词配置
        const sensitiveConfig = await getSystemConfig([
          'sensitive.word.filter',
          'sensitive.word.replace.char'
        ])

        // 合并配置
        const config = { ...basicConfig, ...baiduConfig, ...sensitiveConfig }

        // 设置表单数据
        this.configForm = {
          contentModerationEnabled: config['content-moderation.enabled'] === 'true',
          imageProvider: config['content-moderation.image.provider'] || 'baidu',
          textProvider: config['content-moderation.text.provider'] || 'local',
          autoApprove: config['content-moderation.auto-approve'] === 'true',
          mode: config['content-moderation.mode'] || 'mixed',
          sensitivity: parseInt(config['content-moderation.sensitivity'] || '80'),
          contactInfoFilter: config['content-moderation.contact-info.filter'] === 'true',
          baiduAppId: config['content-moderation.baidu.app-id'] || '',
          baiduApiKey: config['content-moderation.baidu.api-key'] || '',
          baiduSecretKey: config['content-moderation.baidu.secret-key'] || '',
          sensitiveWordFilter: config['sensitive.word.filter'] === 'true',
          sensitiveWordReplaceChar: config['sensitive.word.replace.char'] || '*'
        }
      } catch (error) {
        console.error('获取配置失败', error)
        this.$message.error('获取配置失败')
      } finally {
        this.loading = false
      }
    },
    async saveConfig() {
      this.loading = true
      try {
        // 构建配置数据
        const configData = {
          'content-moderation.enabled': this.configForm.contentModerationEnabled.toString(),
          'content-moderation.image.provider': this.configForm.imageProvider,
          'content-moderation.text.provider': this.configForm.textProvider,
          'content-moderation.auto-approve': this.configForm.autoApprove.toString(),
          'content-moderation.mode': this.configForm.mode,
          'content-moderation.sensitivity': this.configForm.sensitivity.toString(),
          'content-moderation.contact-info.filter': this.configForm.contactInfoFilter.toString(),
          'content-moderation.baidu.app-id': this.configForm.baiduAppId,
          'content-moderation.baidu.api-key': this.configForm.baiduApiKey,
          'content-moderation.baidu.secret-key': this.configForm.baiduSecretKey,
          'sensitive.word.filter': this.configForm.sensitiveWordFilter.toString(),
          'sensitive.word.replace.char': this.configForm.sensitiveWordReplaceChar
        }

        // 更新配置
        await updateSystemConfig(configData)
        this.$message.success('保存配置成功')
      } catch (error) {
        console.error('保存配置失败', error)
        this.$message.error('保存配置失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
