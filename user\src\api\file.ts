import request from '@/utils/request'

/**
 * 获取文件访问URL（带下载凭证）
 * @param fileName 文件名
 * @returns 带下载凭证的文件URL
 */
export function getFileUrl(fileName: string) {
  return request({
    url: '/file/url',
    method: 'get',
    params: { fileName }
  })
}

/**
 * 获取带下载凭证的图片URL
 * 用于私有空间的图片访问
 * @param url 原始图片URL
 * @returns 带下载凭证的图片URL
 */
export async function getPrivateImageUrl(url: string): Promise<string> {
  // 如果URL已经包含token参数，说明已经是带下载凭证的URL，直接返回
  if (url && url.includes('token=')) {
    return url
  }

  // 如果URL是七牛云的公开URL，直接返回（因为当前配置下可以直接访问）
  if (url && url.includes('sw5eg63qc.hn-bkt.clouddn.com')) {
    return url
  }

  try {
    // 从URL中提取文件名
    const domain = process.env.VUE_APP_QINIU_DOMAIN || 'https://sw5eg63qc.hn-bkt.clouddn.com'
    let fileName = url

    // 如果URL包含域名，则提取文件名部分
    if (url.startsWith('http')) {
      fileName = url.replace(domain + '/', '')
      // 移除可能存在的查询参数
      fileName = fileName.split('?')[0]
    }

    // 获取带下载凭证的URL
    const response = await getFileUrl(fileName)
    if (response && response.code === 200 && response.data) {
      return response.data
    }

    // 如果获取失败，返回原始URL
    console.warn('获取私有图片URL失败，使用原始URL:', response)
    return url
  } catch (error) {
    console.warn('获取私有图片URL出错，使用原始URL:', error)
    return url
  }
}

export default {
  getFileUrl,
  getPrivateImageUrl
}
