package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.CommentMention;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 评论用户提及关联Mapper接口
 */
@Mapper
public interface CommentMentionMapper extends BaseMapper<CommentMention> {

    /**
     * 根据评论ID获取用户提及列表
     * @param commentId 评论ID
     * @return 用户提及列表
     */
    @Select("SELECT * FROM ptm_comment_mention WHERE comment_id = #{commentId}")
    List<CommentMention> selectMentionsByCommentId(@Param("commentId") Long commentId);

    /**
     * 根据被提及用户ID获取评论ID列表
     * @param mentionedUserId 被提及用户ID
     * @return 评论ID列表
     */
    @Select("SELECT comment_id FROM ptm_comment_mention WHERE mentioned_user_id = #{mentionedUserId}")
    List<Long> selectCommentIdsByMentionedUserId(@Param("mentionedUserId") Long mentionedUserId);

    /**
     * 根据被提及用户名获取评论ID列表
     * @param mentionedUsername 被提及用户名
     * @return 评论ID列表
     */
    @Select("SELECT comment_id FROM ptm_comment_mention WHERE mentioned_username = #{mentionedUsername}")
    List<Long> selectCommentIdsByMentionedUsername(@Param("mentionedUsername") String mentionedUsername);

    /**
     * 批量插入评论用户提及
     * @param commentMentions 评论用户提及列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<CommentMention> commentMentions);
}
