package com.phototagmoment.service;

import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * 认证服务接口
 * 提供统一的用户认证信息获取和设置方法
 */
public interface AuthenticationService {

    /**
     * 获取当前认证信息
     *
     * @return 认证信息
     */
    Authentication getCurrentAuthentication();

    /**
     * 获取当前认证用户
     *
     * @return 用户对象，如果未认证则返回null
     */
    User getCurrentUser();

    /**
     * 获取当前认证用户ID
     *
     * @return 用户ID，如果未认证则返回null
     */
    Long getCurrentUserId();

    /**
     * 获取当前认证用户名
     *
     * @return 用户名，如果未认证则返回null
     */
    String getCurrentUsername();

    /**
     * 判断当前用户是否已认证
     *
     * @return 是否已认证
     */
    boolean isAuthenticated();

    /**
     * 判断当前用户是否具有指定角色
     *
     * @param role 角色
     * @return 是否具有指定角色
     */
    boolean hasRole(String role);

    /**
     * 获取用户认证信息
     *
     * @param userId 用户ID
     * @param identityType 认证类型
     * @return 用户认证信息
     */
    UserAuth getUserAuth(Long userId, String identityType);

    /**
     * 设置认证信息
     *
     * @param user 用户对象
     * @param password 密码（可为空）
     * @return 认证对象
     */
    Authentication setAuthentication(User user, String password);

    /**
     * 清除认证信息
     */
    void clearAuthentication();

    /**
     * 创建用户详情
     *
     * @param user 用户对象
     * @return 用户详情
     */
    UserDetails createUserDetails(User user);
}
