<template>
  <div class="file-upload-config-test">
    <el-card header="文件上传配置功能测试">
      <div class="test-section">
        <h3>权限测试</h3>
        <p>当前用户权限：</p>
        <ul>
          <li>ADMIN权限: {{ hasPermission('ADMIN') ? '✅' : '❌' }}</li>
          <li>SUPER_ADMIN权限: {{ hasPermission('SUPER_ADMIN') ? '✅' : '❌' }}</li>
        </ul>
      </div>

      <div class="test-section">
        <h3>API接口测试</h3>
        <el-space direction="vertical" style="width: 100%">
          <el-button @click="testGetConfigList" type="primary">
            测试获取配置列表
          </el-button>
          <el-button @click="testToggleConfig" type="warning" :disabled="!testConfigId">
            测试切换配置状态 (ID: {{ testConfigId }})
          </el-button>
          <el-button @click="testSetDefault" type="success" :disabled="!testConfigId">
            测试设置默认配置 (ID: {{ testConfigId }})
          </el-button>
        </el-space>
      </div>

      <div class="test-section">
        <h3>测试结果</h3>
        <el-scrollbar height="300px">
          <div class="test-results">
            <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
              <div class="test-time">{{ result.time }}</div>
              <div class="test-action">{{ result.action }}</div>
              <div class="test-status" :class="result.success ? 'success' : 'error'">
                {{ result.success ? '成功' : '失败' }}
              </div>
              <div class="test-message">{{ result.message }}</div>
            </div>
          </div>
        </el-scrollbar>
      </div>

      <div class="test-section" v-if="configList.length > 0">
        <h3>配置列表 ({{ configList.length }}个)</h3>
        <el-table :data="configList" size="small">
          <el-table-column prop="id" label="ID" width="60" />
          <el-table-column prop="configName" label="配置名称" />
          <el-table-column prop="storageType" label="存储类型" />
          <el-table-column label="启用状态" width="100">
            <template #default="{ row }">
              <el-switch
                v-model="row.enabled"
                @change="handleToggleTest(row)"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button size="small" @click="handleSetDefaultTest(row)">
                设为默认
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { hasPermission } from '@/utils/permission'
import { 
  getConfigList, 
  toggleConfig, 
  setDefaultConfig 
} from '@/api/fileUploadConfig'

// 响应式数据
const testResults = ref<Array<{
  time: string
  action: string
  success: boolean
  message: string
}>>([])

const configList = ref<any[]>([])
const testConfigId = ref<number | null>(null)

// 添加测试结果
const addTestResult = (action: string, success: boolean, message: string) => {
  testResults.value.unshift({
    time: new Date().toLocaleTimeString(),
    action,
    success,
    message
  })
}

// 测试获取配置列表
const testGetConfigList = async () => {
  try {
    const response = await getConfigList({ page: 1, size: 10 })
    const data = (response as any).data
    
    if ((response as any).code === 200) {
      configList.value = data.records || []
      if (configList.value.length > 0) {
        testConfigId.value = configList.value[0].id
      }
      addTestResult('获取配置列表', true, `成功获取${configList.value.length}个配置`)
    } else {
      addTestResult('获取配置列表', false, (response as any).message || '获取失败')
    }
  } catch (error: any) {
    console.error('测试获取配置列表失败:', error)
    addTestResult('获取配置列表', false, error.response?.data?.message || '请求失败')
  }
}

// 测试切换配置状态
const testToggleConfig = async () => {
  if (!testConfigId.value) {
    ElMessage.warning('请先获取配置列表')
    return
  }

  try {
    const response = await toggleConfig(testConfigId.value, true)
    
    if ((response as any).code === 200) {
      addTestResult('切换配置状态', true, (response as any).message || '切换成功')
    } else {
      addTestResult('切换配置状态', false, (response as any).message || '切换失败')
    }
  } catch (error: any) {
    console.error('测试切换配置状态失败:', error)
    addTestResult('切换配置状态', false, error.response?.data?.message || '请求失败')
  }
}

// 测试设置默认配置
const testSetDefault = async () => {
  if (!testConfigId.value) {
    ElMessage.warning('请先获取配置列表')
    return
  }

  try {
    const response = await setDefaultConfig(testConfigId.value)
    
    if ((response as any).code === 200) {
      addTestResult('设置默认配置', true, (response as any).message || '设置成功')
    } else {
      addTestResult('设置默认配置', false, (response as any).message || '设置失败')
    }
  } catch (error: any) {
    console.error('测试设置默认配置失败:', error)
    addTestResult('设置默认配置', false, error.response?.data?.message || '请求失败')
  }
}

// 处理表格中的切换测试
const handleToggleTest = async (row: any) => {
  const originalState = !row.enabled
  
  try {
    const response = await toggleConfig(row.id, row.enabled)
    
    if ((response as any).code === 200) {
      addTestResult(`切换配置${row.configName}`, true, `配置已${row.enabled ? '启用' : '禁用'}`)
    } else {
      row.enabled = originalState
      addTestResult(`切换配置${row.configName}`, false, (response as any).message || '切换失败')
    }
  } catch (error: any) {
    row.enabled = originalState
    addTestResult(`切换配置${row.configName}`, false, error.response?.data?.message || '请求失败')
  }
}

// 处理表格中的设置默认测试
const handleSetDefaultTest = async (row: any) => {
  try {
    const response = await setDefaultConfig(row.id)
    
    if ((response as any).code === 200) {
      addTestResult(`设置${row.configName}为默认`, true, '设置成功')
      // 刷新列表
      testGetConfigList()
    } else {
      addTestResult(`设置${row.configName}为默认`, false, (response as any).message || '设置失败')
    }
  } catch (error: any) {
    addTestResult(`设置${row.configName}为默认`, false, error.response?.data?.message || '请求失败')
  }
}

// 生命周期
onMounted(() => {
  addTestResult('页面初始化', true, '测试页面加载完成')
  testGetConfigList()
})
</script>

<style scoped>
.file-upload-config-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.test-section h3 {
  margin-top: 0;
  color: #303133;
}

.test-results {
  font-family: monospace;
}

.test-result-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.test-time {
  color: #909399;
  min-width: 80px;
}

.test-action {
  color: #606266;
  min-width: 120px;
}

.test-status.success {
  color: #67c23a;
}

.test-status.error {
  color: #f56c6c;
}

.test-message {
  color: #303133;
  flex: 1;
}
</style>
