<template>
  <div class="profile-settings-container">
    <van-nav-bar
      title="个人资料"
      left-arrow
      @click-left="goBack"
      fixed
    />

    <div class="profile-settings-content">
      <van-form @submit="saveProfile">
        <div class="avatar-section">
          <div class="avatar-wrapper" @click="triggerAvatarUpload">
            <van-image
              round
              width="80"
              height="80"
              :src="form.avatar || 'https://randomuser.me/api/portraits/men/1.jpg'"
              fit="cover"
            />
            <div class="avatar-edit-icon">
              <van-icon name="photograph" />
            </div>
          </div>
          <input
            type="file"
            ref="avatarInput"
            accept="image/*"
            class="avatar-input"
            @change="handleAvatarChange"
          />
          <p class="avatar-hint">点击更换头像</p>
        </div>

        <van-cell-group inset>
          <van-field
            v-model="form.nickname"
            name="nickname"
            label="昵称"
            placeholder="请输入昵称"
            :rules="[{ required: true, message: '请输入昵称' }]"
          />

          <van-field
            v-model="form.bio"
            name="bio"
            label="个人简介"
            type="textarea"
            rows="3"
            placeholder="介绍一下自己吧"
            maxlength="200"
            show-word-limit
          />

          <van-field
            v-model="form.gender"
            name="gender"
            label="性别"
            readonly
            is-link
            @click="showGenderPicker = true"
            :value="genderOptions[form.gender]"
          />

          <van-field
            v-model="form.birthday"
            name="birthday"
            label="生日"
            readonly
            is-link
            @click="showDatePicker = true"
            placeholder="请选择生日"
          />

          <van-field
            v-model="form.location"
            name="location"
            label="所在地"
            placeholder="请输入所在地"
          />

          <van-field
            v-model="form.website"
            name="website"
            label="个人网站"
            placeholder="请输入个人网站"
          />
        </van-cell-group>

        <van-cell-group inset title="联系方式" class="contact-group">
          <van-field
            v-model="form.email"
            name="email"
            label="邮箱"
            placeholder="请输入邮箱"
            :rules="[{ pattern: emailPattern, message: '请输入正确的邮箱地址' }]"
          />

          <van-field
            v-model="form.phone"
            name="phone"
            label="手机号"
            placeholder="请输入手机号"
            :rules="[{ pattern: phonePattern, message: '请输入正确的手机号' }]"
          />
        </van-cell-group>

        <div class="submit-button">
          <van-button round block type="primary" native-type="submit" :loading="saving">
            保存
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 性别选择器 -->
    <van-popup
      v-model:show="showGenderPicker"
      position="bottom"
      round
    >
      <van-picker
        :columns="Object.values(genderOptions)"
        @confirm="confirmGender"
        @cancel="showGenderPicker = false"
        show-toolbar
        title="选择性别"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup
      v-model:show="showDatePicker"
      position="bottom"
      round
    >
      <van-date-picker
        v-model="selectedDate"
        title="选择生日"
        :min-date="new Date(1900, 0, 1)"
        :max-date="new Date()"
        @confirm="confirmDate"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showSuccessToast } from 'vant';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();

// 检查登录状态
if (!userStore.isLoggedIn) {
  router.push('/auth/login?redirect=/settings/profile');
}

// 表单数据
const form = ref({
  nickname: '',
  bio: '',
  gender: 0, // 0: 未设置, 1: 男, 2: 女
  birthday: '',
  location: '',
  website: '',
  email: '',
  phone: '',
  avatar: ''
});

// 性别选项
const genderOptions = {
  0: '未设置',
  1: '男',
  2: '女'
};

// 验证规则
const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phonePattern = /^1[3-9]\d{9}$/;

// 状态
const saving = ref(false);
const showGenderPicker = ref(false);
const showDatePicker = ref(false);
const selectedDate = ref(new Date());
const avatarInput = ref<HTMLInputElement | null>(null);

// 返回上一页
const goBack = () => {
  router.back();
};

// 触发头像上传
const triggerAvatarUpload = () => {
  avatarInput.value?.click();
};

// 处理头像变更
const handleAvatarChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (!input.files || input.files.length === 0) return;

  const file = input.files[0];

  try {
    // 创建预览URL
    const preview = URL.createObjectURL(file);
    form.value.avatar = preview;

    // 实际项目中应该上传头像到服务器
    // const { uploadAvatar } = await import('@/api/user');
    // const avatarUrl = await uploadAvatar(file);
    // form.value.avatar = avatarUrl;

    showToast('头像已更新');
  } catch (error) {
    console.error('上传头像失败', error);
    showToast('上传头像失败，请稍后重试');
  }
};

// 确认性别选择
const confirmGender = ({ selectedOptions }) => {
  const selected = selectedOptions[0];
  const key = Object.keys(genderOptions).find(k => genderOptions[k] === selected);

  if (key) {
    form.value.gender = Number(key);
  }

  showGenderPicker.value = false;
};

// 确认日期选择
const confirmDate = ({ selectedValues }) => {
  const [year, month, day] = selectedValues;
  form.value.birthday = `${year}-${month}-${day}`;
  showDatePicker.value = false;
};

// 保存个人资料
const saveProfile = async () => {
  saving.value = true;

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 更新用户信息
    userStore.setUser({
      ...userStore.userInfo,
      ...form.value
    });

    showSuccessToast('个人资料已更新');
    router.push('/settings');
  } catch (error) {
    console.error('保存个人资料失败', error);
    showToast('保存失败，请稍后重试');
  } finally {
    saving.value = false;
  }
};

// 获取用户信息
onMounted(() => {
  if (userStore.userInfo) {
    // 填充表单数据
    form.value = {
      nickname: userStore.userInfo.nickname || '',
      bio: userStore.userInfo.bio || '',
      gender: userStore.userInfo.gender || 0,
      birthday: userStore.userInfo.birthday || '',
      location: userStore.userInfo.location || '',
      website: userStore.userInfo.website || '',
      email: userStore.userInfo.email || '',
      phone: userStore.userInfo.phone || '',
      avatar: userStore.userInfo.avatar || ''
    };

    // 如果有生日，设置日期选择器的初始值
    if (form.value.birthday) {
      const [year, month, day] = form.value.birthday.split('-').map(Number);
      selectedDate.value = new Date(year, month - 1, day);
    }
  }
});
</script>

<style lang="scss" scoped>
.profile-settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px; // 导航栏高度
}

.profile-settings-content {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;

  @media (min-width: 768px) {
    padding: 30px;

    .van-cell-group {
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;

    .avatar-wrapper {
      position: relative;
      cursor: pointer;

      @media (min-width: 768px) {
        .van-image {
          width: 120px !important;
          height: 120px !important;
        }
      }

      .avatar-edit-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: #3498db;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        @media (min-width: 768px) {
          width: 32px;
          height: 32px;
          font-size: 18px;
        }
      }
    }

    .avatar-input {
      display: none;
    }

    .avatar-hint {
      margin-top: 8px;
      font-size: 14px;
      color: #666;

      @media (min-width: 768px) {
        font-size: 16px;
        margin-top: 12px;
      }
    }
  }

  .contact-group {
    margin-top: 16px;
  }

  .submit-button {
    margin: 24px 16px;

    @media (min-width: 768px) {
      margin: 30px auto;
      max-width: 300px;

      .van-button {
        height: 44px;
        font-size: 16px;
      }
    }
  }
}
</style>
