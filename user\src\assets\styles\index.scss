/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 16px;
  color: #333;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
}

a {
  text-decoration: none;
  color: #3498db;
}

/* 通用样式类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }
}

@media (max-width: 576px) {
  html, body {
    font-size: 14px;
  }
}
