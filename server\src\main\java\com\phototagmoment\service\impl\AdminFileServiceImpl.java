package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.FileInfoDTO;
import com.phototagmoment.entity.FileRecord;
import com.phototagmoment.mapper.FileRecordMapper;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.AdminFileService;
import com.phototagmoment.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 后台文件管理服务实现类
 */
@Slf4j
@Service
public class AdminFileServiceImpl implements AdminFileService {

    @Autowired
    private FileRecordMapper fileRecordMapper;

    @Autowired
    private StorageService storageService;

    @Override
    public IPage<FileInfoDTO> getFileList(Page<FileInfoDTO> page, String fileType, Long uploaderId, 
                                          String keyword, String startDate, String endDate) {
        return fileRecordMapper.selectFileList(page, fileType, uploaderId, keyword, startDate, endDate, FileRecord.Status.NORMAL);
    }

    @Override
    public FileInfoDTO getFileDetail(Long fileId) {
        FileInfoDTO fileInfo = fileRecordMapper.selectFileDetail(fileId);
        if (fileInfo != null) {
            // 更新访问记录
            updateFileAccess(fileId);
        }
        return fileInfo;
    }

    @Override
    @Transactional
    public Map<String, Object> batchDeleteFiles(List<Long> fileIds) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (Long fileId : fileIds) {
            try {
                // 获取文件记录
                FileRecord fileRecord = fileRecordMapper.selectById(fileId);
                if (fileRecord == null) {
                    errors.add("文件ID " + fileId + " 不存在");
                    failCount++;
                    continue;
                }

                // 删除物理文件
                boolean deleted = storageService.deleteFile(fileRecord.getFilePath());
                if (!deleted) {
                    log.warn("删除物理文件失败: {}", fileRecord.getFilePath());
                }

                // 删除数据库记录
                fileRecordMapper.deleteById(fileId);
                successCount++;

                log.info("管理员{}删除文件: {}", SecurityUtil.getCurrentUserId(), fileRecord.getFilePath());
            } catch (Exception e) {
                log.error("删除文件失败: fileId={}", fileId, e);
                errors.add("文件ID " + fileId + " 删除失败: " + e.getMessage());
                failCount++;
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        return result;
    }

    @Override
    public boolean renameFile(Long fileId, String newName) {
        try {
            FileRecord fileRecord = fileRecordMapper.selectById(fileId);
            if (fileRecord == null) {
                return false;
            }

            fileRecord.setOriginalName(newName);
            fileRecord.setUpdatedAt(LocalDateTime.now());
            
            int updated = fileRecordMapper.updateById(fileRecord);
            if (updated > 0) {
                log.info("管理员{}重命名文件: {} -> {}", SecurityUtil.getCurrentUserId(), fileRecord.getOriginalName(), newName);
                return true;
            }
        } catch (Exception e) {
            log.error("重命名文件失败: fileId={}, newName={}", fileId, newName, e);
        }
        return false;
    }

    @Override
    public boolean moveToTrash(Long fileId) {
        try {
            FileRecord fileRecord = fileRecordMapper.selectById(fileId);
            if (fileRecord == null) {
                return false;
            }

            fileRecord.setStatus(FileRecord.Status.TRASH);
            fileRecord.setDeletedAt(LocalDateTime.now());
            fileRecord.setUpdatedAt(LocalDateTime.now());
            
            int updated = fileRecordMapper.updateById(fileRecord);
            if (updated > 0) {
                log.info("管理员{}将文件移至回收站: {}", SecurityUtil.getCurrentUserId(), fileRecord.getFilePath());
                return true;
            }
        } catch (Exception e) {
            log.error("移动文件到回收站失败: fileId={}", fileId, e);
        }
        return false;
    }

    @Override
    public boolean restoreFile(Long fileId) {
        try {
            FileRecord fileRecord = fileRecordMapper.selectById(fileId);
            if (fileRecord == null || !fileRecord.isInTrash()) {
                return false;
            }

            fileRecord.setStatus(FileRecord.Status.NORMAL);
            fileRecord.setDeletedAt(null);
            fileRecord.setUpdatedAt(LocalDateTime.now());
            
            int updated = fileRecordMapper.updateById(fileRecord);
            if (updated > 0) {
                log.info("管理员{}恢复文件: {}", SecurityUtil.getCurrentUserId(), fileRecord.getFilePath());
                return true;
            }
        } catch (Exception e) {
            log.error("恢复文件失败: fileId={}", fileId, e);
        }
        return false;
    }

    @Override
    public IPage<FileInfoDTO> getTrashFiles(Page<FileInfoDTO> page) {
        return fileRecordMapper.selectFileList(page, null, null, null, null, null, FileRecord.Status.TRASH);
    }

    @Override
    @Transactional
    public Map<String, Object> clearTrash() {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        try {
            // 获取回收站中的所有文件
            QueryWrapper<FileRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", FileRecord.Status.TRASH);
            List<FileRecord> trashFiles = fileRecordMapper.selectList(queryWrapper);

            for (FileRecord fileRecord : trashFiles) {
                try {
                    // 删除物理文件
                    boolean deleted = storageService.deleteFile(fileRecord.getFilePath());
                    if (!deleted) {
                        log.warn("删除物理文件失败: {}", fileRecord.getFilePath());
                    }

                    // 删除数据库记录
                    fileRecordMapper.deleteById(fileRecord.getId());
                    successCount++;
                } catch (Exception e) {
                    log.error("清理回收站文件失败: {}", fileRecord.getFilePath(), e);
                    errors.add("文件 " + fileRecord.getOriginalName() + " 清理失败: " + e.getMessage());
                    failCount++;
                }
            }

            log.info("管理员{}清空回收站，成功: {}, 失败: {}", SecurityUtil.getCurrentUserId(), successCount, failCount);
        } catch (Exception e) {
            log.error("清空回收站失败", e);
            errors.add("清空回收站失败: " + e.getMessage());
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        return result;
    }

    @Override
    public Map<String, Object> getFileStatistics() {
        Map<String, Object> statistics = fileRecordMapper.selectFileStatistics();
        
        // 添加额外的统计信息
        statistics.put("statisticsTime", LocalDateTime.now());
        
        return statistics;
    }

    @Override
    public Map<String, Object> getStorageUsage() {
        List<Map<String, Object>> storageUsageList = fileRecordMapper.selectStorageUsage();
        
        Map<String, Object> result = new HashMap<>();
        long totalUsedSize = 0;
        int totalFileCount = 0;
        
        for (Map<String, Object> usage : storageUsageList) {
            Long usedSize = (Long) usage.get("usedSize");
            Integer fileCount = (Integer) usage.get("fileCount");
            
            if (usedSize != null) {
                totalUsedSize += usedSize;
            }
            if (fileCount != null) {
                totalFileCount += fileCount;
            }
        }
        
        result.put("storageDetails", storageUsageList);
        result.put("totalUsedSize", totalUsedSize);
        result.put("totalFileCount", totalFileCount);
        result.put("usedSizeFormatted", formatFileSize(totalUsedSize));
        
        return result;
    }

    @Override
    public List<Map<String, Object>> getFileTypeDistribution() {
        return fileRecordMapper.selectFileTypeDistribution();
    }

    @Override
    public Map<String, Object> checkFileIntegrity(List<Long> fileIds) {
        Map<String, Object> result = new HashMap<>();
        int checkedCount = 0;
        int validCount = 0;
        int invalidCount = 0;
        List<String> invalidFiles = new ArrayList<>();

        try {
            List<FileRecord> filesToCheck;
            if (fileIds == null || fileIds.isEmpty()) {
                // 检查所有文件
                QueryWrapper<FileRecord> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("status", FileRecord.Status.NORMAL);
                filesToCheck = fileRecordMapper.selectList(queryWrapper);
            } else {
                // 检查指定文件
                filesToCheck = fileRecordMapper.selectBatchIds(fileIds);
            }

            for (FileRecord fileRecord : filesToCheck) {
                checkedCount++;
                
                // 这里可以添加文件完整性检查逻辑
                // 例如：检查文件是否存在、大小是否匹配、MD5是否正确等
                boolean isValid = true; // 简化实现，实际应该调用存储服务检查
                
                if (isValid) {
                    validCount++;
                } else {
                    invalidCount++;
                    invalidFiles.add(fileRecord.getOriginalName());
                }
            }
        } catch (Exception e) {
            log.error("文件完整性检查失败", e);
        }

        result.put("checkedCount", checkedCount);
        result.put("validCount", validCount);
        result.put("invalidCount", invalidCount);
        result.put("invalidFiles", invalidFiles);
        result.put("checkTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> generateAccessUrl(Long fileId, Integer expireSeconds) {
        try {
            FileRecord fileRecord = fileRecordMapper.selectById(fileId);
            if (fileRecord == null) {
                return null;
            }

            // 更新访问记录
            updateFileAccess(fileId);

            Map<String, Object> result = new HashMap<>();
            result.put("fileId", fileId);
            result.put("fileName", fileRecord.getOriginalName());
            result.put("accessUrl", fileRecord.getFileUrl()); // 简化实现，实际可能需要生成临时URL
            result.put("expireTime", LocalDateTime.now().plusSeconds(expireSeconds));
            result.put("expireSeconds", expireSeconds);
            
            return result;
        } catch (Exception e) {
            log.error("生成访问链接失败: fileId={}", fileId, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> batchMoveFiles(List<Long> fileIds, String targetPath) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        // 简化实现，实际应该实现文件移动逻辑
        for (Long fileId : fileIds) {
            try {
                FileRecord fileRecord = fileRecordMapper.selectById(fileId);
                if (fileRecord == null) {
                    errors.add("文件ID " + fileId + " 不存在");
                    failCount++;
                    continue;
                }

                // 这里应该实现实际的文件移动逻辑
                successCount++;
            } catch (Exception e) {
                log.error("移动文件失败: fileId={}", fileId, e);
                errors.add("文件ID " + fileId + " 移动失败: " + e.getMessage());
                failCount++;
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        return result;
    }

    @Override
    public IPage<FileInfoDTO> searchFiles(Page<FileInfoDTO> page, String keyword, String fileType, 
                                          Long minSize, Long maxSize) {
        return fileRecordMapper.searchFiles(page, keyword, fileType, minSize, maxSize, FileRecord.Status.NORMAL);
    }

    @Override
    public Long recordFileUpload(String originalName, String fileName, String filePath, String fileUrl,
                                Long fileSize, String mimeType, Long uploaderId, String uploaderType, String category) {
        try {
            FileRecord fileRecord = new FileRecord();
            fileRecord.setOriginalName(originalName);
            fileRecord.setFileName(fileName);
            fileRecord.setFilePath(filePath);
            fileRecord.setFileUrl(fileUrl);
            fileRecord.setFileSize(fileSize);
            fileRecord.setMimeType(mimeType);
            fileRecord.setUploaderId(uploaderId);
            fileRecord.setUploaderType(uploaderType);
            fileRecord.setCategory(category);
            fileRecord.setStatus(FileRecord.Status.NORMAL);
            fileRecord.setAccessCount(0);
            
            // 设置文件扩展名
            if (originalName != null && originalName.contains(".")) {
                String extension = originalName.substring(originalName.lastIndexOf(".") + 1).toLowerCase();
                fileRecord.setExtension(extension);
            }
            
            // 设置存储类型（简化实现）
            fileRecord.setStorageType(FileRecord.StorageType.LOCAL);
            
            fileRecord.setCreatedAt(LocalDateTime.now());
            fileRecord.setUpdatedAt(LocalDateTime.now());
            
            int inserted = fileRecordMapper.insert(fileRecord);
            if (inserted > 0) {
                log.info("记录文件上传: uploaderId={}, filePath={}", uploaderId, filePath);
                return fileRecord.getId();
            }
        } catch (Exception e) {
            log.error("记录文件上传失败", e);
        }
        return null;
    }

    @Override
    public void updateFileAccess(Long fileId) {
        try {
            fileRecordMapper.updateFileAccess(fileId, LocalDateTime.now());
        } catch (Exception e) {
            log.error("更新文件访问记录失败: fileId={}", fileId, e);
        }
    }

    @Override
    public List<Map<String, Object>> getUploadStatistics(Integer days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        return fileRecordMapper.selectUploadStatistics(startDate);
    }

    @Override
    public List<FileInfoDTO> getPopularFiles(Integer limit) {
        return fileRecordMapper.selectPopularFiles(limit);
    }

    @Override
    public List<FileInfoDTO> getRecentFiles(Integer limit) {
        return fileRecordMapper.selectRecentFiles(limit);
    }

    @Override
    public Map<String, Object> cleanExpiredTempFiles() {
        Map<String, Object> result = new HashMap<>();
        int cleanedCount = 0;
        List<String> errors = new ArrayList<>();

        try {
            List<FileRecord> expiredFiles = fileRecordMapper.selectExpiredTempFiles(LocalDateTime.now());
            
            for (FileRecord fileRecord : expiredFiles) {
                try {
                    // 删除物理文件
                    storageService.deleteFile(fileRecord.getFilePath());
                    
                    // 删除数据库记录
                    fileRecordMapper.deleteById(fileRecord.getId());
                    cleanedCount++;
                } catch (Exception e) {
                    log.error("清理过期临时文件失败: {}", fileRecord.getFilePath(), e);
                    errors.add("文件 " + fileRecord.getOriginalName() + " 清理失败");
                }
            }
        } catch (Exception e) {
            log.error("清理过期临时文件失败", e);
            errors.add("清理操作失败: " + e.getMessage());
        }

        result.put("cleanedCount", cleanedCount);
        result.put("errors", errors);
        result.put("cleanTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> syncFileStorageStatus() {
        // 简化实现，实际应该同步文件存储状态
        Map<String, Object> result = new HashMap<>();
        result.put("syncTime", LocalDateTime.now());
        result.put("message", "同步完成");
        return result;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
