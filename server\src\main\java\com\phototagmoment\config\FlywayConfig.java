package com.phototagmoment.config;

import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Flyway 配置类
 */
@Slf4j
@Configuration
public class FlywayConfig {

    @Autowired
    private DataSource dataSource;

    /**
     * 配置 Flyway
     */
    @Bean(name = "flyway")
    public Flyway flyway() {
        log.info("初始化 Flyway");
        Flyway flyway = Flyway.configure()
                .dataSource(dataSource)
                .locations("classpath:db/migration")
                .baselineOnMigrate(true)
                .validateOnMigrate(false)  // Disable validation temporarily
                .outOfOrder(true)
                .load();

        try {
            // 暂时禁用 Flyway 迁移，因为有版本冲突
            log.info("Flyway 迁移已禁用，因为有版本冲突");
            // flyway.repair();
            // flyway.migrate();
        } catch (Exception e) {
            log.error("Flyway 迁移失败: {}", e.getMessage(), e);
            // Continue without failing the application
        }

        return flyway;
    }
}
