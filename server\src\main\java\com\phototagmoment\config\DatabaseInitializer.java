package com.phototagmoment.config;

import com.phototagmoment.util.DatabaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据库初始化器
 */
@Component
@Slf4j
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private DatabaseUtil databaseUtil;

    @Override
    public void run(String... args) {
        try {
            log.info("开始初始化数据库结构...");
            
            // 添加评论表的回复数列
            databaseUtil.addColumn("ptm_comment", "reply_count", "INT DEFAULT 0 COMMENT '回复数' AFTER like_count");
            
            log.info("数据库结构初始化完成");
        } catch (Exception e) {
            log.error("数据库结构初始化失败", e);
        }
    }
}
