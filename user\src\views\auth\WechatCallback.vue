<template>
  <div class="wechat-callback-container">
    <div class="callback-content">
      <van-loading v-if="loading" size="24px" vertical>处理微信登录...</van-loading>
      <div v-else-if="error" class="error-message">
        <van-icon name="close" size="48" color="#ee0a24" />
        <p>{{ error }}</p>
        <van-button type="primary" @click="goToLogin">返回登录</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { loginByWechat } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loading = ref(true)
const error = ref('')

// 处理微信登录回调
onMounted(async () => {
  try {
    // 获取授权码
    const code = route.query.code as string
    const state = route.query.state as string
    
    if (!code) {
      error.value = '授权失败，请重新登录'
      loading.value = false
      return
    }
    
    // 调用微信登录接口
    const res = await loginByWechat(code)
    if (res.code === 200 && res.data) {
      // 保存登录信息
      userStore.setToken(res.data.token)
      userStore.setUser(res.data.user)
      
      // 跳转到首页
      router.push('/')
      showToast('登录成功')
    } else {
      error.value = res.message || '登录失败，请重新尝试'
      loading.value = false
    }
  } catch (error: any) {
    console.error('微信登录回调处理失败', error)
    error.value = error.message || '登录失败，请重新尝试'
    loading.value = false
  }
})

// 返回登录页
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.wechat-callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f7f8fa;
}

.callback-content {
  text-align: center;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-message p {
  color: #666;
  margin: 0;
}
</style>
