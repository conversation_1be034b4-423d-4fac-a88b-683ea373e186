package com.phototagmoment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.SensitiveWordDTO;
import com.phototagmoment.entity.SensitiveWord;
import com.phototagmoment.mapper.SensitiveWordMapper;
import com.phototagmoment.service.SensitiveWordService;
import com.phototagmoment.service.SystemConfigService;
import com.phototagmoment.vo.SensitiveWordStatVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 敏感词Service实现类
 */
@Slf4j
@Service
public class SensitiveWordServiceImpl implements SensitiveWordService {

    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;

    @Autowired
    private SystemConfigService systemConfigService;

    // 敏感词Map
    private Map<String, Object> sensitiveWordMap;

    @PostConstruct
    public void init() {
        log.info("系统启动，开始初始化敏感词库...");
        try {
            // 初始化敏感词库
            initSensitiveWordMap();
        } catch (Exception e) {
            log.warn("初始化敏感词库失败，可能是表不存在: {}", e.getMessage());
            // 只在DEBUG级别输出完整堆栈
            if (log.isDebugEnabled()) {
                log.debug("初始化敏感词库失败详细信息:", e);
            }
            // 创建一个空的敏感词库
            sensitiveWordMap = new HashMap<>();
            log.info("已创建空敏感词库");
        }
    }

    @Override
    public List<SensitiveWord> listAllWords() {
        return sensitiveWordMapper.selectList(null);
    }

    @Override
    public IPage<SensitiveWord> pageWords(int page, int pageSize, String keyword, String type) {
        Page<SensitiveWord> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<SensitiveWord> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(SensitiveWord::getWord, keyword);
        }

        if (StringUtils.hasText(type)) {
            queryWrapper.eq(SensitiveWord::getType, type);
        }

        queryWrapper.orderByAsc(SensitiveWord::getWord);

        return sensitiveWordMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public SensitiveWord getWordById(Long id) {
        return sensitiveWordMapper.selectById(id);
    }

    @Override
    @Transactional
    public boolean saveWord(SensitiveWord word) {
        int result = sensitiveWordMapper.insert(word);
        if (result > 0) {
            // 刷新敏感词库
            refreshSensitiveWordMap();
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean batchSaveWord(List<SensitiveWord> words) {
        if (words == null || words.isEmpty()) {
            return false;
        }

        for (SensitiveWord word : words) {
            sensitiveWordMapper.insert(word);
        }

        // 刷新敏感词库
        refreshSensitiveWordMap();

        return true;
    }

    @Override
    @Transactional
    public boolean updateWord(SensitiveWord word) {
        log.info("更新敏感词: {}", word);

        // 检查敏感词是否存在
        SensitiveWord existingWord = sensitiveWordMapper.selectById(word.getId());
        if (existingWord == null) {
            log.error("敏感词不存在: {}", word.getId());
            throw new RuntimeException("敏感词不存在");
        }

        // 设置更新时间
        word.setUpdatedAt(LocalDateTime.now());

        // 保留创建时间
        word.setCreatedAt(existingWord.getCreatedAt());

        // 保留删除标记
        word.setIsDeleted(existingWord.getIsDeleted());

        try {
            int result = sensitiveWordMapper.updateById(word);
            if (result > 0) {
                log.info("敏感词更新成功: {}", word.getId());
                // 刷新敏感词库
                refreshSensitiveWordMap();
            } else {
                log.error("敏感词更新失败: {}", word.getId());
            }

            return result > 0;
        } catch (Exception e) {
            log.error("更新敏感词异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public boolean deleteWord(Long id) {
        int result = sensitiveWordMapper.deleteById(id);
        if (result > 0) {
            // 刷新敏感词库
            refreshSensitiveWordMap();
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean batchDeleteWord(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        int result = 0;
        for (Long id : ids) {
            result += sensitiveWordMapper.deleteById(id);
        }

        if (result > 0) {
            // 刷新敏感词库
            refreshSensitiveWordMap();
        }

        return result > 0;
    }

    @Override
    public boolean containsSensitiveWord(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        // 检查是否开启敏感词过滤
        if (!systemConfigService.getBooleanValue("sensitive.word.filter", true)) {
            return false;
        }

        // 转为小写
        text = text.toLowerCase();

        // 敏感词库为空，则初始化
        if (sensitiveWordMap == null) {
            initSensitiveWordMap();
        }

        // 敏感词库为空，则返回false
        if (sensitiveWordMap == null || sensitiveWordMap.isEmpty()) {
            return false;
        }

        // 检查是否包含敏感词
        for (int i = 0; i < text.length(); i++) {
            int length = checkSensitiveWord(text, i);
            if (length > 0) {
                return true;
            }
        }

        return false;
    }

    @Override
    public Set<String> getSensitiveWords(String text) {
        Set<String> sensitiveWords = new HashSet<>();

        if (!StringUtils.hasText(text)) {
            return sensitiveWords;
        }

        // 检查是否开启敏感词过滤
        if (!systemConfigService.getBooleanValue("sensitive.word.filter", true)) {
            return sensitiveWords;
        }

        // 转为小写
        text = text.toLowerCase();

        // 敏感词库为空，则初始化
        if (sensitiveWordMap == null) {
            initSensitiveWordMap();
        }

        // 敏感词库为空，则返回空集合
        if (sensitiveWordMap == null || sensitiveWordMap.isEmpty()) {
            return sensitiveWords;
        }

        // 获取敏感词
        for (int i = 0; i < text.length(); i++) {
            int length = checkSensitiveWord(text, i);
            if (length > 0) {
                sensitiveWords.add(text.substring(i, i + length));
                i = i + length - 1;
            }
        }

        return sensitiveWords;
    }

    @Override
    public String replaceSensitiveWords(String text, char replaceChar) {
        if (!StringUtils.hasText(text)) {
            return text;
        }

        // 检查是否开启敏感词过滤
        if (!systemConfigService.getBooleanValue("sensitive.word.filter", true)) {
            return text;
        }

        // 转为小写
        String lowerText = text.toLowerCase();

        // 敏感词库为空，则初始化
        if (sensitiveWordMap == null) {
            initSensitiveWordMap();
        }

        // 敏感词库为空，则返回原文本
        if (sensitiveWordMap == null || sensitiveWordMap.isEmpty()) {
            return text;
        }

        // 替换敏感词
        StringBuilder result = new StringBuilder(text);
        for (int i = 0; i < lowerText.length(); i++) {
            int length = checkSensitiveWord(lowerText, i);
            if (length > 0) {
                for (int j = 0; j < length; j++) {
                    result.setCharAt(i + j, replaceChar);
                }
                i = i + length - 1;
            }
        }

        return result.toString();
    }

    @Override
    public String replaceSensitiveWords(String text) {
        return replaceSensitiveWords(text, '*');
    }

    @Override
    public void initSensitiveWordMap() {
        long startTime = System.currentTimeMillis();
        try {
            // 获取所有启用的敏感词（SQL已经过滤了status=1的记录）
            List<SensitiveWord> sensitiveWords = sensitiveWordMapper.getAllEnabledWords();

            // 初始化敏感词库
            sensitiveWordMap = new HashMap<>(sensitiveWords.size());

            // 统计信息
            Map<String, Integer> typeStats = new HashMap<>();
            Map<Integer, Integer> levelStats = new HashMap<>();

            // 将敏感词加入到敏感词库中
            for (SensitiveWord sensitiveWord : sensitiveWords) {
                // 由于SQL已经过滤，这里不再需要检查status

                // 统计类型
                String type = sensitiveWord.getType();
                typeStats.put(type, typeStats.getOrDefault(type, 0) + 1);

                // 统计级别
                Integer level = sensitiveWord.getLevel();
                levelStats.put(level, levelStats.getOrDefault(level, 0) + 1);

                // 构建敏感词树
                String word = sensitiveWord.getWord().toLowerCase();
                Map<String, Object> nowMap = sensitiveWordMap;

                for (int i = 0; i < word.length(); i++) {
                    char keyChar = word.charAt(i);
                    Object tempMap = nowMap.get(String.valueOf(keyChar));

                    if (tempMap != null) {
                        nowMap = (Map<String, Object>) tempMap;
                    } else {
                        Map<String, Object> newMap = new HashMap<>();
                        newMap.put("isEnd", false);
                        nowMap.put(String.valueOf(keyChar), newMap);
                        nowMap = newMap;
                    }

                    if (i == word.length() - 1) {
                        nowMap.put("isEnd", true);
                    }
                }
            }

            long endTime = System.currentTimeMillis();

            // 只输出必要的统计信息
            log.info("敏感词库初始化完成，共加载{}个敏感词，耗时{}ms",
                    sensitiveWords.size(), (endTime - startTime));

            // 详细的统计信息使用DEBUG级别输出
            if (log.isDebugEnabled()) {
                log.debug("敏感词类型统计: {}", typeStats);
                log.debug("敏感词级别统计: {}", levelStats);
            }
        } catch (Exception e) {
            log.error("初始化敏感词库失败: {}", e.getMessage());
            // 只在DEBUG级别输出完整堆栈
            if (log.isDebugEnabled()) {
                log.debug("初始化敏感词库失败详细信息:", e);
            }
            sensitiveWordMap = new HashMap<>();
        }
    }

    @Override
    public void refreshSensitiveWordMap() {
        // 记录刷新操作
        log.info("开始刷新敏感词库...");

        // 重新初始化敏感词库
        initSensitiveWordMap();
    }

    /**
     * 检查文本中是否包含敏感词
     *
     * @param text       文本
     * @param beginIndex 开始位置
     * @return 敏感词长度，0表示不是敏感词
     */
    private int checkSensitiveWord(String text, int beginIndex) {
        boolean flag = false;
        int length = 0;
        Map<String, Object> nowMap = sensitiveWordMap;

        for (int i = beginIndex; i < text.length(); i++) {
            char keyChar = text.charAt(i);
            nowMap = (Map<String, Object>) nowMap.get(String.valueOf(keyChar));

            if (nowMap == null) {
                break;
            }

            length++;

            if ((Boolean) nowMap.get("isEnd")) {
                flag = true;
                break;
            }
        }

        if (!flag) {
            length = 0;
        }

        return length;
    }

    @Override
    public Map<String, Object> testSensitiveWordFilter(String content) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(content)) {
            result.put("contains", false);
            result.put("words", new ArrayList<>());
            result.put("replaced", content);
            return result;
        }

        // 检查是否包含敏感词
        boolean contains = containsSensitiveWord(content);
        result.put("contains", contains);

        if (contains) {
            // 获取敏感词列表
            Set<String> words = getSensitiveWords(content);
            result.put("words", new ArrayList<>(words));

            // 替换敏感词
            String replaced = replaceSensitiveWords(content);
            result.put("replaced", replaced);
        } else {
            result.put("words", new ArrayList<>());
            result.put("replaced", content);
        }

        return result;
    }

    @Override
    public IPage<SensitiveWord> getSensitiveWordList(Integer page, Integer pageSize, String keyword, String type, Integer level) {
        Page<SensitiveWord> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<SensitiveWord> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(SensitiveWord::getWord, keyword);
        }

        if (StringUtils.hasText(type)) {
            queryWrapper.eq(SensitiveWord::getType, type);
        }

        if (level != null) {
            queryWrapper.eq(SensitiveWord::getLevel, level);
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(SensitiveWord::getCreatedAt);

        return sensitiveWordMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public SensitiveWord getSensitiveWordById(Long id) {
        return sensitiveWordMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSensitiveWord(SensitiveWordDTO sensitiveWordDTO) {
        SensitiveWord sensitiveWord = new SensitiveWord();
        BeanUtil.copyProperties(sensitiveWordDTO, sensitiveWord);

        // 设置创建时间
        sensitiveWord.setCreatedAt(LocalDateTime.now());
        sensitiveWord.setUpdatedAt(LocalDateTime.now());

        // 保存敏感词
        sensitiveWordMapper.insert(sensitiveWord);

        // 刷新敏感词库
        refreshSensitiveWordMap();

        return sensitiveWord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSensitiveWord(Long id, SensitiveWordDTO sensitiveWordDTO) {
        // 检查敏感词是否存在
        SensitiveWord sensitiveWord = sensitiveWordMapper.selectById(id);
        if (sensitiveWord == null) {
            return false;
        }

        // 更新敏感词
        BeanUtil.copyProperties(sensitiveWordDTO, sensitiveWord);
        sensitiveWord.setUpdatedAt(LocalDateTime.now());

        int result = sensitiveWordMapper.updateById(sensitiveWord);

        // 刷新敏感词库
        if (result > 0) {
            refreshSensitiveWordMap();
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSensitiveWord(Long id) {
        int result = sensitiveWordMapper.deleteById(id);

        // 刷新敏感词库
        if (result > 0) {
            refreshSensitiveWordMap();
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSensitiveWordStatus(Long id, Boolean status) {
        // 检查敏感词是否存在
        SensitiveWord sensitiveWord = sensitiveWordMapper.selectById(id);
        if (sensitiveWord == null) {
            return false;
        }

        // 更新状态
        sensitiveWord.setStatus(status);
        sensitiveWord.setUpdatedAt(LocalDateTime.now());

        int result = sensitiveWordMapper.updateById(sensitiveWord);

        // 刷新敏感词库
        if (result > 0) {
            refreshSensitiveWordMap();
        }

        return result > 0;
    }

    @Override
    public List<String> getSensitiveWordTypes() {
        return sensitiveWordMapper.selectTypes();
    }

    @Override
    public SensitiveWordStatVO getSensitiveWordStats() {
        SensitiveWordStatVO stats = new SensitiveWordStatVO();

        // 获取敏感词总数
        LambdaQueryWrapper<SensitiveWord> queryWrapper = new LambdaQueryWrapper<>();
        Integer total = Math.toIntExact(sensitiveWordMapper.selectCount(queryWrapper));
        stats.setTotal(total);

        try {
            // 获取启用的敏感词数量
            LambdaQueryWrapper<SensitiveWord> enabledQueryWrapper = new LambdaQueryWrapper<>();
            enabledQueryWrapper.eq(SensitiveWord::getStatus, true);
            Integer enabledCount = Math.toIntExact(sensitiveWordMapper.selectCount(enabledQueryWrapper));
            stats.setEnabledCount(enabledCount);

            // 获取禁用的敏感词数量
            LambdaQueryWrapper<SensitiveWord> disabledQueryWrapper = new LambdaQueryWrapper<>();
            disabledQueryWrapper.eq(SensitiveWord::getStatus, false);
            Integer disabledCount = Math.toIntExact(sensitiveWordMapper.selectCount(disabledQueryWrapper));
            stats.setDisabledCount(disabledCount);
        } catch (Exception e) {
            log.error("获取敏感词状态统计失败: {}", e.getMessage(), e);
            // 设置默认值
            stats.setEnabledCount(0);
            stats.setDisabledCount(0);
        }

        try {
            // 获取按类型统计
            List<Map<String, Object>> typeStats = sensitiveWordMapper.selectCountByType();
            List<SensitiveWordStatVO.TypeStat> typeStatList = new ArrayList<>();
            for (Map<String, Object> typeStat : typeStats) {
                try {
                    SensitiveWordStatVO.TypeStat stat = new SensitiveWordStatVO.TypeStat();
                    stat.setType((String) typeStat.get("type"));

                    // 安全地获取count值
                    Object countObj = typeStat.get("count");
                    if (countObj instanceof Number) {
                        stat.setCount(((Number) countObj).intValue());
                    } else {
                        stat.setCount(0);
                        log.warn("类型统计count值类型异常: {}", countObj);
                    }

                    typeStatList.add(stat);
                } catch (Exception e) {
                    log.error("处理类型统计数据异常: {}", e.getMessage(), e);
                }
            }
            stats.setTypeStats(typeStatList);
        } catch (Exception e) {
            log.error("获取敏感词类型统计失败: {}", e.getMessage(), e);
            stats.setTypeStats(new ArrayList<>());
        }

        try {
            // 获取按级别统计
            List<Map<String, Object>> levelStats = sensitiveWordMapper.selectCountByLevel();
            List<SensitiveWordStatVO.LevelStat> levelStatList = new ArrayList<>();
            for (Map<String, Object> levelStat : levelStats) {
                try {
                    SensitiveWordStatVO.LevelStat stat = new SensitiveWordStatVO.LevelStat();

                    // 安全地获取level值
                    Object levelObj = levelStat.get("level");
                    int level = 0;
                    if (levelObj instanceof Number) {
                        level = ((Number) levelObj).intValue();
                        stat.setLevel(level);
                    } else {
                        stat.setLevel(0);
                        log.warn("级别统计level值类型异常: {}", levelObj);
                    }

                    // 设置级别名称
                    switch (level) {
                        case 1:
                            stat.setLevelName("一般");
                            break;
                        case 2:
                            stat.setLevelName("中等");
                            break;
                        case 3:
                            stat.setLevelName("严重");
                            break;
                        default:
                            stat.setLevelName("未知");
                    }

                    // 安全地获取count值
                    Object countObj = levelStat.get("count");
                    if (countObj instanceof Number) {
                        stat.setCount(((Number) countObj).intValue());
                    } else {
                        stat.setCount(0);
                        log.warn("级别统计count值类型异常: {}", countObj);
                    }

                    levelStatList.add(stat);
                } catch (Exception e) {
                    log.error("处理级别统计数据异常: {}", e.getMessage(), e);
                }
            }
            stats.setLevelStats(levelStatList);
        } catch (Exception e) {
            log.error("获取敏感词级别统计失败: {}", e.getMessage(), e);
            stats.setLevelStats(new ArrayList<>());
        }

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importSensitiveWords(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> failReasons = new ArrayList<>();

        try {
            InputStream inputStream = file.getInputStream();
            ExcelReader reader = ExcelUtil.getReader(inputStream);

            // 读取数据
            List<Map<String, Object>> rows = reader.readAll();

            for (Map<String, Object> row : rows) {
                try {
                    String word = row.get("敏感词").toString();
                    String type = row.get("类型").toString();
                    Integer level = Integer.parseInt(row.get("级别").toString());
                    String replaceWord = row.get("替换词") != null ? row.get("替换词").toString() : "";
                    Boolean status = Boolean.parseBoolean(row.get("状态").toString());

                    // 检查敏感词是否已存在
                    LambdaQueryWrapper<SensitiveWord> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(SensitiveWord::getWord, word);
                    SensitiveWord existWord = sensitiveWordMapper.selectOne(queryWrapper);

                    if (existWord != null) {
                        // 更新敏感词
                        existWord.setType(type);
                        existWord.setLevel(level);
                        existWord.setReplaceWord(replaceWord);
                        existWord.setStatus(status);
                        existWord.setUpdatedAt(LocalDateTime.now());
                        sensitiveWordMapper.updateById(existWord);
                    } else {
                        // 创建敏感词
                        SensitiveWord sensitiveWord = new SensitiveWord();
                        sensitiveWord.setWord(word);
                        sensitiveWord.setType(type);
                        sensitiveWord.setLevel(level);
                        sensitiveWord.setReplaceWord(replaceWord);
                        sensitiveWord.setStatus(status);
                        sensitiveWord.setCreatedAt(LocalDateTime.now());
                        sensitiveWord.setUpdatedAt(LocalDateTime.now());
                        sensitiveWordMapper.insert(sensitiveWord);
                    }

                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    failReasons.add("第" + (successCount + failCount) + "行: " + e.getMessage());
                }
            }

            // 刷新敏感词库
            refreshSensitiveWordMap();

            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("failReasons", failReasons);
        } catch (Exception e) {
            log.error("导入敏感词失败: {}", e.getMessage(), e);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("failReasons", CollUtil.newArrayList("导入失败: " + e.getMessage()));
        }

        return result;
    }

    @Override
    public void exportSensitiveWords(String type, Integer level, HttpServletResponse response) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<SensitiveWord> queryWrapper = new LambdaQueryWrapper<>();

            if (StringUtils.hasText(type)) {
                queryWrapper.eq(SensitiveWord::getType, type);
            }

            if (level != null) {
                queryWrapper.eq(SensitiveWord::getLevel, level);
            }

            // 按创建时间降序排序
            queryWrapper.orderByDesc(SensitiveWord::getCreatedAt);

            // 查询数据
            List<SensitiveWord> sensitiveWords = sensitiveWordMapper.selectList(queryWrapper);

            // 创建Excel写入器
            ExcelWriter writer = ExcelUtil.getWriter();

            // 设置列宽
            writer.setColumnWidth(0, 10);
            writer.setColumnWidth(1, 20);
            writer.setColumnWidth(2, 15);
            writer.setColumnWidth(3, 10);
            writer.setColumnWidth(4, 20);
            writer.setColumnWidth(5, 10);

            // 设置表头
            writer.addHeaderAlias("id", "ID");
            writer.addHeaderAlias("word", "敏感词");
            writer.addHeaderAlias("type", "类型");
            writer.addHeaderAlias("level", "级别");
            writer.addHeaderAlias("replaceWord", "替换词");
            writer.addHeaderAlias("status", "状态");

            // 写入数据
            writer.write(sensitiveWords, true);

            // 设置响应头
            String fileName = URLEncoder.encode("敏感词列表", StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 输出
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            out.close();
        } catch (IOException e) {
            log.error("导出敏感词失败: {}", e.getMessage(), e);
        }
    }
}
