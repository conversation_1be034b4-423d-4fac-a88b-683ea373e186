package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.PhotoNotePublishDTO;
import com.phototagmoment.dto.TagSearchResultDTO;
import com.phototagmoment.entity.PhotoNote;
import com.phototagmoment.mapper.*;
import com.phototagmoment.service.NotificationService;
import com.phototagmoment.service.PhotoNoteService;
import com.phototagmoment.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 照片笔记服务测试类
 */
@ExtendWith(MockitoExtension.class)
class PhotoNoteServiceTest {

    @Mock
    private PhotoNoteMapper photoNoteMapper;

    @Mock
    private PhotoNoteImageMapper photoNoteImageMapper;

    @Mock
    private PhotoNoteTagMapper photoNoteTagMapper;

    @Mock
    private PhotoNoteMentionMapper photoNoteMentionMapper;

    @Mock
    private TagStatsMapper tagStatsMapper;

    @Mock
    private PhotoNoteLikeMapper photoNoteLikeMapper;

    @Mock
    private PhotoNoteCollectionMapper photoNoteCollectionMapper;

    @Mock
    private PhotoMapper photoMapper;

    @Mock
    private UserService userService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private PhotoNoteServiceImpl photoNoteService;

    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testExtractTags() {
        // 测试标签提取功能
        String content = "今天去了#北京#的#故宫#，风景很美！还有#摄影#技巧分享。";
        List<String> tags = photoNoteService.extractTags(content);
        
        assertEquals(3, tags.size());
        assertTrue(tags.contains("北京"));
        assertTrue(tags.contains("故宫"));
        assertTrue(tags.contains("摄影"));
    }

    @Test
    void testExtractMentions() {
        // 测试@用户提取功能
        String content = "和@张三 @李四一起去旅行，@王五也想来。";
        List<String> mentions = photoNoteService.extractMentions(content);
        
        assertEquals(3, mentions.size());
        assertTrue(mentions.contains("张三"));
        assertTrue(mentions.contains("李四"));
        assertTrue(mentions.contains("王五"));
    }

    @Test
    void testProcessContent() {
        // 测试内容处理功能
        String content = "今天去了#北京#，和@张三一起拍照。";
        String processedContent = photoNoteService.processContent(content);
        
        assertTrue(processedContent.contains("<span class=\"tag\" data-tag=\"北京\">#北京#</span>"));
        assertTrue(processedContent.contains("<span class=\"mention\" data-user=\"张三\">@张三</span>"));
    }

    @Test
    void testPublishPhotoNote() {
        // 测试发布照片笔记
        PhotoNotePublishDTO publishDTO = new PhotoNotePublishDTO();
        publishDTO.setTitle("测试标题");
        publishDTO.setContent("测试内容 #测试标签# @测试用户");
        publishDTO.setVisibility(1);
        publishDTO.setAllowComment(1);

        // 创建照片信息
        PhotoNotePublishDTO.PhotoInfoDTO photoInfo = new PhotoNotePublishDTO.PhotoInfoDTO();
        photoInfo.setUrl("http://example.com/photo.jpg");
        photoInfo.setThumbnailUrl("http://example.com/thumb.jpg");
        photoInfo.setSortOrder(1);
        publishDTO.setPhotos(Arrays.asList(photoInfo));

        Long userId = 1L;

        // Mock保存操作
        when(photoNoteImageMapper.batchInsert(any())).thenReturn(1);
        when(photoNoteTagMapper.batchInsert(any())).thenReturn(1);
        when(photoNoteMentionMapper.batchInsert(any())).thenReturn(1);
        when(photoMapper.insert(any())).thenReturn(1);

        // 执行发布
        Long noteId = photoNoteService.publishPhotoNote(publishDTO, userId);

        // 验证结果
        assertNotNull(noteId);
        verify(photoNoteImageMapper).batchInsert(any());
        verify(photoNoteTagMapper).batchInsert(any());
    }

    @Test
    void testLikePhotoNote() {
        // 测试点赞照片笔记
        Long noteId = 1L;
        Long userId = 1L;

        // Mock检查是否已点赞
        when(photoNoteMapper.checkUserLiked(noteId, userId)).thenReturn(false);
        when(photoNoteLikeMapper.insert(any())).thenReturn(1);
        when(photoNoteMapper.incrementLikeCount(noteId)).thenReturn(1);

        boolean result = photoNoteService.likePhotoNote(noteId, userId);

        assertTrue(result);
        verify(photoNoteLikeMapper).insert(any());
        verify(photoNoteMapper).incrementLikeCount(noteId);
    }

    @Test
    void testLikePhotoNoteAlreadyLiked() {
        // 测试重复点赞
        Long noteId = 1L;
        Long userId = 1L;

        // Mock已经点赞过
        when(photoNoteMapper.checkUserLiked(noteId, userId)).thenReturn(true);

        boolean result = photoNoteService.likePhotoNote(noteId, userId);

        assertFalse(result);
        verify(photoNoteLikeMapper, never()).insert(any());
        verify(photoNoteMapper, never()).incrementLikeCount(noteId);
    }

    @Test
    void testUnlikePhotoNote() {
        // 测试取消点赞
        Long noteId = 1L;
        Long userId = 1L;

        // Mock已经点赞过
        when(photoNoteMapper.checkUserLiked(noteId, userId)).thenReturn(true);
        when(photoNoteLikeMapper.delete(any())).thenReturn(1);
        when(photoNoteMapper.decrementLikeCount(noteId)).thenReturn(1);

        boolean result = photoNoteService.unlikePhotoNote(noteId, userId);

        assertTrue(result);
        verify(photoNoteLikeMapper).delete(any());
        verify(photoNoteMapper).decrementLikeCount(noteId);
    }

    @Test
    void testCollectPhotoNote() {
        // 测试收藏照片笔记
        Long noteId = 1L;
        Long userId = 1L;

        // Mock检查是否已收藏
        when(photoNoteMapper.checkUserCollected(noteId, userId)).thenReturn(false);
        when(photoNoteCollectionMapper.insert(any())).thenReturn(1);

        boolean result = photoNoteService.collectPhotoNote(noteId, userId);

        assertTrue(result);
        verify(photoNoteCollectionMapper).insert(any());
    }

    @Test
    void testIncrementViewCount() {
        // 测试增加浏览量
        Long noteId = 1L;
        Long userId = 1L;

        // Mock Redis操作
        when(redisTemplate.hasKey(anyString())).thenReturn(false);
        when(photoNoteMapper.incrementViewCount(noteId)).thenReturn(1);

        photoNoteService.incrementViewCount(noteId, userId);

        verify(photoNoteMapper).incrementViewCount(noteId);
        verify(valueOperations).set(anyString(), eq("1"), anyLong(), any());
    }

    @Test
    void testIncrementViewCountAlreadyViewed() {
        // 测试重复浏览
        Long noteId = 1L;
        Long userId = 1L;

        // Mock已经浏览过
        when(redisTemplate.hasKey(anyString())).thenReturn(true);

        photoNoteService.incrementViewCount(noteId, userId);

        verify(photoNoteMapper, never()).incrementViewCount(noteId);
    }

    @Test
    void testGetHotTags() {
        // 测试获取热门标签
        List<String> mockTags = Arrays.asList("摄影", "旅行", "美食");
        when(photoNoteTagMapper.selectHotTags(20)).thenReturn(mockTags);

        List<String> result = photoNoteService.getHotTags(20);

        assertEquals(3, result.size());
        assertEquals(mockTags, result);
        verify(photoNoteTagMapper).selectHotTags(20);
    }

    @Test
    void testSearchTags() {
        // 测试搜索标签
        String keyword = "摄";
        List<String> mockTags = Arrays.asList("摄影", "摄像");
        when(photoNoteTagMapper.searchTags(keyword, 10)).thenReturn(mockTags);

        List<String> result = photoNoteService.searchTags(keyword, 10);

        assertEquals(2, result.size());
        assertEquals(mockTags, result);
        verify(photoNoteTagMapper).searchTags(keyword, 10);
    }
}
