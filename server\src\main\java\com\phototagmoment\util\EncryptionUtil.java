package com.phototagmoment.util;

import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;

/**
 * 加密工具类
 * 提供AES和RSA加密算法
 */
public class EncryptionUtil {

    // AES加密相关常量
    private static final String AES_ALGORITHM = "AES/GCM/NoPadding";
    private static final int AES_KEY_SIZE = 256;
    private static final int GCM_TAG_LENGTH = 128;
    private static final int GCM_IV_LENGTH = 12;

    // RSA加密相关常量
    private static final String RSA_ALGORITHM = "RSA/ECB/PKCS1Padding";
    private static final int RSA_KEY_SIZE = 2048;

    /**
     * 生成AES密钥
     *
     * @return Base64编码的AES密钥
     * @throws Exception 生成密钥异常
     */
    public static String generateAESKey() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(AES_KEY_SIZE);
        SecretKey key = keyGenerator.generateKey();
        return Base64Utils.encodeToString(key.getEncoded());
    }

    /**
     * AES加密
     *
     * @param plainText 明文
     * @param key       Base64编码的AES密钥
     * @return Base64编码的密文
     * @throws Exception 加密异常
     */
    public static String encryptAES(String plainText, String key) throws Exception {
        byte[] keyBytes = Base64Utils.decodeFromString(key);
        SecretKey secretKey = new SecretKeySpec(keyBytes, "AES");

        // 生成随机IV
        byte[] iv = new byte[GCM_IV_LENGTH];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);

        // 初始化加密器
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);

        // 加密
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

        // 将IV和密文拼接
        byte[] result = new byte[iv.length + encryptedBytes.length];
        System.arraycopy(iv, 0, result, 0, iv.length);
        System.arraycopy(encryptedBytes, 0, result, iv.length, encryptedBytes.length);

        return Base64Utils.encodeToString(result);
    }

    /**
     * AES解密
     *
     * @param encryptedText Base64编码的密文
     * @param key           Base64编码的AES密钥
     * @return 明文
     * @throws Exception 解密异常
     */
    public static String decryptAES(String encryptedText, String key) throws Exception {
        byte[] keyBytes = Base64Utils.decodeFromString(key);
        SecretKey secretKey = new SecretKeySpec(keyBytes, "AES");

        // 解码密文
        byte[] encryptedBytes = Base64Utils.decodeFromString(encryptedText);

        // 提取IV
        byte[] iv = Arrays.copyOfRange(encryptedBytes, 0, GCM_IV_LENGTH);
        byte[] cipherText = Arrays.copyOfRange(encryptedBytes, GCM_IV_LENGTH, encryptedBytes.length);

        // 初始化解密器
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);

        // 解密
        byte[] decryptedBytes = cipher.doFinal(cipherText);

        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    /**
     * 生成RSA密钥对
     *
     * @return RSA密钥对（[0]为公钥，[1]为私钥，均为Base64编码）
     * @throws Exception 生成密钥异常
     */
    public static String[] generateRSAKeyPair() throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(RSA_KEY_SIZE);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        String publicKey = Base64Utils.encodeToString(keyPair.getPublic().getEncoded());
        String privateKey = Base64Utils.encodeToString(keyPair.getPrivate().getEncoded());

        return new String[]{publicKey, privateKey};
    }

    /**
     * RSA公钥加密
     *
     * @param plainText Base64编码的明文
     * @param publicKey Base64编码的公钥
     * @return Base64编码的密文
     * @throws Exception 加密异常
     */
    public static String encryptRSA(String plainText, String publicKey) throws Exception {
        byte[] keyBytes = Base64Utils.decodeFromString(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey key = keyFactory.generatePublic(keySpec);

        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key);

        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64Utils.encodeToString(encryptedBytes);
    }

    /**
     * RSA私钥解密
     *
     * @param encryptedText Base64编码的密文
     * @param privateKey    Base64编码的私钥
     * @return 明文
     * @throws Exception 解密异常
     */
    public static String decryptRSA(String encryptedText, String privateKey) throws Exception {
        byte[] keyBytes = Base64Utils.decodeFromString(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey key = keyFactory.generatePrivate(keySpec);

        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, key);

        byte[] encryptedBytes = Base64Utils.decodeFromString(encryptedText);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    /**
     * 生成MD5哈希
     *
     * @param input 输入字符串
     * @return MD5哈希值（十六进制字符串）
     * @throws Exception 哈希异常
     */
    public static String md5Hash(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hashBytes);
    }

    /**
     * 生成SHA-256哈希
     *
     * @param input 输入字符串
     * @return SHA-256哈希值（十六进制字符串）
     * @throws Exception 哈希异常
     */
    public static String sha256Hash(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hashBytes);
    }

    /**
     * 字节数组转十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
