package com.phototagmoment.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 内容审核服务接口
 * 用于对上传的图片、文本等内容进行审核
 */
public interface ContentModerationService {

    /**
     * 审核图片内容
     *
     * @param file 图片文件
     * @return 审核结果，true表示通过，false表示不通过
     */
    boolean moderateImage(MultipartFile file);

    /**
     * 审核图片内容
     *
     * @param inputStream 图片输入流
     * @param contentType 内容类型
     * @return 审核结果，true表示通过，false表示不通过
     */
    boolean moderateImage(InputStream inputStream, String contentType);

    /**
     * 审核图片内容
     *
     * @param imageUrl 图片URL
     * @return 审核结果，true表示通过，false表示不通过
     */
    boolean moderateImageByUrl(String imageUrl);

    /**
     * 审核文本内容
     *
     * @param text 文本内容
     * @return 审核结果，true表示通过，false表示不通过
     */
    boolean moderateText(String text);

    /**
     * 获取审核失败的原因
     *
     * @return 审核失败原因
     */
    String getFailReason();
}
