<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Store</title>
</head>
<body>
    <h1>Test User Store</h1>
    <script type="module">
        import { useUserStore } from './src/stores/user.js';
        
        // 测试是否可以导入 useUserStore
        console.log('useUserStore imported successfully:', useUserStore);
        
        // 尝试创建 store 实例
        try {
            const userStore = useUserStore();
            console.log('userStore created successfully:', userStore);
        } catch (error) {
            console.error('Error creating userStore:', error);
        }
    </script>
</body>
</html>
