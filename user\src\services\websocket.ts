import { useUserStore } from '@/store/user';
import { showNotify } from 'vant';

/**
 * WebSocket服务
 */
class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: number | null = null;
  private heartbeatInterval: number | null = null;
  private listeners: Map<string, Function[]> = new Map();
  private baseUrl: string;

  constructor() {
    // 根据当前环境确定WebSocket URL
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    this.baseUrl = `${protocol}//${host}/api/ws`;
  }

  /**
   * 连接通知WebSocket
   */
  public connectNotification(): void {
    const userStore = useUserStore();
    if (!userStore.isLoggedIn || !userStore.token) {
      console.warn('用户未登录，无法连接WebSocket');
      return;
    }

    if (this.socket) {
      this.disconnect();
    }

    try {
      // 创建WebSocket连接
      this.socket = new WebSocket(`${this.baseUrl}/notification?token=${userStore.token}`);

      // 连接建立时的处理
      this.socket.onopen = () => {
        console.log('WebSocket连接已建立');
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.emit('connected', null);
      };

      // 接收消息的处理
      this.socket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('收到WebSocket消息:', message);

          // 处理不同类型的消息
          if (message.type === 'notification') {
            this.handleNotification(message.data);
          } else if (message.type === 'system') {
            this.handleSystemMessage(message.data);
          } else if (message.type === 'heartbeat') {
            // 心跳响应，不做特殊处理
          }

          // 触发消息事件
          this.emit('message', message);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      // 连接关闭时的处理
      this.socket.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        this.stopHeartbeat();
        this.emit('disconnected', event);

        // 尝试重新连接
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectTimeout = window.setTimeout(() => {
            this.reconnectAttempts++;
            console.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            this.connectNotification();
          }, 3000 * Math.pow(2, this.reconnectAttempts));
        }
      };

      // 连接错误时的处理
      this.socket.onerror = (error) => {
        console.error('WebSocket连接错误:', error);
        this.emit('error', error);
      };
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
    }
  }

  /**
   * 断开WebSocket连接
   */
  public disconnect(): void {
    if (this.socket) {
      this.stopHeartbeat();
      
      if (this.reconnectTimeout) {
        clearTimeout(this.reconnectTimeout);
        this.reconnectTimeout = null;
      }

      if (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING) {
        this.socket.close();
      }
      
      this.socket = null;
      console.log('WebSocket连接已断开');
    }
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = window.setInterval(() => {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send('ping');
      }
    }, 30000); // 每30秒发送一次心跳
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 处理通知消息
   */
  private handleNotification(notification: any): void {
    // 显示通知
    showNotify({
      type: 'primary',
      message: notification.content,
      duration: 3000,
      onClick: () => {
        // 根据通知类型和目标跳转到相应页面
        this.navigateToNotificationTarget(notification);
      }
    });

    // 更新未读通知数量
    const userStore = useUserStore();
    userStore.updateUnreadCount(userStore.unreadCount + 1);
  }

  /**
   * 处理系统消息
   */
  private handleSystemMessage(message: any): void {
    if (typeof message === 'string') {
      showNotify({
        type: 'info',
        message: message,
        duration: 3000
      });
    } else {
      showNotify({
        type: 'info',
        message: '收到系统消息',
        duration: 3000
      });
    }
  }

  /**
   * 根据通知跳转到目标页面
   */
  private navigateToNotificationTarget(notification: any): void {
    // 根据通知类型和目标ID跳转
    if (notification.targetType === 1) { // 照片
      window.location.href = `/photo/${notification.targetId}`;
    } else if (notification.targetType === 2) { // 评论
      window.location.href = `/photo/${notification.photoId}?comment=${notification.targetId}`;
    } else if (notification.targetType === 3) { // 用户
      window.location.href = `/user/${notification.targetId}`;
    }
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, callback: Function): void {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: any): void {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        callbacks.forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error(`执行事件 ${event} 的回调函数时出错:`, error);
          }
        });
      }
    }
  }
}

// 创建WebSocket服务实例
const websocketService = new WebSocketService();

export default websocketService;
