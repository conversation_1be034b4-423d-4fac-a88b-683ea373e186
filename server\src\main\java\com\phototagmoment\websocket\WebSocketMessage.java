package com.phototagmoment.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebSocket消息模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {

    /**
     * 消息类型
     * notification: 通知消息
     * system: 系统消息
     * heartbeat: 心跳消息
     */
    private String type;

    /**
     * 消息内容
     */
    private Object data;

    /**
     * 手动实现Builder模式
     */
    public static WebSocketMessageBuilder builder() {
        return new WebSocketMessageBuilder();
    }

    /**
     * Builder类
     */
    public static class WebSocketMessageBuilder {
        private String type;
        private Object data;

        public WebSocketMessageBuilder type(String type) {
            this.type = type;
            return this;
        }

        public WebSocketMessageBuilder data(Object data) {
            this.data = data;
            return this;
        }

        public WebSocketMessage build() {
            return new WebSocketMessage(type, data);
        }
    }

    /**
     * 创建通知消息
     */
    public static WebSocketMessage notification(Object data) {
        return WebSocketMessage.builder()
                .type("notification")
                .data(data)
                .build();
    }

    /**
     * 创建系统消息
     */
    public static WebSocketMessage system(Object data) {
        return WebSocketMessage.builder()
                .type("system")
                .data(data)
                .build();
    }

    /**
     * 创建心跳消息
     */
    public static WebSocketMessage heartbeat() {
        return WebSocketMessage.builder()
                .type("heartbeat")
                .data("pong")
                .build();
    }
}
