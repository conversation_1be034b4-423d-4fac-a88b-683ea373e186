import request from '@/utils/request'

/**
 * 用户个人资料
 */
export interface UserProfile {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  bio: string;
  gender: number;
  birthday: string;
  location: string;
  website: string;
  email: string;
  phone: string;
  followingCount: number;
  followerCount: number;
  photoCount: number;
  isFollowing?: boolean;
  isVerified?: boolean;
}

/**
 * 获取用户个人资料
 * @param userId 用户ID
 * @returns 用户个人资料
 */
export function getUserProfile(userId: number) {
  return request({
    url: `/user/${userId}/profile`,
    method: 'get'
  })
}

/**
 * 更新用户个人资料
 * @param data 用户个人资料
 * @returns 是否成功
 */
export function updateUserProfile(data: Partial<UserProfile>) {
  return request({
    url: '/user/profile',
    method: 'put',
    data
  })
}

/**
 * 上传用户头像
 * @param file 头像文件
 * @returns 头像URL
 */
export function uploadAvatar(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/user/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取用户照片列表
 * @param userId 用户ID
 * @param params 查询参数
 * @returns 照片列表
 */
export function getUserPhotos(userId: number, params: {
  page?: number;
  size?: number;
  type?: string; // 照片类型：public-公开，private-私密，all-全部
}) {
  return request({
    url: `/user/${userId}/photos`,
    method: 'get',
    params
  })
}

/**
 * 获取用户收藏的照片
 * @param userId 用户ID
 * @param params 查询参数
 * @returns 照片列表
 */
export function getUserCollections(userId: number, params: {
  page?: number;
  size?: number;
}) {
  return request({
    url: `/user/${userId}/collections`,
    method: 'get',
    params
  })
}

/**
 * 获取用户关注列表
 * @param userId 用户ID
 * @param params 查询参数
 * @returns 用户列表
 */
export function getUserFollowing(userId: number, params: {
  page?: number;
  size?: number;
}) {
  return request({
    url: `/user/${userId}/following`,
    method: 'get',
    params
  })
}

/**
 * 获取用户粉丝列表
 * @param userId 用户ID
 * @param params 查询参数
 * @returns 用户列表
 */
export function getUserFollowers(userId: number, params: {
  page?: number;
  size?: number;
}) {
  return request({
    url: `/user/${userId}/followers`,
    method: 'get',
    params
  })
}

/**
 * 关注用户
 * @param userId 用户ID
 * @returns 是否成功
 */
export function followUser(userId: number) {
  return request({
    url: `/user/follow/${userId}`,
    method: 'post'
  })
}

/**
 * 取消关注用户
 * @param userId 用户ID
 * @returns 是否成功
 */
export function unfollowUser(userId: number) {
  return request({
    url: `/user/unfollow/${userId}`,
    method: 'post'
  })
}

/**
 * 检查是否关注了用户
 * @param userId 用户ID
 * @returns 是否关注
 */
export function checkFollowing(userId: number) {
  return request({
    url: `/user/following/${userId}`,
    method: 'get'
  })
}
