import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, logout, getUserInfo } from '../api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<any>(null)
  const isLoggedIn = ref<boolean>(!!token.value)
  const unreadCount = ref<number>(0)

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!token.value) return

    try {
      const { data } = await getUserInfo()
      userInfo.value = data
    } catch (error) {
      console.error('获取用户信息失败', error)
    }
  }

  // 登录
  const loginAction = async (loginData: any) => {
    try {
      const { data } = await login(loginData)
      token.value = data.token
      localStorage.setItem('token', data.token)
      isLoggedIn.value = true
      await fetchUserInfo()
      return true
    } catch (error) {
      console.error('登录失败', error)
      return false
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      await logout()
      resetState()
      return true
    } catch (error) {
      console.error('登出失败', error)
      return false
    }
  }

  // 设置 Token
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
    isLoggedIn.value = true
  }

  // 设置用户信息
  const setUser = (user: any) => {
    userInfo.value = user
  }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = null
    isLoggedIn.value = false
    unreadCount.value = 0
    localStorage.removeItem('token')
  }

  // 更新未读通知数量
  const updateUnreadCount = async (count: number) => {
    unreadCount.value = count
  }

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    if (!token.value) return

    try {
      const response = await fetch('/api/notification/unread/count')
      const result = await response.json()

      if (result.code === 200) {
        unreadCount.value = result.data
      }
    } catch (error) {
      console.error('获取未读通知数量失败', error)
    }
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    unreadCount,
    loginAction,
    logoutAction,
    fetchUserInfo,
    resetState,
    updateUnreadCount,
    fetchUnreadCount,
    setToken,
    setUser
  }
})
