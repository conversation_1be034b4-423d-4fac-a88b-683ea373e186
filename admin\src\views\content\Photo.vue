<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="form-inline">
        <el-form-item label="标题">
          <el-input
            v-model="listQuery.title"
            placeholder="照片标题"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input
            v-model="listQuery.username"
            placeholder="用户名"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input
            v-model="listQuery.tag"
            placeholder="标签"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="状态" clearable>
            <el-option label="正常" value="1" />
            <el-option label="已删除" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="缩略图" width="120">
        <template #default="scope">
          <el-image
            :src="scope.row.thumbnailUrl"
            :preview-src-list="[scope.row.url]"
            fit="cover"
            style="width: 80px; height: 80px; border-radius: 4px;"
          />
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="标题" min-width="150">
        <template #default="scope">
          <span>{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="用户" width="120">
        <template #default="scope">
          <div class="user-info">
            <el-avatar :size="24" :src="scope.row.user.avatar" />
            <span>{{ scope.row.user.nickname }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="标签" width="180">
        <template #default="scope">
          <el-tag
            v-for="tag in scope.row.tags"
            :key="tag"
            size="small"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="点赞数" width="100">
        <template #default="scope">
          <span>{{ scope.row.likeCount }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="评论数" width="100">
        <template #default="scope">
          <span>{{ scope.row.commentCount }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'">
            {{ scope.row.status ? '正常' : '已删除' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="上传时间" width="160">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作" fixed="right" width="200">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            size="small"
            :type="scope.row.status ? 'danger' : 'success'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status ? '删除' : '恢复' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="listQuery.page"
        v-model:page-size="listQuery.limit"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 照片详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="照片详情"
      width="800px"
    >
      <div v-if="currentPhoto" class="photo-detail">
        <div class="photo-container">
          <el-image
            :src="currentPhoto.url"
            :preview-src-list="[currentPhoto.url]"
            fit="contain"
            class="photo-image"
          />
        </div>
        
        <div class="photo-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="ID">{{ currentPhoto.id }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ currentPhoto.title }}</el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              {{ currentPhoto.description || '暂无描述' }}
            </el-descriptions-item>
            <el-descriptions-item label="用户">
              <div class="user-info">
                <el-avatar :size="24" :src="currentPhoto.user.avatar" />
                <span>{{ currentPhoto.user.nickname }}</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ currentPhoto.user.id }}</el-descriptions-item>
            <el-descriptions-item label="位置" :span="2">
              {{ currentPhoto.location || '未知位置' }}
            </el-descriptions-item>
            <el-descriptions-item label="标签" :span="2">
              <el-tag
                v-for="tag in currentPhoto.tags"
                :key="tag"
                size="small"
                class="tag-item"
              >
                {{ tag }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="点赞数">{{ currentPhoto.likeCount }}</el-descriptions-item>
            <el-descriptions-item label="评论数">{{ currentPhoto.commentCount }}</el-descriptions-item>
            <el-descriptions-item label="收藏数">{{ currentPhoto.collectCount }}</el-descriptions-item>
            <el-descriptions-item label="浏览数">{{ currentPhoto.viewCount }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="currentPhoto.status ? 'success' : 'danger'">
                {{ currentPhoto.status ? '正常' : '已删除' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="上传时间">
              {{ formatDateTime(currentPhoto.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(currentPhoto.fileSize) }}</el-descriptions-item>
            <el-descriptions-item label="文件类型">{{ currentPhoto.fileType }}</el-descriptions-item>
            <el-descriptions-item label="原始文件名" :span="2">
              {{ currentPhoto.originalFilename }}
            </el-descriptions-item>
            <el-descriptions-item label="存储路径" :span="2">
              {{ currentPhoto.storagePath }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="photo-exif" v-if="currentPhoto.exif">
            <h3>EXIF信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="相机">{{ currentPhoto.exif.camera || '-' }}</el-descriptions-item>
              <el-descriptions-item label="镜头">{{ currentPhoto.exif.lens || '-' }}</el-descriptions-item>
              <el-descriptions-item label="光圈">{{ currentPhoto.exif.aperture || '-' }}</el-descriptions-item>
              <el-descriptions-item label="快门速度">{{ currentPhoto.exif.shutterSpeed || '-' }}</el-descriptions-item>
              <el-descriptions-item label="ISO">{{ currentPhoto.exif.iso || '-' }}</el-descriptions-item>
              <el-descriptions-item label="焦距">{{ currentPhoto.exif.focalLength || '-' }}</el-descriptions-item>
              <el-descriptions-item label="拍摄时间" :span="2">
                {{ currentPhoto.exif.dateTime ? formatDateTime(currentPhoto.exif.dateTime) : '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            :type="currentPhoto?.status ? 'danger' : 'success'"
            @click="handleStatusChange(currentPhoto)"
          >
            {{ currentPhoto?.status ? '删除照片' : '恢复照片' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 列表数据
const list = ref<any[]>([])
const total = ref(0)
const listLoading = ref(false)
const dialogVisible = ref(false)
const currentPhoto = ref<any>(null)

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 10,
  title: '',
  username: '',
  tag: '',
  status: ''
})

// 获取照片列表
const getList = () => {
  listLoading.value = true
  
  // 这里应该调用实际的API
  console.log('获取照片列表', listQuery)
  
  // 模拟API请求
  setTimeout(() => {
    // 生成模拟数据
    list.value = Array(listQuery.limit).fill(0).map((_, index) => {
      const id = (listQuery.page - 1) * listQuery.limit + index + 1
      const tags = ['风景', '人像', '美食', '建筑', '街拍', '黑白', '旅行', '城市'].sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 4) + 1)
      
      return {
        id,
        title: `照片标题${id}`,
        description: id % 3 === 0 ? `这是照片${id}的详细描述，介绍了照片的拍摄背景和创作理念。` : null,
        url: `https://picsum.photos/id/${id % 100}/800/600`,
        thumbnailUrl: `https://picsum.photos/id/${id % 100}/200/200`,
        location: id % 4 === 0 ? null : `北京市朝阳区`,
        tags,
        likeCount: Math.floor(Math.random() * 1000),
        commentCount: Math.floor(Math.random() * 100),
        collectCount: Math.floor(Math.random() * 500),
        viewCount: Math.floor(Math.random() * 5000),
        status: id % 10 !== 0, // 90%的照片状态正常
        fileSize: Math.floor(Math.random() * 10000000) + 1000000,
        fileType: 'image/jpeg',
        originalFilename: `IMG_${1000 + id}.JPG`,
        storagePath: `photos/${Math.floor(id / 100)}/${id}.jpg`,
        createdAt: new Date(Date.now() - id * 86400000).toISOString(),
        exif: {
          camera: '佳能 EOS R5',
          lens: 'RF24-70mm F2.8 L IS USM',
          aperture: 'f/2.8',
          shutterSpeed: '1/125',
          iso: '100',
          focalLength: '50mm',
          dateTime: new Date(Date.now() - id * 86400000 - 3600000).toISOString()
        },
        user: {
          id: Math.floor(Math.random() * 100) + 1,
          nickname: `用户${Math.floor(Math.random() * 100) + 1}`,
          avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`
        }
      }
    })
    
    // 设置总数
    total.value = 100
    
    // 关闭加载状态
    listLoading.value = false
  }, 500)
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

// 处理查询
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 重置查询条件
const resetFilter = () => {
  listQuery.title = ''
  listQuery.username = ''
  listQuery.tag = ''
  listQuery.status = ''
  handleFilter()
}

// 处理每页数量变化
const handleSizeChange = (val: number) => {
  listQuery.limit = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  listQuery.page = val
  getList()
}

// 查看照片详情
const handleDetail = (row: any) => {
  currentPhoto.value = { ...row }
  dialogVisible.value = true
}

// 修改照片状态
const handleStatusChange = (row: any) => {
  if (!row) return
  
  const statusText = row.status ? '删除' : '恢复'
  const messageText = `确定要${statusText}照片 "${row.title}" 吗？`
  
  ElMessageBox.confirm(messageText, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里应该调用实际的API
    console.log(`${statusText}照片`, row.id)
    
    // 模拟API请求
    setTimeout(() => {
      // 更新照片状态
      const photo = list.value.find(item => item.id === row.id)
      if (photo) {
        photo.status = !photo.status
      }
      
      // 如果是在详情对话框中操作，也更新当前照片状态
      if (currentPhoto.value && currentPhoto.value.id === row.id) {
        currentPhoto.value.status = !currentPhoto.value.status
      }
      
      ElMessage({
        type: 'success',
        message: `${statusText}成功！`
      })
    }, 300)
  }).catch(() => {
    // 取消操作
  })
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0px;
  
  .filter-container {
    margin-bottom: 20px;
    padding: 18px 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    
    .el-avatar {
      margin-right: 8px;
    }
  }
  
  .tag-item {
    margin-right: 5px;
    margin-bottom: 5px;
  }
  
  .photo-detail {
    display: flex;
    flex-direction: column;
    
    .photo-container {
      margin-bottom: 20px;
      text-align: center;
      
      .photo-image {
        max-width: 100%;
        max-height: 400px;
      }
    }
    
    .photo-info {
      .photo-exif {
        margin-top: 20px;
        
        h3 {
          margin-bottom: 15px;
        }
      }
    }
  }
}
</style>
