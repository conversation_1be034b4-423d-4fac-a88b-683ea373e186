import request from '@/utils/request'

// 文件上传配置接口
interface FileUploadConfig {
  id?: number;
  configName: string;
  storageType: string;
  enabled: boolean;
  isDefault?: boolean;
  configParams?: StorageConfigParams;
  uploadLimits?: UploadLimitsConfig;
  pathConfig?: PathConfig;
  description?: string;
  sortOrder?: number;
  status?: number;
  lastTestTime?: string;
  lastTestResult?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 存储配置参数接口
interface StorageConfigParams {
  // 通用配置
  domain?: string;
  useHttps?: boolean;
  connectTimeout?: number;
  readTimeout?: number;
  
  // 本地存储配置
  localPath?: string;
  
  // 七牛云配置
  qiniuAccessKey?: string;
  qiniuSecretKey?: string;
  qiniuBucket?: string;
  qiniuRegion?: string;
  
  // 阿里云OSS配置
  aliyunAccessKeyId?: string;
  aliyunAccessKeySecret?: string;
  aliyunBucket?: string;
  aliyunEndpoint?: string;
  
  // 腾讯云COS配置
  tencentSecretId?: string;
  tencentSecretKey?: string;
  tencentBucket?: string;
  tencentRegion?: string;
  
  // AWS S3配置
  awsAccessKey?: string;
  awsSecretKey?: string;
  awsBucket?: string;
  awsRegion?: string;
  
  // MinIO配置
  minioEndpoint?: string;
  minioAccessKey?: string;
  minioSecretKey?: string;
  minioBucket?: string;
}

// 上传限制配置接口
interface UploadLimitsConfig {
  maxFileSize?: number;
  maxFileCount?: number;
  allowedFileTypes?: string[];
  forbiddenFileTypes?: string[];
  imageMaxDimensions?: Record<string, number>;
  enableFileTypeCheck?: boolean;
  enableContentCheck?: boolean;
  enableVirusScan?: boolean;
}

// 路径配置接口
interface PathConfig {
  rootPath?: string;
  fileNamingRule?: string;
  directoryStructure?: string;
  enableDateDirectory?: boolean;
  enableUserDirectory?: boolean;
  enableTypeDirectory?: boolean;
  customPrefix?: string;
  thumbnailDirectory?: string;
  tempDirectory?: string;
}

// 测试结果接口
interface TestResult {
  success: boolean;
  message: string;
  responseTime?: number;
  testTime?: string;
  errorDetails?: string;
}

// 查询参数接口
interface ConfigListParams {
  page?: number;
  size?: number;
  keyword?: string;
  storageType?: string;
  enabled?: boolean;
  status?: number;
}

/**
 * 获取配置列表
 * @param params 查询参数
 */
export function getConfigList(params: ConfigListParams) {
  return request({
    url: '/admin/file-upload-config/list',
    method: 'get',
    params
  })
}

/**
 * 获取配置详情
 * @param configId 配置ID
 */
export function getConfigDetail(configId: number) {
  return request({
    url: `/admin/file-upload-config/${configId}`,
    method: 'get'
  })
}

/**
 * 创建配置
 * @param config 配置信息
 */
export function createConfig(config: FileUploadConfig) {
  return request({
    url: '/admin/file-upload-config',
    method: 'post',
    data: config
  })
}

/**
 * 更新配置
 * @param configId 配置ID
 * @param config 配置信息
 */
export function updateConfig(configId: number, config: FileUploadConfig) {
  return request({
    url: `/admin/file-upload-config/${configId}`,
    method: 'put',
    data: config
  })
}

/**
 * 删除配置
 * @param configId 配置ID
 */
export function deleteConfig(configId: number) {
  return request({
    url: `/admin/file-upload-config/${configId}`,
    method: 'delete'
  })
}

/**
 * 批量删除配置
 * @param configIds 配置ID列表
 */
export function batchDeleteConfigs(configIds: number[]) {
  return request({
    url: '/admin/file-upload-config/batch',
    method: 'delete',
    data: configIds
  })
}

/**
 * 启用/禁用配置
 * @param configId 配置ID
 * @param enabled 启用状态
 */
export function toggleConfig(configId: number, enabled: boolean) {
  return request({
    url: `/admin/file-upload-config/${configId}/toggle`,
    method: 'put',
    params: { enabled }
  })
}

/**
 * 设置默认配置
 * @param configId 配置ID
 */
export function setDefaultConfig(configId: number) {
  return request({
    url: `/admin/file-upload-config/${configId}/set-default`,
    method: 'put'
  })
}

/**
 * 获取默认配置
 */
export function getDefaultConfig() {
  return request({
    url: '/admin/file-upload-config/default',
    method: 'get'
  })
}

/**
 * 获取启用的配置列表
 */
export function getEnabledConfigs() {
  return request({
    url: '/admin/file-upload-config/enabled',
    method: 'get'
  })
}

/**
 * 测试配置连接
 * @param configId 配置ID
 */
export function testConfig(configId: number) {
  return request({
    url: `/admin/file-upload-config/${configId}/test`,
    method: 'post'
  })
}

/**
 * 测试配置连接（不保存）
 * @param config 配置信息
 */
export function testConfigWithoutSave(config: FileUploadConfig) {
  return request({
    url: '/admin/file-upload-config/test-without-save',
    method: 'post',
    data: config
  })
}

/**
 * 批量测试配置
 * @param configIds 配置ID列表
 */
export function batchTestConfigs(configIds: number[]) {
  return request({
    url: '/admin/file-upload-config/batch-test',
    method: 'post',
    data: configIds
  })
}

/**
 * 复制配置
 * @param configId 配置ID
 * @param newConfigName 新配置名称
 */
export function copyConfig(configId: number, newConfigName: string) {
  return request({
    url: `/admin/file-upload-config/${configId}/copy`,
    method: 'post',
    params: { newConfigName }
  })
}

/**
 * 获取配置统计信息
 */
export function getConfigStatistics() {
  return request({
    url: '/admin/file-upload-config/statistics',
    method: 'get'
  })
}

/**
 * 获取存储类型统计
 */
export function getStorageTypeStatistics() {
  return request({
    url: '/admin/file-upload-config/storage-type-statistics',
    method: 'get'
  })
}

/**
 * 获取配置健康状态
 */
export function getConfigHealthStatus() {
  return request({
    url: '/admin/file-upload-config/health-status',
    method: 'get'
  })
}

/**
 * 刷新配置缓存
 */
export function refreshConfigCache() {
  return request({
    url: '/admin/file-upload-config/refresh-cache',
    method: 'post'
  })
}

/**
 * 检查配置名称是否可用
 * @param configName 配置名称
 * @param excludeId 排除的配置ID
 */
export function checkConfigName(configName: string, excludeId?: number) {
  return request({
    url: '/admin/file-upload-config/check-name',
    method: 'get',
    params: { configName, excludeId }
  })
}

/**
 * 获取最近测试失败的配置
 * @param limit 数量限制
 */
export function getRecentFailedConfigs(limit: number = 10) {
  return request({
    url: '/admin/file-upload-config/recent-failed',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取长时间未测试的配置
 * @param days 天数
 * @param limit 数量限制
 */
export function getUntestedConfigs(days: number = 7, limit: number = 10) {
  return request({
    url: '/admin/file-upload-config/untested',
    method: 'get',
    params: { days, limit }
  })
}

// 导出类型定义
export type {
  FileUploadConfig,
  StorageConfigParams,
  UploadLimitsConfig,
  PathConfig,
  TestResult,
  ConfigListParams
}
