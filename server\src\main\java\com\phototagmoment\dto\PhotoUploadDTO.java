package com.phototagmoment.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 照片上传DTO
 */
@Data
public class PhotoUploadDTO {

    /**
     * 照片标题
     */
    @NotBlank(message = "照片标题不能为空")
    @Size(max = 100, message = "照片标题不能超过100个字符")
    private String title;

    /**
     * 照片描述
     */
    @Size(max = 500, message = "照片描述不能超过500个字符")
    private String description;

    /**
     * 拍摄地点
     */
    @Size(max = 100, message = "拍摄地点不能超过100个字符")
    private String location;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 可见性: 0-私密, 1-公开, 2-好友可见
     */
    @NotNull(message = "可见性不能为空")
    private Integer visibility;

    /**
     * 是否允许评论: 0-不允许, 1-允许
     */
    @NotNull(message = "是否允许评论不能为空")
    private Integer allowComment;

    /**
     * 是否允许下载: 0-不允许, 1-允许
     */
    @NotNull(message = "是否允许下载不能为空")
    private Integer allowDownload;

    /**
     * 原始文件名
     */
    private String originalFilename;
}
