package com.phototagmoment.controller.admin;

import com.phototagmoment.common.Result;
import com.phototagmoment.entity.Admin;
import com.phototagmoment.mapper.AdminMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理员密码重置控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/password")
@Tag(name = "管理员密码重置", description = "管理员密码重置接口")
public class AdminPasswordResetController {

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 重置管理员密码
     *
     * @param username 用户名
     * @param password 新密码
     * @return 重置结果
     */
    @PostMapping("/reset")
    @Operation(summary = "重置管理员密码", description = "重置指定管理员的密码")
    public Result<Boolean> resetPassword(
            @RequestParam String username,
            @RequestParam String password) {
        log.info("重置管理员密码请求: {}", username);
        try {
            // 查找管理员
            Admin admin = adminMapper.selectByUsername(username);
            if (admin == null) {
                log.error("重置密码失败: 用户名不存在, 用户名={}", username);
                return Result.fail("用户名不存在");
            }

            // 更新密码
            String encodedPassword = passwordEncoder.encode(password);
            admin.setPassword(encodedPassword);
            int result = adminMapper.updateById(admin);

            if (result > 0) {
                log.info("重置密码成功: 用户名={}", username);
                return Result.success(true, "密码重置成功");
            } else {
                log.error("重置密码失败: 更新数据库失败, 用户名={}", username);
                return Result.fail("密码重置失败");
            }
        } catch (Exception e) {
            log.error("重置密码异常: 用户名={}, 错误={}", username, e.getMessage(), e);
            return Result.fail("系统错误: " + e.getMessage());
        }
    }
}
