package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.UserAuth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户认证信息Mapper
 */
@Mapper
public interface UserAuthMapper extends BaseMapper<UserAuth> {

    /**
     * 根据认证类型和标识查询用户认证信息
     */
    @Select("SELECT * FROM ptm_user_auth WHERE identity_type = #{identityType} AND identifier = #{identifier} LIMIT 1")
    UserAuth selectByTypeAndIdentifier(@Param("identityType") String identityType, @Param("identifier") String identifier);

    /**
     * 根据用户ID和认证类型查询用户认证信息
     */
    @Select("SELECT * FROM ptm_user_auth WHERE user_id = #{userId} AND identity_type = #{identityType} LIMIT 1")
    UserAuth selectByUserIdAndType(@Param("userId") Long userId, @Param("identityType") String identityType);
}
