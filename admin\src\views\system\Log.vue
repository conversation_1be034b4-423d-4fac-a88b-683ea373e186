<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="form-inline">
        <el-form-item label="操作人">
          <el-input
            v-model="listQuery.username"
            placeholder="操作人"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="listQuery.operationType" placeholder="操作类型" clearable>
            <el-option label="登录" value="LOGIN" />
            <el-option label="登出" value="LOGOUT" />
            <el-option label="新增" value="INSERT" />
            <el-option label="修改" value="UPDATE" />
            <el-option label="删除" value="DELETE" />
            <el-option label="上传" value="UPLOAD" />
            <el-option label="下载" value="DOWNLOAD" />
            <el-option label="导入" value="IMPORT" />
            <el-option label="导出" value="EXPORT" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作状态">
          <el-select v-model="listQuery.status" placeholder="操作状态" clearable>
            <el-option label="成功" value="1" />
            <el-option label="失败" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作人" width="120">
        <template #default="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作类型" width="100">
        <template #default="scope">
          <el-tag :type="getOperationTypeTag(scope.row.operationType)">
            {{ getOperationTypeText(scope.row.operationType) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作描述" min-width="200">
        <template #default="scope">
          <span>{{ scope.row.description }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作方法" width="180">
        <template #default="scope">
          <span>{{ scope.row.method }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="请求参数" width="100">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="showParams(scope.row)"
            v-if="scope.row.params"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作IP" width="130">
        <template #default="scope">
          <span>{{ scope.row.ip }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作地点" width="150">
        <template #default="scope">
          <span>{{ scope.row.location || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'">
            {{ scope.row.status ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="错误信息" width="100">
        <template #default="scope">
          <el-button
            size="small"
            type="danger"
            @click="showErrorMsg(scope.row)"
            v-if="scope.row.errorMsg"
          >
            查看
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作时间" width="160">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.operationTime) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="耗时" width="100">
        <template #default="scope">
          <span>{{ scope.row.time }}ms</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="listQuery.page"
        v-model:page-size="listQuery.limit"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 参数详情对话框 -->
    <el-dialog v-model="paramsDialogVisible" title="请求参数" width="600px">
      <pre class="params-content">{{ currentParams }}</pre>
    </el-dialog>
    
    <!-- 错误信息对话框 -->
    <el-dialog v-model="errorMsgDialogVisible" title="错误信息" width="600px">
      <pre class="error-content">{{ currentErrorMsg }}</pre>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// 列表数据
const list = ref<any[]>([])
const total = ref(0)
const listLoading = ref(false)
const dateRange = ref<[string, string] | null>(null)
const paramsDialogVisible = ref(false)
const errorMsgDialogVisible = ref(false)
const currentParams = ref('')
const currentErrorMsg = ref('')

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 10,
  username: '',
  operationType: '',
  status: '',
  startTime: '',
  endTime: ''
})

// 获取操作日志列表
const getList = () => {
  listLoading.value = true
  
  // 这里应该调用实际的API
  console.log('获取操作日志列表', listQuery)
  
  // 模拟API请求
  setTimeout(() => {
    // 生成模拟数据
    list.value = Array(listQuery.limit).fill(0).map((_, index) => {
      const id = (listQuery.page - 1) * listQuery.limit + index + 1
      const operationType = ['LOGIN', 'LOGOUT', 'INSERT', 'UPDATE', 'DELETE', 'UPLOAD', 'DOWNLOAD', 'IMPORT', 'EXPORT', 'OTHER'][id % 10]
      const status = id % 5 !== 0 // 80%的操作成功
      
      return {
        id,
        username: `admin${id % 5 + 1}`,
        operationType,
        description: getOperationDescription(operationType, id),
        method: `com.phototagmoment.controller.${getControllerName(operationType)}.${getMethodName(operationType)}`,
        params: id % 3 === 0 ? null : JSON.stringify({
          id: id,
          name: `测试数据${id}`,
          status: status ? 1 : 0,
          createTime: new Date().toISOString()
        }, null, 2),
        ip: `192.168.1.${id % 255}`,
        location: id % 4 === 0 ? null : '中国 北京 朝阳区',
        status,
        errorMsg: !status ? `操作失败：${getErrorMessage(operationType)}` : null,
        operationTime: new Date(Date.now() - id * 3600000).toISOString(),
        time: Math.floor(Math.random() * 1000) + 10
      }
    })
    
    // 设置总数
    total.value = 100
    
    // 关闭加载状态
    listLoading.value = false
  }, 500)
}

// 获取操作类型标签样式
const getOperationTypeTag = (type: string) => {
  switch (type) {
    case 'LOGIN':
    case 'LOGOUT':
      return 'info'
    case 'INSERT':
      return 'success'
    case 'UPDATE':
      return 'warning'
    case 'DELETE':
      return 'danger'
    case 'UPLOAD':
    case 'DOWNLOAD':
      return 'primary'
    case 'IMPORT':
    case 'EXPORT':
      return ''
    default:
      return 'info'
  }
}

// 获取操作类型文本
const getOperationTypeText = (type: string) => {
  switch (type) {
    case 'LOGIN':
      return '登录'
    case 'LOGOUT':
      return '登出'
    case 'INSERT':
      return '新增'
    case 'UPDATE':
      return '修改'
    case 'DELETE':
      return '删除'
    case 'UPLOAD':
      return '上传'
    case 'DOWNLOAD':
      return '下载'
    case 'IMPORT':
      return '导入'
    case 'EXPORT':
      return '导出'
    default:
      return '其他'
  }
}

// 获取操作描述
const getOperationDescription = (type: string, id: number) => {
  switch (type) {
    case 'LOGIN':
      return '用户登录系统'
    case 'LOGOUT':
      return '用户退出系统'
    case 'INSERT':
      return `新增${getEntityName(id)}数据`
    case 'UPDATE':
      return `修改${getEntityName(id)}数据`
    case 'DELETE':
      return `删除${getEntityName(id)}数据`
    case 'UPLOAD':
      return '上传文件'
    case 'DOWNLOAD':
      return '下载文件'
    case 'IMPORT':
      return '导入数据'
    case 'EXPORT':
      return '导出数据'
    default:
      return '其他操作'
  }
}

// 获取实体名称
const getEntityName = (id: number) => {
  const entities = ['用户', '照片', '评论', '标签', '相册']
  return entities[id % entities.length]
}

// 获取控制器名称
const getControllerName = (type: string) => {
  switch (type) {
    case 'LOGIN':
    case 'LOGOUT':
      return 'AuthController'
    case 'INSERT':
    case 'UPDATE':
    case 'DELETE':
      return 'AdminController'
    case 'UPLOAD':
    case 'DOWNLOAD':
      return 'FileController'
    case 'IMPORT':
    case 'EXPORT':
      return 'ExportController'
    default:
      return 'CommonController'
  }
}

// 获取方法名称
const getMethodName = (type: string) => {
  switch (type) {
    case 'LOGIN':
      return 'login'
    case 'LOGOUT':
      return 'logout'
    case 'INSERT':
      return 'add'
    case 'UPDATE':
      return 'update'
    case 'DELETE':
      return 'delete'
    case 'UPLOAD':
      return 'upload'
    case 'DOWNLOAD':
      return 'download'
    case 'IMPORT':
      return 'importData'
    case 'EXPORT':
      return 'exportData'
    default:
      return 'execute'
  }
}

// 获取错误信息
const getErrorMessage = (type: string) => {
  const errors = [
    '数据库连接异常',
    '参数验证失败',
    '权限不足',
    '资源不存在',
    '操作超时'
  ]
  return errors[Math.floor(Math.random() * errors.length)]
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 处理日期范围变化
const handleDateRangeChange = (val: [string, string] | null) => {
  if (val) {
    listQuery.startTime = val[0]
    listQuery.endTime = val[1]
  } else {
    listQuery.startTime = ''
    listQuery.endTime = ''
  }
}

// 处理查询
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 重置查询条件
const resetFilter = () => {
  listQuery.username = ''
  listQuery.operationType = ''
  listQuery.status = ''
  listQuery.startTime = ''
  listQuery.endTime = ''
  dateRange.value = null
  handleFilter()
}

// 处理每页数量变化
const handleSizeChange = (val: number) => {
  listQuery.limit = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  listQuery.page = val
  getList()
}

// 显示参数详情
const showParams = (row: any) => {
  currentParams.value = row.params
  paramsDialogVisible.value = true
}

// 显示错误信息
const showErrorMsg = (row: any) => {
  currentErrorMsg.value = row.errorMsg
  errorMsgDialogVisible.value = true
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0px;
  
  .filter-container {
    margin-bottom: 20px;
    padding: 18px 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .params-content, .error-content {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    font-family: monospace;
  }
  
  .error-content {
    color: #f56c6c;
  }
}
</style>
