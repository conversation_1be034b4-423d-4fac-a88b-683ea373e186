package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.dto.PhotoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 照片Mapper接口
 */
@Mapper
@Repository
public interface PhotoMapper extends BaseMapper<Photo> {

    /**
     * 分页查询照片列表
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param currentUserId 当前用户ID
     * @return 照片列表
     */
    IPage<PhotoDTO> selectPhotoPage(Page<PhotoDTO> page, @Param("userId") Long userId, @Param("currentUserId") Long currentUserId);

    /**
     * 查询照片详情
     *
     * @param photoId 照片ID
     * @param currentUserId 当前用户ID
     * @return 照片详情
     */
    PhotoDTO selectPhotoDetail(@Param("photoId") Long photoId, @Param("currentUserId") Long currentUserId);

    /**
     * 增加照片浏览数
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int incrementViewCount(@Param("photoId") Long photoId);

    /**
     * 增加照片点赞数
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int incrementLikeCount(@Param("photoId") Long photoId);

    /**
     * 减少照片点赞数
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int decrementLikeCount(@Param("photoId") Long photoId);

    /**
     * 增加照片收藏数
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int incrementCollectCount(@Param("photoId") Long photoId);

    /**
     * 减少照片收藏数
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int decrementCollectCount(@Param("photoId") Long photoId);

    /**
     * 增加照片评论数
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int incrementCommentCount(@Param("photoId") Long photoId);

    /**
     * 减少照片评论数
     *
     * @param photoId 照片ID
     * @return 影响行数
     */
    int decrementCommentCount(@Param("photoId") Long photoId);

    /**
     * 根据照片ID列表查询照片列表
     *
     * @param page 分页参数
     * @param photoIds 照片ID列表
     * @param currentUserId 当前用户ID
     * @return 照片列表
     */
    IPage<PhotoDTO> selectPhotosByIds(Page<PhotoDTO> page, @Param("photoIds") List<Long> photoIds, @Param("currentUserId") Long currentUserId);

    /**
     * 根据照片ID列表查询照片详情列表
     *
     * @param photoIds 照片ID列表
     * @param currentUserId 当前用户ID
     * @return 照片详情列表
     */
    List<PhotoDTO> selectPhotoDetailsByIds(@Param("photoIds") List<Long> photoIds, @Param("currentUserId") Long currentUserId);

    /**
     * 检查用户是否已点赞照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    Boolean checkUserLiked(@Param("photoId") Long photoId, @Param("userId") Long userId);

    /**
     * 检查用户是否已收藏照片
     *
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 是否已收藏
     */
    Boolean checkUserCollected(@Param("photoId") Long photoId, @Param("userId") Long userId);

    /**
     * 获取照片的浏览次数
     *
     * @param photoId 照片ID
     * @return 浏览次数
     */
    @Select("SELECT view_count FROM ptm_photo WHERE id = #{photoId}")
    int getViewCount(@Param("photoId") Long photoId);

    /**
     * 根据标签查询照片
     *
     * @param tags  标签列表
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 照片列表
     */
    @Select({
        "<script>",
        "SELECT p.* FROM ptm_photo p",
        "JOIN ptm_photo_tag t ON p.id = t.photo_id",
        "WHERE p.status = 1 AND p.is_deleted = 0",
        "AND t.tag_name IN",
        "<foreach collection='tags' item='tag' open='(' separator=',' close=')'>",
        "#{tag}",
        "</foreach>",
        "GROUP BY p.id",
        "ORDER BY COUNT(p.id) DESC, p.created_at DESC",
        "LIMIT #{offset}, #{limit}",
        "</script>"
    })
    List<Photo> getPhotosByTags(@Param("tags") List<String> tags, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据标签查询照片（包含用户信息）
     *
     * @param tags 标签列表
     * @param currentUserId 当前用户ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 照片列表
     */
    List<PhotoDTO> getPhotosByTagsWithUserInfo(@Param("tags") List<String> tags, @Param("currentUserId") Long currentUserId, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计标签相关的照片数量
     *
     * @param tags 标签列表
     * @return 照片数量
     */
    @Select({
        "<script>",
        "SELECT COUNT(DISTINCT p.id) FROM ptm_photo p",
        "JOIN ptm_photo_tag t ON p.id = t.photo_id",
        "WHERE p.status = 1 AND p.is_deleted = 0",
        "AND t.tag_name IN",
        "<foreach collection='tags' item='tag' open='(' separator=',' close=')'>",
        "#{tag}",
        "</foreach>",
        "</script>"
    })
    int countPhotosByTags(@Param("tags") List<String> tags);

    /**
     * 分页查询照片列表（包含用户信息）
     *
     * @param page 分页参数
     * @param currentUserId 当前用户ID
     * @return 照片列表
     */
    IPage<PhotoDTO> selectPhotoPageWithUserInfo(Page<PhotoDTO> page, @Param("currentUserId") Long currentUserId);

    /**
     * 根据条件分页查询照片列表（包含用户信息）
     *
     * @param page 分页参数
     * @param startTime 开始时间
     * @param currentUserId 当前用户ID
     * @return 照片列表
     */
    IPage<PhotoDTO> selectHotPhotosWithUserInfo(Page<PhotoDTO> page, @Param("startTime") LocalDateTime startTime, @Param("currentUserId") Long currentUserId);

    /**
     * 查询用户关注的人发布的照片（包含用户信息）
     *
     * @param page 分页参数
     * @param followingIds 关注的用户ID列表
     * @param currentUserId 当前用户ID
     * @return 照片列表
     */
    IPage<PhotoDTO> selectFollowingPhotosWithUserInfo(Page<PhotoDTO> page, @Param("followingIds") List<Long> followingIds, @Param("currentUserId") Long currentUserId);
}
