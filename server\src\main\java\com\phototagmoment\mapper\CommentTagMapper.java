package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.CommentTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 评论标签关联Mapper接口
 */
@Mapper
public interface CommentTagMapper extends BaseMapper<CommentTag> {

    /**
     * 根据评论ID获取标签列表
     * @param commentId 评论ID
     * @return 标签名称列表
     */
    @Select("SELECT tag_name FROM ptm_comment_tag WHERE comment_id = #{commentId}")
    List<String> selectTagsByCommentId(@Param("commentId") Long commentId);

    /**
     * 根据标签名称获取评论ID列表
     * @param tagName 标签名称
     * @return 评论ID列表
     */
    @Select("SELECT comment_id FROM ptm_comment_tag WHERE tag_name = #{tagName}")
    List<Long> selectCommentIdsByTagName(@Param("tagName") String tagName);

    /**
     * 批量插入评论标签
     * @param commentTags 评论标签列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<CommentTag> commentTags);
}
