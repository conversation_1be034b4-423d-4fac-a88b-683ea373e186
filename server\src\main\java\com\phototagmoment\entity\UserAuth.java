package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户认证信息实体类
 */
@Data
@TableName("ptm_user_auth")
public class UserAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 认证类型：wechat微信，alipay支付宝，email邮箱，phone手机
     */
    private String identityType;

    /**
     * 认证标识（微信openid、邮箱、手机号）
     */
    private String identifier;

    /**
     * 凭证（密码、token）
     */
    private String credential;

    /**
     * 是否已验证
     */
    private Integer verified;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
