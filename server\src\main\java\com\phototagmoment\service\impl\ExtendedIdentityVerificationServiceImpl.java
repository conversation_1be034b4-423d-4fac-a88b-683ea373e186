package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.dto.IdentityVerificationDTO;
import com.phototagmoment.entity.IdentityVerification;
import com.phototagmoment.mapper.IdentityVerificationMapper;
import com.phototagmoment.service.ExtendedIdentityVerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 扩展的实名认证服务实现类
 */
@Service
@Primary
public class ExtendedIdentityVerificationServiceImpl extends ServiceImpl<IdentityVerificationMapper, IdentityVerification> implements ExtendedIdentityVerificationService {

    @Autowired
    private LocalIdentityVerificationServiceImpl localIdentityVerificationService;

    @Override
    public Long submitVerification(Long userId, IdentityVerificationDTO dto) {
        return localIdentityVerificationService.submitVerification(userId, dto);
    }

    @Override
    public IdentityVerification getUserVerification(Long userId) {
        return localIdentityVerificationService.getUserVerification(userId);
    }

    @Override
    public boolean isUserVerified(Long userId) {
        return localIdentityVerificationService.isUserVerified(userId);
    }

    @Override
    public boolean verifyByAlipay(Long userId, String name, String idCard) {
        return localIdentityVerificationService.verifyByAlipay(userId, name, idCard);
    }

    @Override
    public boolean verifyByWechat(Long userId, String name, String idCard) {
        return localIdentityVerificationService.verifyByWechat(userId, name, idCard);
    }

    @Override
    public boolean verifyByFaceRecognition(Long userId, String name, String idCard, String faceImageUrl) {
        return localIdentityVerificationService.verifyByFaceRecognition(userId, name, idCard, faceImageUrl);
    }

    @Override
    public boolean reviewVerification(Long verificationId, Integer status, String reason) {
        return localIdentityVerificationService.reviewVerification(verificationId, status, reason);
    }

    @Override
    public IPage<IdentityVerification> page(Page<IdentityVerification> page, LambdaQueryWrapper<IdentityVerification> queryWrapper) {
        return super.page(page, queryWrapper);
    }

    @Override
    public IdentityVerification getById(Long id) {
        return super.getById(id);
    }
}
