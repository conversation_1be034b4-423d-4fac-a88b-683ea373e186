-- 检查ptm_photo表中是否存在collect_count字段，如果不存在则添加
-- MySQL不支持IF NOT EXISTS语法，使用SHOW COLUMNS和存储过程来实现
SET @dbname = DATABASE();
SET @tablename = "ptm_photo";
SET @columnname = "collect_count";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      (TABLE_SCHEMA = @dbname)
      AND (TABLE_NAME = @tablename)
      AND (COLUMN_NAME = @columnname)
  ) > 0,
  "SELECT 1",
  CONCAT("ALTER TABLE ", @tablename, " ADD COLUMN ", @columnname, " INT DEFAULT 0 COMMENT '收藏数' AFTER `comment_count`")
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;
