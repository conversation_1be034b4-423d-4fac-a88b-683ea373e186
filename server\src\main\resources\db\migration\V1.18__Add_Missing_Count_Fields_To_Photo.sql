-- 添加缺少的计数字段到 ptm_photo 表
-- 检查 like_count 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'like_count';

-- 如果 like_count 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN like_count INT DEFAULT 0 COMMENT \'点赞数\'',
    'SELECT \'like_count column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 comment_count 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'comment_count';

-- 如果 comment_count 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN comment_count INT DEFAULT 0 COMMENT \'评论数\'',
    'SELECT \'comment_count column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 collect_count 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'collect_count';

-- 如果 collect_count 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN collect_count INT DEFAULT 0 COMMENT \'收藏数\'',
    'SELECT \'collect_count column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 view_count 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'view_count';

-- 如果 view_count 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN view_count INT DEFAULT 0 COMMENT \'浏览数\'',
    'SELECT \'view_count column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
