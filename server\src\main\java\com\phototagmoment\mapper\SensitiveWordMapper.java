package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.SensitiveWord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 敏感词Mapper接口
 */
@Mapper
public interface SensitiveWordMapper extends BaseMapper<SensitiveWord> {

    /**
     * 获取所有启用的敏感词
     *
     * @return 敏感词列表
     */
    @Select("SELECT * FROM ptm_sensitive_word WHERE status = 1")
    List<SensitiveWord> getAllEnabledWords();

    /**
     * 根据类型获取敏感词
     *
     * @param type 类型
     * @return 敏感词列表
     */
    @Select("SELECT * FROM ptm_sensitive_word WHERE type = #{type}")
    List<SensitiveWord> getWordsByType(String type);

    /**
     * 获取所有敏感词类型
     *
     * @return 类型列表
     */
    @Select("SELECT DISTINCT type FROM ptm_sensitive_word ORDER BY type")
    List<String> selectTypes();

    /**
     * 按类型统计敏感词数量
     *
     * @return 统计结果
     */
    @Select("SELECT type, COUNT(*) as count FROM ptm_sensitive_word GROUP BY type ORDER BY count DESC")
    List<Map<String, Object>> selectCountByType();

    /**
     * 按级别统计敏感词数量
     *
     * @return 统计结果
     */
    @Select("SELECT level, COUNT(*) as count FROM ptm_sensitive_word GROUP BY level ORDER BY level")
    List<Map<String, Object>> selectCountByLevel();
}
