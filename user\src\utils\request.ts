import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { showNotify } from 'vant'
import router from '../router'
import * as mockApi from './mockApi'

// 是否启用Mock模式
const USE_MOCK = import.meta.env.DEV && import.meta.env.VITE_USE_MOCK === 'true'

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: '/api', // API基础URL
  timeout: 10000, // 请求超时时间，减少到10秒
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  // 添加重试配置
  // 注意：axios本身不支持重试，这里只是为了记录配置
  // 实际的重试逻辑在响应拦截器中实现
  // retry: 3, // 最大重试次数
  // retryDelay: 1000 // 重试间隔时间
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 特殊处理登录请求
    const isLoginRequest = config.url && (config.url.includes('/login') || config.url.includes('/auth/login'));

    if (isLoginRequest) {
      console.log('检测到登录请求:', config.url);

      // 确保headers对象存在
      if (!config.headers) {
        config.headers = {}
      }

      // 确保Content-Type正确设置为application/json
      config.headers['Content-Type'] = 'application/json';

      // 打印登录请求详情
      console.log('登录请求详情:');
      console.log('- 请求URL:', config.url);
      console.log('- 请求方法:', config.method);
      console.log('- 请求头:', config.headers);
      console.log('- 请求数据:', config.data);

      return config;
    }

    // 非登录请求的处理
    // 从localStorage获取token
    let token = localStorage.getItem('token')

    // 如果localStorage中没有token，尝试从sessionStorage获取
    if (!token || token.trim() === '') {
      token = sessionStorage.getItem('token')
      if (token) {
        console.log('从sessionStorage获取到token')
      }
    }

    // 打印请求信息
    console.log(`请求: ${config.method?.toUpperCase()} ${config.url}`)

    // 确保headers对象存在
    if (!config.headers) {
      config.headers = {}
    }

    // 如果有token则添加到请求头
    if (token && token.trim() !== '') {
      // 设置Authorization头
      config.headers['Authorization'] = `Bearer ${token}`

      // 同时设置token头，以防后端检查这个头
      config.headers['token'] = token

      console.log('添加token到请求头:', `Bearer ${token}`)
      console.log('请求URL:', config.url)
      console.log('完整请求头:', config.headers)
    } else {
      console.log('没有找到有效的token')
    }

    // 确保Content-Type正确设置
    if (!config.headers['Content-Type'] && config.method?.toLowerCase() !== 'get') {
      config.headers['Content-Type'] = 'application/json'
    }

    // 添加时间戳，避免缓存
    if (config.method?.toLowerCase() === 'get') {
      config.params = { ...config.params, _t: Date.now() }
    }

    // 打印完整的请求配置
    console.log('完整请求配置:', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data
    });

    return config
  },
  (error) => {
    console.error('请求错误', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data
    console.log('响应拦截器收到响应:', response.config.url, res)

    // 特殊处理登录请求，打印更详细的信息
    if (response.config.url && (response.config.url.includes('/login') || response.config.url.includes('/auth/login'))) {
      console.log('登录请求响应详情:');
      console.log('- 请求URL:', response.config.url);
      console.log('- 请求方法:', response.config.method);
      console.log('- 请求头:', response.config.headers);
      console.log('- 请求数据:', response.config.data);
      console.log('- 响应状态:', response.status);
      console.log('- 响应头:', response.headers);
      console.log('- 响应数据:', res);
    }

    // 检查响应格式
    if (res === null || res === undefined) {
      console.error('响应数据为空', response.config.url);
      return Promise.reject(new Error('响应数据为空'));
    }

    // 处理没有code字段的响应（可能是直接返回数据的接口）
    if (res.code === undefined) {
      console.log('响应没有code字段，直接返回数据', response.config.url);
      return res;
    }

    // 如果返回的状态码不是200，说明接口请求失败
    if (res.code !== 200) {
      // 对于用户信息接口，如果是401错误，不显示通知
      if (response.config.url === '/user/info' && res.code === 401) {
        console.log('用户信息接口返回401，静默处理')
      }
      // 对于通知列表接口，如果是401错误，不显示通知
      else if (response.config.url?.includes('/notification/list') && res.code === 401) {
        console.log('通知列表接口返回401，静默处理')
      }
      // 对于上传凭证接口，记录详细错误信息
      else if (response.config.url?.includes('/photo/upload/token') || response.config.url?.includes('/photo/upload/batch-token')) {
        console.error('获取上传凭证失败', res);
        showNotify({ type: 'danger', message: res.message || '获取上传凭证失败' });
      }
      // 对于保存照片信息接口，记录详细错误信息
      else if (response.config.url?.includes('/photo/save-info')) {
        console.error('保存照片信息失败', res);
        console.error('请求数据:', response.config.data);
        showNotify({ type: 'danger', message: res.message || '保存照片信息失败' });
      }
      else {
        showNotify({ type: 'danger', message: res.message || '系统错误' })
      }

      // 401: 未登录或token过期
      if (res.code === 401) {
        console.log('接口返回401，检查是否需要跳转登录页')

        // 清除本地token
        localStorage.removeItem('token')

        // 检查当前路径是否需要登录
        const currentPath = router.currentRoute.value.path
        const requiresAuth = router.currentRoute.value.meta.requiresAuth

        // 只有当路由需要登录时才跳转到登录页
        if (requiresAuth) {
          console.log('当前路由需要登录，跳转到登录页')
          router.replace({
            path: '/auth/login',
            query: { redirect: router.currentRoute.value.fullPath }
          })
        } else {
          console.log('当前路由不需要登录，不跳转')
        }
      }

      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res
    }
  },
  (error) => {
    console.error('响应错误', error)

    // 检查是否为503错误（后端服务不可用），如果是则尝试使用Mock API
    if (error.response && error.response.status === 503) {
      console.log('后端服务不可用，尝试使用Mock API')
      return handleMockResponse(error.config)
    }

    // 检查是否为网络错误，如果是则尝试使用Mock API
    if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
      console.log('网络错误，尝试使用Mock API')
      return handleMockResponse(error.config)
    }

    // 特殊处理登录请求错误
    if (error.config && error.config.url && (error.config.url.includes('/login') || error.config.url.includes('/auth/login'))) {
      console.error('登录请求失败，详细错误信息:');
      console.error('- 请求URL:', error.config.url);
      console.error('- 请求方法:', error.config.method);
      console.error('- 请求头:', error.config.headers);
      console.error('- 请求数据:', error.config.data);

      if (error.response) {
        console.error('- 响应状态:', error.response.status);
        console.error('- 响应头:', error.response.headers);
        console.error('- 响应数据:', error.response.data);
      } else {
        console.error('- 无响应对象，可能是网络错误或请求被阻止');
      }

      console.error('- 错误消息:', error.message);
      console.error('- 错误代码:', error.code);
      console.error('- 错误堆栈:', error.stack);
    }

    // 处理网络错误
    let message = '网络错误，请稍后重试'

    // 处理连接重置错误
    if (error.code === 'ECONNRESET') {
      console.log('连接被重置，可能是网络问题或服务器问题')
      message = '连接被重置，请稍后重试'

      // 对于通知列表接口，静默处理连接重置错误
      if (error.config && error.config.url && error.config.url.includes('/notification/list')) {
        console.log('通知列表接口连接重置，静默处理')
        return Promise.reject(error)
      }
    }
    // 处理网络错误
    else if (error.code === 'ERR_NETWORK') {
      console.log('网络错误，可能是服务器未启动或网络断开')
      message = '网络连接失败，请检查网络设置'

      // 对于通知列表接口，静默处理网络错误
      if (error.config && error.config.url && error.config.url.includes('/notification/list')) {
        console.log('通知列表接口网络错误，静默处理')
        return Promise.reject(error)
      }
    }
    // 处理超时错误
    else if (error.code === 'ECONNABORTED') {
      console.log('请求超时')
      message = '请求超时，请稍后重试'
    }
    // 处理HTTP错误
    else if (error.response) {
      console.log('HTTP错误状态码:', error.response.status)

      switch (error.response.status) {
        case 400:
          message = '请求错误'
          break
        case 401:
          message = '未授权，请登录'
          console.log('401错误，检查是否需要跳转登录页')

          // 清除本地token
          localStorage.removeItem('token')

          // 检查当前路径是否需要登录
          const currentPath = router.currentRoute.value.path
          const requiresAuth = router.currentRoute.value.meta.requiresAuth

          // 只有当路由需要登录时才跳转到登录页
          if (requiresAuth) {
            console.log('当前路由需要登录，跳转到登录页')
            router.replace({
              path: '/auth/login',
              query: { redirect: router.currentRoute.value.fullPath }
            })
          } else {
            console.log('当前路由不需要登录，不跳转')
          }
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误${error.response.status}`
      }
    }

    // 对于通知列表接口，静默处理所有错误
    if (error.config && error.config.url && error.config.url.includes('/notification/list')) {
      console.log('通知列表接口错误，静默处理')
      return Promise.reject(error)
    }

    // 对于保存照片信息接口，记录详细错误信息
    if (error.config && error.config.url && error.config.url.includes('/photo/save-info')) {
      console.error('保存照片信息请求失败:', error);
      console.error('请求数据:', error.config.data);
      message = '保存照片信息失败，请稍后重试';
    }

    // 显示错误通知
    showNotify({ type: 'danger', message })
    return Promise.reject(error)
  }
)

// Mock API处理函数
const handleMockResponse = async (config: any) => {
  if (!config || !config.url) {
    return Promise.reject(new Error('无效的请求配置'))
  }

  const url = config.url
  const method = config.method?.toLowerCase()
  const params = config.params || {}
  const data = config.data || {}

  console.log(`使用Mock API处理请求: ${method?.toUpperCase()} ${url}`)

  try {
    // 根据URL匹配对应的Mock API
    if (url.includes('/recommendation/home')) {
      return await mockApi.mockHomeRecommendations(params)
    }

    if (url.includes('/recommendation/following')) {
      return await mockApi.mockFollowingPhotos(params)
    }

    if (url.includes('/recommendation/hot')) {
      return await mockApi.mockHotPhotos(params)
    }

    if (url.includes('/recommendation/interest-tags')) {
      return await mockApi.mockUserInterestTags()
    }

    if (url.includes('/photo-notes/') && url.includes('/comments') && method === 'get') {
      const photoId = extractPhotoIdFromUrl(url)
      return await mockApi.mockPhotoComments({ photoId, ...params })
    }

    if (url.includes('/photo-notes/') && url.includes('/comments') && method === 'post') {
      const photoId = extractPhotoIdFromUrl(url)
      return await mockApi.mockAddPhotoComment({ photoId, ...data })
    }

    if (url.includes('/comments/') && url.includes('/like')) {
      const commentId = extractCommentIdFromUrl(url)
      return await mockApi.mockLikeComment(commentId)
    }

    if (url.includes('/photo-notes/') && !url.includes('/comments')) {
      const photoId = extractPhotoIdFromUrl(url)
      return await mockApi.mockPhotoDetail(photoId)
    }

    if (url.includes('/search/')) {
      return await mockApi.mockSearch(params)
    }

    if (url.includes('/user/') && url.includes('/profile')) {
      const userId = extractUserIdFromUrl(url)
      return await mockApi.mockUserProfile(userId)
    }

    // 默认返回成功响应
    return {
      code: 200,
      message: 'Mock API 响应',
      data: null
    }
  } catch (error) {
    console.error('Mock API处理失败:', error)
    return Promise.reject(error)
  }
}

// 从URL中提取照片ID
const extractPhotoIdFromUrl = (url: string): number => {
  const match = url.match(/\/photo-notes\/(\d+)/)
  return match ? parseInt(match[1]) : 1
}

// 从URL中提取评论ID
const extractCommentIdFromUrl = (url: string): number => {
  const match = url.match(/\/comments\/(\d+)/)
  return match ? parseInt(match[1]) : 1
}

// 从URL中提取用户ID
const extractUserIdFromUrl = (url: string): number => {
  const match = url.match(/\/user\/(\d+)/)
  return match ? parseInt(match[1]) : 1
}

export default service
