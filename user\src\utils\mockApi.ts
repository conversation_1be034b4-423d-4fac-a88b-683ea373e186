/**
 * Mock API service for development
 * This file provides mock data for API calls when the backend is not available
 */

// Mock photo data
const generateMockPhotos = (count: number, startId: number = 1) => {
  return Array.from({ length: count }, (_, i) => {
    const id = startId + i;
    return {
      id,
      title: `照片标题 ${id}`,
      description: `这是照片 ${id} 的描述，展示了一些美丽的风景或有趣的瞬间。`,
      url: `https://picsum.photos/id/${(id % 100) + 100}/800/600`,
      thumbnailUrl: `https://picsum.photos/id/${(id % 100) + 100}/400/300`,
      uploadTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toISOString(),
      userId: Math.floor(Math.random() * 10) + 1,
      userName: `用户${Math.floor(Math.random() * 10) + 1}`,
      userAvatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
      viewCount: Math.floor(Math.random() * 1000),
      likeCount: Math.floor(Math.random() * 200),
      commentCount: Math.floor(Math.random() * 50),
      isLiked: Math.random() > 0.5,
      isCollected: Math.random() > 0.7,
      tags: ['风景', '旅行', '城市', '人像', '美食'].filter(() => Math.random() > 0.5)
    };
  });
};

// Mock user data
const generateMockUsers = (count: number) => {
  return Array.from({ length: count }, (_, i) => {
    const id = i + 1;
    return {
      id,
      username: `user${id}`,
      nickname: `用户${id}`,
      avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${id}.jpg`,
      bio: '热爱摄影，记录生活中的美好瞬间',
      followingCount: Math.floor(Math.random() * 200),
      followerCount: Math.floor(Math.random() * 300),
      photoCount: Math.floor(Math.random() * 100),
      isFollowing: Math.random() > 0.5
    };
  });
};

// Mock interest tags
const mockInterestTags = [
  '风景', '旅行', '城市', '人像', '美食', '建筑', '动物', '植物', '黑白', '夜景'
];

// Mock API response
const mockApiResponse = (data: any) => {
  return {
    code: 200,
    message: 'success',
    data
  };
};

// Mock paginated response
const mockPaginatedResponse = (records: any[], page: number, size: number, total: number) => {
  return mockApiResponse({
    records,
    page,
    size,
    total,
    pages: Math.ceil(total / size)
  });
};

/**
 * Mock API for home recommendations
 */
export const mockHomeRecommendations = (params: any) => {
  const { page = 1, size = 10 } = params;
  const total = 100;
  const photos = generateMockPhotos(size, (page - 1) * size + 1);
  return Promise.resolve(mockPaginatedResponse(photos, page, size, total));
};

/**
 * Mock API for following photos
 */
export const mockFollowingPhotos = (params: any) => {
  const { page = 1, size = 10 } = params;
  const total = 50;
  const photos = generateMockPhotos(size, (page - 1) * size + 1);
  return Promise.resolve(mockPaginatedResponse(photos, page, size, total));
};

/**
 * Mock API for hot photos
 */
export const mockHotPhotos = (params: any) => {
  const { page = 1, size = 10 } = params;
  const total = 80;
  const photos = generateMockPhotos(size, (page - 1) * size + 1);
  return Promise.resolve(mockPaginatedResponse(photos, page, size, total));
};

/**
 * Mock API for recommended photos
 */
export const mockRecommendedPhotos = (params: any) => {
  const { page = 1, size = 10 } = params;
  const total = 60;
  const photos = generateMockPhotos(size, (page - 1) * size + 1);
  return Promise.resolve(mockPaginatedResponse(photos, page, size, total));
};

/**
 * Mock API for user interest tags
 */
export const mockUserInterestTags = () => {
  return Promise.resolve(mockApiResponse(mockInterestTags));
};

/**
 * Mock API for recording user behavior
 */
export const mockRecordUserBehavior = () => {
  return Promise.resolve(mockApiResponse(null));
};

/**
 * Mock API for updating user interest model
 */
export const mockUpdateUserInterestModel = () => {
  return Promise.resolve(mockApiResponse(null));
};

/**
 * Mock API for photo detail
 */
export const mockPhotoDetail = (id: number) => {
  const photo = generateMockPhotos(1, id)[0];
  const comments = Array.from({ length: 5 }, (_, i) => ({
    id: i + 1,
    content: `这是一条评论，评论ID为 ${i + 1}`,
    createTime: new Date(Date.now() - Math.floor(Math.random() * 10) * 86400000).toISOString(),
    userId: Math.floor(Math.random() * 10) + 1,
    userName: `用户${Math.floor(Math.random() * 10) + 1}`,
    userAvatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
    likeCount: Math.floor(Math.random() * 20),
    isLiked: Math.random() > 0.5
  }));

  return Promise.resolve(mockApiResponse({
    ...photo,
    comments
  }));
};

/**
 * Mock API for photo comments
 */
export const mockPhotoComments = (params: any) => {
  const { photoId, page = 1, size = 10 } = params;
  const total = 20;

  const comments = Array.from({ length: Math.min(size, total - (page - 1) * size) }, (_, i) => ({
    id: (page - 1) * size + i + 1,
    photoId,
    content: `这是照片 ${photoId} 的第 ${(page - 1) * size + i + 1} 条评论`,
    userId: Math.floor(Math.random() * 10) + 1,
    userName: `用户${Math.floor(Math.random() * 10) + 1}`,
    userAvatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
    likeCount: Math.floor(Math.random() * 20),
    isLiked: Math.random() > 0.5,
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 10) * 86400000).toISOString()
  }));

  return Promise.resolve(mockPaginatedResponse(comments, page, size, total));
};

/**
 * Mock API for adding a comment
 */
export const mockAddPhotoComment = (params: any) => {
  const { photoId, content } = params;

  return Promise.resolve(mockApiResponse({
    id: Math.floor(Math.random() * 1000) + 1,
    photoId,
    content,
    userId: 1, // 当前用户ID
    createdAt: new Date().toISOString()
  }));
};

/**
 * Mock API for liking a comment
 */
export const mockLikeComment = (id: number) => {
  return Promise.resolve(mockApiResponse(true));
};

/**
 * Mock API for user profile
 */
export const mockUserProfile = (id: number) => {
  const user = generateMockUsers(1)[0];
  user.id = id;
  const photos = generateMockPhotos(9, id * 10);

  return Promise.resolve(mockApiResponse({
    user,
    photos
  }));
};

/**
 * Mock API for search
 */
export const mockSearch = (params: any) => {
  const { keyword, type = 'all', page = 1, size = 10 } = params;
  let total = 0;
  let records: any[] = [];

  switch (type) {
    case 'photo':
    case 'photos':
      total = 30;
      records = generateMockPhotos(size, (page - 1) * size + 1);
      break;
    case 'user':
    case 'users':
      total = 20;
      records = generateMockUsers(size);
      break;
    case 'tag':
    case 'tags':
      total = 10;
      records = mockInterestTags
        .filter(tag => tag.includes(keyword))
        .map((tag, i) => ({
          id: i + 1,
          name: tag,
          count: Math.floor(Math.random() * 1000)
        }));
      break;
    default:
      total = 60;
      records = [
        ...generateMockPhotos(5, (page - 1) * size + 1),
        ...generateMockUsers(3)
      ];
  }

  return Promise.resolve(mockPaginatedResponse(records, page, size, total));
};
