package com.phototagmoment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件信息DTO
 */
@Data
@Schema(description = "文件信息")
public class FileInfoDTO {

    @Schema(description = "文件ID")
    private Long id;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "存储文件名")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "文件大小（格式化）")
    private String fileSizeFormatted;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "MIME类型")
    private String mimeType;

    @Schema(description = "文件扩展名")
    private String extension;

    @Schema(description = "文件分类")
    private String category;

    @Schema(description = "上传者ID")
    private Long uploaderId;

    @Schema(description = "上传者名称")
    private String uploaderName;

    @Schema(description = "上传者类型")
    private String uploaderType;

    @Schema(description = "存储类型")
    private String storageType;

    @Schema(description = "是否在回收站")
    private Boolean inTrash;

    @Schema(description = "文件状态")
    private Integer status;

    @Schema(description = "文件标签")
    private String tags;

    @Schema(description = "文件描述")
    private String description;

    @Schema(description = "访问次数")
    private Integer accessCount;

    @Schema(description = "最后访问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    /**
     * 格式化文件大小
     */
    public String getFileSizeFormatted() {
        if (fileSize == null) {
            return "0 B";
        }
        
        long size = fileSize;
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取文件图标
     */
    public String getFileIcon() {
        if (extension == null) {
            return "file";
        }
        
        String ext = extension.toLowerCase();
        switch (ext) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "image";
            case "pdf":
                return "pdf";
            case "doc":
            case "docx":
                return "word";
            case "xls":
            case "xlsx":
                return "excel";
            case "ppt":
            case "pptx":
                return "powerpoint";
            case "txt":
                return "text";
            case "zip":
            case "rar":
            case "7z":
                return "archive";
            case "mp4":
            case "avi":
            case "mov":
            case "wmv":
                return "video";
            case "mp3":
            case "wav":
            case "flac":
                return "audio";
            default:
                return "file";
        }
    }

    /**
     * 判断是否为图片文件
     */
    public boolean isImage() {
        if (extension == null) {
            return false;
        }
        String ext = extension.toLowerCase();
        return ext.equals("jpg") || ext.equals("jpeg") || ext.equals("png") || 
               ext.equals("gif") || ext.equals("bmp") || ext.equals("webp");
    }

    /**
     * 判断是否为视频文件
     */
    public boolean isVideo() {
        if (extension == null) {
            return false;
        }
        String ext = extension.toLowerCase();
        return ext.equals("mp4") || ext.equals("avi") || ext.equals("mov") || 
               ext.equals("wmv") || ext.equals("flv") || ext.equals("mkv");
    }

    /**
     * 判断是否为音频文件
     */
    public boolean isAudio() {
        if (extension == null) {
            return false;
        }
        String ext = extension.toLowerCase();
        return ext.equals("mp3") || ext.equals("wav") || ext.equals("flac") || 
               ext.equals("aac") || ext.equals("ogg");
    }

    /**
     * 判断是否为文档文件
     */
    public boolean isDocument() {
        if (extension == null) {
            return false;
        }
        String ext = extension.toLowerCase();
        return ext.equals("pdf") || ext.equals("doc") || ext.equals("docx") || 
               ext.equals("xls") || ext.equals("xlsx") || ext.equals("ppt") || 
               ext.equals("pptx") || ext.equals("txt");
    }

    /**
     * 获取文件类别
     */
    public String getFileCategory() {
        if (isImage()) {
            return "图片";
        } else if (isVideo()) {
            return "视频";
        } else if (isAudio()) {
            return "音频";
        } else if (isDocument()) {
            return "文档";
        } else {
            return "其他";
        }
    }
}
