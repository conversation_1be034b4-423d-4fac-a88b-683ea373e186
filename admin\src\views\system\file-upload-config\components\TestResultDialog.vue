<template>
  <el-dialog
    v-model="dialogVisible"
    title="连接测试结果"
    width="500px"
    :close-on-click-modal="false"
  >
    <div v-if="testResult" class="test-result-container">
      <!-- 测试状态 -->
      <div class="test-status">
        <div class="status-icon">
          <el-icon 
            :size="48" 
            :color="testResult.success ? '#67c23a' : '#f56c6c'"
          >
            <check v-if="testResult.success" />
            <close v-else />
          </el-icon>
        </div>
        <div class="status-text">
          <h3 :class="testResult.success ? 'success-text' : 'error-text'">
            {{ testResult.success ? '连接测试成功' : '连接测试失败' }}
          </h3>
          <p class="status-message">{{ testResult.message }}</p>
        </div>
      </div>

      <!-- 测试详情 -->
      <el-card class="test-details">
        <template #header>
          <span>测试详情</span>
        </template>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="测试结果">
            <el-tag :type="testResult.success ? 'success' : 'danger'">
              {{ testResult.success ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="响应时间">
            <span v-if="testResult.responseTime !== undefined">
              {{ testResult.responseTime }}ms
              <el-tag 
                v-if="testResult.responseTime < 1000" 
                type="success" 
                size="small" 
                style="margin-left: 8px;"
              >
                快速
              </el-tag>
              <el-tag 
                v-else-if="testResult.responseTime < 3000" 
                type="warning" 
                size="small" 
                style="margin-left: 8px;"
              >
                正常
              </el-tag>
              <el-tag 
                v-else 
                type="danger" 
                size="small" 
                style="margin-left: 8px;"
              >
                缓慢
              </el-tag>
            </span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="测试时间">
            {{ formatDateTime(testResult.testTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态消息">
            {{ testResult.message }}
          </el-descriptions-item>
          <el-descriptions-item v-if="testResult.errorDetails" label="错误详情">
            <div class="error-details">
              {{ testResult.errorDetails }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 性能评估 -->
      <el-card v-if="testResult.success && testResult.responseTime" class="performance-card">
        <template #header>
          <span>性能评估</span>
        </template>
        <div class="performance-content">
          <div class="performance-meter">
            <el-progress
              type="circle"
              :percentage="getPerformanceScore(testResult.responseTime)"
              :color="getPerformanceColor(testResult.responseTime)"
              :width="100"
            />
          </div>
          <div class="performance-info">
            <div class="performance-level">
              {{ getPerformanceLevel(testResult.responseTime) }}
            </div>
            <div class="performance-description">
              {{ getPerformanceDescription(testResult.responseTime) }}
            </div>
          </div>
        </div>
      </el-card>

      <!-- 建议 -->
      <el-card v-if="getSuggestions().length > 0" class="suggestions-card">
        <template #header>
          <span>优化建议</span>
        </template>
        <ul class="suggestions-list">
          <li v-for="(suggestion, index) in getSuggestions()" :key="index">
            {{ suggestion }}
          </li>
        </ul>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button v-if="testResult && !testResult.success" type="primary" @click="handleRetry">
          重新测试
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Check, Close } from '@element-plus/icons-vue'
import type { TestResult } from '@/api/fileUploadConfig'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  modelValue: boolean
  testResult?: TestResult | null
}

const props = withDefaults(defineProps<Props>(), {
  testResult: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  retry: []
}>()

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const getPerformanceScore = (responseTime: number) => {
  if (responseTime < 500) return 100
  if (responseTime < 1000) return 90
  if (responseTime < 2000) return 70
  if (responseTime < 3000) return 50
  if (responseTime < 5000) return 30
  return 10
}

const getPerformanceColor = (responseTime: number) => {
  if (responseTime < 1000) return '#67c23a'
  if (responseTime < 3000) return '#e6a23c'
  return '#f56c6c'
}

const getPerformanceLevel = (responseTime: number) => {
  if (responseTime < 500) return '优秀'
  if (responseTime < 1000) return '良好'
  if (responseTime < 2000) return '一般'
  if (responseTime < 3000) return '较慢'
  if (responseTime < 5000) return '缓慢'
  return '很慢'
}

const getPerformanceDescription = (responseTime: number) => {
  if (responseTime < 500) return '响应速度非常快，用户体验优秀'
  if (responseTime < 1000) return '响应速度良好，满足大部分使用场景'
  if (responseTime < 2000) return '响应速度一般，可以接受'
  if (responseTime < 3000) return '响应速度较慢，建议优化网络配置'
  if (responseTime < 5000) return '响应速度缓慢，影响用户体验'
  return '响应速度很慢，需要检查网络连接和配置'
}

const getSuggestions = () => {
  if (!props.testResult) return []
  
  const suggestions = []
  
  if (!props.testResult.success) {
    suggestions.push('检查网络连接是否正常')
    suggestions.push('验证配置参数是否正确')
    suggestions.push('确认存储服务是否可用')
    
    if (props.testResult.errorDetails) {
      if (props.testResult.errorDetails.includes('timeout')) {
        suggestions.push('增加连接超时时间')
      }
      if (props.testResult.errorDetails.includes('authentication')) {
        suggestions.push('检查认证信息是否正确')
      }
      if (props.testResult.errorDetails.includes('permission')) {
        suggestions.push('检查账户权限设置')
      }
    }
  } else if (props.testResult.responseTime && props.testResult.responseTime > 2000) {
    suggestions.push('考虑选择更近的存储区域')
    suggestions.push('检查网络带宽是否充足')
    suggestions.push('优化存储服务配置')
  }
  
  return suggestions
}

const handleRetry = () => {
  emit('retry')
}
</script>

<style scoped>
.test-result-container {
  max-height: 600px;
  overflow-y: auto;
}

.test-status {
  display: flex;
  align-items: center;
  padding: 24px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.status-icon {
  margin-right: 16px;
}

.status-text {
  flex: 1;
  text-align: left;
}

.status-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.success-text {
  color: #67c23a;
}

.error-text {
  color: #f56c6c;
}

.status-message {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.test-details,
.performance-card,
.suggestions-card {
  margin-bottom: 16px;
}

.test-details:last-child,
.performance-card:last-child,
.suggestions-card:last-child {
  margin-bottom: 0;
}

.error-details {
  padding: 8px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #f56c6c;
  white-space: pre-wrap;
  word-break: break-all;
}

.performance-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.performance-meter {
  margin-right: 24px;
}

.performance-info {
  flex: 1;
}

.performance-level {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.performance-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.suggestions-list {
  margin: 0;
  padding: 0 0 0 20px;
  color: #606266;
}

.suggestions-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.suggestions-list li:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  text-align: right;
}
</style>
