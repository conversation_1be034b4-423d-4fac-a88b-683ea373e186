package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 用户Mapper
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM ptm_user WHERE username = #{username} AND is_deleted = 0 LIMIT 1")
    User selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM ptm_user WHERE email = #{email} AND is_deleted = 0 LIMIT 1")
    User selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     */
    @Select("SELECT * FROM ptm_user WHERE phone = #{phone} AND is_deleted = 0 LIMIT 1")
    User selectByPhone(@Param("phone") String phone);

    /**
     * 根据昵称查询用户
     */
    @Select("SELECT * FROM ptm_user WHERE nickname = #{nickname} AND is_deleted = 0 LIMIT 1")
    User selectByNickname(@Param("nickname") String nickname);

    /**
     * 根据微信OpenID查询用户
     */
    @Select("SELECT * FROM ptm_user WHERE wechat_open_id = #{openId} AND is_deleted = 0 LIMIT 1")
    User selectByWechatOpenId(@Param("openId") String openId);

    /**
     * 根据微信UnionID查询用户
     */
    @Select("SELECT * FROM ptm_user WHERE wechat_union_id = #{unionId} AND is_deleted = 0 LIMIT 1")
    User selectByWechatUnionId(@Param("unionId") String unionId);

    /**
     * 增加用户关注数
     */
    @Update("UPDATE ptm_user SET following_count = following_count + 1 WHERE id = #{userId}")
    int incrementFollowingCount(@Param("userId") Long userId);

    /**
     * 减少用户关注数
     */
    @Update("UPDATE ptm_user SET following_count = following_count - 1 WHERE id = #{userId} AND following_count > 0")
    int decrementFollowingCount(@Param("userId") Long userId);

    /**
     * 增加用户粉丝数
     */
    @Update("UPDATE ptm_user SET follower_count = follower_count + 1 WHERE id = #{userId}")
    int incrementFollowerCount(@Param("userId") Long userId);

    /**
     * 减少用户粉丝数
     */
    @Update("UPDATE ptm_user SET follower_count = follower_count - 1 WHERE id = #{userId} AND follower_count > 0")
    int decrementFollowerCount(@Param("userId") Long userId);

    /**
     * 查询用户关注列表
     */
    IPage<UserDTO> selectFollowingList(Page<UserDTO> page, @Param("userId") Long userId);

    /**
     * 查询用户粉丝列表
     */
    IPage<UserDTO> selectFollowerList(Page<UserDTO> page, @Param("userId") Long userId);

    /**
     * 获取推荐用户
     *
     * @param limit 数量限制
     * @param userId 当前用户ID
     * @return 推荐用户列表
     */
    List<User> getRecommendedUsers(@Param("limit") int limit, @Param("userId") Long userId);

    /**
     * 查询用户统计信息
     *
     * @return 用户统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalUsers, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as activeUsers, " +
            "COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as todayNewUsers, " +
            "COUNT(CASE WHEN DATE(last_login_time) = CURDATE() THEN 1 END) as todayActiveUsers " +
            "FROM ptm_user WHERE is_deleted = 0")
    Map<String, Object> selectUserStatistics();

    /**
     * 查询月度用户增长数据
     *
     * @return 月度用户增长数据
     */
    List<Map<String, Object>> selectMonthlyUserGrowth();

    /**
     * 查询月度活跃用户数据
     *
     * @return 月度活跃用户数据
     */
    List<Map<String, Object>> selectMonthlyActiveUsers();

    /**
     * 查询年度用户增长数据
     *
     * @return 年度用户增长数据
     */
    List<Map<String, Object>> selectYearlyUserGrowth();

    /**
     * 查询年度活跃用户数据
     *
     * @return 年度活跃用户数据
     */
    List<Map<String, Object>> selectYearlyActiveUsers();

    /**
     * 查询周度用户增长数据
     *
     * @return 周度用户增长数据
     */
    List<Map<String, Object>> selectWeeklyUserGrowth();

    /**
     * 查询周度活跃用户数据
     *
     * @return 周度活跃用户数据
     */
    List<Map<String, Object>> selectWeeklyActiveUsers();

    /**
     * 查询最新用户
     *
     * @param limit 数量限制
     * @return 最新用户列表
     */
    @Select("SELECT id, username, nickname, avatar, created_at " +
            "FROM ptm_user " +
            "WHERE is_deleted = 0 " +
            "ORDER BY created_at DESC " +
            "LIMIT #{limit}")
    List<User> selectLatestUsers(@Param("limit") Integer limit);
}
