package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.PhotoNoteReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 照片笔记举报Mapper接口
 */
@Mapper
public interface PhotoNoteReportMapper extends BaseMapper<PhotoNoteReport> {

    /**
     * 检查用户是否已举报过该照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否已举报
     */
    @Select("SELECT COUNT(*) > 0 FROM ptm_photo_note_report WHERE note_id = #{noteId} AND report_user_id = #{userId} AND is_deleted = 0")
    boolean checkUserReported(@Param("noteId") Long noteId, @Param("userId") Long userId);

    /**
     * 统计照片笔记的举报次数
     *
     * @param noteId 照片笔记ID
     * @return 举报次数
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_note_report WHERE note_id = #{noteId} AND is_deleted = 0")
    int countReportsByNoteId(@Param("noteId") Long noteId);
}
