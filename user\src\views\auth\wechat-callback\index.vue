<template>
  <div class="wechat-callback">
    <div class="loading-container">
      <van-loading type="spinner" size="48px" />
      <p class="mt-4">微信登录中，请稍候...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast, showSuccessToast, showFailToast } from 'vant';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 处理微信回调
const handleWechatCallback = async () => {
  try {
    // 获取回调参数
    const code = route.query.code as string;
    const state = route.query.state as string;
    
    if (!code) {
      showFailToast('登录失败，缺少授权码');
      router.push('/auth/login');
      return;
    }
    
    // 微信登录
    const response = await fetch('/api/wechat/mp/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        code,
      }),
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 登录成功
      const { token, user } = result.data;
      
      // 保存登录信息
      userStore.setToken(token);
      userStore.setUser(user);
      
      showSuccessToast('登录成功');
      
      // 跳转到首页或重定向页面
      const redirect = route.query.redirect as string || '/';
      router.push(redirect);
    } else {
      showFailToast(result.message || '登录失败');
      router.push('/auth/login');
    }
  } catch (error) {
    console.error('微信登录失败:', error);
    showFailToast('登录失败，请稍后再试');
    router.push('/auth/login');
  }
};

// 组件挂载时处理回调
onMounted(() => {
  handleWechatCallback();
});
</script>

<style lang="scss" scoped>
.wechat-callback {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    p {
      margin-top: 16px;
      color: #666;
    }
  }
}
</style>
