<template>
  <div>
    <el-form :model="configForm" label-width="180px">
      <el-form-item label="启用实名认证">
        <el-switch v-model="configForm.enabled" />
      </el-form-item>
      <el-form-item label="认证服务提供商">
        <el-select v-model="configForm.provider" placeholder="请选择认证服务提供商">
          <el-option label="本地认证" value="local" />
          <el-option label="支付宝认证" value="alipay" />
          <el-option label="微信认证" value="wechat" />
          <el-option label="人脸识别认证" value="face" />
        </el-select>
      </el-form-item>

      <el-divider content-position="left">支付宝认证配置</el-divider>
      <el-form-item label="应用ID">
        <el-input v-model="configForm.alipayAppId" placeholder="请输入支付宝应用ID" />
      </el-form-item>
      <el-form-item label="应用私钥">
        <el-input v-model="configForm.alipayPrivateKey" type="textarea" :rows="3" placeholder="请输入支付宝应用私钥" />
      </el-form-item>
      <el-form-item label="支付宝公钥">
        <el-input v-model="configForm.alipayPublicKey" type="textarea" :rows="3" placeholder="请输入支付宝公钥" />
      </el-form-item>

      <el-divider content-position="left">微信认证配置</el-divider>
      <el-form-item label="应用ID">
        <el-input v-model="configForm.wechatAppId" placeholder="请输入微信应用ID" />
      </el-form-item>
      <el-form-item label="应用密钥">
        <el-input v-model="configForm.wechatAppSecret" placeholder="请输入微信应用密钥" show-password />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getSystemConfig, updateSystemConfig } from '@/api/system/config'

export default {
  name: 'IdentityVerificationConfig',
  setup() {
    const loading = ref(false)
    const configForm = reactive({
      enabled: false,
      provider: 'local',
      alipayAppId: '',
      alipayPrivateKey: '',
      alipayPublicKey: '',
      wechatAppId: '',
      wechatAppSecret: ''
    })

    // 获取配置
    const fetchConfig = async () => {
      loading.value = true
      try {
        // 获取实名认证配置
        const config = await getSystemConfig([
          'identity-verification.enabled',
          'identity-verification.provider',
          'identity-verification.alipay.app-id',
          'identity-verification.alipay.private-key',
          'identity-verification.alipay.public-key',
          'identity-verification.wechat.app-id',
          'identity-verification.wechat.app-secret'
        ])

        // 设置表单数据
        configForm.enabled = config['identity-verification.enabled'] === 'true'
        configForm.provider = config['identity-verification.provider'] || 'local'
        configForm.alipayAppId = config['identity-verification.alipay.app-id'] || ''
        configForm.alipayPrivateKey = config['identity-verification.alipay.private-key'] || ''
        configForm.alipayPublicKey = config['identity-verification.alipay.public-key'] || ''
        configForm.wechatAppId = config['identity-verification.wechat.app-id'] || ''
        configForm.wechatAppSecret = config['identity-verification.wechat.app-secret'] || ''

        loading.value = false
      } catch (error) {
        console.error('获取实名认证配置失败', error)
        ElMessage.error('获取配置失败，请稍后再试')
        loading.value = false
      }
    }

    // 保存配置
    const saveConfig = async () => {
      loading.value = true
      try {
        // 构建配置数据
        const configData = {
          'identity-verification.enabled': configForm.enabled.toString(),
          'identity-verification.provider': configForm.provider,
          'identity-verification.alipay.app-id': configForm.alipayAppId,
          'identity-verification.alipay.private-key': configForm.alipayPrivateKey,
          'identity-verification.alipay.public-key': configForm.alipayPublicKey,
          'identity-verification.wechat.app-id': configForm.wechatAppId,
          'identity-verification.wechat.app-secret': configForm.wechatAppSecret
        }

        // 更新配置
        const res = await updateSystemConfig(configData)
        if (res.code === 200) {
          ElMessage.success('保存成功')
        } else {
          ElMessage.error(res.message || '保存失败')
        }
        loading.value = false
      } catch (error) {
        console.error('保存实名认证配置失败', error)
        ElMessage.error('保存失败，请稍后再试')
        loading.value = false
      }
    }

    onMounted(() => {
      fetchConfig()
    })

    return {
      loading,
      configForm,
      fetchConfig,
      saveConfig
    }
  }
}
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}
</style>
