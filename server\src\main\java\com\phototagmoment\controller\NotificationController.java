package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.NotificationDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.NotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 通知控制器
 */
@Slf4j
@RestController
@RequestMapping("/notification")
@Tag(name = "通知接口", description = "通知相关接口")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    /**
     * 获取用户通知列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取用户通知列表", description = "分页获取当前用户的通知列表")
    public ApiResponse<IPage<NotificationDTO>> getNotificationList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "通知类型：1关注，2点赞，3评论，4回复，5系统，不传则查询所有类型") @RequestParam(required = false) Integer type) {
        Long userId = SecurityUtil.getCurrentUserId();
        // 如果用户未登录，返回空数据
        if (userId == null) {
            return ApiResponse.success(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>());
        }
        IPage<NotificationDTO> notificationPage = notificationService.getUserNotifications(userId, page, size, type);
        return ApiResponse.success(notificationPage);
    }

    /**
     * 获取未读通知数量
     */
    @GetMapping("/unread/count")
    @Operation(summary = "获取未读通知数量", description = "获取当前用户的未读通知数量")
    public ApiResponse<Integer> getUnreadCount() {
        Long userId = SecurityUtil.getCurrentUserId();
        // 如果用户未登录，返回0
        if (userId == null) {
            return ApiResponse.success(0);
        }
        int count = notificationService.getUnreadCount(userId);
        return ApiResponse.success(count);
    }

    /**
     * 标记通知为已读
     */
    @PostMapping("/read/{notificationId}")
    @Operation(summary = "标记通知为已读", description = "标记指定通知为已读")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> markAsRead(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = notificationService.markAsRead(notificationId, userId);
        return ApiResponse.success(result);
    }

    /**
     * 标记所有通知为已读
     */
    @PostMapping("/read/all")
    @Operation(summary = "标记所有通知为已读", description = "标记当前用户的所有通知为已读")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> markAllAsRead() {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = notificationService.markAllAsRead(userId);
        return ApiResponse.success(result);
    }

    /**
     * 删除通知
     */
    @DeleteMapping("/{notificationId}")
    @Operation(summary = "删除通知", description = "删除指定通知")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> deleteNotification(
            @Parameter(description = "通知ID") @PathVariable Long notificationId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = notificationService.deleteNotification(notificationId, userId);
        return ApiResponse.success(result);
    }
}
