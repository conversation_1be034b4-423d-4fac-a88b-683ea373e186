import request from '@/utils/request'

// 是否使用模拟数据
const USE_MOCK = false

/**
 * 评论
 */
export interface Comment {
  id: number
  photoId: number
  userId: number
  content: string
  likeCount: number
  replyCount: number
  isLiked: boolean
  createdAt: string
  user: {
    id: number
    username: string
    nickname: string
    avatar: string
  }
  replies?: Comment[]
  replyToUser?: {
    id: number
    username: string
    nickname: string
  }
}

/**
 * 获取照片评论列表
 * @param photoId 照片ID
 * @param page 页码
 * @param size 每页大小
 * @returns 评论列表
 */
export function getPhotoComments(
  photoId: number,
  page: number = 1,
  size: number = 10
): Promise<Comment[]> {
  if (USE_MOCK) {
    // 模拟数据
    const mockComments: Comment[] = Array.from({ length: 5 }, (_, i) => ({
      id: i + 1,
      photoId,
      userId: Math.floor(Math.random() * 10) + 1,
      content: `这是一条评论，评论ID为 ${i + 1}`,
      likeCount: Math.floor(Math.random() * 10),
      replyCount: i % 2 === 0 ? 2 : 0,
      isLiked: Math.random() > 0.5,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10) * 86400000).toISOString(),
      user: {
        id: Math.floor(Math.random() * 10) + 1,
        username: `user${Math.floor(Math.random() * 10) + 1}`,
        nickname: `用户${Math.floor(Math.random() * 10) + 1}`,
        avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
      },
      replies: i % 2 === 0 ? Array.from({ length: 2 }, (_, j) => ({
        id: (i + 1) * 100 + j + 1,
        photoId,
        userId: Math.floor(Math.random() * 10) + 1,
        content: `这是一条回复，回复ID为 ${(i + 1) * 100 + j + 1}`,
        likeCount: Math.floor(Math.random() * 5),
        replyCount: 0,
        isLiked: Math.random() > 0.5,
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 5) * 86400000).toISOString(),
        user: {
          id: Math.floor(Math.random() * 10) + 1,
          username: `user${Math.floor(Math.random() * 10) + 1}`,
          nickname: `用户${Math.floor(Math.random() * 10) + 1}`,
          avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
        },
        replyToUser: {
          id: Math.floor(Math.random() * 10) + 1,
          username: `user${Math.floor(Math.random() * 10) + 1}`,
          nickname: `用户${Math.floor(Math.random() * 10) + 1}`,
        }
      })) : []
    }));

    return Promise.resolve(mockComments);
  }

  return request({
    url: `/comment/photo/${photoId}`,
    method: 'get',
    params: { page, size }
  })
}

/**
 * 添加评论
 * @param photoId 照片ID
 * @param content 评论内容
 * @returns 新评论
 */
export function addComment(photoId: number, content: string): Promise<Comment> {
  if (USE_MOCK) {
    // 从localStorage获取当前用户信息
    let userId = 1;
    let username = 'currentUser';
    let nickname = '当前用户';
    let avatar = 'https://randomuser.me/api/portraits/men/1.jpg';

    try {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        const user = JSON.parse(userInfo);
        userId = user.id || 1;
        username = user.username || 'currentUser';
        nickname = user.nickname || '当前用户';
        avatar = user.avatar || 'https://randomuser.me/api/portraits/men/1.jpg';
      }
    } catch (e) {
      console.error('解析用户信息失败', e);
    }

    // 模拟数据
    const mockComment: Comment = {
      id: Date.now(),
      photoId,
      userId,
      content,
      likeCount: 0,
      replyCount: 0,
      isLiked: false,
      createdAt: new Date().toISOString(),
      user: {
        id: userId,
        username,
        nickname,
        avatar,
      },
      replies: []
    };

    return Promise.resolve(mockComment);
  }

  return request({
    url: '/comment',
    method: 'post',
    data: { photoId, content }
  })
}

/**
 * 回复评论
 * @param commentId 评论ID
 * @param content 回复内容
 * @param replyToUserId 回复用户ID
 * @returns 新回复
 */
export function replyComment(
  commentId: number,
  content: string,
  replyToUserId?: number
): Promise<Comment> {
  return request({
    url: '/comment/reply',
    method: 'post',
    data: { commentId, content, replyToUserId }
  })
}

/**
 * 点赞评论
 * @param commentId 评论ID
 * @returns 是否成功
 */
export function likeComment(commentId: number): Promise<boolean> {
  return request({
    url: `/comment/like/${commentId}`,
    method: 'post'
  })
}

/**
 * 取消点赞评论
 * @param commentId 评论ID
 * @returns 是否成功
 */
export function unlikeComment(commentId: number): Promise<boolean> {
  return request({
    url: `/comment/unlike/${commentId}`,
    method: 'post'
  })
}

/**
 * 删除评论
 * @param commentId 评论ID
 * @returns 是否成功
 */
export function deleteComment(commentId: number): Promise<boolean> {
  return request({
    url: `/comment/${commentId}`,
    method: 'delete'
  })
}
