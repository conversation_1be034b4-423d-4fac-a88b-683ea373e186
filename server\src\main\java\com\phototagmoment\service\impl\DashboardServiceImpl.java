package com.phototagmoment.service.impl;

import com.phototagmoment.dto.DashboardStatsDTO;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.entity.PhotoNote;
import com.phototagmoment.entity.User;
import com.phototagmoment.mapper.CommentMapper;
import com.phototagmoment.mapper.FileRecordMapper;
import com.phototagmoment.mapper.PhotoNoteMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.DashboardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制台服务实现
 */
@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PhotoNoteMapper photoNoteMapper;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private FileRecordMapper fileRecordMapper;

    @Override
    public DashboardStatsDTO getDashboardStats() {
        DashboardStatsDTO stats = new DashboardStatsDTO();

        // 获取基础统计数据
        Map<String, Object> userStats = userMapper.selectUserStatistics();
        Map<String, Object> photoStats = photoNoteMapper.selectPhotoNoteStatistics();
        Map<String, Object> commentStats = commentMapper.selectCommentStatistics();
        Map<String, Object> fileStats = fileRecordMapper.selectFileStatistics();

        // 用户统计
        stats.setUserCount(getLongValue(userStats, "totalUsers"));
        stats.setTodayUsers(getLongValue(userStats, "todayUsers"));
        stats.setActiveUsers(getLongValue(userStats, "activeUsers"));
        stats.setUserTrend(calculateTrend(userStats, "weekUsers", "lastWeekUsers"));

        // 照片统计
        stats.setPhotoCount(getLongValue(photoStats, "totalPhotos"));
        stats.setTodayPhotos(getLongValue(photoStats, "todayPhotos"));
        stats.setPendingPhotos(getLongValue(photoStats, "pendingPhotos"));
        stats.setPhotoTrend(calculateTrend(photoStats, "weekPhotos", "lastWeekPhotos"));

        // 评论统计
        stats.setCommentCount(getLongValue(commentStats, "totalComments"));
        stats.setTodayComments(getLongValue(commentStats, "todayComments"));
        stats.setPendingComments(getLongValue(commentStats, "pendingComments"));
        stats.setCommentTrend(calculateTrend(commentStats, "weekComments", "lastWeekComments"));

        // 存储统计
        stats.setStorageUsed(getLongValue(fileStats, "totalSize"));
        stats.setStorageUsedFormatted(formatFileSize(stats.getStorageUsed()));
        stats.setTodayStorage(getLongValue(fileStats, "todaySize"));
        stats.setStorageTrend(calculateTrend(fileStats, "weekSize", "lastWeekSize"));

        // 系统运行天数
        stats.setSystemRunDays(calculateSystemRunDays());

        return stats;
    }

    @Override
    public Map<String, Object> getUserGrowthTrend(String period) {
        Map<String, Object> result = new HashMap<>();

        List<Map<String, Object>> newUserData;
        List<Map<String, Object>> activeUserData;
        List<String> dateLabels;

        switch (period) {
            case "month":
                newUserData = userMapper.selectMonthlyUserGrowth();
                activeUserData = userMapper.selectMonthlyActiveUsers();
                dateLabels = generateMonthLabels();
                break;
            case "year":
                newUserData = userMapper.selectYearlyUserGrowth();
                activeUserData = userMapper.selectYearlyActiveUsers();
                dateLabels = generateYearLabels();
                break;
            default: // week
                newUserData = userMapper.selectWeeklyUserGrowth();
                activeUserData = userMapper.selectWeeklyActiveUsers();
                dateLabels = generateWeekLabels();
                break;
        }

        result.put("newUsers", newUserData);
        result.put("activeUsers", activeUserData);
        result.put("labels", dateLabels);

        return result;
    }

    @Override
    public List<Map<String, Object>> getContentDistribution() {
        return photoNoteMapper.selectContentDistribution();
    }

    @Override
    public List<UserDTO> getLatestUsers(Integer limit) {
        // 需要转换User实体为UserDTO
        List<User> users = userMapper.selectLatestUsers(limit);
        return users.stream().map(this::convertToUserDTO).collect(Collectors.toList());
    }

    @Override
    public List<PhotoNoteDTO> getLatestPhotos(Integer limit) {
        // 需要转换PhotoNote实体为PhotoNoteDTO
        List<PhotoNote> photos = photoNoteMapper.selectLatestPhotos(limit);
        return photos.stream().map(this::convertToPhotoNoteDTO).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();

        // 系统基本信息
        systemInfo.put("serverTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("osVersion", System.getProperty("os.version"));

        // 内存信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        systemInfo.put("totalMemory", formatFileSize(totalMemory));
        systemInfo.put("usedMemory", formatFileSize(usedMemory));
        systemInfo.put("freeMemory", formatFileSize(freeMemory));
        systemInfo.put("maxMemory", formatFileSize(maxMemory));
        systemInfo.put("memoryUsagePercent",
            BigDecimal.valueOf(usedMemory * 100.0 / maxMemory).setScale(2, RoundingMode.HALF_UP));

        return systemInfo;
    }

    // 辅助方法
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0L;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return 0L;
    }

    private BigDecimal calculateTrend(Map<String, Object> stats, String currentKey, String previousKey) {
        Long current = getLongValue(stats, currentKey);
        Long previous = getLongValue(stats, previousKey);

        if (previous == 0) return BigDecimal.ZERO;

        return BigDecimal.valueOf((current - previous) * 100.0 / previous)
                .setScale(2, RoundingMode.HALF_UP);
    }

    private String formatFileSize(Long bytes) {
        if (bytes == null || bytes == 0) return "0 B";

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes.doubleValue();

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }

    private Long calculateSystemRunDays() {
        // 这里可以从配置或数据库中获取系统启动时间
        // 暂时返回一个固定值
        return 365L;
    }

    private List<String> generateWeekLabels() {
        return Arrays.asList("周一", "周二", "周三", "周四", "周五", "周六", "周日");
    }

    private List<String> generateMonthLabels() {
        List<String> labels = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 29; i >= 0; i--) {
            labels.add(now.minusDays(i).format(DateTimeFormatter.ofPattern("MM-dd")));
        }
        return labels;
    }

    private List<String> generateYearLabels() {
        List<String> labels = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 11; i >= 0; i--) {
            labels.add(now.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }
        return labels;
    }

    /**
     * 转换User实体为UserDTO
     */
    private UserDTO convertToUserDTO(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setNickname(user.getNickname());
        dto.setAvatar(user.getAvatar());
        dto.setCreatedAt(user.getCreatedAt());
        return dto;
    }

    /**
     * 转换PhotoNote实体为PhotoNoteDTO
     */
    private PhotoNoteDTO convertToPhotoNoteDTO(PhotoNote photoNote) {
        PhotoNoteDTO dto = new PhotoNoteDTO();
        dto.setId(photoNote.getId());
        dto.setUserId(photoNote.getUserId());
        dto.setTitle(photoNote.getTitle());
        dto.setContent(photoNote.getContent());
        dto.setViewCount(photoNote.getViewCount());
        dto.setLikeCount(photoNote.getLikeCount());
        dto.setCommentCount(photoNote.getCommentCount());
        dto.setCreatedAt(photoNote.getCreatedAt());
        return dto;
    }
}
