package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.entity.DictData;
import com.phototagmoment.entity.DictType;

import java.util.List;
import java.util.Map;

/**
 * 数据字典Service接口
 */
public interface DictService {

    /**
     * 获取所有字典类型
     *
     * @return 字典类型列表
     */
    List<DictType> listAllDictTypes();

    /**
     * 分页获取字典类型
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @param keyword  关键字
     * @return 分页结果
     */
    IPage<DictType> pageDictTypes(int page, int pageSize, String keyword);

    /**
     * 根据ID获取字典类型
     *
     * @param id 字典类型ID
     * @return 字典类型
     */
    DictType getDictTypeById(Long id);

    /**
     * 根据字典类型获取字典类型对象
     *
     * @param dictType 字典类型
     * @return 字典类型对象
     */
    DictType getDictTypeByType(String dictType);

    /**
     * 保存字典类型
     *
     * @param dictType 字典类型
     * @return 是否成功
     */
    boolean saveDictType(DictType dictType);

    /**
     * 更新字典类型
     *
     * @param dictType 字典类型
     * @return 是否成功
     */
    boolean updateDictType(DictType dictType);

    /**
     * 删除字典类型
     *
     * @param id 字典类型ID
     * @return 是否成功
     */
    boolean deleteDictType(Long id);

    /**
     * 获取所有字典数据
     *
     * @return 字典数据列表
     */
    List<DictData> listAllDictData();

    /**
     * 分页获取字典数据
     *
     * @param page       页码
     * @param pageSize   每页大小
     * @param dictTypeId 字典类型ID
     * @param keyword    关键字
     * @return 分页结果
     */
    IPage<DictData> pageDictData(int page, int pageSize, Long dictTypeId, String keyword);

    /**
     * 根据ID获取字典数据
     *
     * @param id 字典数据ID
     * @return 字典数据
     */
    DictData getDictDataById(Long id);

    /**
     * 根据字典类型ID获取字典数据列表
     *
     * @param dictTypeId 字典类型ID
     * @return 字典数据列表
     */
    List<DictData> getDictDataByTypeId(Long dictTypeId);

    /**
     * 根据字典类型获取字典数据列表
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<DictData> getDictDataByType(String dictType);

    /**
     * 根据字典类型获取字典数据Map
     *
     * @param dictType 字典类型
     * @return 字典数据Map，key为dictValue，value为dictLabel
     */
    Map<String, String> getDictDataMapByType(String dictType);

    /**
     * 保存字典数据
     *
     * @param dictData 字典数据
     * @return 是否成功
     */
    boolean saveDictData(DictData dictData);

    /**
     * 更新字典数据
     *
     * @param dictData 字典数据
     * @return 是否成功
     */
    boolean updateDictData(DictData dictData);

    /**
     * 删除字典数据
     *
     * @param id 字典数据ID
     * @return 是否成功
     */
    boolean deleteDictData(Long id);

    /**
     * 刷新字典缓存
     */
    void refreshCache();
}
