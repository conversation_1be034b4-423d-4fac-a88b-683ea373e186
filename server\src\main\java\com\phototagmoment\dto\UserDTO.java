package com.phototagmoment.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户DTO
 */
@Data
public class UserDTO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别: 0-未知, 1-男, 2-女
     */
    private Integer gender;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 所在地
     */
    private String location;

    /**
     * 个人网站
     */
    private String website;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 照片数
     */
    private Integer photoCount;

    /**
     * 是否已关注
     */
    private Boolean isFollowing;

    /**
     * 真实姓名（实名认证）
     */
    private String realName;

    /**
     * 身份证号（实名认证）
     */
    private String idCard;

    /**
     * 认证状态：0-待审核，1-已通过，2-已拒绝
     */
    private Integer verificationStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
