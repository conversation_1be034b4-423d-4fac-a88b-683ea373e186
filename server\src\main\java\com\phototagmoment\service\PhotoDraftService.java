package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.dto.PhotoDraftDTO;
import com.phototagmoment.entity.PhotoDraft;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 照片草稿服务接口
 */
public interface PhotoDraftService extends IService<PhotoDraft> {

    /**
     * 保存草稿
     *
     * @param draftDTO 草稿信息
     * @param files 照片文件列表
     * @param userId 用户ID
     * @return 草稿ID
     */
    Long saveDraft(PhotoDraftDTO draftDTO, List<MultipartFile> files, Long userId);

    /**
     * 更新草稿
     *
     * @param draftId 草稿ID
     * @param draftDTO 草稿信息
     * @param files 照片文件列表
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean updateDraft(Long draftId, PhotoDraftDTO draftDTO, List<MultipartFile> files, Long userId);

    /**
     * 获取草稿详情
     *
     * @param draftId 草稿ID
     * @param userId 用户ID
     * @return 草稿详情
     */
    PhotoDraftDTO getDraftDetail(Long draftId, Long userId);

    /**
     * 获取用户草稿列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @return 草稿列表
     */
    IPage<PhotoDraftDTO> getUserDrafts(int page, int size, Long userId);

    /**
     * 删除草稿
     *
     * @param draftId 草稿ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteDraft(Long draftId, Long userId);

    /**
     * 发布草稿
     *
     * @param draftId 草稿ID
     * @param userId 用户ID
     * @return 照片ID列表
     */
    List<Long> publishDraft(Long draftId, Long userId);
}
