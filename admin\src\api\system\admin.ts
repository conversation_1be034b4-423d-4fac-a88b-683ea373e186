import request from '@/utils/request'

/**
 * 获取管理员列表
 * @param params 查询参数
 */
export function getAdminList(params?: any) {
  return request({
    url: '/admin/system/admin/list',
    method: 'get',
    params
  })
}

/**
 * 获取管理员详情
 * @param id 管理员ID
 */
export function getAdminDetail(id: number) {
  return request({
    url: `/admin/system/admin/${id}`,
    method: 'get'
  })
}

/**
 * 创建管理员
 * @param data 管理员信息
 */
export function createAdmin(data: any) {
  return request({
    url: '/admin/system/admin',
    method: 'post',
    data
  })
}

/**
 * 更新管理员
 * @param id 管理员ID
 * @param data 管理员信息
 */
export function updateAdmin(id: number, data: any) {
  return request({
    url: `/admin/system/admin/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除管理员
 * @param id 管理员ID
 */
export function deleteAdmin(id: number) {
  return request({
    url: `/admin/system/admin/${id}`,
    method: 'delete'
  })
}

/**
 * 更新管理员状态
 * @param id 管理员ID
 * @param status 状态：0-禁用，1-启用
 */
export function updateAdminStatus(id: number, status: number) {
  return request({
    url: `/admin/system/admin/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 重置管理员密码
 * @param id 管理员ID
 */
export function resetAdminPassword(id: number) {
  return request({
    url: `/admin/system/admin/${id}/reset-password`,
    method: 'put'
  })
}

/**
 * 修改管理员密码
 * @param data 密码信息
 */
export function updateAdminPassword(data: any) {
  return request({
    url: '/admin/system/admin/password',
    method: 'put',
    data
  })
}
