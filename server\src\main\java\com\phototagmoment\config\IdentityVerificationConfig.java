package com.phototagmoment.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.phototagmoment.service.SystemConfigService;

import javax.annotation.PostConstruct;

/**
 * 实名认证配置类
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "identity-verification")
public class IdentityVerificationConfig {

    /**
     * 系统配置服务
     */
    @Autowired
    private SystemConfigService configService;

    /**
     * 是否启用实名认证
     */
    private boolean enabled = false;

    /**
     * 实名认证服务提供商
     * 可选值：local, alipay, wechat, face
     */
    private String provider = "local";

    /**
     * 支付宝实名认证配置
     */
    private AlipayConfig alipay = new AlipayConfig();

    /**
     * 微信实名认证配置
     */
    private WechatConfig wechat = new WechatConfig();

    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        log.info("初始化实名认证配置");

        try {
            // 从数据库读取配置
            this.enabled = configService.getBooleanValue("identity-verification.enabled", false);
            this.provider = configService.getConfigValue("identity-verification.provider", "local");

            // 支付宝实名认证配置
            this.alipay.setAppId(configService.getConfigValue("identity-verification.alipay.app-id", ""));
            this.alipay.setPrivateKey(configService.getConfigValue("identity-verification.alipay.private-key", ""));
            this.alipay.setPublicKey(configService.getConfigValue("identity-verification.alipay.public-key", ""));

            // 微信实名认证配置
            this.wechat.setAppId(configService.getConfigValue("identity-verification.wechat.app-id", ""));
            this.wechat.setAppSecret(configService.getConfigValue("identity-verification.wechat.app-secret", ""));

            log.info("从数据库读取实名认证配置成功");
        } catch (Exception e) {
            log.error("从数据库读取实名认证配置失败，将使用默认配置: {}", e.getMessage());
        }

        log.info("实名认证配置: enabled={}, provider={}", this.enabled, this.provider);
    }

    /**
     * 人脸识别实名认证配置
     */
    private FaceConfig face = new FaceConfig();

    /**
     * 支付宝实名认证配置
     */
    @Data
    public static class AlipayConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用私钥
         */
        private String privateKey;

        /**
         * 支付宝公钥
         */
        private String publicKey;

        /**
         * 网关地址
         */
        private String gatewayUrl = "https://openapi.alipay.com/gateway.do";

        /**
         * 签名类型
         */
        private String signType = "RSA2";

        /**
         * 编码
         */
        private String charset = "UTF-8";

        /**
         * 格式
         */
        private String format = "json";
    }

    /**
     * 微信实名认证配置
     */
    @Data
    public static class WechatConfig {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appSecret;

        /**
         * 商户ID
         */
        private String mchId;

        /**
         * 商户密钥
         */
        private String mchKey;

        /**
         * 证书路径
         */
        private String certPath;
    }

    /**
     * 人脸识别实名认证配置
     */
    @Data
    public static class FaceConfig {
        /**
         * 服务提供商
         * 可选值：aliyun, tencent, baidu
         */
        private String provider = "aliyun";

        /**
         * 阿里云配置
         */
        private AliyunConfig aliyun = new AliyunConfig();

        /**
         * 腾讯云配置
         */
        private TencentConfig tencent = new TencentConfig();

        /**
         * 百度云配置
         */
        private BaiduConfig baidu = new BaiduConfig();

        /**
         * 阿里云配置
         */
        @Data
        public static class AliyunConfig {
            /**
             * 访问密钥ID
             */
            private String accessKeyId;

            /**
             * 访问密钥密码
             */
            private String accessKeySecret;

            /**
             * 区域ID
             */
            private String regionId = "cn-shanghai";
        }

        /**
         * 腾讯云配置
         */
        @Data
        public static class TencentConfig {
            /**
             * 访问密钥ID
             */
            private String secretId;

            /**
             * 访问密钥密码
             */
            private String secretKey;

            /**
             * 区域ID
             */
            private String region = "ap-guangzhou";
        }

        /**
         * 百度云配置
         */
        @Data
        public static class BaiduConfig {
            /**
             * 应用ID
             */
            private String appId;

            /**
             * 应用密钥
             */
            private String apiKey;

            /**
             * 密钥
             */
            private String secretKey;
        }
    }
}
