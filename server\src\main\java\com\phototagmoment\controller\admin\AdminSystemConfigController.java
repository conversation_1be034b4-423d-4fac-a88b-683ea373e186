package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.entity.SystemConfig;
import com.phototagmoment.service.SystemConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统配置管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/config")
@Tag(name = "系统配置管理", description = "系统配置管理相关接口")
public class AdminSystemConfigController {

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 获取所有系统配置
     */
    @GetMapping("/list")
    @Operation(summary = "获取所有系统配置", description = "获取所有系统配置")
    public Result<List<SystemConfig>> list() {
        List<SystemConfig> configs = systemConfigService.listAllConfigs();
        return Result.success(configs);
    }

    /**
     * 分页获取系统配置
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取系统配置", description = "分页获取系统配置")
    public Result<IPage<SystemConfig>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        IPage<SystemConfig> configPage = systemConfigService.pageConfigs(page, pageSize, keyword);
        return Result.success(configPage);
    }

    /**
     * 根据ID获取系统配置
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取系统配置", description = "根据ID获取系统配置")
    public Result<SystemConfig> getById(@Parameter(description = "配置ID") @PathVariable Long id) {
        SystemConfig config = systemConfigService.getConfigById(id);
        return Result.success(config);
    }

    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/value/{configKey}")
    @Operation(summary = "根据配置键获取配置值", description = "根据配置键获取配置值")
    public Result<String> getConfigValue(@Parameter(description = "配置键") @PathVariable String configKey) {
        String value = systemConfigService.getConfigValue(configKey);
        return Result.success(value);
    }

    /**
     * 批量获取配置值
     */
    @PostMapping("/batch")
    @Operation(summary = "批量获取配置值", description = "批量获取配置值")
    public Result<Map<String, String>> batchGetConfigValues(@Parameter(description = "配置键列表") @RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> keys = (List<String>) params.get("keys");
        if (keys == null || keys.isEmpty()) {
            return Result.fail("配置键列表不能为空");
        }

        Map<String, String> result = new HashMap<>();
        for (String key : keys) {
            String value = systemConfigService.getConfigValue(key);
            result.put(key, value);
        }

        return Result.success(result);
    }

    /**
     * 保存系统配置
     */
    @PostMapping
    @Operation(summary = "保存系统配置", description = "保存系统配置")
    public Result<Boolean> save(@Parameter(description = "系统配置") @RequestBody SystemConfig config) {
        boolean success = systemConfigService.saveConfig(config);
        return Result.success(success);
    }

    /**
     * 批量保存系统配置
     */
    @PutMapping("/batch")
    @Operation(summary = "批量保存系统配置", description = "批量保存系统配置")
    public Result<Boolean> batchSave(@Parameter(description = "配置键值对") @RequestBody Map<String, String> configs) {
        if (configs == null || configs.isEmpty()) {
            return Result.fail("配置不能为空");
        }

        boolean success = true;
        for (Map.Entry<String, String> entry : configs.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 检查配置是否存在
            String existingValue = systemConfigService.getConfigValue(key);
            if (existingValue != null) {
                // 更新配置
                success = success && systemConfigService.updateConfigValue(key, value);
            } else {
                // 创建新配置
                SystemConfig config = new SystemConfig();
                config.setConfigKey(key);
                config.setConfigValue(value);
                config.setConfigName(key);
                config.setConfigType(getConfigType(value));
                config.setRemark("管理员创建的配置");
                config.setDescription("管理员通过后台创建的配置项");
                config.setIsSystem(true);
                success = success && systemConfigService.saveConfig(config);
            }
        }

        // 刷新缓存
        systemConfigService.refreshCache();

        return Result.success(success);
    }

    /**
     * 更新系统配置
     */
    @PutMapping
    @Operation(summary = "更新系统配置", description = "更新系统配置")
    public Result<Boolean> update(@Parameter(description = "系统配置") @RequestBody SystemConfig config) {
        boolean success = systemConfigService.updateConfig(config);
        return Result.success(success);
    }

    /**
     * 更新配置值
     */
    @PutMapping("/value")
    @Operation(summary = "更新配置值", description = "更新配置值")
    public Result<Boolean> updateConfigValue(
            @Parameter(description = "配置键") @RequestParam String configKey,
            @Parameter(description = "配置值") @RequestParam String configValue) {
        boolean success = systemConfigService.updateConfigValue(configKey, configValue);
        return Result.success(success);
    }

    /**
     * 删除系统配置
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除系统配置", description = "删除系统配置")
    public Result<Boolean> delete(@Parameter(description = "配置ID") @PathVariable Long id) {
        boolean success = systemConfigService.deleteConfig(id);
        return Result.success(success);
    }

    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新配置缓存", description = "刷新配置缓存")
    public Result<Boolean> refreshCache() {
        systemConfigService.refreshCache();
        return Result.success(true);
    }

    /**
     * 获取配置类型
     */
    private String getConfigType(String value) {
        if (value == null || value.isEmpty()) {
            return "string";
        }

        if (value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")) {
            return "boolean";
        }

        try {
            Integer.parseInt(value);
            return "number";
        } catch (NumberFormatException e) {
            try {
                Double.parseDouble(value);
                return "number";
            } catch (NumberFormatException e2) {
                if (value.startsWith("{") && value.endsWith("}") || value.startsWith("[") && value.endsWith("]")) {
                    return "json";
                }
                return "string";
            }
        }
    }
}
