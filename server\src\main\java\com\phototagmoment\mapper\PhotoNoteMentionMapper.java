package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.entity.PhotoNoteMention;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 照片笔记@用户Mapper接口
 */
@Mapper
public interface PhotoNoteMentionMapper extends BaseMapper<PhotoNoteMention> {

    /**
     * 根据照片笔记ID查询@用户列表
     *
     * @param noteId 照片笔记ID
     * @return @用户列表
     */
    @Select("SELECT pnm.mentioned_user_id, u.nickname as mentioned_user_nickname, u.avatar as mentioned_user_avatar " +
            "FROM ptm_photo_note_mention pnm " +
            "LEFT JOIN ptm_user u ON pnm.mentioned_user_id = u.id " +
            "WHERE pnm.note_id = #{noteId}")
    List<PhotoNoteDTO.PhotoNoteMentionDTO> selectMentionsByNoteId(@Param("noteId") Long noteId);

    /**
     * 批量插入照片笔记@用户
     *
     * @param mentions @用户列表
     * @return 影响行数
     */
    int batchInsert(@Param("mentions") List<PhotoNoteMention> mentions);

    /**
     * 删除照片笔记的所有@用户
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    int deleteByNoteId(@Param("noteId") Long noteId);

    /**
     * 查询用户被@的照片笔记数量
     *
     * @param userId 用户ID
     * @return 被@的照片笔记数量
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_note_mention WHERE mentioned_user_id = #{userId}")
    Long countMentionsByUserId(@Param("userId") Long userId);
}
