package com.phototagmoment.service.impl;

import com.baidu.aip.contentcensor.AipContentCensor;
import com.phototagmoment.service.ImageModerationService;
import com.phototagmoment.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.HashMap;

/**
 * 百度图像审核服务实现类
 * 使用百度内容安全服务进行图像内容审核
 */
@Slf4j
@Service("baiduImageModerationServiceImpl")
@ConditionalOnProperty(name = "content-moderation.image.provider", havingValue = "baidu", matchIfMissing = true)
public class BaiduContentModerationServiceImpl implements ImageModerationService {

    @Autowired
    private SystemConfigService configService;

    private AipContentCensor client;
    private String failReason;

    /**
     * 初始化百度内容审核客户端
     */
    @PostConstruct
    public void init() {
        try {
            // 使用默认值，确保即使配置未加载也能初始化
            String appId = "your_app_id";
            String apiKey = "your_api_key";
            String secretKey = "your_secret_key";

            try {
                // 从数据库配置服务获取参数
                appId = configService.getConfigValue("content-moderation.baidu.app-id", appId);
                apiKey = configService.getConfigValue("content-moderation.baidu.api-key", apiKey);
                secretKey = configService.getConfigValue("content-moderation.baidu.secret-key", secretKey);

                log.info("从数据库获取百度内容审核配置成功: appId={}, apiKey={}, secretKey={}",
                    appId,
                    apiKey.substring(0, Math.min(3, apiKey.length())) + "****",
                    secretKey.substring(0, Math.min(3, secretKey.length())) + "****");
            } catch (Exception e) {
                log.warn("从配置服务获取百度内容审核参数失败，将使用默认值: {}", e.getMessage());
            }

            // 初始化百度内容审核客户端
            client = new AipContentCensor(appId, apiKey, secretKey);

            // 设置网络连接参数
            client.setConnectionTimeoutInMillis(2000);
            client.setSocketTimeoutInMillis(60000);

            // 从数据库获取连接超时配置
            try {
                int connectionTimeout = configService.getIntValue("content-moderation.connection-timeout", 2000);
                int socketTimeout = configService.getIntValue("content-moderation.socket-timeout", 60000);

                client.setConnectionTimeoutInMillis(connectionTimeout);
                client.setSocketTimeoutInMillis(socketTimeout);

                log.info("从数据库获取连接超时配置成功: connectionTimeout={}, socketTimeout={}",
                    connectionTimeout, socketTimeout);
            } catch (Exception e) {
                log.warn("从配置服务获取连接超时配置失败，将使用默认值: {}", e.getMessage());
            }

            log.info("百度图像审核服务初始化成功");
        } catch (Exception e) {
            log.error("百度图像审核服务初始化失败: {}", e.getMessage(), e);
        }
    }

    // 移除不再需要的方法

    @Override
    public boolean moderateImage(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            failReason = "文件为空";
            return false;
        }

        try {
            return moderateImage(file.getInputStream(), file.getContentType());
        } catch (IOException e) {
            log.error("读取图片文件失败", e);
            failReason = "读取图片文件失败";
            return false;
        }
    }

    @Override
    public boolean moderateImage(InputStream inputStream, String contentType) {
        if (inputStream == null) {
            failReason = "图片输入流为空";
            return false;
        }

        if (contentType == null || !contentType.startsWith("image/")) {
            failReason = "不支持的文件类型";
            return false;
        }

        if (client == null) {
            log.error("百度内容审核客户端未初始化");
            failReason = "内容审核服务未初始化";
            return false;
        }

        try {
            // 读取图片数据
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[1024];
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            byte[] imageBytes = buffer.toByteArray();

            // 调用百度内容审核服务进行图片审核
            return callBaiduImageModeration(imageBytes);
        } catch (IOException e) {
            log.error("读取图片数据失败", e);
            failReason = "读取图片数据失败";
            return false;
        } catch (Exception e) {
            log.error("调用百度内容审核服务失败", e);
            failReason = "调用内容审核服务失败";
            return false;
        }
    }

    @Override
    public boolean moderateImageByUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            failReason = "图片URL为空";
            return false;
        }

        if (client == null) {
            log.error("百度内容审核客户端未初始化");
            failReason = "内容审核服务未初始化";
            return false;
        }

        try {
            // 调用百度内容审核服务进行图片审核
            return callBaiduImageModerationByUrl(imageUrl);
        } catch (Exception e) {
            log.error("调用百度内容审核服务失败", e);
            failReason = "调用内容审核服务失败";
            return false;
        }
    }

    /**
     * 审核文本内容
     * 注意：此方法不是接口方法，仅供内部使用
     *
     * @param text 文本内容
     * @return 审核结果，true表示通过，false表示不通过
     */
    public boolean moderateText(String text) {
        if (text == null || text.isEmpty()) {
            failReason = "文本内容为空";
            return false;
        }

        if (client == null) {
            log.error("百度内容审核客户端未初始化");
            failReason = "内容审核服务未初始化";
            return false;
        }

        try {
            // 调用百度内容审核服务进行文本审核
            return callBaiduTextModeration(text);
        } catch (Exception e) {
            log.error("调用百度内容审核服务失败", e);
            failReason = "调用内容审核服务失败";
            return false;
        }
    }

    @Override
    public String getFailReason() {
        return failReason;
    }

    /**
     * 调用百度内容审核服务进行图片审核
     *
     * @param imageBytes 图片字节数组
     * @return 审核结果，true表示通过，false表示不通过
     */
    private boolean callBaiduImageModeration(byte[] imageBytes) {
        // 设置可选参数
        HashMap<String, Object> options = new HashMap<>();
        // 是否需要返回高清截图，默认不返回
        options.put("sceneconf", "{\"recog_scene\":\"1,2,3,4,5,6,7\"}");

        // 调用百度内容审核API
        JSONObject response = client.imageCensorUserDefined(imageBytes, options);
        log.info("百度图片审核结果: {}", response.toString());

        // 解析审核结果
        return parseImageModerationResult(response);
    }

    /**
     * 调用百度内容审核服务进行图片审核
     *
     * @param imageUrl 图片URL
     * @return 审核结果，true表示通过，false表示不通过
     */
    private boolean callBaiduImageModerationByUrl(String imageUrl) {
        // 设置可选参数
        HashMap<String, Object> options = new HashMap<>();
        // 是否需要返回高清截图，默认不返回
        options.put("sceneconf", "{\"recog_scene\":\"1,2,3,4,5,6,7\"}");

        // 调用百度内容审核API
        // 使用URL方法而不是字节数组方法
        JSONObject response = client.imageCensorUserDefined(imageUrl, null, options);
        log.info("百度图片审核结果: {}", response.toString());

        // 解析审核结果
        return parseImageModerationResult(response);
    }

    /**
     * 调用百度内容审核服务进行文本审核
     *
     * @param text 文本内容
     * @return 审核结果，true表示通过，false表示不通过
     */
    private boolean callBaiduTextModeration(String text) {
        // 调用百度内容审核API
        JSONObject response = client.textCensorUserDefined(text);
        log.info("百度文本审核结果: {}", response.toString());

        // 解析审核结果
        return parseTextModerationResult(response);
    }

    /**
     * 解析图片审核结果
     *
     * @param response 百度内容审核API返回的结果
     * @return 审核结果，true表示通过，false表示不通过
     */
    private boolean parseImageModerationResult(JSONObject response) {
        try {
            // 检查是否有错误
            if (response.has("error_code") && response.getInt("error_code") != 0) {
                failReason = "审核服务错误: " + response.getString("error_msg");
                return false;
            }

            // 获取审核结果
            int conclusionType = response.getInt("conclusionType");
            String conclusion = response.getString("conclusion");

            // 1表示审核通过，2表示不确定，3表示审核不通过
            if (conclusionType == 1) {
                return true;
            } else {
                // 获取不通过的原因
                StringBuilder reason = new StringBuilder();
                reason.append(conclusion).append(": ");

                if (response.has("data") && response.getJSONArray("data").length() > 0) {
                    for (int i = 0; i < response.getJSONArray("data").length(); i++) {
                        JSONObject data = response.getJSONArray("data").getJSONObject(i);
                        reason.append(data.getString("msg")).append("; ");
                    }
                }

                failReason = reason.toString();
                return false;
            }
        } catch (Exception e) {
            log.error("解析百度图片审核结果失败", e);
            failReason = "解析审核结果失败";
            return false;
        }
    }

    /**
     * 解析文本审核结果
     *
     * @param response 百度内容审核API返回的结果
     * @return 审核结果，true表示通过，false表示不通过
     */
    private boolean parseTextModerationResult(JSONObject response) {
        try {
            // 检查是否有错误
            if (response.has("error_code") && response.getInt("error_code") != 0) {
                failReason = "审核服务错误: " + response.getString("error_msg");
                return false;
            }

            // 获取审核结果
            int conclusionType = response.getInt("conclusionType");
            String conclusion = response.getString("conclusion");

            // 1表示审核通过，2表示不确定，3表示审核不通过
            if (conclusionType == 1) {
                return true;
            } else {
                // 获取不通过的原因
                StringBuilder reason = new StringBuilder();
                reason.append(conclusion).append(": ");

                if (response.has("data") && response.getJSONArray("data").length() > 0) {
                    for (int i = 0; i < response.getJSONArray("data").length(); i++) {
                        JSONObject data = response.getJSONArray("data").getJSONObject(i);
                        reason.append(data.getString("msg")).append("; ");
                    }
                }

                failReason = reason.toString();
                return false;
            }
        } catch (Exception e) {
            log.error("解析百度文本审核结果失败", e);
            failReason = "解析审核结果失败";
            return false;
        }
    }
}
