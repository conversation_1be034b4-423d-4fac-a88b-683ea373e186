package com.phototagmoment.service;

import java.io.InputStream;

/**
 * 存储服务接口
 */
public interface StorageService {

    /**
     * 上传文件
     *
     * @param inputStream 文件输入流
     * @param path 存储路径
     * @param contentType 文件类型
     * @return 文件访问URL
     */
    String uploadFile(InputStream inputStream, String path, String contentType);

    /**
     * 上传文件
     *
     * @param bytes 文件字节数组
     * @param path 存储路径
     * @param contentType 文件类型
     * @return 文件访问URL
     */
    String uploadFile(byte[] bytes, String path, String contentType);

    /**
     * 删除文件
     *
     * @param path 存储路径
     * @return 是否成功
     */
    boolean deleteFile(String path);

    /**
     * 获取文件访问URL
     *
     * @param path 存储路径
     * @return 文件访问URL
     */
    String getFileUrl(String path);
}
