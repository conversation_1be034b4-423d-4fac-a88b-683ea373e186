package com.phototagmoment.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * JWT访问拒绝处理器
 * 当用户尝试访问需要特定权限的资源而没有足够权限时，将调用此类
 */
@Slf4j
@Component
public class JwtAccessDeniedHandler implements AccessDeniedHandler {

    private final ObjectMapper objectMapper;

    public JwtAccessDeniedHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException)
            throws IOException {
        String requestURI = request.getRequestURI();

        // 检查是否是白名单请求
        if (requestURI.contains("/login") ||
            requestURI.contains("/auth/") ||
            requestURI.contains("/swagger") ||
            requestURI.contains("/v3/api-docs") ||
            requestURI.contains("/doc.html") ||
            requestURI.contains("/webjars") ||
            requestURI.contains("/search") ||
            requestURI.contains("/recommendation") ||
            requestURI.contains("/photo/list") ||
            requestURI.contains("/photo/detail") ||
            requestURI.contains("/tag") ||
            requestURI.contains("/user/profile") ||
            requestURI.contains("/user/photos") ||
            requestURI.contains("/user/collections") ||
            requestURI.contains("/dict") ||
            requestURI.contains("/notification")) {
            log.info("白名单请求，不处理访问拒绝异常: {}", requestURI);
            return;
        }

        log.error("Access denied error: {}", accessDeniedException.getMessage());

        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        ApiResponse<Void> apiResponse = ApiResponse.failed(ResultCode.FORBIDDEN);

        response.getWriter().write(objectMapper.writeValueAsString(apiResponse));
    }
}
