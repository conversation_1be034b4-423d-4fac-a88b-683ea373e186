package com.phototagmoment.service;

import com.phototagmoment.dto.WechatLoginDTO;
import com.phototagmoment.dto.WechatUserInfoDTO;

/**
 * 微信服务接口
 */
public interface WechatService {

    /**
     * 获取微信公众号授权URL
     *
     * @param redirectUrl 回调URL
     * @param state       状态参数
     * @return 授权URL
     */
    String getMpAuthUrl(String redirectUrl, String state);

    /**
     * 微信公众号登录
     *
     * @param code 授权码
     * @return 登录结果
     */
    WechatLoginDTO mpLogin(String code);

    /**
     * 获取微信公众号用户信息
     *
     * @param openId 用户openId
     * @return 用户信息
     */
    WechatUserInfoDTO getMpUserInfo(String openId);

    /**
     * 微信小程序登录
     *
     * @param code 授权码
     * @return 登录结果
     */
    WechatLoginDTO miniAppLogin(String code);

    /**
     * 获取微信小程序用户信息
     *
     * @param sessionKey    会话密钥
     * @param encryptedData 加密数据
     * @param iv            加密算法的初始向量
     * @return 用户信息
     */
    WechatUserInfoDTO getMiniAppUserInfo(String sessionKey, String encryptedData, String iv);

    /**
     * 验证微信小程序用户信息
     *
     * @param sessionKey    会话密钥
     * @param rawData       原始数据
     * @param signature     签名
     * @return 是否验证成功
     */
    boolean verifyMiniAppUserInfo(String sessionKey, String rawData, String signature);
}
