package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.phototagmoment.annotation.Encrypted;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@TableName("ptm_user")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 邮箱
     */
    @Encrypted
    private String email;

    /**
     * 手机号
     */
    @Encrypted
    private String phone;

    /**
     * 性别：0未知，1男，2女
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 状态：0禁用，1正常
     */
    private Integer status;

    /**
     * 是否实名认证：0否，1是
     */
    private Integer isVerified;

    /**
     * 是否管理员：0否，1是
     */
    private Integer isAdmin;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 照片数
     */
    private Integer photoCount;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 注册IP
     */
    private String registerIp;

    /**
     * 注册来源
     */
    private String registerSource;

    /**
     * 微信OpenID
     */
    private String wechatOpenId;

    /**
     * 微信UnionID
     */
    private String wechatUnionId;

    /**
     * 微信昵称
     */
    private String wechatNickname;

    /**
     * 真实姓名
     */
    @Encrypted
    private String realName;

    /**
     * 身份证号
     */
    @Encrypted
    private String idCard;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDeleted;
}
