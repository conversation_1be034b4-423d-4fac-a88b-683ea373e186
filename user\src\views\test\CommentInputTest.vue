<template>
  <div class="comment-input-test">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="评论输入功能测试"
      left-arrow
      @click-left="$router.back()"
      class="test-navbar"
    />

    <!-- 测试说明 -->
    <div class="test-info">
      <van-cell-group title="评论输入功能测试">
        <van-cell title="测试目标" value="验证评论输入弹窗的显示和功能" />
        <van-cell title="测试内容" value="点击按钮打开评论输入框，输入内容并提交" />
        <van-cell title="期望效果" value="评论输入框正常显示，支持#标签#和@用户提及" />
      </van-cell-group>
    </div>

    <!-- 状态显示 -->
    <div class="status-section">
      <van-cell-group title="当前状态">
        <van-cell title="登录状态" :value="isLoggedIn ? '已登录' : '未登录'" />
        <van-cell title="用户信息" :value="userStore.user?.nickname || '无'" />
        <van-cell title="评论输入框状态" :value="showCommentInput ? '显示' : '隐藏'" />
        <van-cell title="评论文本" :value="commentText || '空'" />
      </van-cell-group>
    </div>

    <!-- 测试按钮 -->
    <div class="test-buttons">
      <van-button type="primary" @click="showCommentBox" block>
        打开评论输入框
      </van-button>
      
      <van-button type="success" @click="forceShowInput" block style="margin-top: 12px;">
        强制显示评论输入框
      </van-button>
      
      <van-button type="warning" @click="toggleLogin" block style="margin-top: 12px;">
        {{ isLoggedIn ? '模拟登出' : '模拟登录' }}
      </van-button>
      
      <van-button type="default" @click="clearAll" block style="margin-top: 12px;">
        清空所有状态
      </van-button>
    </div>

    <!-- 评论输入框 -->
    <van-popup
      v-model="showCommentInput"
      position="bottom"
      :style="{ height: '50%', minHeight: '300px' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="comment-input-container">
        <div class="comment-input-header">
          <span>写评论</span>
          <van-icon name="cross" @click="closeCommentInput" />
        </div>
        
        <div class="comment-input-body">
          <van-field
            v-model="commentText"
            type="textarea"
            placeholder="说点什么...支持#标签#和@用户提及"
            rows="4"
            autosize
            maxlength="500"
            show-word-limit
            @input="onCommentInput"
          />
          <!-- 实时预览评论内容 -->
          <div v-if="commentText.trim()" class="comment-preview">
            <div class="preview-label">预览：</div>
            <div class="preview-content" v-html="processCommentContent(commentText)" @click="handleContentClick"></div>
          </div>
        </div>
        
        <div class="comment-input-footer">
          <div class="comment-input-tips">
            <span class="tip-item">支持 #标签# 和 @用户名</span>
          </div>
          <van-button
            type="primary"
            @click="submitComment"
            :disabled="!commentText.trim()"
            block
          >
            发布评论
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 测试结果显示 -->
    <div class="test-results">
      <van-cell-group title="测试结果">
        <van-cell title="最后提取的标签" :value="lastExtractedTags.join(', ') || '无'" />
        <van-cell title="最后提取的用户" :value="lastExtractedMentions.join(', ') || '无'" />
        <van-cell title="提交次数" :value="submitCount.toString()" />
      </van-cell-group>
    </div>

    <!-- 操作日志 -->
    <div class="log-section">
      <van-cell-group title="操作日志">
        <div class="log-content">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const showCommentInput = ref(false)
const commentText = ref('')
const lastExtractedTags = ref([])
const lastExtractedMentions = ref([])
const submitCount = ref(0)
const logs = ref([])

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 添加日志
const addLog = (message) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  logs.value.unshift({ time, message })
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
  console.log(`[${time}] ${message}`)
}

// 处理评论内容的标签和@用户高亮显示
const processCommentContent = (content) => {
  if (!content) return ''

  let processedContent = content

  // 处理标签 #标签名称# 格式，显示为蓝色可点击
  processedContent = processedContent.replace(/#([^#]+)#/g, '<span class="tag-highlight" data-tag="$1">#$1#</span>')

  // 处理@用户提及 @用户昵称 格式，显示为橙色可点击
  processedContent = processedContent.replace(/@([^\s@]+)/g, '<span class="mention-highlight" data-mention="$1">@$1</span>')

  return processedContent
}

// 评论输入处理
const onCommentInput = (value) => {
  addLog(`评论输入: ${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`)
}

// 提取评论中的标签和@用户提及
const extractCommentData = (content) => {
  const tags = []
  const mentions = []

  // 提取标签
  const tagMatches = content.match(/#([^#]+)#/g)
  if (tagMatches) {
    tagMatches.forEach(match => {
      const tagName = match.slice(1, -1) // 去掉前后的#号
      if (tagName && !tags.includes(tagName)) {
        tags.push(tagName)
      }
    })
  }

  // 提取@用户提及
  const mentionMatches = content.match(/@([^\s@]+)/g)
  if (mentionMatches) {
    mentionMatches.forEach(match => {
      const username = match.slice(1) // 去掉@号
      if (username && !mentions.includes(username)) {
        mentions.push(username)
      }
    })
  }

  return { tags, mentions }
}

// 显示评论输入框
const showCommentBox = () => {
  addLog('点击打开评论输入框')
  
  if (!isLoggedIn.value) {
    addLog('用户未登录，显示登录提示')
    showToast('请先登录')
    return
  }
  
  addLog('准备显示评论输入框')
  showCommentInput.value = true
  addLog(`评论输入框状态设置为: ${showCommentInput.value}`)
}

// 强制显示评论输入框
const forceShowInput = () => {
  addLog('强制显示评论输入框')
  showCommentInput.value = true
  showToast('强制显示评论输入框')
}

// 关闭评论输入框
const closeCommentInput = () => {
  addLog('关闭评论输入框')
  showCommentInput.value = false
  commentText.value = ''
}

// 提交评论
const submitComment = () => {
  addLog('提交评论')
  
  if (!commentText.value.trim()) {
    addLog('评论内容为空')
    showToast('请输入评论内容')
    return
  }

  // 提取评论中的标签和@用户提及
  const { tags, mentions } = extractCommentData(commentText.value.trim())
  lastExtractedTags.value = tags
  lastExtractedMentions.value = mentions
  
  addLog(`提取标签: ${tags.join(', ') || '无'}`)
  addLog(`提取用户提及: ${mentions.join(', ') || '无'}`)
  
  submitCount.value++
  addLog(`评论提交成功，第${submitCount.value}次提交`)
  
  showToast('评论提交成功（测试模式）')
  closeCommentInput()
}

// 模拟登录/登出
const toggleLogin = () => {
  if (isLoggedIn.value) {
    // 模拟登出
    userStore.logout()
    addLog('模拟用户登出')
    showToast('已登出')
  } else {
    // 模拟登录
    userStore.setUser({
      id: 1,
      username: 'testuser',
      nickname: '测试用户',
      avatar: 'https://via.placeholder.com/40x40/4CAF50/FFFFFF?text=T'
    })
    addLog('模拟用户登录')
    showToast('已登录')
  }
}

// 清空所有状态
const clearAll = () => {
  showCommentInput.value = false
  commentText.value = ''
  lastExtractedTags.value = []
  lastExtractedMentions.value = []
  submitCount.value = 0
  logs.value = []
  addLog('清空所有状态')
  showToast('已清空所有状态')
}

// 内容点击处理函数
const handleContentClick = (event) => {
  const target = event.target
  
  // 处理标签点击
  if (target.classList.contains('tag-highlight')) {
    const tagName = target.getAttribute('data-tag')
    addLog(`点击标签: ${tagName}`)
    showToast(`点击了标签: ${tagName}`)
  }

  // 处理用户提及点击
  if (target.classList.contains('mention-highlight')) {
    const username = target.getAttribute('data-mention')
    addLog(`点击用户提及: ${username}`)
    showToast(`点击了用户: ${username}`)
  }
}

onMounted(() => {
  addLog('评论输入功能测试页面已加载')
})
</script>

<style scoped>
.comment-input-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.test-info,
.status-section,
.test-results,
.log-section {
  margin: 16px;
}

.test-buttons {
  margin: 16px;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 12px;
}

.log-time {
  color: #999;
  margin-right: 8px;
  min-width: 60px;
}

.log-message {
  color: #333;
  flex: 1;
}

/* 评论输入框样式 */
.comment-input-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comment-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: 600;
}

.comment-input-body {
  flex: 1;
  padding: 16px;
}

.comment-input-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 评论预览样式 */
.comment-preview {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #1989fa;
}

.preview-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.preview-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

/* 评论输入提示 */
.comment-input-tips {
  margin-bottom: 12px;
  text-align: center;
}

.tip-item {
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

/* 标签和用户提及高亮样式 */
.preview-content :deep(.tag-highlight) {
  color: #1989fa;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.preview-content :deep(.tag-highlight:hover) {
  color: #0570d1;
  text-decoration: underline;
}

.preview-content :deep(.mention-highlight) {
  color: #ff8c00;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.preview-content :deep(.mention-highlight:hover) {
  color: #e67700;
  text-decoration: underline;
}
</style>
