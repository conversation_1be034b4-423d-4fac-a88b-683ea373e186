package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.LoginDTO;
import com.phototagmoment.dto.RegisterDTO;
import com.phototagmoment.service.UserService;
import com.phototagmoment.vo.TokenVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Tag(name = "认证接口", description = "包括登录、注册、登出等接口")
public class AuthController {

    private final UserService userService;

    public AuthController(UserService userService) {
        this.userService = userService;
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "注册新用户")
    public ApiResponse<Long> register(@Validated @RequestBody RegisterDTO registerDTO) {
        Long userId = userService.register(registerDTO);
        return ApiResponse.success(userId, "注册成功");
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录并返回token")
    public ApiResponse<TokenVO> login(@Validated @RequestBody LoginDTO loginDTO) {
        try {
            log.info("用户登录请求: {}", loginDTO.getUsername());
            log.info("登录请求数据: {}", loginDTO);

            // 直接调用userService.login方法，不使用认证管理器
            // 避免使用Spring Security的认证机制，防止循环依赖
            TokenVO tokenVO = userService.login(loginDTO);

            if (tokenVO != null) {
                log.info("用户登录成功: {}", loginDTO.getUsername());
                log.info("生成的token: {}", tokenVO.getToken());
                return ApiResponse.success(tokenVO, "登录成功");
            } else {
                log.error("登录失败: 无法生成令牌");
                return ApiResponse.failed("登录失败: 无法生成令牌");
            }
        } catch (Exception e) {
            log.error("登录失败: {}", e.getMessage(), e);
            // 返回更友好的错误消息
            String errorMessage = "登录失败";
            if (e.getMessage() != null && !e.getMessage().isEmpty()) {
                if (e.getMessage().contains("Bad credentials")) {
                    errorMessage = "用户名或密码错误";
                } else if (e.getMessage().contains("User is disabled")) {
                    errorMessage = "用户已被禁用";
                } else if (e.getMessage().contains("User account has expired")) {
                    errorMessage = "用户账号已过期";
                } else if (e.getMessage().contains("User credentials have expired")) {
                    errorMessage = "用户凭证已过期";
                } else if (e.getMessage().contains("User account is locked")) {
                    errorMessage = "用户账号已锁定";
                } else if (e.getMessage().contains("StackOverflowError")) {
                    errorMessage = "系统错误: 循环依赖";
                } else {
                    errorMessage = "登录失败: " + e.getMessage();
                }
            }
            return ApiResponse.failed(errorMessage);
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出")
    public ApiResponse<Void> logout() {
        userService.logout();
        return ApiResponse.success(null, "登出成功");
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名是否存在", description = "检查用户名是否已被注册")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.checkUsernameExists(username);
        return ApiResponse.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱是否存在", description = "检查邮箱是否已被注册")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = userService.checkEmailExists(email);
        return ApiResponse.success(exists);
    }

    /**
     * 检查手机号是否存在
     */
    @GetMapping("/check-phone")
    @Operation(summary = "检查手机号是否存在", description = "检查手机号是否已被注册")
    public ApiResponse<Boolean> checkPhone(@RequestParam String phone) {
        boolean exists = userService.checkPhoneExists(phone);
        return ApiResponse.success(exists);
    }
}
