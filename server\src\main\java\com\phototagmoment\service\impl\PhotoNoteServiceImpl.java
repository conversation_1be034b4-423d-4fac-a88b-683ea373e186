package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.PhotoNotePublishDTO;
import com.phototagmoment.dto.TagSearchResultDTO;
import com.phototagmoment.entity.*;
import com.phototagmoment.controller.admin.AdminPhotoNoteController.PhotoNoteStatsDTO;
import com.phototagmoment.mapper.*;
import com.phototagmoment.service.PhotoNoteService;
import com.phototagmoment.service.NotificationService;
import com.phototagmoment.service.UserService;
import com.phototagmoment.service.SystemConfigService;
import com.phototagmoment.service.QiniuStorageService;
import com.phototagmoment.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 照片笔记服务实现类
 */
@Slf4j
@Service
public class PhotoNoteServiceImpl extends ServiceImpl<PhotoNoteMapper, PhotoNote> implements PhotoNoteService {

    @Autowired
    private PhotoNoteMapper photoNoteMapper;

    @Autowired
    private PhotoNoteImageMapper photoNoteImageMapper;

    @Autowired
    private PhotoNoteTagMapper photoNoteTagMapper;

    @Autowired
    private PhotoNoteMentionMapper photoNoteMentionMapper;

    @Autowired
    private TagStatsMapper tagStatsMapper;

    @Autowired
    private PhotoNoteLikeMapper photoNoteLikeMapper;

    @Autowired
    private PhotoNoteCollectionMapper photoNoteCollectionMapper;

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private UserFollowMapper userFollowMapper;

    @Autowired
    private PhotoNoteReportMapper photoNoteReportMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private NotificationService notificationService;

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired(required = false)
    private QiniuStorageService qiniuStorageService;

    // Tag正则表达式：#标签名称#
    private static final Pattern TAG_PATTERN = Pattern.compile("#([\\u4e00-\\u9fa5a-zA-Z0-9_]{2,20})#");

    // @用户正则表达式：@用户昵称
    private static final Pattern MENTION_PATTERN = Pattern.compile("@([\\u4e00-\\u9fa5a-zA-Z0-9_]{2,20})");

    // 浏览量去重缓存前缀
    private static final String VIEW_COUNT_CACHE_PREFIX = "photo_note_view:";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long publishPhotoNote(PhotoNotePublishDTO publishDTO, Long userId) {
        log.info("用户{}发布照片笔记，标题：{}", userId, publishDTO.getTitle());

        // 1. 创建照片笔记记录
        PhotoNote photoNote = new PhotoNote();
        BeanUtils.copyProperties(publishDTO, photoNote);
        photoNote.setUserId(userId);
        photoNote.setPhotoCount(publishDTO.getPhotos().size());
        photoNote.setStatus(0); // 待审核状态
        photoNote.setCreatedAt(LocalDateTime.now());
        photoNote.setUpdatedAt(LocalDateTime.now());

        // 保存照片笔记
        save(photoNote);
        Long noteId = photoNote.getId();

        // 2. 保存照片信息
        savePhotoNoteImages(noteId, publishDTO.getPhotos());

        // 3. 处理标签
        List<String> tags = extractTags(publishDTO.getContent());
        if (!CollectionUtils.isEmpty(tags)) {
            savePhotoNoteTags(noteId, tags);
            // 更新标签统计
            updateTagStats(tags);
        }

        // 4. 处理@用户
        List<String> mentionNicknames = extractMentions(publishDTO.getContent());
        if (!CollectionUtils.isEmpty(mentionNicknames)) {
            List<Long> mentionedUserIds = savePhotoNoteMentions(noteId, userId, mentionNicknames);
            // 发送@通知
            sendMentionNotifications(noteId, userId, mentionedUserIds, publishDTO.getTitle());
        }

        log.info("照片笔记发布成功，ID：{}", noteId);
        return noteId;
    }

    @Override
    public IPage<PhotoNoteDTO> getPhotoNoteList(Integer page, Integer size, Long userId, Long currentUserId, Integer status) {
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectPhotoNotePage(pageParam, userId, currentUserId, status);
    }

    @Override
    public IPage<PhotoNoteDTO> getAdminPhotoNoteList(Integer page, Integer size, Long userId, Integer status) {
        log.info("管理端查询照片笔记列表，页码：{}，大小：{}，用户ID：{}，状态：{}", page, size, userId, status);
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectAdminPhotoNotePage(pageParam, userId, status);
    }

    @Override
    public PhotoNoteDTO getPhotoNoteDetail(Long noteId, Long currentUserId) {
        PhotoNoteDTO photoNote = photoNoteMapper.selectPhotoNoteDetail(noteId, currentUserId);
        if (photoNote == null) {
            return null;
        }

        // 查询照片列表
        List<PhotoNoteImage> images = photoNoteImageMapper.selectImagesByNoteId(noteId);
        if (!CollectionUtils.isEmpty(images)) {
            List<PhotoNoteDTO.PhotoNoteImageDTO> imageDTOs = images.stream()
                    .map(this::convertToImageDTO)
                    .collect(Collectors.toList());
            photoNote.setImages(imageDTOs);
        }

        // 查询标签列表
        List<String> tags = photoNoteTagMapper.selectTagsByNoteId(noteId);
        photoNote.setTags(tags);

        // 查询@用户列表
        List<PhotoNoteDTO.PhotoNoteMentionDTO> mentions = photoNoteMentionMapper.selectMentionsByNoteId(noteId);
        photoNote.setMentions(mentions);

        // 处理正文内容，添加Tag和@用户的HTML标记
        photoNote.setProcessedContent(processContent(photoNote.getContent()));

        return photoNote;
    }

    @Override
    public TagSearchResultDTO searchPhotoNotesByTag(String tagName, Integer page, Integer size, String sortType, Long currentUserId) {
        TagSearchResultDTO result = new TagSearchResultDTO();
        result.setTagName(tagName);

        // 查询标签统计信息
        TagStats tagStats = tagStatsMapper.selectByTagName(tagName);
        if (tagStats != null) {
            result.setHotScore(tagStats.getHotScore());
        }

        // 查询标签下的照片笔记总数
        Long totalCount = tagStatsMapper.countNotesByTag(tagName);
        result.setTotalCount(totalCount);

        // 分页查询照片笔记
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        IPage<PhotoNoteDTO> notePage = photoNoteMapper.selectPhotoNotesByTag(pageParam, tagName, currentUserId, sortType);
        result.setNotes(notePage.getRecords());

        // 查询相关标签
        List<TagSearchResultDTO.RelatedTagDTO> relatedTags = tagStatsMapper.selectRelatedTags(tagName, 10);
        result.setRelatedTags(relatedTags);

        return result;
    }

    @Override
    public IPage<PhotoNoteDTO> getUserPhotoNotes(Long userId, Integer page, Integer size, Long currentUserId, Integer visibility) {
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectUserPhotoNotes(pageParam, userId, currentUserId, visibility);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean likePhotoNote(Long noteId, Long userId) {
        // 检查是否已点赞
        if (photoNoteMapper.checkUserLiked(noteId, userId)) {
            return false;
        }

        // 添加点赞记录
        PhotoNoteLike like = new PhotoNoteLike();
        like.setNoteId(noteId);
        like.setUserId(userId);
        like.setCreatedAt(LocalDateTime.now());

        photoNoteLikeMapper.insert(like);

        // 增加点赞数
        photoNoteMapper.incrementLikeCount(noteId);

        // 更新相关标签的热度分数
        updateTagHotScoreForNote(noteId, 0, 1);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlikePhotoNote(Long noteId, Long userId) {
        // 检查是否已点赞
        if (!photoNoteMapper.checkUserLiked(noteId, userId)) {
            return false;
        }

        // 删除点赞记录
        photoNoteLikeMapper.delete(new LambdaQueryWrapper<PhotoNoteLike>()
                .eq(PhotoNoteLike::getNoteId, noteId)
                .eq(PhotoNoteLike::getUserId, userId));

        // 减少点赞数
        photoNoteMapper.decrementLikeCount(noteId);

        // 更新相关标签的热度分数
        updateTagHotScoreForNote(noteId, 0, -1);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean collectPhotoNote(Long noteId, Long userId) {
        // 检查是否已收藏
        if (photoNoteMapper.checkUserCollected(noteId, userId)) {
            return false;
        }

        // 添加收藏记录
        PhotoNoteCollection collection = new PhotoNoteCollection();
        collection.setNoteId(noteId);
        collection.setUserId(userId);
        collection.setCreatedAt(LocalDateTime.now());

        photoNoteCollectionMapper.insert(collection);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean uncollectPhotoNote(Long noteId, Long userId) {
        // 检查是否已收藏
        if (!photoNoteMapper.checkUserCollected(noteId, userId)) {
            return false;
        }

        // 删除收藏记录
        photoNoteCollectionMapper.delete(new LambdaQueryWrapper<PhotoNoteCollection>()
                .eq(PhotoNoteCollection::getNoteId, noteId)
                .eq(PhotoNoteCollection::getUserId, userId));

        return true;
    }

    @Override
    public boolean incrementViewCount(Long noteId, Long userId) {
        // 使用Redis去重，同一用户24小时内只计算一次浏览量
        String cacheKey = VIEW_COUNT_CACHE_PREFIX + noteId + ":" + userId;
        boolean hasViewed = false;

        if (redisTemplate != null) {
            try {
                Boolean redisResult = redisTemplate.hasKey(cacheKey);
                hasViewed = Boolean.TRUE.equals(redisResult);
            } catch (Exception e) {
                log.warn("Redis操作失败，无法检查浏览记录缓存: {}", e.getMessage());
                // Redis失败时，直接增加浏览量（可能会有重复计算，但不影响主要功能）
                hasViewed = false;
            }
        } else {
            log.warn("Redis不可用，无法进行浏览去重");
        }

        if (!hasViewed) {
            // 增加浏览量
            int result = photoNoteMapper.incrementViewCount(noteId);

            // 设置缓存，24小时过期
            if (redisTemplate != null) {
                try {
                    redisTemplate.opsForValue().set(cacheKey, "1", 24, TimeUnit.HOURS);
                } catch (Exception e) {
                    log.warn("Redis操作失败，无法设置浏览记录缓存: {}", e.getMessage());
                }
            }

            // 更新相关标签的热度分数
            updateTagHotScoreForNote(noteId, 1, 0);

            return result > 0;
        }

        return false; // 已经浏览过，不重复计算
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePhotoNote(Long noteId, Long userId) {
        // 检查照片笔记是否存在且属于当前用户
        PhotoNote photoNote = getById(noteId);
        if (photoNote == null || !photoNote.getUserId().equals(userId)) {
            return false;
        }

        // 软删除照片笔记
        photoNote.setStatus(3); // 已删除状态
        photoNote.setUpdatedAt(LocalDateTime.now());
        updateById(photoNote);

        // 减少相关标签的使用次数
        List<String> tags = photoNoteTagMapper.selectTagsByNoteId(noteId);
        if (!CollectionUtils.isEmpty(tags)) {
            for (String tag : tags) {
                tagStatsMapper.decrementTagUsage(tag);
            }
        }

        return true;
    }

    @Override
    public IPage<PhotoNoteDTO> getHotPhotoNotes(Integer page, Integer size, Integer days, Long currentUserId) {
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectHotPhotoNotes(pageParam, days, currentUserId);
    }

    @Override
    public IPage<PhotoNoteDTO> getRecommendedPhotoNotes(Integer page, Integer size, Long userId) {
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectRecommendedPhotoNotes(pageParam, userId);
    }

    @Override
    public IPage<PhotoNoteDTO> searchPhotoNotes(String keyword, Integer page, Integer size, Long currentUserId) {
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.searchPhotoNotes(pageParam, keyword, currentUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditPhotoNote(Long noteId, Integer status, String rejectReason) {
        PhotoNote photoNote = getById(noteId);
        if (photoNote == null) {
            return false;
        }

        photoNote.setStatus(status);
        if (status == 2 && StringUtils.hasText(rejectReason)) {
            photoNote.setRejectReason(rejectReason);
        }
        photoNote.setUpdatedAt(LocalDateTime.now());

        return updateById(photoNote);
    }

    @Override
    public List<String> getHotTags(Integer limit) {
        return photoNoteTagMapper.selectHotTags(limit);
    }

    @Override
    public List<String> searchTags(String keyword, Integer limit) {
        return photoNoteTagMapper.searchTags(keyword, limit);
    }

    @Override
    public String processContent(String content) {
        if (!StringUtils.hasText(content)) {
            return content;
        }

        String processedContent = content;

        // 处理Tag：#标签名称# -> <span class="tag">#标签名称#</span>
        Matcher tagMatcher = TAG_PATTERN.matcher(processedContent);
        processedContent = tagMatcher.replaceAll("<span class=\"tag\" data-tag=\"$1\">#$1#</span>");

        // 处理@用户：@用户昵称 -> <span class="mention" data-user="用户昵称">@用户昵称</span>
        Matcher mentionMatcher = MENTION_PATTERN.matcher(processedContent);
        processedContent = mentionMatcher.replaceAll("<span class=\"mention\" data-user=\"$1\">@$1</span>");

        return processedContent;
    }

    @Override
    public List<String> extractTags(String content) {
        if (!StringUtils.hasText(content)) {
            return new ArrayList<>();
        }

        List<String> tags = new ArrayList<>();
        Matcher matcher = TAG_PATTERN.matcher(content);
        while (matcher.find()) {
            String tag = matcher.group(1);
            if (!tags.contains(tag) && tags.size() < 10) {
                tags.add(tag);
            }
        }
        return tags;
    }

    @Override
    public List<String> extractMentions(String content) {
        if (!StringUtils.hasText(content)) {
            return new ArrayList<>();
        }

        List<String> mentions = new ArrayList<>();
        Matcher matcher = MENTION_PATTERN.matcher(content);
        while (matcher.find()) {
            String mention = matcher.group(1);
            if (!mentions.contains(mention) && mentions.size() < 10) {
                mentions.add(mention);
            }
        }
        return mentions;
    }

    /**
     * 保存照片笔记图片关联
     */
    private void savePhotoNoteImages(Long noteId, List<PhotoNotePublishDTO.PhotoInfoDTO> photos) {
        // 获取当前用户ID
        Long currentUserId = UserUtil.getCurrentUserId();

        List<PhotoNoteImage> images = new ArrayList<>();
        for (int i = 0; i < photos.size(); i++) {
            PhotoNotePublishDTO.PhotoInfoDTO photoInfo = photos.get(i);

            // 先保存照片信息到photo表
            Photo photo = new Photo();
            photo.setUserId(currentUserId); // 设置照片的用户ID
            photo.setUrl(photoInfo.getUrl());
            photo.setThumbnailUrl(photoInfo.getThumbnailUrl());
            // 为 storage_path 提供默认值，如果没有提供的话
            String storagePath = photoInfo.getStoragePath();
            if (storagePath == null || storagePath.trim().isEmpty()) {
                // 使用 URL 作为默认的存储路径
                storagePath = photoInfo.getUrl();
            }
            photo.setStoragePath(storagePath);
            photo.setOriginalFilename(photoInfo.getOriginalFilename());
            photo.setFileSize(photoInfo.getFileSize());
            photo.setFileType(photoInfo.getFileType());
            photo.setWidth(photoInfo.getWidth());
            photo.setHeight(photoInfo.getHeight());
            photo.setStatus(1); // 正常状态
            photo.setCreatedAt(LocalDateTime.now());
            photoMapper.insert(photo);

            // 创建照片笔记图片关联
            PhotoNoteImage image = new PhotoNoteImage();
            image.setNoteId(noteId);
            image.setPhotoId(photo.getId());
            image.setSortOrder(photoInfo.getSortOrder() != null ? photoInfo.getSortOrder() : i + 1);
            image.setCreatedAt(LocalDateTime.now());
            images.add(image);
        }

        if (!CollectionUtils.isEmpty(images)) {
            photoNoteImageMapper.batchInsert(images);
        }
    }

    /**
     * 保存照片笔记标签
     */
    private void savePhotoNoteTags(Long noteId, List<String> tags) {
        List<PhotoNoteTag> photoNoteTags = tags.stream()
                .map(tag -> {
                    PhotoNoteTag photoNoteTag = new PhotoNoteTag();
                    photoNoteTag.setNoteId(noteId);
                    photoNoteTag.setTagName(tag);
                    photoNoteTag.setCreatedAt(LocalDateTime.now());
                    return photoNoteTag;
                })
                .collect(Collectors.toList());

        photoNoteTagMapper.batchInsert(photoNoteTags);
    }

    /**
     * 保存照片笔记@用户
     */
    private List<Long> savePhotoNoteMentions(Long noteId, Long mentionUserId, List<String> mentionNicknames) {
        List<Long> mentionedUserIds = new ArrayList<>();
        List<PhotoNoteMention> mentions = new ArrayList<>();

        for (String nickname : mentionNicknames) {
            // 根据昵称查找用户
            User user = userService.getUserByNickname(nickname);
            if (user != null && !user.getId().equals(mentionUserId)) {
                mentionedUserIds.add(user.getId());

                PhotoNoteMention mention = new PhotoNoteMention();
                mention.setNoteId(noteId);
                mention.setMentionedUserId(user.getId());
                mention.setMentionUserId(mentionUserId);
                mention.setCreatedAt(LocalDateTime.now());
                mentions.add(mention);
            }
        }

        if (!CollectionUtils.isEmpty(mentions)) {
            photoNoteMentionMapper.batchInsert(mentions);
        }

        return mentionedUserIds;
    }

    /**
     * 更新标签统计
     */
    private void updateTagStats(List<String> tags) {
        for (String tag : tags) {
            tagStatsMapper.incrementTagUsage(tag);
        }
    }

    /**
     * 发送@通知
     */
    private void sendMentionNotifications(Long noteId, Long mentionUserId, List<Long> mentionedUserIds, String noteTitle) {
        for (Long mentionedUserId : mentionedUserIds) {
            try {
                // 发送@通知
                notificationService.sendMentionNotification(mentionedUserId, mentionUserId, noteId, noteTitle);
            } catch (Exception e) {
                log.error("发送@通知失败，被@用户ID：{}，照片笔记ID：{}", mentionedUserId, noteId, e);
            }
        }
    }

    /**
     * 更新照片笔记相关标签的热度分数
     */
    private void updateTagHotScoreForNote(Long noteId, Integer viewIncrement, Integer likeIncrement) {
        List<String> tags = photoNoteTagMapper.selectTagsByNoteId(noteId);
        for (String tag : tags) {
            tagStatsMapper.updateTagHotScore(tag, viewIncrement, likeIncrement);
        }
    }

    /**
     * 转换图片实体为DTO
     */
    private PhotoNoteDTO.PhotoNoteImageDTO convertToImageDTO(PhotoNoteImage image) {
        PhotoNoteDTO.PhotoNoteImageDTO dto = new PhotoNoteDTO.PhotoNoteImageDTO();
        dto.setPhotoId(image.getPhotoId());
        dto.setSortOrder(image.getSortOrder());

        // 这里需要从Photo表查询图片信息
        Photo photo = photoMapper.selectById(image.getPhotoId());
        if (photo != null) {
            String url = photo.getUrl();
            String thumbnailUrl = photo.getThumbnailUrl();

            // 如果是七牛云私有空间，生成带下载凭证的URL
            try {
                boolean qiniuIsPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", false);
                if (qiniuIsPrivate && qiniuStorageService != null) {
                    // 处理照片URL
                    if (url != null && !url.isEmpty()) {
                        String storagePath = extractStoragePathFromUrl(url, photo.getStoragePath());
                        log.info("照片ID: {}, 原始URL: {}, 存储路径: {}, 提取的路径: {}",
                                photo.getId(), url, photo.getStoragePath(), storagePath);
                        String privateUrl = qiniuStorageService.getFileUrl(storagePath);
                        if (privateUrl != null && !privateUrl.isEmpty()) {
                            log.info("生成的私有URL: {}", privateUrl);
                            url = privateUrl;
                        }
                    }

                    // 处理缩略图URL
                    if (thumbnailUrl != null && !thumbnailUrl.isEmpty()) {
                        // 缩略图使用原图的存储路径，但需要去掉图片处理参数
                        String storagePath = extractStoragePathFromUrl(thumbnailUrl, photo.getStoragePath());
                        String privateUrl = qiniuStorageService.getFileUrl(storagePath);
                        if (privateUrl != null && !privateUrl.isEmpty()) {
                            // 重新添加缩略图处理参数
                            thumbnailUrl = privateUrl + "?imageView2/2/w/300/h/300/q/80";
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("处理私有空间URL失败: {}", e.getMessage());
                // 不影响业务，继续使用原URL
            }

            dto.setUrl(url);
            dto.setThumbnailUrl(thumbnailUrl);
            dto.setWidth(photo.getWidth());
            dto.setHeight(photo.getHeight());
        }

        return dto;
    }

    /**
     * 从URL中提取存储路径（优先使用数据库中的storage_path）
     */
    private String extractStoragePathFromUrl(String url, String storagePath) {
        // 优先使用数据库中存储的storage_path
        if (storagePath != null && !storagePath.isEmpty()) {
            log.debug("使用数据库存储路径: {}", storagePath);
            return storagePath;
        }

        // 如果storage_path为空，从URL中提取
        if (url == null || url.isEmpty()) {
            return null;
        }

        try {
            // 移除查询参数
            int queryIndex = url.indexOf('?');
            if (queryIndex != -1) {
                url = url.substring(0, queryIndex);
            }

            // 查找域名后的路径部分
            // 例如：http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/90682cffacf24fee9e4e1feff76f5dc0
            // 提取：phototagmoment/photos/1/2025/05/27/90682cffacf24fee9e4e1feff76f5dc0
            int domainEndIndex = url.indexOf('/', 8); // 跳过 "http://" 或 "https://"
            if (domainEndIndex != -1 && domainEndIndex < url.length() - 1) {
                String path = url.substring(domainEndIndex + 1);
                log.debug("从URL提取存储路径: {}", path);
                return path;
            }

            // 如果无法提取路径，返回文件名
            int lastSlashIndex = url.lastIndexOf('/');
            if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
                String fileName = url.substring(lastSlashIndex + 1);
                log.debug("提取文件名: {}", fileName);
                return fileName;
            }

            return url;
        } catch (Exception e) {
            log.warn("提取存储路径失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从URL中提取文件名（保留原方法以备其他地方使用）
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        try {
            // 移除查询参数
            int queryIndex = url.indexOf('?');
            if (queryIndex != -1) {
                url = url.substring(0, queryIndex);
            }

            // 提取文件名部分
            int lastSlashIndex = url.lastIndexOf('/');
            if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
                return url.substring(lastSlashIndex + 1);
            }

            return url;
        } catch (Exception e) {
            log.warn("提取文件名失败: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public IPage<PhotoNoteDTO> getFollowingPhotoNotes(Integer page, Integer size, Long currentUserId, Long lastId) {
        log.info("获取用户{}关注的照片笔记，页码：{}，大小：{}，lastId：{}", currentUserId, page, size, lastId);

        if (currentUserId == null) {
            log.warn("用户未登录，无法获取关注的照片笔记");
            return new Page<>(page, size);
        }

        // 获取用户关注的用户ID列表
        List<Long> followingIds = userFollowMapper.getFollowingIds(currentUserId);
        if (CollectionUtils.isEmpty(followingIds)) {
            log.info("用户{}没有关注任何人", currentUserId);
            return new Page<>(page, size);
        }

        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectFollowingPhotoNotes(pageParam, followingIds, currentUserId, lastId);
    }

    @Override
    public IPage<PhotoNoteDTO> getLatestPhotoNotes(Integer page, Integer size, Long currentUserId, Long lastId) {
        log.info("获取最新照片笔记，页码：{}，大小：{}，lastId：{}", page, size, lastId);

        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectLatestPhotoNotes(pageParam, currentUserId, lastId);
    }

    @Override
    public IPage<PhotoNoteDTO> getHotPhotoNotes(Integer page, Integer size, Long currentUserId, Long lastId) {
        log.info("获取热门照片笔记，页码：{}，大小：{}，lastId：{}", page, size, lastId);

        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        // 默认获取最近7天的热门内容
        return photoNoteMapper.selectHotPhotoNotes(pageParam, 7, currentUserId, lastId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reportPhotoNote(Long noteId, Long userId, String reason, String description) {
        log.info("用户{}举报照片笔记{}，原因：{}", userId, noteId, reason);

        try {
            // 检查照片笔记是否存在
            PhotoNote photoNote = getById(noteId);
            if (photoNote == null) {
                log.warn("照片笔记{}不存在", noteId);
                return false;
            }

            // 检查用户是否已举报过
            if (photoNoteReportMapper.checkUserReported(noteId, userId)) {
                log.warn("用户{}已举报过照片笔记{}", userId, noteId);
                return false;
            }

            // 创建举报记录
            PhotoNoteReport report = new PhotoNoteReport();
            report.setNoteId(noteId);
            report.setReportUserId(userId);
            report.setReason(reason);
            report.setDescription(description);
            report.setReportType(mapReasonToType(reason));
            report.setStatus(0); // 待处理
            report.setCreatedAt(LocalDateTime.now());
            report.setUpdatedAt(LocalDateTime.now());

            int result = photoNoteReportMapper.insert(report);

            if (result > 0) {
                log.info("举报记录创建成功，ID：{}", report.getId());

                // 检查举报次数，如果超过阈值则自动下架
                int reportCount = photoNoteReportMapper.countReportsByNoteId(noteId);
                if (reportCount >= 5) { // 举报次数阈值
                    log.warn("照片笔记{}举报次数达到阈值{}，自动下架", noteId, reportCount);
                    photoNote.setStatus(2); // 设置为审核拒绝状态
                    photoNote.setRejectReason("多次被举报，自动下架");
                    photoNote.setUpdatedAt(LocalDateTime.now());
                    updateById(photoNote);
                }

                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("举报照片笔记失败", e);
            throw e;
        }
    }

    /**
     * 将举报原因映射为举报类型
     */
    private String mapReasonToType(String reason) {
        if (reason == null) {
            return "other";
        }

        String lowerReason = reason.toLowerCase();
        if (lowerReason.contains("违法") || lowerReason.contains("违规")) {
            return "illegal";
        } else if (lowerReason.contains("色情") || lowerReason.contains("低俗")) {
            return "pornographic";
        } else if (lowerReason.contains("暴力") || lowerReason.contains("血腥")) {
            return "violent";
        } else if (lowerReason.contains("垃圾") || lowerReason.contains("广告")) {
            return "spam";
        } else if (lowerReason.contains("骚扰") || lowerReason.contains("恶意")) {
            return "harassment";
        } else {
            return "other";
        }
    }

    @Override
    public PhotoNoteStatsDTO getPhotoNoteStats() {
        log.info("获取照片笔记统计信息");

        PhotoNoteStatsDTO stats = new PhotoNoteStatsDTO();

        try {
            // 总数统计
            LambdaQueryWrapper<PhotoNote> totalWrapper = new LambdaQueryWrapper<>();
            totalWrapper.eq(PhotoNote::getIsDeleted, 0);
            long totalCount = count(totalWrapper);
            stats.setTotalCount(totalCount);

            // 待审核数统计
            LambdaQueryWrapper<PhotoNote> pendingWrapper = new LambdaQueryWrapper<>();
            pendingWrapper.eq(PhotoNote::getIsDeleted, 0).eq(PhotoNote::getStatus, 0);
            long pendingCount = count(pendingWrapper);
            stats.setPendingCount(pendingCount);

            // 已通过数统计
            LambdaQueryWrapper<PhotoNote> approvedWrapper = new LambdaQueryWrapper<>();
            approvedWrapper.eq(PhotoNote::getIsDeleted, 0).eq(PhotoNote::getStatus, 1);
            long approvedCount = count(approvedWrapper);
            stats.setApprovedCount(approvedCount);

            // 已拒绝数统计
            LambdaQueryWrapper<PhotoNote> rejectedWrapper = new LambdaQueryWrapper<>();
            rejectedWrapper.eq(PhotoNote::getIsDeleted, 0).eq(PhotoNote::getStatus, 2);
            long rejectedCount = count(rejectedWrapper);
            stats.setRejectedCount(rejectedCount);

            // 已删除数统计
            LambdaQueryWrapper<PhotoNote> deletedWrapper = new LambdaQueryWrapper<>();
            deletedWrapper.eq(PhotoNote::getIsDeleted, 0).eq(PhotoNote::getStatus, 3);
            long deletedCount = count(deletedWrapper);
            stats.setDeletedCount(deletedCount);

            // 今日新增数统计
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime todayEnd = todayStart.plusDays(1);
            LambdaQueryWrapper<PhotoNote> todayWrapper = new LambdaQueryWrapper<>();
            todayWrapper.eq(PhotoNote::getIsDeleted, 0)
                       .ge(PhotoNote::getCreatedAt, todayStart)
                       .lt(PhotoNote::getCreatedAt, todayEnd);
            long todayCount = count(todayWrapper);
            stats.setTodayCount(todayCount);

            // 本周新增数统计
            LocalDateTime weekStart = LocalDateTime.now().minusDays(7);
            LambdaQueryWrapper<PhotoNote> weekWrapper = new LambdaQueryWrapper<>();
            weekWrapper.eq(PhotoNote::getIsDeleted, 0)
                      .ge(PhotoNote::getCreatedAt, weekStart);
            long weekCount = count(weekWrapper);
            stats.setWeekCount(weekCount);

            // 本月新增数统计
            LocalDateTime monthStart = LocalDateTime.now().minusDays(30);
            LambdaQueryWrapper<PhotoNote> monthWrapper = new LambdaQueryWrapper<>();
            monthWrapper.eq(PhotoNote::getIsDeleted, 0)
                       .ge(PhotoNote::getCreatedAt, monthStart);
            long monthCount = count(monthWrapper);
            stats.setMonthCount(monthCount);

            log.info("照片笔记统计信息获取成功：总数={}, 待审核={}, 已通过={}, 已拒绝={}, 已删除={}, 今日={}, 本周={}, 本月={}",
                    totalCount, pendingCount, approvedCount, rejectedCount, deletedCount, todayCount, weekCount, monthCount);

            return stats;
        } catch (Exception e) {
            log.error("获取照片笔记统计信息失败", e);
            throw e;
        }
    }
}
