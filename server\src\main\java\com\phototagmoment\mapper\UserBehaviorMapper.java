package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.UserBehavior;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户行为Mapper接口
 */
@Mapper
public interface UserBehaviorMapper extends BaseMapper<UserBehavior> {

    /**
     * 获取用户的行为数据
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 用户行为列表
     */
    @Select("SELECT * FROM ptm_user_behavior WHERE user_id = #{userId} ORDER BY behavior_time DESC LIMIT #{limit}")
    List<UserBehavior> getUserBehaviors(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 获取用户对特定照片的行为
     *
     * @param userId     用户ID
     * @param photoId    照片ID
     * @param behaviorType 行为类型
     * @return 用户行为
     */
    @Select("SELECT * FROM ptm_user_behavior WHERE user_id = #{userId} AND photo_id = #{photoId} AND behavior_type = #{behaviorType} LIMIT 1")
    UserBehavior getUserBehavior(@Param("userId") Long userId, @Param("photoId") Long photoId, @Param("behaviorType") String behaviorType);

    /**
     * 统计用户的行为数量
     *
     * @param userId      用户ID
     * @param behaviorType 行为类型
     * @return 行为数量
     */
    @Select("SELECT COUNT(*) FROM ptm_user_behavior WHERE user_id = #{userId} AND behavior_type = #{behaviorType}")
    int countUserBehaviors(@Param("userId") Long userId, @Param("behaviorType") String behaviorType);

    /**
     * 统计照片的行为数量
     *
     * @param photoId     照片ID
     * @param behaviorType 行为类型
     * @return 行为数量
     */
    @Select("SELECT COUNT(*) FROM ptm_user_behavior WHERE photo_id = #{photoId} AND behavior_type = #{behaviorType}")
    int countPhotoBehaviors(@Param("photoId") Long photoId, @Param("behaviorType") String behaviorType);
}
