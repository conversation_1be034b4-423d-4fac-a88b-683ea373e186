package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.UserFollow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户关注Mapper接口
 */
@Mapper
@Repository
public interface UserFollowMapper extends BaseMapper<UserFollow> {

    /**
     * 获取用户关注的人ID列表
     *
     * @param userId 用户ID
     * @return 关注的人ID列表
     */
    @Select("SELECT follow_user_id FROM ptm_user_follow WHERE user_id = #{userId} AND is_deleted = 0")
    List<Long> getFollowingIds(@Param("userId") Long userId);

    /**
     * 获取关注用户的人ID列表
     *
     * @param userId 用户ID
     * @return 粉丝ID列表
     */
    @Select("SELECT user_id FROM ptm_user_follow WHERE follow_user_id = #{userId} AND is_deleted = 0")
    List<Long> getFollowerIds(@Param("userId") Long userId);

    /**
     * 检查用户是否关注了另一个用户
     *
     * @param userId       用户ID
     * @param followUserId 被关注的用户ID
     * @return 是否关注
     */
    @Select("SELECT COUNT(*) FROM ptm_user_follow WHERE user_id = #{userId} AND follow_user_id = #{followUserId} AND is_deleted = 0")
    int checkUserFollowed(@Param("userId") Long userId, @Param("followUserId") Long followUserId);

    /**
     * 统计用户的关注数
     *
     * @param userId 用户ID
     * @return 关注数
     */
    @Select("SELECT COUNT(*) FROM ptm_user_follow WHERE user_id = #{userId} AND is_deleted = 0")
    int countFollowing(@Param("userId") Long userId);

    /**
     * 统计用户的粉丝数
     *
     * @param userId 用户ID
     * @return 粉丝数
     */
    @Select("SELECT COUNT(*) FROM ptm_user_follow WHERE follow_user_id = #{userId} AND is_deleted = 0")
    int countFollowers(@Param("userId") Long userId);
}
