package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.vo.PhotoVO;
import com.phototagmoment.dto.PhotoNoteDTO;

import java.util.List;

/**
 * 推荐服务接口
 */
public interface RecommendationService {

    /**
     * 获取用户关注的人发布的照片
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 照片分页列表
     */
    IPage<PhotoVO> getFollowingPhotos(Long userId, int page, int size);

    /**
     * 获取热门照片
     *
     * @param page 页码
     * @param size 每页大小
     * @return 照片分页列表
     */
    IPage<PhotoVO> getHotPhotos(int page, int size);

    /**
     * 获取热门照片（带时间范围）
     *
     * @param days 最近几天
     * @param page 页码
     * @param size 每页大小
     * @return 照片分页列表
     */
    IPage<PhotoVO> getHotPhotos(int days, int page, int size);

    /**
     * 基于用户兴趣推荐照片
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 照片分页列表
     */
    IPage<PhotoVO> getRecommendedPhotos(Long userId, int page, int size);

    /**
     * 获取首页推荐照片（混合推荐）
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 照片分页列表
     */
    IPage<PhotoVO> getHomeRecommendations(Long userId, int page, int size);

    /**
     * 更新用户兴趣模型
     *
     * @param userId 用户ID
     */
    void updateUserInterestModel(Long userId);

    /**
     * 记录用户行为
     *
     * @param userId   用户ID
     * @param photoId  照片ID
     * @param behavior 行为类型（view, like, comment, collect）
     */
    void recordUserBehavior(Long userId, Long photoId, String behavior);

    /**
     * 获取用户可能感兴趣的标签
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 标签列表
     */
    List<String> getUserInterestTags(Long userId, int limit);

    /**
     * 获取用户兴趣标签（默认限制20个）
     *
     * @param userId 用户ID
     * @return 标签列表
     */
    List<String> getUserInterestTags(Long userId);

    /**
     * 获取个性化推荐照片笔记
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 推荐照片笔记列表
     */
    IPage<PhotoNoteDTO> getRecommendedPhotoNotes(Integer page, Integer size, Long userId, Long lastId);

    /**
     * 获取热门照片笔记
     *
     * @param page 页码
     * @param size 每页大小
     * @return 照片笔记分页列表
     */
    IPage<PhotoNoteDTO> getHotPhotoNotes(int page, int size);

    /**
     * 获取热门照片笔记（带时间范围）
     *
     * @param days 最近几天
     * @param page 页码
     * @param size 每页大小
     * @return 照片笔记分页列表
     */
    IPage<PhotoNoteDTO> getHotPhotoNotes(int days, int page, int size);

    /**
     * 获取关注用户的照片笔记
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 照片笔记分页列表
     */
    IPage<PhotoNoteDTO> getFollowingPhotoNotes(Long userId, int page, int size);

    /**
     * 获取首页推荐照片笔记（混合推荐）
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 照片笔记分页列表
     */
    IPage<PhotoNoteDTO> getHomePhotoNoteRecommendations(Long userId, int page, int size);

    /**
     * 基于用户兴趣推荐照片笔记
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 照片笔记分页列表
     */
    IPage<PhotoNoteDTO> getRecommendedPhotoNotes(Long userId, int page, int size);

    /**
     * 更新用户兴趣标签
     *
     * @param userId 用户ID
     * @param tags 兴趣标签列表
     * @return 是否成功
     */
    boolean updateUserInterestTags(Long userId, List<String> tags);

    /**
     * 记录用户行为（用于推荐算法）
     *
     * @param userId 用户ID
     * @param noteId 照片笔记ID
     * @param actionType 行为类型：view, like, comment, collect, share
     * @param weight 权重
     */
    void recordUserBehavior(Long userId, Long noteId, String actionType, Double weight);

    /**
     * 获取推荐配置
     *
     * @return 推荐配置
     */
    RecommendConfig getRecommendConfig();

    /**
     * 推荐配置类
     */
    class RecommendConfig {
        private Double userBehaviorWeight = 0.4; // 用户行为权重
        private Double followingWeight = 0.3; // 关注关系权重
        private Double tagInterestWeight = 0.2; // 标签兴趣权重
        private Double timeDecayWeight = 0.1; // 时间衰减权重
        private Double diversityFactor = 0.3; // 多样性因子

        public Double getUserBehaviorWeight() {
            return userBehaviorWeight;
        }

        public void setUserBehaviorWeight(Double userBehaviorWeight) {
            this.userBehaviorWeight = userBehaviorWeight;
        }

        public Double getFollowingWeight() {
            return followingWeight;
        }

        public void setFollowingWeight(Double followingWeight) {
            this.followingWeight = followingWeight;
        }

        public Double getTagInterestWeight() {
            return tagInterestWeight;
        }

        public void setTagInterestWeight(Double tagInterestWeight) {
            this.tagInterestWeight = tagInterestWeight;
        }

        public Double getTimeDecayWeight() {
            return timeDecayWeight;
        }

        public void setTimeDecayWeight(Double timeDecayWeight) {
            this.timeDecayWeight = timeDecayWeight;
        }

        public Double getDiversityFactor() {
            return diversityFactor;
        }

        public void setDiversityFactor(Double diversityFactor) {
            this.diversityFactor = diversityFactor;
        }
    }
}
