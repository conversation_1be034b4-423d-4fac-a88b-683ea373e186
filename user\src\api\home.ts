import request from '@/utils/request'

// 照片信息接口（匹配后端PhotoNoteImageDTO）
export interface PhotoInfo {
  photoId: number
  url: string
  thumbnailUrl: string
  width: number
  height: number
  sortOrder: number
}

// 用户信息接口
export interface UserInfo {
  id: number
  nickname: string
  avatar: string
  isVerified?: boolean
}

// @用户信息接口（匹配后端PhotoNoteMentionDTO）
export interface MentionInfo {
  mentionedUserId: number
  mentionedUserNickname: string
  mentionedUserAvatar: string
}

// 首页照片笔记接口（匹配后端PhotoNoteDTO）
export interface HomePhotoNote {
  id: number
  userId: number
  nickname: string
  avatar: string
  title?: string
  content: string
  processedContent: string
  images: PhotoInfo[]
  tags: string[]
  mentions: MentionInfo[]
  photoCount: number
  viewCount: number
  likeCount: number
  commentCount: number
  shareCount: number
  visibility: number // 0-私密, 1-公开, 2-好友可见
  allowComment: number // 0-不允许, 1-允许
  status: number
  location?: string
  longitude?: number
  latitude?: number
  isLiked: boolean
  isCollected: boolean
  createdAt: string
  updatedAt: string
}

// 首页数据响应接口
export interface HomeDataResponse {
  records: HomePhotoNote[]
  total: number
  current: number
  size: number
  hasMore: boolean
}

// 首页查询参数接口
export interface HomeQueryParams {
  page?: number
  size?: number
  type?: 'recommend' | 'following' | 'latest' | 'hot'
  lastId?: number // 用于游标分页
}

// 推荐配置接口
export interface RecommendConfig {
  userBehaviorWeight: number // 用户行为权重
  followingWeight: number // 关注关系权重
  tagInterestWeight: number // 标签兴趣权重
  timeDecayWeight: number // 时间衰减权重
  diversityFactor: number // 多样性因子
}

/**
 * 获取首页照片笔记流
 * @param params 查询参数
 */
export function getHomePhotoNotes(params: HomeQueryParams = {}) {
  return request<HomeDataResponse>({
    url: '/api/photo-notes/home',
    method: 'get',
    params: {
      page: 1,
      size: 10,
      type: 'recommend',
      ...params
    }
  })
}

/**
 * 获取推荐照片笔记
 * @param params 查询参数
 */
export function getRecommendedPhotoNotes(params: HomeQueryParams = {}) {
  return request<HomeDataResponse>({
    url: '/api/photo-notes/recommend',
    method: 'get',
    params: {
      page: 1,
      size: 10,
      ...params
    }
  })
}

/**
 * 获取关注用户的照片笔记
 * @param params 查询参数
 */
export function getFollowingPhotoNotes(params: HomeQueryParams = {}) {
  return request<HomeDataResponse>({
    url: '/api/photo-notes/following',
    method: 'get',
    params: {
      page: 1,
      size: 10,
      ...params
    }
  })
}

/**
 * 获取最新照片笔记
 * @param params 查询参数
 */
export function getLatestPhotoNotes(params: HomeQueryParams = {}) {
  return request<HomeDataResponse>({
    url: '/api/photo-notes/latest',
    method: 'get',
    params: {
      page: 1,
      size: 10,
      ...params
    }
  })
}

/**
 * 获取热门照片笔记
 * @param params 查询参数
 */
export function getHotPhotoNotes(params: HomeQueryParams = {}) {
  return request<HomeDataResponse>({
    url: '/api/photo-notes/hot',
    method: 'get',
    params: {
      page: 1,
      size: 10,
      ...params
    }
  })
}

/**
 * 点赞照片笔记
 * @param noteId 照片笔记ID
 */
export function likePhotoNote(noteId: number) {
  return request<{
    isLiked: boolean
    likeCount: number
  }>({
    url: `/api/photo-notes/${noteId}/like`,
    method: 'post'
  })
}

/**
 * 取消点赞照片笔记
 * @param noteId 照片笔记ID
 */
export function unlikePhotoNote(noteId: number) {
  return request<{
    isLiked: boolean
    likeCount: number
  }>({
    url: `/api/photo-notes/${noteId}/unlike`,
    method: 'post'
  })
}

/**
 * 收藏照片笔记
 * @param noteId 照片笔记ID
 */
export function collectPhotoNote(noteId: number) {
  return request<{
    isCollected: boolean
    collectCount: number
  }>({
    url: `/api/photo-notes/${noteId}/collect`,
    method: 'post'
  })
}

/**
 * 取消收藏照片笔记
 * @param noteId 照片笔记ID
 */
export function uncollectPhotoNote(noteId: number) {
  return request<{
    isCollected: boolean
    collectCount: number
  }>({
    url: `/api/photo-notes/${noteId}/uncollect`,
    method: 'post'
  })
}

/**
 * 增加照片笔记浏览量
 * @param noteId 照片笔记ID
 */
export function incrementPhotoNoteView(noteId: number) {
  return request<boolean>({
    url: `/api/photo-notes/${noteId}/view`,
    method: 'post'
  })
}

/**
 * 举报照片笔记
 * @param noteId 照片笔记ID
 * @param reason 举报原因
 * @param description 详细描述
 */
export function reportPhotoNote(noteId: number, reason: string, description?: string) {
  return request<boolean>({
    url: `/api/photo-notes/${noteId}/report`,
    method: 'post',
    data: {
      reason,
      description
    }
  })
}

/**
 * 获取推荐配置
 */
export function getRecommendConfig() {
  return request<RecommendConfig>({
    url: '/api/photo-notes/recommend/config',
    method: 'get'
  })
}

/**
 * 更新用户兴趣标签
 * @param tags 兴趣标签列表
 */
export function updateUserInterestTags(tags: string[]) {
  return request<boolean>({
    url: '/api/user/interest-tags',
    method: 'put',
    data: { tags }
  })
}

/**
 * 获取用户兴趣标签
 */
export function getUserInterestTags() {
  return request<string[]>({
    url: '/api/user/interest-tags',
    method: 'get'
  })
}
