package com.phototagmoment.service;

/**
 * 短信服务接口
 */
public interface SmsService {

    /**
     * 发送验证码
     *
     * @param phone 手机号
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String phone);

    /**
     * 验证验证码
     *
     * @param phone 手机号
     * @param code  验证码
     * @return 是否验证成功
     */
    boolean verifyCode(String phone, String code);

    /**
     * 发送通知短信
     *
     * @param phone   手机号
     * @param content 短信内容
     * @return 是否发送成功
     */
    boolean sendNotification(String phone, String content);

    /**
     * 获取手机号剩余发送次数
     *
     * @param phone 手机号
     * @return 剩余发送次数
     */
    int getRemainingAttempts(String phone);

    /**
     * 获取手机号冷却时间（秒）
     *
     * @param phone 手机号
     * @return 冷却时间（秒）
     */
    long getCooldownTime(String phone);
}
