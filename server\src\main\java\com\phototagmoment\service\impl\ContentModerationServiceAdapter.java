package com.phototagmoment.service.impl;

import com.phototagmoment.service.ContentModerationService;
import com.phototagmoment.service.ImageModerationService;
import com.phototagmoment.service.SystemConfigService;
import com.phototagmoment.service.TextModerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 内容审核服务适配器
 * 将内容审核请求分发到图像审核和文本审核服务
 */
@Slf4j
@Service
@Primary
public class ContentModerationServiceAdapter implements ContentModerationService {

    @Autowired
    private ImageModerationService imageModerationService;

    @Autowired
    private TextModerationService textModerationService;

    @Autowired
    private SystemConfigService configService;

    private String failReason;

    @Override
    public boolean moderateImage(MultipartFile file) {
        // 检查是否启用内容审核
        boolean contentModerationEnabled = configService.getBooleanValue("content-moderation.enabled", true);
        if (!contentModerationEnabled) {
            return true;
        }

        // 检查是否自动通过审核
        boolean autoApprove = configService.getBooleanValue("content-moderation.auto-approve", false);
        if (autoApprove) {
            return true;
        }

        try {
            // 调用图像审核服务
            boolean result = imageModerationService.moderateImage(file);
            if (!result) {
                failReason = imageModerationService.getFailReason();
            }
            return result;
        } catch (Exception e) {
            log.error("图像审核服务调用失败", e);
            // 审核服务异常时，默认通过
            return true;
        }
    }

    @Override
    public boolean moderateImage(InputStream inputStream, String contentType) {
        // 检查是否启用内容审核
        boolean contentModerationEnabled = configService.getBooleanValue("content-moderation.enabled", true);
        if (!contentModerationEnabled) {
            return true;
        }

        // 检查是否自动通过审核
        boolean autoApprove = configService.getBooleanValue("content-moderation.auto-approve", false);
        if (autoApprove) {
            return true;
        }

        try {
            // 调用图像审核服务
            boolean result = imageModerationService.moderateImage(inputStream, contentType);
            if (!result) {
                failReason = imageModerationService.getFailReason();
            }
            return result;
        } catch (Exception e) {
            log.error("图像审核服务调用失败", e);
            // 审核服务异常时，默认通过
            return true;
        }
    }

    @Override
    public boolean moderateImageByUrl(String imageUrl) {
        // 检查是否启用内容审核
        boolean contentModerationEnabled = configService.getBooleanValue("content-moderation.enabled", true);
        if (!contentModerationEnabled) {
            return true;
        }

        // 检查是否自动通过审核
        boolean autoApprove = configService.getBooleanValue("content-moderation.auto-approve", false);
        if (autoApprove) {
            return true;
        }

        try {
            // 调用图像审核服务
            boolean result = imageModerationService.moderateImageByUrl(imageUrl);
            if (!result) {
                failReason = imageModerationService.getFailReason();
            }
            return result;
        } catch (Exception e) {
            log.error("图像审核服务调用失败", e);
            // 审核服务异常时，默认通过
            return true;
        }
    }

    @Override
    public boolean moderateText(String text) {
        // 检查是否启用内容审核
        boolean contentModerationEnabled = configService.getBooleanValue("content-moderation.enabled", true);
        if (!contentModerationEnabled) {
            return true;
        }

        // 检查是否自动通过审核
        boolean autoApprove = configService.getBooleanValue("content-moderation.auto-approve", false);
        if (autoApprove) {
            return true;
        }

        try {
            // 调用文本审核服务
            boolean result = textModerationService.moderateText(text);
            if (!result) {
                failReason = textModerationService.getFailReason();
            }
            return result;
        } catch (Exception e) {
            log.error("文本审核服务调用失败", e);
            // 审核服务异常时，默认通过
            return true;
        }
    }

    @Override
    public String getFailReason() {
        return failReason;
    }
}
