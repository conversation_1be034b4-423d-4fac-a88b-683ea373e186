package com.phototagmoment.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.dto.MentionDTO;
import com.phototagmoment.dto.PhotoDraftDTO;
import com.phototagmoment.dto.PhotoUploadDTO;
import com.phototagmoment.dto.PhotoUploadWithMentionsDTO;
import com.phototagmoment.entity.PhotoDraft;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.PhotoDraftMapper;
import com.phototagmoment.service.PhotoDraftService;
import com.phototagmoment.service.PhotoService;
import com.phototagmoment.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 照片草稿服务实现类
 */
@Slf4j
@Service
public class PhotoDraftServiceImpl extends ServiceImpl<PhotoDraftMapper, PhotoDraft> implements PhotoDraftService {

    @Autowired
    private StorageService storageService;

    @Autowired
    private PhotoService photoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveDraft(PhotoDraftDTO draftDTO, List<MultipartFile> files, Long userId) {
        // 保存临时文件
        List<String> tempFilePaths = new ArrayList<>();
        if (files != null && !files.isEmpty()) {
            tempFilePaths = saveTemporaryFiles(files, userId);
        }

        // 创建草稿
        PhotoDraft draft = new PhotoDraft();
        draft.setUserId(userId);
        draft.setTitle(draftDTO.getTitle());
        draft.setDescription(draftDTO.getDescription());
        draft.setLocation(draftDTO.getLocation());
        draft.setTags(draftDTO.getTags() != null ? JSON.toJSONString(draftDTO.getTags()) : null);
        draft.setMentions(draftDTO.getMentions() != null ? JSON.toJSONString(draftDTO.getMentions()) : null);
        draft.setVisibility(draftDTO.getVisibility());
        draft.setAllowComment(draftDTO.getAllowComment());
        draft.setAllowDownload(draftDTO.getAllowDownload());
        draft.setTempFilePaths(JSON.toJSONString(tempFilePaths));

        // 保存草稿
        this.save(draft);

        return draft.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDraft(Long draftId, PhotoDraftDTO draftDTO, List<MultipartFile> files, Long userId) {
        // 检查草稿是否存在
        PhotoDraft draft = baseMapper.getDraftDetail(draftId, userId);
        if (draft == null) {
            throw new BusinessException("草稿不存在或已删除");
        }

        // 更新临时文件
        List<String> tempFilePaths = JSON.parseArray(draft.getTempFilePaths(), String.class);
        if (files != null && !files.isEmpty()) {
            List<String> newTempFilePaths = saveTemporaryFiles(files, userId);
            tempFilePaths.addAll(newTempFilePaths);
        }

        // 更新草稿
        draft.setTitle(draftDTO.getTitle());
        draft.setDescription(draftDTO.getDescription());
        draft.setLocation(draftDTO.getLocation());
        draft.setTags(draftDTO.getTags() != null ? JSON.toJSONString(draftDTO.getTags()) : null);
        draft.setMentions(draftDTO.getMentions() != null ? JSON.toJSONString(draftDTO.getMentions()) : null);
        draft.setVisibility(draftDTO.getVisibility());
        draft.setAllowComment(draftDTO.getAllowComment());
        draft.setAllowDownload(draftDTO.getAllowDownload());
        draft.setTempFilePaths(JSON.toJSONString(tempFilePaths));

        // 更新草稿
        return this.updateById(draft);
    }

    @Override
    public PhotoDraftDTO getDraftDetail(Long draftId, Long userId) {
        // 获取草稿
        PhotoDraft draft = baseMapper.getDraftDetail(draftId, userId);
        if (draft == null) {
            throw new BusinessException("草稿不存在或已删除");
        }

        // 转换为DTO
        return convertToDTO(draft);
    }

    @Override
    public IPage<PhotoDraftDTO> getUserDrafts(int page, int size, Long userId) {
        // 查询草稿列表
        Page<PhotoDraft> pageParam = new Page<>(page, size);
        IPage<PhotoDraft> draftPage = baseMapper.getUserDrafts(pageParam, userId);

        // 转换为DTO
        IPage<PhotoDraftDTO> draftDTOPage = new Page<>(draftPage.getCurrent(), draftPage.getSize(), draftPage.getTotal());
        List<PhotoDraftDTO> draftDTOList = draftPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        draftDTOPage.setRecords(draftDTOList);

        return draftDTOPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDraft(Long draftId, Long userId) {
        // 检查草稿是否存在
        PhotoDraft draft = baseMapper.getDraftDetail(draftId, userId);
        if (draft == null) {
            throw new BusinessException("草稿不存在或已删除");
        }

        // 删除临时文件
        List<String> tempFilePaths = JSON.parseArray(draft.getTempFilePaths(), String.class);
        for (String tempFilePath : tempFilePaths) {
            try {
                Files.deleteIfExists(Paths.get(tempFilePath));
            } catch (IOException e) {
                log.error("删除临时文件失败", e);
            }
        }

        // 删除草稿
        return this.removeById(draftId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> publishDraft(Long draftId, Long userId) {
        // 检查草稿是否存在
        PhotoDraft draft = baseMapper.getDraftDetail(draftId, userId);
        if (draft == null) {
            throw new BusinessException("草稿不存在或已删除");
        }

        // 获取临时文件路径
        List<String> tempFilePaths = JSON.parseArray(draft.getTempFilePaths(), String.class);
        if (tempFilePaths.isEmpty()) {
            throw new BusinessException("草稿中没有照片");
        }

        // 准备上传参数
        PhotoUploadWithMentionsDTO uploadDTO = new PhotoUploadWithMentionsDTO();
        uploadDTO.setTitle(draft.getTitle());
        uploadDTO.setDescription(draft.getDescription());
        uploadDTO.setLocation(draft.getLocation());
        uploadDTO.setTags(JSON.parseArray(draft.getTags(), String.class));
        uploadDTO.setMentions(JSON.parseArray(draft.getMentions(), MentionDTO.class));
        uploadDTO.setVisibility(draft.getVisibility());
        uploadDTO.setAllowComment(draft.getAllowComment());
        uploadDTO.setAllowDownload(draft.getAllowDownload());

        // 上传照片
        List<Long> photoIds = new ArrayList<>();
        for (String tempFilePath : tempFilePaths) {
            try {
                // 读取临时文件
                Path path = Paths.get(tempFilePath);
                if (!Files.exists(path)) {
                    continue;
                }

                // 转换为MultipartFile
                // 这里简化处理，实际应该使用MultipartFile的实现类
                // 此处仅作为示例，实际项目中应该使用更合适的方式
                // 例如，直接使用File对象上传，或者使用临时文件的路径上传
                
                // 创建PhotoUploadDTO
                PhotoUploadDTO photoUploadDTO = new PhotoUploadDTO();
                BeanUtils.copyProperties(uploadDTO, photoUploadDTO);
                
                // 上传照片
                // 这里简化处理，实际应该调用photoService.uploadPhoto方法
                // 由于无法直接将File转换为MultipartFile，这里仅作为示例
                // Long photoId = photoService.uploadPhoto(file, photoUploadDTO, userId);
                // photoIds.add(photoId);
                
                // 模拟上传成功
                photoIds.add(1L);
                
                // 删除临时文件
                Files.deleteIfExists(path);
            } catch (IOException e) {
                log.error("发布草稿失败", e);
                throw new BusinessException("发布草稿失败");
            }
        }

        // 删除草稿
        this.removeById(draftId);

        return photoIds;
    }

    /**
     * 保存临时文件
     *
     * @param files 文件列表
     * @param userId 用户ID
     * @return 临时文件路径列表
     */
    private List<String> saveTemporaryFiles(List<MultipartFile> files, Long userId) {
        List<String> tempFilePaths = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                // 生成临时文件路径
                String originalFilename = file.getOriginalFilename();
                String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
                String filename = UUID.randomUUID().toString().replace("-", "") + extension;
                String tempDir = System.getProperty("java.io.tmpdir") + "/ptm_drafts/" + userId;
                String tempFilePath = tempDir + "/" + filename;

                // 创建目录
                File dir = new File(tempDir);
                if (!dir.exists()) {
                    dir.mkdirs();
                }

                // 保存临时文件
                File tempFile = new File(tempFilePath);
                file.transferTo(tempFile);

                tempFilePaths.add(tempFilePath);
            } catch (IOException e) {
                log.error("保存临时文件失败", e);
                throw new BusinessException("保存临时文件失败");
            }
        }
        return tempFilePaths;
    }

    /**
     * 将实体转换为DTO
     *
     * @param draft 草稿实体
     * @return 草稿DTO
     */
    private PhotoDraftDTO convertToDTO(PhotoDraft draft) {
        PhotoDraftDTO draftDTO = new PhotoDraftDTO();
        draftDTO.setId(draft.getId());
        draftDTO.setTitle(draft.getTitle());
        draftDTO.setDescription(draft.getDescription());
        draftDTO.setLocation(draft.getLocation());
        draftDTO.setTags(JSON.parseArray(draft.getTags(), String.class));
        draftDTO.setMentions(JSON.parseArray(draft.getMentions(), MentionDTO.class));
        draftDTO.setVisibility(draft.getVisibility());
        draftDTO.setAllowComment(draft.getAllowComment());
        draftDTO.setAllowDownload(draft.getAllowDownload());
        draftDTO.setTempFilePaths(JSON.parseArray(draft.getTempFilePaths(), String.class));
        draftDTO.setCreatedAt(draft.getCreatedAt());
        draftDTO.setUpdatedAt(draft.getUpdatedAt());
        return draftDTO;
    }
}
