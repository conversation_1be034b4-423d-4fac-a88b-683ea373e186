package com.phototagmoment.controller;

import com.phototagmoment.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * API文档测试控制器
 */
@RestController
@RequestMapping("/api-doc-test")
@Tag(name = "API文档测试", description = "用于测试API文档功能")
public class ApiDocTestController {

    /**
     * 测试GET请求
     */
    @GetMapping("/hello")
    @Operation(summary = "测试GET请求", description = "返回一个简单的问候消息")
    public Result<String> hello(@Parameter(description = "名称") @RequestParam(required = false) String name) {
        if (name == null || name.isEmpty()) {
            return Result.success("Hello, World!");
        }
        return Result.success("Hello, " + name + "!");
    }

    /**
     * 测试POST请求
     */
    @PostMapping("/echo")
    @Operation(summary = "测试POST请求", description = "返回请求体中的消息")
    public Result<String> echo(@Parameter(description = "消息") @RequestBody String message) {
        return Result.success("Echo: " + message);
    }

    /**
     * 测试PUT请求
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "测试PUT请求", description = "更新指定ID的资源")
    public Result<String> update(
            @Parameter(description = "资源ID") @PathVariable Long id,
            @Parameter(description = "新值") @RequestParam String value) {
        return Result.success("Updated resource " + id + " with value: " + value);
    }

    /**
     * 测试DELETE请求
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "测试DELETE请求", description = "删除指定ID的资源")
    public Result<Boolean> delete(@Parameter(description = "资源ID") @PathVariable Long id) {
        return Result.success(true, "Deleted resource " + id);
    }
}
