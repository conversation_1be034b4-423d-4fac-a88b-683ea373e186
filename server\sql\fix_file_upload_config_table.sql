-- PhotoTagMoment 文件上传配置表结构修复脚本
-- 修复日期: 2025-05-24
-- 问题: ptm_file_upload_config表缺少created_by和updated_by字段

USE phototag_moment;

-- 检查表结构
SELECT 'Current table structure:' as info;
DESCRIBE ptm_file_upload_config;

-- 添加缺失的字段
SELECT 'Adding missing fields...' as info;

-- 添加创建者ID字段
ALTER TABLE ptm_file_upload_config 
ADD COLUMN created_by BIGINT NULL COMMENT '创建者ID' 
AFTER last_test_result;

-- 添加更新者ID字段  
ALTER TABLE ptm_file_upload_config 
ADD COLUMN updated_by BIGINT NULL COMMENT '更新者ID' 
AFTER created_by;

-- 验证修复结果
SELECT 'Updated table structure:' as info;
DESCRIBE ptm_file_upload_config;

-- 检查现有数据
SELECT 'Current data count:' as info;
SELECT COUNT(*) as total_configs FROM ptm_file_upload_config WHERE is_deleted = 0;

-- 为现有数据设置默认的创建者和更新者（设为1，表示系统管理员）
UPDATE ptm_file_upload_config 
SET created_by = 1, updated_by = 1 
WHERE created_by IS NULL OR updated_by IS NULL;

-- 验证数据更新
SELECT 'Sample data after update:' as info;
SELECT id, config_name, storage_type, enabled, created_by, updated_by, created_at, updated_at 
FROM ptm_file_upload_config 
WHERE is_deleted = 0 
LIMIT 5;

SELECT 'Table structure fix completed successfully!' as result;
