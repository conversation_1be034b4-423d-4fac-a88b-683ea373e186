<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.UserMapper">

    <!-- 查询用户关注列表 -->
    <select id="selectFollowingList" resultType="com.phototagmoment.dto.UserDTO">
        SELECT
            u.id,
            u.username,
            u.nickname,
            u.avatar,
            u.email,
            u.phone,
            u.gender,
            u.bio,
            u.location,
            u.website,
            u.following_count AS followingCount,
            u.follower_count AS followerCount,
            u.photo_count AS photoCount,
            u.created_at AS createdAt,
            1 AS isFollowing
        FROM
            ptm_user u
        INNER JOIN
            ptm_user_follow uf ON u.id = uf.following_id
        WHERE
            uf.follower_id = #{userId}
            AND u.status = 1
            AND u.is_deleted = 0
        ORDER BY
            uf.created_at DESC
    </select>

    <!-- 查询用户粉丝列表 -->
    <select id="selectFollowerList" resultType="com.phototagmoment.dto.UserDTO">
        SELECT
            u.id,
            u.username,
            u.nickname,
            u.avatar,
            u.email,
            u.phone,
            u.gender,
            u.bio,
            u.location,
            u.website,
            u.following_count AS followingCount,
            u.follower_count AS followerCount,
            u.photo_count AS photoCount,
            u.created_at AS createdAt,
            CASE WHEN uf2.id IS NOT NULL THEN 1 ELSE 0 END AS isFollowing
        FROM
            ptm_user u
        INNER JOIN
            ptm_user_follow uf ON u.id = uf.follower_id
        LEFT JOIN
            ptm_user_follow uf2 ON uf2.follower_id = #{userId} AND uf2.following_id = u.id
        WHERE
            uf.following_id = #{userId}
            AND u.status = 1
            AND u.is_deleted = 0
        ORDER BY
            uf.created_at DESC
    </select>

    <!-- 获取推荐用户 -->
    <select id="getRecommendedUsers" resultType="com.phototagmoment.entity.User">
        SELECT
            u.*
        FROM
            ptm_user u
        WHERE
            u.status = 1
            AND u.is_deleted = 0
            <if test="userId != null">
                AND u.id != #{userId}
                AND u.id NOT IN (
                    SELECT following_id FROM ptm_user_follow WHERE follower_id = #{userId}
                )
            </if>
        ORDER BY
            u.follower_count DESC, u.photo_count DESC
        LIMIT #{limit}
    </select>

</mapper>
