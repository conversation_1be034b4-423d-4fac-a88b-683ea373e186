<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .file-list {
            margin-top: 20px;
        }
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .file-info {
            flex: 1;
        }
        .file-name {
            font-weight: bold;
            color: #333;
        }
        .file-size {
            color: #666;
            font-size: 12px;
        }
        .remove-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .remove-btn:hover {
            background-color: #c82333;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PhotoTagMoment 后台文件上传测试</h1>
        
        <div class="form-group">
            <label for="fileType">文件类型:</label>
            <select id="fileType">
                <option value="general">通用文件</option>
                <option value="image">图片</option>
                <option value="document">文档</option>
                <option value="video">视频</option>
            </select>
        </div>

        <div class="form-group">
            <label for="fileInput">选择文件:</label>
            <input type="file" id="fileInput" multiple accept="*/*">
        </div>

        <div class="file-list" id="fileList"></div>

        <button onclick="uploadFiles()" id="uploadBtn">上传文件</button>
        <button onclick="clearFiles()">清空文件</button>
        <button onclick="testAPI()">测试API连接</button>

        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let selectedFiles = [];
        const API_BASE = 'http://localhost:8080/api';
        
        // 模拟管理员token（实际使用时需要从登录获取）
        const ADMIN_TOKEN = 'your-admin-token-here';

        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            selectedFiles = [...selectedFiles, ...files];
            updateFileList();
        });

        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <button class="remove-btn" onclick="removeFile(${index})">移除</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
        }

        function clearFiles() {
            selectedFiles = [];
            document.getElementById('fileInput').value = '';
            updateFileList();
            showResult('', 'info');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
        }

        function updateProgress(percent) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            
            if (percent > 0) {
                progressContainer.style.display = 'block';
                progressBar.style.width = percent + '%';
            } else {
                progressContainer.style.display = 'none';
            }
        }

        async function testAPI() {
            showResult('正在测试API连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/admin/file-manage/statistics`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${ADMIN_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`API连接成功！\n响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`API连接失败！\n状态码: ${response.status}\n状态文本: ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult(`API连接错误: ${error.message}`, 'error');
            }
        }

        async function uploadFiles() {
            if (selectedFiles.length === 0) {
                showResult('请先选择文件', 'error');
                return;
            }

            const fileType = document.getElementById('fileType').value;
            const uploadBtn = document.getElementById('uploadBtn');
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            updateProgress(0);
            
            try {
                if (selectedFiles.length === 1) {
                    // 单文件上传
                    await uploadSingleFile(selectedFiles[0], fileType);
                } else {
                    // 批量上传
                    await uploadMultipleFiles(selectedFiles, fileType);
                }
            } catch (error) {
                showResult(`上传失败: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传文件';
                updateProgress(0);
            }
        }

        async function uploadSingleFile(file, fileType) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', fileType);

            showResult('正在上传文件...', 'info');
            updateProgress(50);

            const response = await fetch(`${API_BASE}/admin/file/upload`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${ADMIN_TOKEN}`
                },
                body: formData
            });

            updateProgress(100);

            if (response.ok) {
                const result = await response.json();
                showResult(`单文件上传成功！\n响应数据: ${JSON.stringify(result, null, 2)}`, 'success');
            } else {
                const error = await response.text();
                showResult(`单文件上传失败！\n状态码: ${response.status}\n错误信息: ${error}`, 'error');
            }
        }

        async function uploadMultipleFiles(files, fileType) {
            const formData = new FormData();
            files.forEach(file => {
                formData.append('files', file);
            });
            formData.append('type', fileType);

            showResult('正在批量上传文件...', 'info');
            updateProgress(50);

            const response = await fetch(`${API_BASE}/admin/file/batch-upload`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${ADMIN_TOKEN}`
                },
                body: formData
            });

            updateProgress(100);

            if (response.ok) {
                const result = await response.json();
                showResult(`批量上传完成！\n响应数据: ${JSON.stringify(result, null, 2)}`, 'success');
            } else {
                const error = await response.text();
                showResult(`批量上传失败！\n状态码: ${response.status}\n错误信息: ${error}`, 'error');
            }
        }
    </script>
</body>
</html>
