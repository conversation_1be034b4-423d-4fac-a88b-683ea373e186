-- 创建用户行为表
CREATE TABLE IF NOT EXISTS `ptm_user_behavior` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `photo_id` bigint(20) NOT NULL COMMENT '照片ID',
  `behavior_type` varchar(20) NOT NULL COMMENT '行为类型（view, like, comment, collect）',
  `behavior_time` datetime NOT NULL COMMENT '行为时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_behavior_type` (`behavior_type`),
  KEY `idx_behavior_time` (`behavior_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为表';

-- 创建用户兴趣标签表
CREATE TABLE IF NOT EXISTS `ptm_user_interest` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `interest_score` double NOT NULL DEFAULT '0' COMMENT '兴趣分数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_tag` (`user_id`,`tag_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tag_name` (`tag_name`),
  KEY `idx_interest_score` (`interest_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户兴趣标签表';

-- 创建照片热度表
CREATE TABLE IF NOT EXISTS `ptm_photo_hotness` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `photo_id` bigint(20) NOT NULL COMMENT '照片ID',
  `hot_score` double NOT NULL DEFAULT '0' COMMENT '热度分数',
  `like_count` int(11) NOT NULL DEFAULT '0' COMMENT '点赞数',
  `comment_count` int(11) NOT NULL DEFAULT '0' COMMENT '评论数',
  `collect_count` int(11) NOT NULL DEFAULT '0' COMMENT '收藏数',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_photo_id` (`photo_id`),
  KEY `idx_hot_score` (`hot_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片热度表';

-- 添加照片表的浏览次数字段（如果不存在）
SET @column_exists = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'view_count');
SET @add_view_count_column = IF(@column_exists = 0, 'ALTER TABLE `ptm_photo` ADD COLUMN `view_count` int(11) NOT NULL DEFAULT 0 COMMENT "浏览次数" AFTER `status`', 'SELECT "Column view_count already exists"');

PREPARE stmt FROM @add_view_count_column;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建推荐记录表
CREATE TABLE IF NOT EXISTS `ptm_recommendation_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `photo_id` bigint(20) NOT NULL COMMENT '照片ID',
  `recommendation_type` varchar(20) NOT NULL COMMENT '推荐类型（interest, hot, following）',
  `recommendation_time` datetime NOT NULL COMMENT '推荐时间',
  `is_clicked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被点击',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_photo_time` (`user_id`,`photo_id`,`recommendation_time`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_recommendation_type` (`recommendation_type`),
  KEY `idx_recommendation_time` (`recommendation_time`),
  KEY `idx_is_clicked` (`is_clicked`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐记录表';
