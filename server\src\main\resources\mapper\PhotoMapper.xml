<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.PhotoMapper">

    <!-- 分页查询照片列表 -->
    <select id="selectPhotoPage" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            <if test="userId != null">
                p.user_id = #{userId}
            </if>
            <if test="userId == null">
                p.visibility = 1 AND p.status = 1
            </if>
            AND p.is_deleted = 0
        ORDER BY
            p.created_at DESC
    </select>

    <!-- 查询照片详情 -->
    <select id="selectPhotoDetail" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            p.id = #{photoId}
    </select>

    <!-- 增加照片浏览数 -->
    <update id="incrementViewCount">
        UPDATE ptm_photo
        SET view_count = view_count + 1
        WHERE id = #{photoId}
    </update>

    <!-- 增加照片点赞数 -->
    <update id="incrementLikeCount">
        UPDATE ptm_photo
        SET like_count = like_count + 1
        WHERE id = #{photoId}
    </update>

    <!-- 减少照片点赞数 -->
    <update id="decrementLikeCount">
        UPDATE ptm_photo
        SET like_count = like_count - 1
        WHERE id = #{photoId} AND like_count > 0
    </update>

    <!-- 增加照片收藏数 -->
    <update id="incrementCollectCount">
        UPDATE ptm_photo
        SET collect_count = collect_count + 1
        WHERE id = #{photoId}
    </update>

    <!-- 减少照片收藏数 -->
    <update id="decrementCollectCount">
        UPDATE ptm_photo
        SET collect_count = collect_count - 1
        WHERE id = #{photoId} AND collect_count > 0
    </update>

    <!-- 增加照片评论数 -->
    <update id="incrementCommentCount">
        UPDATE ptm_photo
        SET comment_count = comment_count + 1
        WHERE id = #{photoId}
    </update>

    <!-- 减少照片评论数 -->
    <update id="decrementCommentCount">
        UPDATE ptm_photo
        SET comment_count = comment_count - 1
        WHERE id = #{photoId} AND comment_count > 0
    </update>

    <!-- 根据照片ID列表查询照片列表 -->
    <select id="selectPhotosByIds" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            p.id IN
            <foreach collection="photoIds" item="photoId" open="(" separator="," close=")">
                #{photoId}
            </foreach>
        ORDER BY
            FIELD(p.id,
            <foreach collection="photoIds" item="photoId" separator=",">
                #{photoId}
            </foreach>
            )
    </select>

    <!-- 检查用户是否已点赞照片 -->
    <select id="checkUserLiked" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM ptm_photo_like
        WHERE photo_id = #{photoId} AND user_id = #{userId}
    </select>

    <!-- 检查用户是否已收藏照片 -->
    <select id="checkUserCollected" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM ptm_photo_collect
        WHERE photo_id = #{photoId} AND user_id = #{userId}
    </select>

    <!-- 根据照片ID列表查询照片详情列表 -->
    <select id="selectPhotoDetailsByIds" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            p.id IN
            <foreach collection="photoIds" item="photoId" open="(" separator="," close=")">
                #{photoId}
            </foreach>
        ORDER BY
            FIELD(p.id,
            <foreach collection="photoIds" item="photoId" separator=",">
                #{photoId}
            </foreach>
            )
    </select>

    <!-- 分页查询照片列表（包含用户信息） -->
    <select id="selectPhotoPageWithUserInfo" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            p.status = 1
            AND p.is_deleted = 0
        ORDER BY
            p.created_at DESC
    </select>

    <!-- 根据条件分页查询照片列表（包含用户信息） -->
    <select id="selectHotPhotosWithUserInfo" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            p.created_at >= #{startTime}
            AND p.status = 1
            AND p.is_deleted = 0
        ORDER BY
            (p.like_count * 1 + p.comment_count * 2 + p.collect_count * 3 + p.view_count * 0.1) DESC
    </select>

    <!-- 查询用户关注的人发布的照片（包含用户信息） -->
    <select id="selectFollowingPhotosWithUserInfo" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            p.user_id IN
            <foreach collection="followingIds" item="followingId" open="(" separator="," close=")">
                #{followingId}
            </foreach>
            AND p.status = 1
            AND p.is_deleted = 0
        ORDER BY
            p.created_at DESC
    </select>

    <!-- 根据标签查询照片（包含用户信息） -->
    <select id="getPhotosByTagsWithUserInfo" resultType="com.phototagmoment.dto.PhotoDTO">
        SELECT
            p.id, p.user_id, p.group_id, p.title, p.description, p.url, p.thumbnail_url, p.storage_path,
            p.original_filename, p.file_type, p.width, p.height, p.location,
            p.visibility, p.allow_comment, p.allow_download,
            p.like_count, p.comment_count, p.collect_count, p.view_count,
            p.status, p.reject_reason, p.is_deleted, p.created_at, p.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN pl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked,
            CASE WHEN pc.id IS NOT NULL THEN 1 ELSE 0 END AS isCollected
        FROM
            ptm_photo p
        JOIN
            ptm_photo_tag t ON p.id = t.photo_id
        LEFT JOIN
            ptm_user u ON p.user_id = u.id
        LEFT JOIN
            ptm_photo_like pl ON p.id = pl.photo_id AND pl.user_id = #{currentUserId}
        LEFT JOIN
            ptm_photo_collect pc ON p.id = pc.photo_id AND pc.user_id = #{currentUserId}
        WHERE
            p.status = 1
            AND p.is_deleted = 0
            AND t.tag_name IN
            <foreach collection="tags" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
        GROUP BY
            p.id
        ORDER BY
            COUNT(p.id) DESC, p.created_at DESC
        LIMIT
            #{offset}, #{limit}
    </select>

</mapper>
