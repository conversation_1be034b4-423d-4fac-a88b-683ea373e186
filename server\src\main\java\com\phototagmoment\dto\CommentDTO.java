package com.phototagmoment.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论DTO
 */
@Data
public class CommentDTO {

    /**
     * 评论ID
     */
    private Long id;

    /**
     * 照片ID
     */
    private Long photoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 父评论ID，如果是回复则不为空
     */
    private Long parentId;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 回复数
     */
    private Integer replyCount;

    /**
     * 是否已点赞
     */
    private Boolean isLiked;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 用户信息
     */
    private UserDTO user;

    /**
     * 回复的用户信息，如果是回复则不为空
     */
    private UserDTO replyToUser;

    /**
     * 回复列表，如果是评论则不为空
     */
    private List<CommentDTO> replies;
}
