-- 添加 taken_time 字段到 ptm_photo 表
-- 检查 taken_time 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'taken_time';

-- 如果 taken_time 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN taken_time DATETIME NULL COMMENT \'拍摄时间\'',
    'SELECT \'taken_time column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
