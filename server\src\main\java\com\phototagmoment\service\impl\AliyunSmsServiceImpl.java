package com.phototagmoment.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.phototagmoment.config.SmsConfig;
import com.phototagmoment.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云短信服务实现类
 */
@Slf4j
@Service
public class AliyunSmsServiceImpl implements SmsService {

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private IAcsClient acsClient;
    private Random random = new Random();

    // Redis Key 前缀
    private static final String VERIFICATION_CODE_PREFIX = "sms:code:";
    private static final String DAILY_COUNT_PREFIX = "sms:daily:";
    private static final String LAST_SEND_TIME_PREFIX = "sms:last:";

    @PostConstruct
    public void init() {
        if (!smsConfig.isEnabled() || !"aliyun".equals(smsConfig.getProvider())) {
            log.warn("阿里云短信服务未启用");
            return;
        }

        try {
            // 初始化阿里云客户端
            IClientProfile profile = DefaultProfile.getProfile(
                    smsConfig.getAliyun().getRegionId(),
                    smsConfig.getAliyun().getAccessKeyId(),
                    smsConfig.getAliyun().getAccessKeySecret());
            DefaultProfile.addEndpoint(
                    smsConfig.getAliyun().getRegionId(),
                    "Dysmsapi",
                    "dysmsapi.aliyuncs.com");
            acsClient = new DefaultAcsClient(profile);
            log.info("阿里云短信服务初始化成功");
        } catch (Exception e) {
            log.error("阿里云短信服务初始化失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean sendVerificationCode(String phone) {
        if (!smsConfig.isEnabled() || acsClient == null) {
            log.warn("短信服务未启用");
            return false;
        }

        // 检查发送频率限制
        if (!checkSendFrequency(phone)) {
            log.warn("发送频率过高: {}", phone);
            return false;
        }

        // 检查每日发送次数限制
        if (!checkDailyLimit(phone)) {
            log.warn("超过每日发送次数限制: {}", phone);
            return false;
        }

        // 生成验证码
        String code = generateVerificationCode();

        // 发送短信
        try {
            SendSmsRequest request = new SendSmsRequest();
            request.setPhoneNumbers(phone);
            request.setSignName(smsConfig.getAliyun().getSignName());
            request.setTemplateCode(smsConfig.getAliyun().getTemplateCode());
            request.setTemplateParam("{\"code\":\"" + code + "\"}");

            SendSmsResponse response = acsClient.getAcsResponse(request);
            if ("OK".equals(response.getCode())) {
                // 保存验证码到Redis
                saveVerificationCode(phone, code);
                // 更新发送记录
                updateSendRecord(phone);
                log.info("短信发送成功: {}, {}", phone, response.getBizId());
                return true;
            } else {
                log.error("短信发送失败: {}, {}, {}", phone, response.getCode(), response.getMessage());
                return false;
            }
        } catch (ClientException e) {
            log.error("短信发送异常: {}, {}", phone, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean verifyCode(String phone, String code) {
        if (!smsConfig.isEnabled()) {
            log.warn("短信服务未启用");
            return false;
        }

        try {
            String key = VERIFICATION_CODE_PREFIX + phone;
            Object savedCode = redisTemplate.opsForValue().get(key);

            if (savedCode != null && savedCode.toString().equals(code)) {
                // 验证成功后删除验证码
                redisTemplate.delete(key);
                return true;
            }
        } catch (Exception e) {
            log.error("验证码验证失败: {}", e.getMessage(), e);
            // 如果Redis不可用，为了开发测试方便，使用默认验证码
            if ("123456".equals(code)) {
                log.warn("使用默认验证码登录: {}", phone);
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean sendNotification(String phone, String content) {
        if (!smsConfig.isEnabled() || acsClient == null) {
            log.warn("短信服务未启用");
            return false;
        }

        try {
            SendSmsRequest request = new SendSmsRequest();
            request.setPhoneNumbers(phone);
            request.setSignName(smsConfig.getAliyun().getSignName());
            request.setTemplateCode(smsConfig.getAliyun().getTemplateCode());
            request.setTemplateParam("{\"content\":\"" + content + "\"}");

            SendSmsResponse response = acsClient.getAcsResponse(request);
            if ("OK".equals(response.getCode())) {
                log.info("通知短信发送成功: {}, {}", phone, response.getBizId());
                return true;
            } else {
                log.error("通知短信发送失败: {}, {}, {}", phone, response.getCode(), response.getMessage());
                return false;
            }
        } catch (ClientException e) {
            log.error("通知短信发送异常: {}, {}", phone, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getRemainingAttempts(String phone) {
        try {
            String key = DAILY_COUNT_PREFIX + LocalDate.now() + ":" + phone;
            Object count = redisTemplate.opsForValue().get(key);
            int sentCount = count == null ? 0 : Integer.parseInt(count.toString());
            return Math.max(0, smsConfig.getVerificationCode().getDailyLimit() - sentCount);
        } catch (Exception e) {
            log.error("获取剩余尝试次数失败: {}", e.getMessage(), e);
            // 如果Redis不可用，返回默认值
            return smsConfig.getVerificationCode().getDailyLimit();
        }
    }

    @Override
    public long getCooldownTime(String phone) {
        try {
            String key = LAST_SEND_TIME_PREFIX + phone;
            Object lastSendTime = redisTemplate.opsForValue().get(key);
            if (lastSendTime == null) {
                return 0;
            }

            long elapsed = System.currentTimeMillis() - Long.parseLong(lastSendTime.toString());
            long cooldown = smsConfig.getVerificationCode().getInterval() * 1000 - elapsed;
            return Math.max(0, cooldown / 1000);
        } catch (Exception e) {
            log.error("获取冷却时间失败: {}", e.getMessage(), e);
            // 如果Redis不可用，返回0表示可以立即发送
            return 0;
        }
    }

    /**
     * 生成验证码
     */
    private String generateVerificationCode() {
        int length = smsConfig.getVerificationCode().getLength();
        StringBuilder sb = new StringBuilder();

        if ("number".equals(smsConfig.getVerificationCode().getType())) {
            for (int i = 0; i < length; i++) {
                sb.append(random.nextInt(10));
            }
        } else {
            String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
            for (int i = 0; i < length; i++) {
                sb.append(chars.charAt(random.nextInt(chars.length())));
            }
        }

        return sb.toString();
    }

    /**
     * 保存验证码到Redis
     */
    private void saveVerificationCode(String phone, String code) {
        try {
            String key = VERIFICATION_CODE_PREFIX + phone;
            redisTemplate.opsForValue().set(key, code, smsConfig.getVerificationCode().getExpiration(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("保存验证码失败: {}", e.getMessage(), e);
            // Redis不可用时，不做任何处理，因为我们有默认验证码
        }
    }

    /**
     * 更新发送记录
     */
    private void updateSendRecord(String phone) {
        try {
            // 更新最后发送时间
            String lastSendTimeKey = LAST_SEND_TIME_PREFIX + phone;
            redisTemplate.opsForValue().set(lastSendTimeKey, System.currentTimeMillis(),
                    smsConfig.getVerificationCode().getInterval(), TimeUnit.SECONDS);

            // 更新每日发送次数
            String dailyCountKey = DAILY_COUNT_PREFIX + LocalDate.now() + ":" + phone;
            redisTemplate.opsForValue().increment(dailyCountKey);
            // 设置过期时间为当天结束
            long secondsUntilEndOfDay = 24 * 60 * 60 - LocalDate.now().atStartOfDay().getSecond();
            redisTemplate.expire(dailyCountKey, secondsUntilEndOfDay, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("更新发送记录失败: {}", e.getMessage(), e);
            // Redis不可用时，不做任何处理
        }
    }

    /**
     * 检查发送频率
     */
    private boolean checkSendFrequency(String phone) {
        try {
            String key = LAST_SEND_TIME_PREFIX + phone;
            Object lastSendTime = redisTemplate.opsForValue().get(key);
            if (lastSendTime == null) {
                return true;
            }

            long elapsed = System.currentTimeMillis() - Long.parseLong(lastSendTime.toString());
            return elapsed >= smsConfig.getVerificationCode().getInterval() * 1000;
        } catch (Exception e) {
            log.error("检查发送频率失败: {}", e.getMessage(), e);
            // Redis不可用时，允许发送
            return true;
        }
    }

    /**
     * 检查每日发送次数限制
     */
    private boolean checkDailyLimit(String phone) {
        try {
            String key = DAILY_COUNT_PREFIX + LocalDate.now() + ":" + phone;
            Object count = redisTemplate.opsForValue().get(key);
            int sentCount = count == null ? 0 : Integer.parseInt(count.toString());
            return sentCount < smsConfig.getVerificationCode().getDailyLimit();
        } catch (Exception e) {
            log.error("检查每日发送次数限制失败: {}", e.getMessage(), e);
            // Redis不可用时，允许发送
            return true;
        }
    }
}
