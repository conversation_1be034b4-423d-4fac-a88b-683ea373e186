import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, logout, getAdminInfo } from '../api/user'

export const useUserStore = defineStore('admin-user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('admin_token') || '')
  const userInfo = ref<any>(null)
  const roles = ref<string[]>([])
  const permissions = ref<string[]>([])

  // 获取管理员信息
  const fetchUserInfo = async () => {
    if (!token.value) {
      throw new Error('未登录或token不存在')
    }

    try {
      const { data } = await getAdminInfo()
      if (!data) {
        throw new Error('获取管理员信息失败，返回数据为空')
      }
      userInfo.value = data
      roles.value = data.roles || []
      permissions.value = data.permissions || []
      return data
    } catch (error) {
      console.error('获取管理员信息失败', error)
      // 重新抛出错误，让调用者知道请求失败
      throw error
    }
  }

  // 登录
  const loginAction = async (loginData: any) => {
    try {
      const { data } = await login(loginData)
      token.value = data.token
      localStorage.setItem('admin_token', data.token)
      await fetchUserInfo()
      return true
    } catch (error) {
      console.error('登录失败', error)
      return false
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      await logout()
      resetState()
      return true
    } catch (error) {
      console.error('登出失败', error)
      return false
    }
  }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = null
    roles.value = []
    permissions.value = []
    localStorage.removeItem('admin_token')
  }

  // 判断是否有权限
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }

  return {
    token,
    userInfo,
    roles,
    permissions,
    loginAction,
    logoutAction,
    fetchUserInfo,
    resetState,
    hasPermission
  }
})
