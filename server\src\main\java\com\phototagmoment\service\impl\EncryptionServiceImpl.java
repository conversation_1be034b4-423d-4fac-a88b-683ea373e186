package com.phototagmoment.service.impl;

import com.phototagmoment.config.EncryptionConfig;
import com.phototagmoment.service.EncryptionService;
import com.phototagmoment.util.EncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;

/**
 * 加密服务实现类
 */
@Slf4j
@Service
public class EncryptionServiceImpl implements EncryptionService {

    private static final int GCM_TAG_LENGTH = 128;
    private static final int GCM_IV_LENGTH = 12;

    @Autowired
    private EncryptionConfig encryptionConfig;

    @Override
    public String encryptUserData(String fieldName, String value) {
        if (!encryptionConfig.isEnabled() || !isEncryptedField(fieldName) || value == null || value.isEmpty()) {
            return value;
        }

        // 检查加密密钥是否为空
        String userDataKey = encryptionConfig.getUserDataKey();
        if (userDataKey == null || userDataKey.isEmpty()) {
            log.warn("用户数据加密密钥为空，跳过加密");
            return value;
        }

        try {
            // 根据字段名称处理不同的加密策略
            if ("phone".equals(fieldName)) {
                // 手机号特殊处理，避免加密后长度过长
                // 如果已经是掩码形式（包含*），则不再加密
                if (value.contains("*")) {
                    return value;
                }

                // 如果长度超过20，截断处理
                String encrypted = EncryptionUtil.encryptAES(value, userDataKey);
                if (encrypted.length() > 20) {
                    log.warn("加密后的手机号长度超过数据库限制，将进行截断处理");
                    return encrypted.substring(0, 20);
                }
                return encrypted;
            } else {
                return EncryptionUtil.encryptAES(value, userDataKey);
            }
        } catch (Exception e) {
            log.error("加密用户数据失败: {}", e.getMessage(), e);
            return value;
        }
    }

    @Override
    public String decryptUserData(String fieldName, String value) {
        // 快速检查：如果加密未启用、字段不需要加密、值为空，或者值包含掩码字符，则直接返回原值
        if (!encryptionConfig.isEnabled() || !isEncryptedField(fieldName) ||
            value == null || value.isEmpty() || value.contains("*") || value.contains("@")) {
            return value;
        }

        // 检查加密密钥是否为空
        String userDataKey = encryptionConfig.getUserDataKey();
        if (userDataKey == null || userDataKey.isEmpty()) {
            log.warn("用户数据加密密钥为空，跳过解密");
            return value;
        }

        // 检查是否是有效的Base64编码
        if (!isValidBase64(value)) {
            return value;
        }

        try {
            // 尝试解密，如果失败则返回原值
            String decrypted = EncryptionUtil.decryptAES(value, userDataKey);

            // 对于邮箱字段，验证解密后的值是否是有效的邮箱格式
            if ("email".equals(fieldName) && !isValidEmail(decrypted)) {
                log.warn("解密后的邮箱格式无效: {}", decrypted);
                return value;
            }

            return decrypted;
        } catch (Exception e) {
            // 解密失败，记录日志并返回原值
            log.warn("解密字段 {} 失败: {}", fieldName, e.getMessage());
            return value;
        }
    }

    /**
     * 检查字符串是否是有效的邮箱格式
     *
     * @param email 待检查的邮箱
     * @return 是否是有效的邮箱
     */
    private boolean isValidEmail(String email) {
        if (email == null || email.isEmpty()) {
            return false;
        }

        // 简单的邮箱格式验证
        return email.matches("^[A-Za-z0-9+_.-]+@(.+)$");
    }

    /**
     * 检查字符串是否是有效的Base64编码
     *
     * @param str 待检查的字符串
     * @return 是否是有效的Base64编码
     */
    private boolean isValidBase64(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        try {
            // 尝试解码，如果成功则可能是Base64
            Base64.getDecoder().decode(str);

            // 检查是否符合Base64格式
            return str.matches("^[A-Za-z0-9+/=]+$");
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    public boolean encryptPhoto(InputStream input, OutputStream output) {
        if (!encryptionConfig.isEnabled() || input == null || output == null) {
            return false;
        }

        try {
            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            SecureRandom random = new SecureRandom();
            random.nextBytes(iv);

            // 写入IV
            output.write(iv);

            // 初始化加密器
            byte[] keyBytes = Base64.getDecoder().decode(encryptionConfig.getPhotoKey());
            SecretKey secretKey = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);

            // 加密数据
            try (CipherOutputStream cos = new CipherOutputStream(output, cipher)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    cos.write(buffer, 0, bytesRead);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("加密照片数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean decryptPhoto(InputStream input, OutputStream output) {
        if (!encryptionConfig.isEnabled() || input == null || output == null) {
            return false;
        }

        try {
            // 读取IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            int bytesRead = input.read(iv);
            if (bytesRead != GCM_IV_LENGTH) {
                throw new IOException("无法读取IV");
            }

            // 初始化解密器
            byte[] keyBytes = Base64.getDecoder().decode(encryptionConfig.getPhotoKey());
            SecretKey secretKey = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);

            // 解密数据
            try (CipherInputStream cis = new CipherInputStream(input, cipher)) {
                byte[] buffer = new byte[8192];
                while ((bytesRead = cis.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("解密照片数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String encryptWithPublicKey(String data) {
        if (!encryptionConfig.isEnabled() || data == null || data.isEmpty()) {
            return data;
        }

        // 检查RSA公钥是否为空
        String rsaPublicKey = encryptionConfig.getRsaPublicKey();
        if (rsaPublicKey == null || rsaPublicKey.isEmpty()) {
            log.warn("RSA公钥为空，跳过加密");
            return data;
        }

        try {
            return EncryptionUtil.encryptRSA(data, rsaPublicKey);
        } catch (Exception e) {
            log.error("RSA加密失败: {}", e.getMessage(), e);
            return data;
        }
    }

    @Override
    public String decryptWithPrivateKey(String data) {
        if (!encryptionConfig.isEnabled() || data == null || data.isEmpty()) {
            return data;
        }

        // 检查RSA私钥是否为空
        String rsaPrivateKey = encryptionConfig.getRsaPrivateKey();
        if (rsaPrivateKey == null || rsaPrivateKey.isEmpty()) {
            log.warn("RSA私钥为空，跳过解密");
            return data;
        }

        try {
            return EncryptionUtil.decryptRSA(data, rsaPrivateKey);
        } catch (Exception e) {
            log.error("RSA解密失败: {}", e.getMessage(), e);
            return data;
        }
    }

    @Override
    public boolean isEncryptedField(String fieldName) {
        if (!StringUtils.hasText(fieldName)) {
            return false;
        }
        return Arrays.asList(encryptionConfig.getEncryptedUserFields()).contains(fieldName);
    }

    @Override
    public String getRsaPublicKey() {
        return encryptionConfig.getRsaPublicKey();
    }
}
