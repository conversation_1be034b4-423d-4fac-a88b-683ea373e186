package com.phototagmoment.service.impl;

import com.phototagmoment.service.ImageModerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * 默认图像审核服务实现类
 * 当没有其他ImageModerationService实现时使用
 * 默认通过所有审核
 */
@Slf4j
@Service
@ConditionalOnMissingBean(name = "baiduImageModerationServiceImpl")
public class DefaultImageModerationServiceImpl implements ImageModerationService {

    private String failReason;

    @Override
    public boolean moderateImage(MultipartFile file) {
        log.info("使用默认图像审核服务，自动通过审核");
        return true;
    }

    @Override
    public boolean moderateImage(InputStream inputStream, String contentType) {
        log.info("使用默认图像审核服务，自动通过审核");
        return true;
    }

    @Override
    public boolean moderateImageByUrl(String imageUrl) {
        log.info("使用默认图像审核服务，自动通过审核");
        return true;
    }

    @Override
    public String getFailReason() {
        return failReason;
    }
}
