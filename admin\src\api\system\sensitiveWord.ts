import request from '@/utils/request'

/**
 * 获取敏感词列表（分页）
 * @param params 查询参数
 */
export function getSensitiveWordList(params?: any) {
  return request({
    url: '/admin/system/sensitive-word/page',
    method: 'get',
    params
  })
}

/**
 * 获取敏感词详情
 * @param id 敏感词ID
 */
export function getSensitiveWordDetail(id: number) {
  return request({
    url: `/admin/system/sensitive-word/${id}`,
    method: 'get'
  })
}

/**
 * 创建敏感词
 * @param data 敏感词信息
 */
export function createSensitiveWord(data: any) {
  return request({
    url: '/admin/system/sensitive-word',
    method: 'post',
    data
  })
}

/**
 * 更新敏感词
 * @param id 敏感词ID
 * @param data 敏感词信息
 */
export function updateSensitiveWord(id: number, data: any) {
  // 确保ID字段存在于数据中
  const updatedData = { ...data, id }
  return request({
    url: '/admin/system/sensitive-word',
    method: 'put',
    data: updatedData
  })
}

/**
 * 删除敏感词
 * @param id 敏感词ID
 */
export function deleteSensitiveWord(id: number) {
  return request({
    url: `/admin/system/sensitive-word/${id}`,
    method: 'delete'
  })
}

/**
 * 更新敏感词状态
 * @param id 敏感词ID
 * @param status 状态：false-禁用，true-启用
 */
export function updateSensitiveWordStatus(id: number, status: boolean) {
  return request({
    url: `/admin/system/sensitive-word/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 获取敏感词类型列表
 */
export function getSensitiveWordTypes() {
  return request({
    url: '/admin/system/sensitive-word/types',
    method: 'get'
  })
}

/**
 * 获取敏感词统计信息
 */
export function getSensitiveWordStats() {
  return request({
    url: '/admin/system/sensitive-word/stats',
    method: 'get'
  })
}

/**
 * 测试敏感词过滤
 * @param content 待测试内容
 */
export function testSensitiveWordFilter(content: string) {
  return request({
    url: '/admin/system/sensitive-word/test',
    method: 'post',
    data: { content }
  })
}

/**
 * 导出敏感词
 * @param type 类型
 * @param level 级别
 */
export function exportSensitiveWords(type?: string, level?: number) {
  return request({
    url: '/admin/system/sensitive-word/export',
    method: 'get',
    params: { type, level },
    responseType: 'blob'
  })
}

/**
 * 从文件批量导入敏感词
 * @param file 敏感词文件
 */
export function importSensitiveWords(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/admin/system/sensitive-word/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 从目录批量导入敏感词
 * @param directoryPath 目录路径，默认为Sensitive-lexicon
 */
export function importSensitiveWordsFromDirectory(directoryPath?: string) {
  return request({
    url: '/admin/system/sensitive-word/import/directory',
    method: 'post',
    params: { directoryPath }
  })
}
