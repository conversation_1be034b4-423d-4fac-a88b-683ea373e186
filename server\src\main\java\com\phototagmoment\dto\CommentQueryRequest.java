package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 评论查询请求DTO
 */
@Data
@Schema(description = "评论查询请求")
public class CommentQueryRequest {

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;

    @Schema(description = "评论内容关键词")
    private String content;

    @Schema(description = "用户昵称")
    private String username;

    @Schema(description = "状态：1-正常，0-已删除")
    private Integer status;

    @Schema(description = "照片ID")
    private Long photoId;

    @Schema(description = "开始时间", example = "2023-01-01 00:00:00")
    private String startTime;

    @Schema(description = "结束时间", example = "2023-12-31 23:59:59")
    private String endTime;

    @Schema(description = "排序字段", example = "createdAt")
    private String sortField = "createdAt";

    @Schema(description = "排序方向", example = "desc")
    private String sortOrder = "desc";
}
