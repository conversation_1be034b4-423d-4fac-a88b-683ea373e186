# 简化的权限测试脚本

Write-Host "=== 权限测试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:8081/api"

# 测试登录
Write-Host "`n--- 测试登录 ---" -ForegroundColor Yellow
$loginData = '{"username":"admin_test","password":"123456"}'

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/admin/system/login" -Method POST -Body $loginData -ContentType "application/json"
    
    if ($loginResponse.code -eq 200) {
        Write-Host "✅ 登录成功" -ForegroundColor Green
        $token = $loginResponse.data.token
        Write-Host "Token: $($token.Substring(0, 20))..." -ForegroundColor Cyan
        
        # 测试获取用户信息
        Write-Host "`n--- 测试获取用户信息 ---" -ForegroundColor Yellow
        $headers = @{
            Authorization = "Bearer $token"
        }
        
        $userInfoResponse = Invoke-RestMethod -Uri "$baseUrl/admin/system/info" -Method GET -Headers $headers
        
        if ($userInfoResponse.code -eq 200) {
            Write-Host "✅ 获取用户信息成功" -ForegroundColor Green
            $userInfo = $userInfoResponse.data
            
            Write-Host "用户名: $($userInfo.username)" -ForegroundColor Cyan
            Write-Host "姓名: $($userInfo.name)" -ForegroundColor Cyan
            
            if ($userInfo.roles) {
                Write-Host "角色: $($userInfo.roles -join ', ')" -ForegroundColor Cyan
            } else {
                Write-Host "角色: 无" -ForegroundColor Red
            }
            
            if ($userInfo.permissions) {
                Write-Host "权限: $($userInfo.permissions -join ', ')" -ForegroundColor Cyan
            } else {
                Write-Host "权限: 无" -ForegroundColor Red
            }
            
            # 测试配置列表
            Write-Host "`n--- 测试配置列表 ---" -ForegroundColor Yellow
            $configResponse = Invoke-RestMethod -Uri "$baseUrl/admin/file-upload-config/list?page=1&size=5" -Method GET -Headers $headers
            
            if ($configResponse.code -eq 200) {
                Write-Host "✅ 配置列表获取成功，共 $($configResponse.data.total) 个配置" -ForegroundColor Green
                
                if ($configResponse.data.records.Count -gt 0) {
                    $config = $configResponse.data.records[0]
                    Write-Host "测试配置: $($config.configName) (ID: $($config.id), 启用: $($config.enabled))" -ForegroundColor Cyan
                }
            } else {
                Write-Host "❌ 配置列表获取失败: $($configResponse.message)" -ForegroundColor Red
            }
            
        } else {
            Write-Host "❌ 获取用户信息失败: $($userInfoResponse.message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ 登录失败: $($loginResponse.message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
