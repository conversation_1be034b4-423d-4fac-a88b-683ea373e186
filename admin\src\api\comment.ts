import request from '@/utils/request'

// 评论查询参数接口
export interface CommentQueryParams {
  page?: number
  size?: number
  content?: string
  username?: string
  status?: number
  photoId?: number
  startTime?: string
  endTime?: string
}

// 评论数据接口
export interface CommentData {
  id: number
  content: string
  likeCount: number
  replyCount: number
  status: number
  ip?: string
  createdAt: string
  updatedAt?: string
  user: {
    id: number
    nickname: string
    avatar: string
  }
  photo: {
    id: number
    title: string
    url: string
    thumbnailUrl: string
    createdAt: string
    user: {
      id: number
      nickname: string
    }
  }
  replies?: CommentData[]
}

// 评论列表响应接口
export interface CommentListResponse {
  records: CommentData[]
  total: number
  size: number
  current: number
  pages: number
}

// 评论统计接口
export interface CommentStatistics {
  totalComments: number
  normalComments: number
  deletedComments: number
  todayComments: number
  weekComments: number
  monthComments: number
}

/**
 * 获取评论列表
 * @param params 查询参数
 */
export function getCommentList(params: CommentQueryParams) {
  return request<CommentListResponse>({
    url: '/admin/comment/list',
    method: 'get',
    params
  })
}

/**
 * 获取评论详情
 * @param commentId 评论ID
 */
export function getCommentDetail(commentId: number) {
  return request<CommentData>({
    url: `/admin/comment/${commentId}`,
    method: 'get'
  })
}

/**
 * 修改评论状态
 * @param commentId 评论ID
 * @param status 状态：1-正常，0-删除
 * @param reason 操作原因
 */
export function updateCommentStatus(commentId: number, status: number, reason?: string) {
  return request<boolean>({
    url: `/admin/comment/${commentId}/status`,
    method: 'put',
    params: {
      status,
      reason
    }
  })
}

/**
 * 批量修改评论状态
 * @param commentIds 评论ID列表
 * @param status 状态：1-正常，0-删除
 * @param reason 操作原因
 */
export function batchUpdateCommentStatus(commentIds: number[], status: number, reason?: string) {
  return request<boolean>({
    url: '/admin/comment/batch/status',
    method: 'put',
    params: {
      commentIds: commentIds.join(','),
      status,
      reason
    }
  })
}

/**
 * 修改回复状态
 * @param commentId 评论ID
 * @param replyId 回复ID
 * @param status 状态：1-正常，0-删除
 * @param reason 操作原因
 */
export function updateReplyStatus(commentId: number, replyId: number, status: number, reason?: string) {
  return request<boolean>({
    url: `/admin/comment/${commentId}/reply/${replyId}/status`,
    method: 'put',
    params: {
      status,
      reason
    }
  })
}

/**
 * 获取评论统计信息
 */
export function getCommentStatistics() {
  return request<CommentStatistics>({
    url: '/admin/comment/statistics',
    method: 'get'
  })
}

/**
 * 永久删除评论
 * @param commentId 评论ID
 * @param reason 删除原因
 */
export function deleteCommentPermanently(commentId: number, reason?: string) {
  return request<boolean>({
    url: `/admin/comment/${commentId}`,
    method: 'delete',
    params: {
      reason
    }
  })
}
