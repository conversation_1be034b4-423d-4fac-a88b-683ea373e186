/**
 * 时间格式化工具函数
 */

/**
 * 格式化相对时间
 * @param date 日期对象
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 转换为秒
  const seconds = Math.floor(diff / 1000)
  
  if (seconds < 60) {
    return '刚刚'
  }
  
  // 转换为分钟
  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) {
    return `${minutes}分钟前`
  }
  
  // 转换为小时
  const hours = Math.floor(minutes / 60)
  if (hours < 24) {
    return `${hours}小时前`
  }
  
  // 转换为天
  const days = Math.floor(hours / 24)
  if (days < 7) {
    return `${days}天前`
  }
  
  // 转换为周
  const weeks = Math.floor(days / 7)
  if (weeks < 4) {
    return `${weeks}周前`
  }
  
  // 转换为月
  const months = Math.floor(days / 30)
  if (months < 12) {
    return `${months}个月前`
  }
  
  // 转换为年
  const years = Math.floor(days / 365)
  return `${years}年前`
}

/**
 * 格式化日期时间
 * @param date 日期对象或字符串
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(date: Date | string, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const d = typeof date === 'string' ? new Date(date) : date
  
  if (isNaN(d.getTime())) {
    return ''
  }
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期
 * @param date 日期对象或字符串
 * @returns 格式化后的日期字符串 (YYYY-MM-DD)
 */
export function formatDate(date: Date | string): string {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间
 * @param date 日期对象或字符串
 * @returns 格式化后的时间字符串 (HH:mm:ss)
 */
export function formatTime(date: Date | string): string {
  return formatDateTime(date, 'HH:mm:ss')
}

/**
 * 判断是否为今天
 * @param date 日期对象或字符串
 * @returns 是否为今天
 */
export function isToday(date: Date | string): boolean {
  const d = typeof date === 'string' ? new Date(date) : date
  const today = new Date()
  
  return d.getFullYear() === today.getFullYear() &&
         d.getMonth() === today.getMonth() &&
         d.getDate() === today.getDate()
}

/**
 * 判断是否为昨天
 * @param date 日期对象或字符串
 * @returns 是否为昨天
 */
export function isYesterday(date: Date | string): boolean {
  const d = typeof date === 'string' ? new Date(date) : date
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  
  return d.getFullYear() === yesterday.getFullYear() &&
         d.getMonth() === yesterday.getMonth() &&
         d.getDate() === yesterday.getDate()
}

/**
 * 智能格式化时间
 * 今天显示时间，昨天显示"昨天"，其他显示日期
 * @param date 日期对象或字符串
 * @returns 智能格式化后的时间字符串
 */
export function smartFormatTime(date: Date | string): string {
  if (isToday(date)) {
    return formatTime(date)
  } else if (isYesterday(date)) {
    return '昨天'
  } else {
    return formatDate(date)
  }
}

/**
 * 获取时间戳
 * @param date 日期对象或字符串，默认为当前时间
 * @returns 时间戳（毫秒）
 */
export function getTimestamp(date?: Date | string): number {
  if (!date) {
    return Date.now()
  }
  
  const d = typeof date === 'string' ? new Date(date) : date
  return d.getTime()
}

/**
 * 从时间戳创建日期对象
 * @param timestamp 时间戳（毫秒）
 * @returns 日期对象
 */
export function fromTimestamp(timestamp: number): Date {
  return new Date(timestamp)
}
