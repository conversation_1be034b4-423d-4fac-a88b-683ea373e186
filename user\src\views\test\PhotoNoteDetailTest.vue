<template>
  <div class="test-page">
    <h1>照片笔记详情API测试</h1>

    <div class="test-section">
      <h2>测试参数</h2>
      <input v-model="testNoteId" placeholder="输入照片笔记ID" />
      <button @click="testApi">测试API</button>
    </div>

    <div class="test-section">
      <h2>API响应</h2>
      <div class="response-box">
        <pre>{{ responseText }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>错误信息</h2>
      <div class="error-box">
        <pre>{{ errorText }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>网络状态</h2>
      <div>
        <p>当前时间: {{ currentTime }}</p>
        <p>后端状态: <span :class="backendStatus">{{ backendStatusText }}</span></p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getPhotoNoteDetail } from '@/api/photo'

const testNoteId = ref('31')
const responseText = ref('')
const errorText = ref('')
const currentTime = ref('')
const backendStatus = ref('unknown')
const backendStatusText = ref('检测中...')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const checkBackendStatus = async () => {
  try {
    // 先测试基本连接
    const response = await fetch('http://localhost:8081/api/photo-notes/31', {
      method: 'GET',
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    if (response.ok) {
      backendStatus.value = 'online'
      backendStatusText.value = '在线'
    } else {
      backendStatus.value = 'error'
      backendStatusText.value = `错误 ${response.status}`
    }
  } catch (error) {
    backendStatus.value = 'offline'
    backendStatusText.value = '离线'
  }
}

const testApi = async () => {
  try {
    responseText.value = '请求中...'
    errorText.value = ''

    console.log('开始测试API，照片笔记ID:', testNoteId.value)

    const response = await getPhotoNoteDetail(Number(testNoteId.value))

    responseText.value = JSON.stringify(response, null, 2)
    console.log('API测试成功:', response)

  } catch (error) {
    console.error('API测试失败:', error)
    errorText.value = JSON.stringify({
      message: error.message,
      stack: error.stack,
      response: error.response?.data,
      status: error.response?.status
    }, null, 2)
    responseText.value = '请求失败'
  }
}

onMounted(() => {
  updateTime()
  checkBackendStatus()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-section h2 {
  margin-top: 0;
  color: #333;
}

input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  width: 200px;
}

button {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}

.response-box, .error-box {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.error-box {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.online {
  color: green;
  font-weight: bold;
}

.offline {
  color: red;
  font-weight: bold;
}

.error {
  color: orange;
  font-weight: bold;
}

.unknown {
  color: gray;
}
</style>
