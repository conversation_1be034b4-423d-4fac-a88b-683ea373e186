<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.PhotoNoteMapper">

    <!-- 照片笔记DTO结果映射 -->
    <resultMap id="PhotoNoteDTOMap" type="com.phototagmoment.dto.PhotoNoteDTO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="photo_count" property="photoCount"/>
        <result column="view_count" property="viewCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="comment_count" property="commentCount"/>
        <result column="share_count" property="shareCount"/>
        <result column="visibility" property="visibility"/>
        <result column="allow_comment" property="allowComment"/>
        <result column="status" property="status"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="location" property="location"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="is_liked" property="isLiked"/>
        <result column="is_collected" property="isCollected"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseSelectColumns">
        pn.id,
        pn.user_id,
        u.nickname,
        u.avatar,
        pn.title,
        pn.content,
        pn.photo_count,
        pn.view_count,
        pn.like_count,
        pn.comment_count,
        pn.share_count,
        pn.visibility,
        pn.allow_comment,
        pn.status,
        pn.reject_reason,
        pn.location,
        pn.longitude,
        pn.latitude,
        pn.created_at,
        pn.updated_at
    </sql>

    <!-- 点赞和收藏状态查询 -->
    <sql id="LikeAndCollectStatus">
        <if test="currentUserId != null">
            ,(SELECT COUNT(*) > 0 FROM ptm_photo_note_like pnl WHERE pnl.note_id = pn.id AND pnl.user_id = #{currentUserId}) as is_liked,
            (SELECT COUNT(*) > 0 FROM ptm_photo_note_collection pnc WHERE pnc.note_id = pn.id AND pnc.user_id = #{currentUserId}) as is_collected
        </if>
        <if test="currentUserId == null">
            ,false as is_liked,
            false as is_collected
        </if>
    </sql>

    <!-- 管理端状态查询（无用户相关状态） -->
    <sql id="AdminStatus">
        ,false as is_liked,
        false as is_collected
    </sql>

    <!-- 分页查询照片笔记列表 -->
    <select id="selectPhotoNotePage" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        <where>
            pn.is_deleted = 0
            <if test="userId != null">
                AND pn.user_id = #{userId}
            </if>
            <if test="status != null">
                AND pn.status = #{status}
            </if>
            <if test="currentUserId != null and userId != currentUserId">
                AND (pn.visibility = 1 OR (pn.visibility = 2 AND EXISTS(
                    SELECT 1 FROM ptm_follow f WHERE f.follower_id = #{currentUserId} AND f.followed_id = pn.user_id
                )))
            </if>
        </where>
        ORDER BY pn.created_at DESC
    </select>

    <!-- 管理端分页查询照片笔记列表（无可见性限制） -->
    <select id="selectAdminPhotoNotePage" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="AdminStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        <where>
            pn.is_deleted = 0
            <if test="userId != null">
                AND pn.user_id = #{userId}
            </if>
            <if test="status != null">
                AND pn.status = #{status}
            </if>
        </where>
        ORDER BY pn.created_at DESC
    </select>

    <!-- 查询照片笔记详情 -->
    <select id="selectPhotoNoteDetail" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        WHERE pn.id = #{noteId} AND pn.is_deleted = 0
    </select>

    <!-- 根据标签查询照片笔记 -->
    <select id="selectPhotoNotesByTag" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        INNER JOIN ptm_photo_note_tag pnt ON pn.id = pnt.note_id
        WHERE pnt.tag_name = #{tagName}
        AND pn.status = 1
        AND pn.is_deleted = 0
        AND (pn.visibility = 1 OR (pn.visibility = 2 AND #{currentUserId} IS NOT NULL AND EXISTS(
            SELECT 1 FROM ptm_follow f WHERE f.follower_id = #{currentUserId} AND f.followed_id = pn.user_id
        )))
        <choose>
            <when test="sortType == 'hot'">
                ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC
            </when>
            <otherwise>
                ORDER BY pn.created_at DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据多个标签查询照片笔记 -->
    <select id="selectPhotoNotesByTags" resultMap="PhotoNoteDTOMap">
        SELECT DISTINCT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        INNER JOIN ptm_photo_note_tag pnt ON pn.id = pnt.note_id
        WHERE pnt.tag_name IN
        <foreach collection="tags" item="tag" open="(" separator="," close=")">
            #{tag}
        </foreach>
        AND pn.status = 1
        AND pn.is_deleted = 0
        AND (pn.visibility = 1 OR (pn.visibility = 2 AND #{currentUserId} IS NOT NULL AND EXISTS(
            SELECT 1 FROM ptm_follow f WHERE f.follower_id = #{currentUserId} AND f.followed_id = pn.user_id
        )))
        ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC
    </select>

    <!-- 查询用户的照片笔记 -->
    <select id="selectUserPhotoNotes" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        <where>
            pn.user_id = #{userId}
            AND pn.is_deleted = 0
            AND pn.status = 1
            <if test="visibility != null">
                AND pn.visibility = #{visibility}
            </if>
            <if test="currentUserId != null and userId != currentUserId">
                AND (pn.visibility = 1 OR (pn.visibility = 2 AND EXISTS(
                    SELECT 1 FROM ptm_follow f WHERE f.follower_id = #{currentUserId} AND f.followed_id = pn.user_id
                )))
            </if>
        </where>
        ORDER BY pn.created_at DESC
    </select>

    <!-- 查询热门照片笔记 -->
    <select id="selectHotPhotoNotes" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        WHERE pn.status = 1
        AND pn.is_deleted = 0
        AND pn.visibility = 1
        <if test="days != null and days > 0">
            AND pn.created_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        ORDER BY (pn.like_count * 0.5 + pn.view_count * 0.1 + pn.comment_count * 0.3) DESC, pn.created_at DESC
    </select>

    <!-- 搜索照片笔记 -->
    <select id="searchPhotoNotes" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        WHERE pn.status = 1
        AND pn.is_deleted = 0
        AND pn.visibility = 1
        AND (pn.title LIKE CONCAT('%', #{keyword}, '%') OR pn.content LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY pn.created_at DESC
    </select>

    <!-- 查询关注用户的照片笔记 -->
    <select id="selectFollowingPhotoNotes" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        WHERE pn.status = 1
        AND pn.is_deleted = 0
        AND pn.visibility IN (1, 2)
        AND pn.user_id IN
        <foreach collection="followingIds" item="followingId" open="(" separator="," close=")">
            #{followingId}
        </foreach>
        <if test="lastId != null">
            AND pn.id &lt; #{lastId}
        </if>
        ORDER BY pn.created_at DESC
    </select>

    <!-- 查询最新照片笔记 -->
    <select id="selectLatestPhotoNotes" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        WHERE pn.status = 1
        AND pn.is_deleted = 0
        AND pn.visibility = 1
        <if test="lastId != null">
            AND pn.id &lt; #{lastId}
        </if>
        ORDER BY pn.created_at DESC
    </select>

    <!-- 更新推荐照片笔记查询 -->
    <select id="selectRecommendedPhotoNotes" resultMap="PhotoNoteDTOMap">
        SELECT
        <include refid="BaseSelectColumns"/>
        <include refid="LikeAndCollectStatus"/>,
        (
            -- 计算推荐分数
            CASE
                -- 关注用户的内容权重更高
                WHEN pn.user_id IN
                <foreach collection="followingIds" item="followingId" open="(" separator="," close=")">
                    #{followingId}
                </foreach>
                THEN 0.3
                ELSE 0.0
            END
            +
            CASE
                -- 兴趣标签匹配权重
                WHEN EXISTS (
                    SELECT 1 FROM ptm_photo_note_tag pnt
                    WHERE pnt.note_id = pn.id
                    AND pnt.tag_name IN
                    <foreach collection="interestTags" item="tag" open="(" separator="," close=")">
                        #{tag}
                    </foreach>
                )
                THEN 0.2
                ELSE 0.0
            END
            +
            -- 热度分数
            (pn.like_count * 0.1 + pn.view_count * 0.05 + pn.comment_count * 0.15)
            +
            -- 时间衰减分数
            (1.0 / (1.0 + TIMESTAMPDIFF(HOUR, pn.created_at, NOW()) / 24.0))
        ) as recommend_score
        FROM ptm_photo_note pn
        LEFT JOIN ptm_user u ON pn.user_id = u.id
        WHERE pn.status = 1
        AND pn.is_deleted = 0
        AND pn.visibility = 1
        AND pn.user_id != #{userId}
        <if test="lastId != null">
            AND pn.id &lt; #{lastId}
        </if>
        ORDER BY recommend_score DESC, pn.created_at DESC
    </select>

</mapper>
