-- 创建用户兴趣标签表（如果不存在）
CREATE TABLE IF NOT EXISTS ptm_user_interest (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    interest_score DOUBLE NOT NULL DEFAULT 1.0 COMMENT '兴趣分数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_tag_name (tag_name),
    INDEX idx_interest_score (interest_score),
    UNIQUE KEY uk_user_tag (user_id, tag_name) COMMENT '用户和标签的唯一组合'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户兴趣标签表';

-- 添加外键约束
ALTER TABLE ptm_user_interest 
ADD CONSTRAINT fk_user_interest_user_id 
FOREIGN KEY (user_id) REFERENCES ptm_user(id) ON DELETE CASCADE;
