package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.CommentLike;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 评论点赞Mapper接口
 */
@Mapper
@Repository
public interface CommentLikeMapper extends BaseMapper<CommentLike> {

    /**
     * 检查用户是否点赞了评论
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 是否点赞
     */
    @Select("SELECT COUNT(*) FROM ptm_comment_like WHERE comment_id = #{commentId} AND user_id = #{userId}")
    int checkLiked(@Param("commentId") Long commentId, @Param("userId") Long userId);

    /**
     * 获取评论的点赞用户ID列表
     *
     * @param commentId 评论ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM ptm_comment_like WHERE comment_id = #{commentId}")
    List<Long> getLikedUserIds(@Param("commentId") Long commentId);

    /**
     * 统计评论的点赞数
     *
     * @param commentId 评论ID
     * @return 点赞数
     */
    @Select("SELECT COUNT(*) FROM ptm_comment_like WHERE comment_id = #{commentId}")
    int countByCommentId(@Param("commentId") Long commentId);
}
