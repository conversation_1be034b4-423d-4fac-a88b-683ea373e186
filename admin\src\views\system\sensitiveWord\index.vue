<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="queryParams.keyword"
        placeholder="请输入敏感词"
        style="width: 200px;"
        class="filter-item"
        clearable
        @keyup.enter="handleSearch"
      />
      <el-select
        v-model="queryParams.type"
        placeholder="请选择类型"
        style="width: 150px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="type in typeOptions"
          :key="type"
          :label="type"
          :value="type"
        />
      </el-select>
      <el-select
        v-model="queryParams.level"
        placeholder="请选择级别"
        style="width: 150px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="一般" :value="1" />
        <el-option label="中等" :value="2" />
        <el-option label="严重" :value="3" />
      </el-select>
      <el-button type="primary" class="filter-item" @click="handleSearch">
        <el-icon><Search /></el-icon>
        搜索
      </el-button>
      <el-button type="primary" @click="handleCreate">
        <el-icon><Plus /></el-icon>
        新增敏感词
      </el-button>
      <el-button type="success" @click="handleImport">
        <el-icon><Upload /></el-icon>
        导入
      </el-button>
      <el-button type="warning" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出
      </el-button>
      <el-button type="info" @click="handleTest">
        <el-icon><Monitor /></el-icon>
        测试
      </el-button>
    </div>

    <el-card class="stat-card" v-if="stats">
      <div class="stat-header">敏感词统计</div>
      <div class="stat-content">
        <div class="stat-item">
          <div class="stat-label">总数</div>
          <div class="stat-value">{{ stats.total }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">启用</div>
          <div class="stat-value">{{ stats.enabledCount }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">禁用</div>
          <div class="stat-value">{{ stats.disabledCount }}</div>
        </div>
      </div>
      <div class="stat-charts">
        <div class="chart-item">
          <div class="chart-title">按类型统计</div>
          <div class="chart-content">
            <div v-for="item in stats.typeStats" :key="item.type" class="chart-row">
              <div class="chart-label">{{ item.type }}</div>
              <div class="chart-bar">
                <div class="chart-bar-inner" :style="{ width: (item.count / stats.total * 100) + '%' }"></div>
              </div>
              <div class="chart-value">{{ item.count }}</div>
            </div>
          </div>
        </div>
        <div class="chart-item">
          <div class="chart-title">按级别统计</div>
          <div class="chart-content">
            <div v-for="item in stats.levelStats" :key="item.level" class="chart-row">
              <div class="chart-label">{{ item.levelName }}</div>
              <div class="chart-bar">
                <div class="chart-bar-inner" :style="{ width: (item.count / stats.total * 100) + '%' }"></div>
              </div>
              <div class="chart-value">{{ item.count }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-table
      v-loading="loading"
      :data="sensitiveWordList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="word" label="敏感词" min-width="150" />
      <el-table-column prop="type" label="类型" width="120" />
      <el-table-column prop="level" label="级别" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.level === 1" type="info">一般</el-tag>
          <el-tag v-else-if="scope.row.level === 2" type="warning">中等</el-tag>
          <el-tag v-else-if="scope.row.level === 3" type="danger">严重</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="replaceWord" label="替换词" min-width="150" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" min-width="150" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleUpdate(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            link
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        :disabled="loading"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>

    <!-- 敏感词表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="敏感词" prop="word">
          <el-input v-model="form.word" placeholder="请输入敏感词" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
            <el-option
              v-for="type in typeOptions"
              :key="type"
              :label="type"
              :value="type"
            />
            <el-option label="自定义" value="custom">
              <el-input
                v-model="customType"
                placeholder="请输入自定义类型"
                @change="handleCustomTypeChange"
              />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="级别" prop="level">
          <el-select v-model="form.level" placeholder="请选择级别" style="width: 100%">
            <el-option label="一般" :value="1" />
            <el-option label="中等" :value="2" />
            <el-option label="严重" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="替换词" prop="replaceWord">
          <el-input v-model="form.replaceWord" placeholder="请输入替换词" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="form.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      title="导入敏感词"
      v-model="importDialogVisible"
      width="600px"
      append-to-body
    >
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        accept=".xlsx,.xls,.csv,.txt,.json,.xml"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持多种文件格式：
            <el-tag size="small" type="success" class="format-tag">Excel (.xlsx/.xls)</el-tag>
            <el-tag size="small" type="success" class="format-tag">CSV (.csv)</el-tag>
            <el-tag size="small" type="success" class="format-tag">文本 (.txt)</el-tag>
            <el-tag size="small" type="success" class="format-tag">JSON (.json)</el-tag>
            <el-tag size="small" type="success" class="format-tag">XML (.xml)</el-tag>
          </div>
          <div class="format-info">
            <div class="format-title">文件格式说明：</div>
            <div class="format-item">
              <div class="format-name">Excel/CSV：</div>
              <div class="format-desc">第一列为敏感词，第二列为类型，第三列为级别，第四列为替换词</div>
            </div>
            <div class="format-item">
              <div class="format-name">文本：</div>
              <div class="format-desc">每行一个敏感词，支持格式：敏感词[类型:级别]</div>
            </div>
            <div class="format-item">
              <div class="format-name">JSON：</div>
              <div class="format-desc">数组格式：[{"word":"敏感词","type":"类型","level":1}]</div>
            </div>
            <div class="format-item">
              <div class="format-name">XML：</div>
              <div class="format-desc">格式：&lt;sensitiveWords&gt;&lt;word type="类型" level="1"&gt;敏感词&lt;/word&gt;&lt;/sensitiveWords&gt;</div>
            </div>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImport">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入结果对话框 -->
    <el-dialog
      title="敏感词导入结果"
      v-model="importResultDialogVisible"
      width="600px"
      append-to-body
    >
      <div v-if="importResult" class="import-result">
        <div v-if="importResult.totalFiles !== undefined" class="result-section">
          <h3>文件处理结果</h3>
          <div class="result-item">
            <div class="result-label">总处理文件数：</div>
            <div class="result-value">{{ importResult.totalFiles }}</div>
          </div>
          <div class="result-item">
            <div class="result-label">成功处理文件数：</div>
            <div class="result-value">{{ importResult.successFiles }}</div>
          </div>
          <div class="result-item">
            <div class="result-label">失败处理文件数：</div>
            <div class="result-value">{{ importResult.failedFiles }}</div>
          </div>
        </div>

        <div class="result-section">
          <h3>敏感词处理结果</h3>
          <div class="result-item">
            <div class="result-label">总敏感词数：</div>
            <div class="result-value">{{ importResult.totalWords || importResult.successCount + importResult.failCount }}</div>
          </div>
          <div class="result-item">
            <div class="result-label">成功导入敏感词数：</div>
            <div class="result-value">{{ importResult.successWords || importResult.successCount }}</div>
          </div>
          <div class="result-item">
            <div class="result-label">失败导入敏感词数：</div>
            <div class="result-value">{{ importResult.failedWords || importResult.failCount }}</div>
          </div>
          <div class="result-item" v-if="importResult.duplicateWords !== undefined">
            <div class="result-label">重复敏感词数：</div>
            <div class="result-value">{{ importResult.duplicateWords }}</div>
          </div>
        </div>

        <div v-if="importResult.typeStats" class="result-section">
          <h3>按类型统计</h3>
          <div class="result-item" v-for="(count, type) in importResult.typeStats" :key="type">
            <div class="result-label">{{ type }}：</div>
            <div class="result-value">{{ count }}</div>
          </div>
        </div>

        <div v-if="importResult.levelStats" class="result-section">
          <h3>按级别统计</h3>
          <div class="result-item" v-for="(count, level) in importResult.levelStats" :key="level">
            <div class="result-label">
              {{ level == 1 ? '一般' : level == 2 ? '中等' : level == 3 ? '严重' : '未知' }}：
            </div>
            <div class="result-value">{{ count }}</div>
          </div>
        </div>

        <div v-if="importResult.failedFileList && importResult.failedFileList.length > 0" class="result-section">
          <h3>失败文件列表</h3>
          <div class="result-item" v-for="(file, index) in importResult.failedFileList" :key="index">
            <div class="result-label">{{ file.fileName }}：</div>
            <div class="result-value">{{ file.errorMessage }}</div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 测试对话框 -->
    <el-dialog
      title="敏感词过滤测试"
      v-model="testDialogVisible"
      width="600px"
      append-to-body
    >
      <el-form label-width="80px">
        <el-form-item label="测试内容">
          <el-input
            v-model="testContent"
            type="textarea"
            :rows="5"
            placeholder="请输入测试内容"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitTest">测试</el-button>
        </el-form-item>
      </el-form>
      <div v-if="testResult" class="test-result">
        <div class="result-item">
          <div class="result-label">是否包含敏感词：</div>
          <div class="result-value">
            <el-tag :type="testResult.contains ? 'danger' : 'success'">
              {{ testResult.contains ? '是' : '否' }}
            </el-tag>
          </div>
        </div>
        <div class="result-item" v-if="testResult.contains">
          <div class="result-label">包含的敏感词：</div>
          <div class="result-value">
            <el-tag
              v-for="(word, index) in testResult.words"
              :key="index"
              type="danger"
              class="word-tag"
            >
              {{ word }}
            </el-tag>
          </div>
        </div>
        <div class="result-item" v-if="testResult.contains">
          <div class="result-label">替换后的内容：</div>
          <div class="result-value">{{ testResult.replaced }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus, Upload, Download, Monitor, UploadFilled } from '@element-plus/icons-vue'
import {
  getSensitiveWordList,
  getSensitiveWordDetail,
  createSensitiveWord,
  updateSensitiveWord,
  deleteSensitiveWord,
  updateSensitiveWordStatus,
  getSensitiveWordTypes,
  getSensitiveWordStats,
  testSensitiveWordFilter,
  exportSensitiveWords,
  importSensitiveWords,
  importSensitiveWordsFromDirectory
} from '@/api/system/sensitiveWord'

// 敏感词列表
const sensitiveWordList = ref([])
// 总记录数
const total = ref(0)
// 加载状态
const loading = ref(false)
// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  keyword: '',
  type: '',
  level: undefined
})
// 对话框标题
const dialogTitle = ref('新增敏感词')
// 对话框可见性
const dialogVisible = ref(false)
// 导入对话框可见性
const importDialogVisible = ref(false)
// 导入结果
const importResult = ref(null)
// 导入结果对话框可见性
const importResultDialogVisible = ref(false)
// 测试对话框可见性
const testDialogVisible = ref(false)
// 表单引用
const formRef = ref()
// 表单数据
const form = reactive({
  id: undefined,
  word: '',
  type: '',
  level: 1,
  replaceWord: '',
  status: true
})
// 自定义类型
const customType = ref('')
// 类型选项
const typeOptions = ref([])
// 上传文件
const uploadFile = ref(null)
// 测试内容
const testContent = ref('')
// 测试结果
const testResult = ref(null)
// 统计信息
const stats = ref(null)
// 表单校验规则
const rules = {
  word: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择级别', trigger: 'change' }]
}

// 获取敏感词列表
const fetchSensitiveWordList = async () => {
  loading.value = true
  try {
    const { data } = await getSensitiveWordList({
      page: queryParams.page,
      pageSize: queryParams.pageSize,
      keyword: queryParams.keyword,
      type: queryParams.type,
      level: queryParams.level
    })
    sensitiveWordList.value = data.records
    total.value = data.total
  } catch (error) {
    console.error('获取敏感词列表失败', error)
    ElMessage.error('获取敏感词列表失败')
  } finally {
    loading.value = false
  }
}

// 获取敏感词类型列表
const fetchSensitiveWordTypes = async () => {
  try {
    const { data } = await getSensitiveWordTypes()
    typeOptions.value = data
  } catch (error) {
    console.error('获取敏感词类型列表失败', error)
    ElMessage.error('获取敏感词类型列表失败')
  }
}

// 获取敏感词统计信息
const fetchSensitiveWordStats = async () => {
  try {
    const { data } = await getSensitiveWordStats()
    stats.value = data
  } catch (error) {
    console.error('获取敏感词统计信息失败', error)
    ElMessage.error('获取敏感词统计信息失败')
  }
}

// 重置表单
const resetForm = () => {
  form.id = undefined
  form.word = ''
  form.type = ''
  form.level = 1
  form.replaceWord = ''
  form.status = true
  customType.value = ''
  formRef.value?.resetFields()
}

// 处理搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchSensitiveWordList()
}

// 处理页码变更
const handleCurrentChange = (page) => {
  queryParams.page = page
  fetchSensitiveWordList()
}

// 处理每页条数变更
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.page = 1
  fetchSensitiveWordList()
}

// 处理新增敏感词
const handleCreate = () => {
  resetForm()
  dialogTitle.value = '新增敏感词'
  dialogVisible.value = true
}

// 处理编辑敏感词
const handleUpdate = async (row) => {
  resetForm()
  dialogTitle.value = '编辑敏感词'

  try {
    const { data } = await getSensitiveWordDetail(row.id)
    Object.assign(form, data)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取敏感词详情失败', error)
    ElMessage.error('获取敏感词详情失败')
  }
}

// 处理删除敏感词
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该敏感词吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteSensitiveWord(row.id)
      ElMessage.success('删除成功')
      fetchSensitiveWordList()
      fetchSensitiveWordStats()
    } catch (error) {
      console.error('删除敏感词失败', error)
      ElMessage.error('删除敏感词失败')
    }
  }).catch(() => {})
}

// 处理状态变更
const handleStatusChange = async (row) => {
  try {
    await updateSensitiveWordStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
    fetchSensitiveWordStats()
  } catch (error) {
    console.error('更新敏感词状态失败', error)
    ElMessage.error('更新敏感词状态失败')
    // 恢复状态
    row.status = !row.status
  }
}

// 处理自定义类型变更
const handleCustomTypeChange = () => {
  if (customType.value) {
    form.type = customType.value
  }
}

// 处理导入
const handleImport = () => {
  uploadFile.value = null
  importDialogVisible.value = true
}

// 处理文件变更
const handleFileChange = (file) => {
  uploadFile.value = file.raw
}

// 提交导入
const submitImport = async () => {
  try {
    // 文件导入
    if (!uploadFile.value) {
      ElMessage.warning('请选择文件')
      return
    }

    const { data } = await importSensitiveWords(uploadFile.value)

    // 显示导入结果
    const successWords = data.successWords || 0
    const failedWords = data.failedWords || 0
    const duplicateWords = data.duplicateWords || 0
    const totalWords = data.totalWords || 0

    ElMessage.success(`导入成功，总词数: ${totalWords}, 成功: ${successWords}, 失败: ${failedWords}, 重复: ${duplicateWords}`)
    importResult.value = data

    // 关闭导入对话框
    importDialogVisible.value = false

    // 显示导入结果对话框
    importResultDialogVisible.value = true

    // 刷新数据
    fetchSensitiveWordList()
    fetchSensitiveWordStats()
  } catch (error) {
    console.error('导入敏感词失败', error)
    ElMessage.error('导入敏感词失败: ' + (error.message || '未知错误'))
  }
}

// 处理导出
const handleExport = async () => {
  try {
    const response = await exportSensitiveWords(queryParams.type, queryParams.level)

    // 创建Blob对象
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })

    // 创建下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = '敏感词列表.xlsx'
    link.click()

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出敏感词失败', error)
    ElMessage.error('导出敏感词失败')
  }
}

// 处理测试
const handleTest = () => {
  testContent.value = ''
  testResult.value = null
  testDialogVisible.value = true
}

// 提交测试
const submitTest = async () => {
  if (!testContent.value) {
    ElMessage.warning('请输入测试内容')
    return
  }

  try {
    const { data } = await testSensitiveWordFilter(testContent.value)
    testResult.value = data
  } catch (error) {
    console.error('测试敏感词过滤失败', error)
    ElMessage.error('测试敏感词过滤失败')
  }
}

// 提交表单
const submitForm = async () => {
  formRef.value?.validate(async (valid) => {
    if (!valid) return

    try {
      // 确保数据类型正确
      const formData = {
        ...form,
        level: Number(form.level), // 确保级别是数字类型
        status: Boolean(form.status) // 确保状态是布尔类型
      }

      console.log('提交表单数据:', formData)

      if (formData.id) {
        // 更新敏感词
        await updateSensitiveWord(formData.id, formData)
        ElMessage.success('更新成功')
      } else {
        // 创建敏感词
        await createSensitiveWord(formData)
        ElMessage.success('创建成功')
      }
      dialogVisible.value = false
      fetchSensitiveWordList()
      fetchSensitiveWordStats()

      // 如果是新类型，刷新类型列表
      if (!typeOptions.value.includes(formData.type)) {
        fetchSensitiveWordTypes()
      }
    } catch (error) {
      console.error('保存敏感词失败', error)
      ElMessage.error('保存敏感词失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchSensitiveWordList()
  fetchSensitiveWordTypes()
  fetchSensitiveWordStats()
})
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.stat-card {
  margin-bottom: 20px;
}
.stat-header {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}
.stat-content {
  display: flex;
  margin-bottom: 20px;
}
.stat-item {
  flex: 1;
  text-align: center;
  padding: 10px;
  border-right: 1px solid #eee;
}
.stat-item:last-child {
  border-right: none;
}
.stat-label {
  font-size: 14px;
  color: #666;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-top: 5px;
}
.stat-charts {
  display: flex;
}
.chart-item {
  flex: 1;
  padding: 10px;
}
.chart-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}
.chart-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.chart-label {
  width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chart-bar {
  flex: 1;
  height: 20px;
  background-color: #f5f5f5;
  border-radius: 10px;
  overflow: hidden;
  margin: 0 10px;
}
.chart-bar-inner {
  height: 100%;
  background-color: #409EFF;
  border-radius: 10px;
}
.chart-value {
  width: 40px;
  text-align: right;
}
.test-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}
.result-item {
  margin-bottom: 10px;
}
.result-label {
  font-weight: bold;
  margin-bottom: 5px;
}
.word-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
.directory-import-container {
  padding: 20px;
}
.directory-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  color: #666;
}
.directory-info p {
  margin: 5px 0;
  line-height: 1.5;
}
.import-result {
  padding: 10px;
}
.result-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}
.result-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #409EFF;
}
.format-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}
.format-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-size: 13px;
}
.format-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #606266;
}
.format-item {
  display: flex;
  margin-bottom: 5px;
}
.format-name {
  font-weight: bold;
  width: 90px;
  flex-shrink: 0;
}
.format-desc {
  color: #606266;
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  padding: 10px 0;
  text-align: right;
  background-color: #fff;
}

/* 加载状态指示器样式 */
.el-table {
  position: relative;
}
.el-loading-mask {
  z-index: 10;
}
</style>
