<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knife4j Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #333;
        }
        .links {
            margin-top: 20px;
        }
        .links a {
            display: block;
            margin-bottom: 10px;
            color: #0066cc;
            text-decoration: none;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Knife4j API Documentation Test</h1>
        <p>This page helps test if the Knife4j API documentation is properly configured and accessible.</p>
        
        <div class="links">
            <h2>Documentation Links:</h2>
            <a href="/api/doc.html" target="_blank">Knife4j Documentation (with /api prefix)</a>
            <a href="/doc.html" target="_blank">Knife4j Documentation (without /api prefix)</a>
            <a href="/api/swagger-ui.html" target="_blank">Swagger UI (with /api prefix)</a>
            <a href="/swagger-ui.html" target="_blank">Swagger UI (without /api prefix)</a>
            <a href="/api/v3/api-docs" target="_blank">OpenAPI JSON (with /api prefix)</a>
            <a href="/v3/api-docs" target="_blank">OpenAPI JSON (without /api prefix)</a>
        </div>
        
        <div class="troubleshooting">
            <h2>Troubleshooting:</h2>
            <p>If the links above don't work, check the following:</p>
            <ol>
                <li>Verify that the server is running</li>
                <li>Check if the context path is correctly set to "/api"</li>
                <li>Ensure that the security configuration allows access to these paths</li>
                <li>Check if the resource handlers are properly configured in WebMvcConfig</li>
                <li>Look for any errors in the server logs</li>
            </ol>
        </div>
    </div>
</body>
</html>
