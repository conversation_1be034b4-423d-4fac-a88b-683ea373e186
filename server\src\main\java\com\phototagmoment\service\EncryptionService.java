package com.phototagmoment.service;

import java.io.InputStream;
import java.io.OutputStream;

/**
 * 加密服务接口
 */
public interface EncryptionService {

    /**
     * 加密用户数据
     *
     * @param fieldName 字段名
     * @param value     字段值
     * @return 加密后的值
     */
    String encryptUserData(String fieldName, String value);

    /**
     * 解密用户数据
     *
     * @param fieldName 字段名
     * @param value     加密后的值
     * @return 解密后的值
     */
    String decryptUserData(String fieldName, String value);

    /**
     * 加密照片数据
     *
     * @param input  输入流
     * @param output 输出流
     * @return 是否成功
     */
    boolean encryptPhoto(InputStream input, OutputStream output);

    /**
     * 解密照片数据
     *
     * @param input  输入流
     * @param output 输出流
     * @return 是否成功
     */
    boolean decryptPhoto(InputStream input, OutputStream output);

    /**
     * 使用RSA公钥加密数据
     *
     * @param data 明文数据
     * @return 加密后的数据
     */
    String encryptWithPublicKey(String data);

    /**
     * 使用RSA私钥解密数据
     *
     * @param data 加密后的数据
     * @return 解密后的数据
     */
    String decryptWithPrivateKey(String data);

    /**
     * 判断字段是否需要加密
     *
     * @param fieldName 字段名
     * @return 是否需要加密
     */
    boolean isEncryptedField(String fieldName);

    /**
     * 获取RSA公钥
     *
     * @return RSA公钥
     */
    String getRsaPublicKey();
}
