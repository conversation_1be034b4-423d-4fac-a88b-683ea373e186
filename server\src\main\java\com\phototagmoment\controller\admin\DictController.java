package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.entity.DictData;
import com.phototagmoment.entity.DictType;
import com.phototagmoment.service.DictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 数据字典Controller
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/dict")
@Tag(name = "数据字典管理", description = "数据字典管理相关接口")
public class DictController {

    @Autowired
    private DictService dictService;

    /**
     * 获取所有字典类型
     */
    @GetMapping("/type/list")
    @Operation(summary = "获取所有字典类型", description = "获取所有字典类型")
    public Result<List<DictType>> listTypes() {
        List<DictType> types = dictService.listAllDictTypes();
        return Result.success(types);
    }

    /**
     * 分页获取字典类型
     */
    @GetMapping("/type/page")
    @Operation(summary = "分页获取字典类型", description = "分页获取字典类型")
    public Result<IPage<DictType>> pageTypes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        IPage<DictType> pageResult = dictService.pageDictTypes(page, pageSize, keyword);
        return Result.success(pageResult);
    }

    /**
     * 根据ID获取字典类型
     */
    @GetMapping("/type/{id}")
    @Operation(summary = "根据ID获取字典类型", description = "根据ID获取字典类型")
    public Result<DictType> getTypeById(@Parameter(description = "字典类型ID") @PathVariable Long id) {
        DictType dictType = dictService.getDictTypeById(id);
        return Result.success(dictType);
    }

    /**
     * 根据字典类型获取字典类型对象
     */
    @GetMapping("/type/code/{dictType}")
    @Operation(summary = "根据字典类型获取字典类型对象", description = "根据字典类型获取字典类型对象")
    public Result<DictType> getTypeByType(@Parameter(description = "字典类型") @PathVariable String dictType) {
        DictType type = dictService.getDictTypeByType(dictType);
        return Result.success(type);
    }

    /**
     * 保存字典类型
     */
    @PostMapping("/type")
    @Operation(summary = "保存字典类型", description = "保存字典类型")
    public Result<Boolean> saveType(@RequestBody DictType dictType) {
        boolean result = dictService.saveDictType(dictType);
        return Result.success(result);
    }

    /**
     * 更新字典类型
     */
    @PutMapping("/type")
    @Operation(summary = "更新字典类型", description = "更新字典类型")
    public Result<Boolean> updateType(@RequestBody DictType dictType) {
        boolean result = dictService.updateDictType(dictType);
        return Result.success(result);
    }

    /**
     * 删除字典类型
     */
    @DeleteMapping("/type/{id}")
    @Operation(summary = "删除字典类型", description = "删除字典类型")
    public Result<Boolean> deleteType(@Parameter(description = "字典类型ID") @PathVariable Long id) {
        boolean result = dictService.deleteDictType(id);
        return Result.success(result);
    }

    /**
     * 获取所有字典数据
     */
    @GetMapping("/data/list")
    @Operation(summary = "获取所有字典数据", description = "获取所有字典数据")
    public Result<List<DictData>> listData() {
        List<DictData> data = dictService.listAllDictData();
        return Result.success(data);
    }

    /**
     * 分页获取字典数据
     */
    @GetMapping("/data/page")
    @Operation(summary = "分页获取字典数据", description = "分页获取字典数据")
    public Result<IPage<DictData>> pageData(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "字典类型ID") @RequestParam(required = false) Long dictTypeId,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        IPage<DictData> pageResult = dictService.pageDictData(page, pageSize, dictTypeId, keyword);
        return Result.success(pageResult);
    }

    /**
     * 根据ID获取字典数据
     */
    @GetMapping("/data/{id}")
    @Operation(summary = "根据ID获取字典数据", description = "根据ID获取字典数据")
    public Result<DictData> getDataById(@Parameter(description = "字典数据ID") @PathVariable Long id) {
        DictData dictData = dictService.getDictDataById(id);
        return Result.success(dictData);
    }

    /**
     * 根据字典类型ID获取字典数据列表
     */
    @GetMapping("/data/type/{dictTypeId}")
    @Operation(summary = "根据字典类型ID获取字典数据列表", description = "根据字典类型ID获取字典数据列表")
    public Result<List<DictData>> getDataByTypeId(@Parameter(description = "字典类型ID") @PathVariable Long dictTypeId) {
        List<DictData> data = dictService.getDictDataByTypeId(dictTypeId);
        return Result.success(data);
    }

    /**
     * 根据字典类型获取字典数据列表
     */
    @GetMapping("/data/code/{dictType}")
    @Operation(summary = "根据字典类型获取字典数据列表", description = "根据字典类型获取字典数据列表")
    public Result<List<DictData>> getDataByType(@Parameter(description = "字典类型") @PathVariable String dictType) {
        List<DictData> data = dictService.getDictDataByType(dictType);
        return Result.success(data);
    }

    /**
     * 根据字典类型获取字典数据Map
     */
    @GetMapping("/data/map/{dictType}")
    @Operation(summary = "根据字典类型获取字典数据Map", description = "根据字典类型获取字典数据Map，key为dictValue，value为dictLabel")
    public Result<Map<String, String>> getDataMapByType(@Parameter(description = "字典类型") @PathVariable String dictType) {
        Map<String, String> dataMap = dictService.getDictDataMapByType(dictType);
        return Result.success(dataMap);
    }

    /**
     * 保存字典数据
     */
    @PostMapping("/data")
    @Operation(summary = "保存字典数据", description = "保存字典数据")
    public Result<Boolean> saveData(@RequestBody DictData dictData) {
        boolean result = dictService.saveDictData(dictData);
        return Result.success(result);
    }

    /**
     * 更新字典数据
     */
    @PutMapping("/data")
    @Operation(summary = "更新字典数据", description = "更新字典数据")
    public Result<Boolean> updateData(@RequestBody DictData dictData) {
        boolean result = dictService.updateDictData(dictData);
        return Result.success(result);
    }

    /**
     * 删除字典数据
     */
    @DeleteMapping("/data/{id}")
    @Operation(summary = "删除字典数据", description = "删除字典数据")
    public Result<Boolean> deleteData(@Parameter(description = "字典数据ID") @PathVariable Long id) {
        boolean result = dictService.deleteDictData(id);
        return Result.success(result);
    }

    /**
     * 刷新字典缓存
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新字典缓存", description = "刷新字典缓存")
    public Result<Boolean> refreshCache() {
        dictService.refreshCache();
        return Result.success(true);
    }
}
