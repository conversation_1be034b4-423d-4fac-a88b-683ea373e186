package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.dto.NotificationDTO;
import com.phototagmoment.entity.Comment;
import com.phototagmoment.entity.Notification;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.entity.User;
import com.phototagmoment.mapper.CommentMapper;
import com.phototagmoment.mapper.NotificationMapper;
import com.phototagmoment.mapper.PhotoMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.NotificationService;
import com.phototagmoment.websocket.NotificationWebSocketHandler;
import com.phototagmoment.websocket.WebSocketMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 通知服务实现类
 */
@Slf4j
@Service
public class NotificationServiceImpl extends ServiceImpl<NotificationMapper, Notification> implements NotificationService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private NotificationWebSocketHandler notificationWebSocketHandler;

    @Override
    public boolean pushNotification(Notification notification) {
        // 保存通知到数据库
        boolean saved = this.save(notification);

        if (saved) {
            try {
                // 构建通知DTO
                NotificationDTO dto = new NotificationDTO();
                dto.setId(notification.getId());
                dto.setUserId(notification.getUserId());
                dto.setSenderId(notification.getSenderId());
                dto.setType(notification.getType());
                dto.setTargetId(notification.getTargetId());
                dto.setTargetType(notification.getTargetType());
                dto.setContent(notification.getContent());
                dto.setIsRead(notification.getIsRead());
                dto.setCreatedAt(notification.getCreatedAt());

                // 如果有发送者，获取发送者信息
                if (notification.getSenderId() != null) {
                    User sender = userMapper.selectById(notification.getSenderId());
                    if (sender != null) {
                        dto.setSenderName(sender.getUsername());
                        dto.setSenderAvatar(sender.getAvatar());
                    }
                }

                // 获取目标内容
                if (notification.getTargetType() != null && notification.getTargetId() != null) {
                    if (notification.getTargetType() == 1) { // 照片
                        Photo photo = photoMapper.selectById(notification.getTargetId());
                        if (photo != null) {
                            dto.setTargetContent(photo.getTitle());
                            dto.setTargetThumbnail(photo.getThumbnailUrl());
                        }
                    } else if (notification.getTargetType() == 2) { // 评论
                        Comment comment = commentMapper.selectById(notification.getTargetId());
                        if (comment != null) {
                            dto.setTargetContent(comment.getContent());
                        }
                    } else if (notification.getTargetType() == 3) { // 用户
                        User user = userMapper.selectById(notification.getTargetId());
                        if (user != null) {
                            dto.setTargetContent(user.getNickname());
                            dto.setTargetThumbnail(user.getAvatar());
                        }
                    }
                }

                // 通过WebSocket推送通知
                WebSocketMessage message = WebSocketMessage.notification(dto);
                return notificationWebSocketHandler.sendMessageToUser(notification.getUserId(), message);
            } catch (Exception e) {
                log.error("推送通知失败: {}", e.getMessage(), e);
            }
        }

        return saved;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createFollowNotification(Long followerId, Long followingId) {
        // 不给自己发通知
        if (followerId.equals(followingId)) {
            return true;
        }

        // 获取关注者信息
        User follower = userMapper.selectById(followerId);
        if (follower == null) {
            return false;
        }

        // 创建通知
        Notification notification = new Notification();
        notification.setUserId(followingId);
        notification.setSenderId(followerId);
        notification.setType(1); // 关注通知
        notification.setTargetId(followerId);
        notification.setTargetType(3); // 用户
        notification.setContent(follower.getNickname() + " 关注了你");
        notification.setIsRead(0);

        return this.pushNotification(notification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createLikeNotification(Long userId, Long photoId, Long photoUserId) {
        // 不给自己发通知
        if (userId.equals(photoUserId)) {
            return true;
        }

        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        // 获取照片信息
        Photo photo = photoMapper.selectById(photoId);
        if (photo == null) {
            return false;
        }

        // 创建通知
        Notification notification = new Notification();
        notification.setUserId(photoUserId);
        notification.setSenderId(userId);
        notification.setType(2); // 点赞通知
        notification.setTargetId(photoId);
        notification.setTargetType(1); // 照片
        notification.setContent(user.getNickname() + " 点赞了你的照片");
        notification.setIsRead(0);

        return this.pushNotification(notification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCommentNotification(Long userId, Long photoId, Long photoUserId, Long commentId, String content) {
        // 不给自己发通知
        if (userId.equals(photoUserId)) {
            return true;
        }

        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        // 获取照片信息
        Photo photo = photoMapper.selectById(photoId);
        if (photo == null) {
            return false;
        }

        // 创建通知
        Notification notification = new Notification();
        notification.setUserId(photoUserId);
        notification.setSenderId(userId);
        notification.setType(3); // 评论通知
        notification.setTargetId(photoId);
        notification.setTargetType(1); // 照片
        notification.setContent(user.getNickname() + " 评论了你的照片: " + (content.length() > 20 ? content.substring(0, 20) + "..." : content));
        notification.setIsRead(0);

        return this.pushNotification(notification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createReplyNotification(Long userId, Long commentId, Long replyToUserId, String content) {
        // 不给自己发通知
        if (userId.equals(replyToUserId)) {
            return true;
        }

        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        // 获取评论信息
        Comment comment = commentMapper.selectById(commentId);
        if (comment == null) {
            return false;
        }

        // 创建通知
        Notification notification = new Notification();
        notification.setUserId(replyToUserId);
        notification.setSenderId(userId);
        notification.setType(4); // 回复通知
        notification.setTargetId(commentId);
        notification.setTargetType(2); // 评论
        notification.setContent(user.getNickname() + " 回复了你的评论: " + (content.length() > 20 ? content.substring(0, 20) + "..." : content));
        notification.setIsRead(0);

        return this.pushNotification(notification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSystemNotification(Long userId, String content) {
        // 创建通知
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setSenderId(null);
        notification.setType(5); // 系统通知
        notification.setTargetId(null);
        notification.setTargetType(null);
        notification.setContent(content);
        notification.setIsRead(0);

        return this.pushNotification(notification);
    }

    @Override
    public IPage<NotificationDTO> getUserNotifications(Long userId, int page, int size, Integer type) {
        Page<NotificationDTO> pageParam = new Page<>(page, size);
        return baseMapper.selectUserNotifications(pageParam, userId, type);
    }

    @Override
    public int getUnreadCount(Long userId) {
        return baseMapper.countUnreadNotifications(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(Long notificationId, Long userId) {
        // 检查通知是否存在且属于该用户
        LambdaQueryWrapper<Notification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Notification::getId, notificationId)
                .eq(Notification::getUserId, userId);
        if (this.count(queryWrapper) == 0) {
            return false;
        }

        return baseMapper.markAsRead(notificationId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAllAsRead(Long userId) {
        return baseMapper.markAllAsRead(userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotification(Long notificationId, Long userId) {
        // 检查通知是否存在且属于该用户
        LambdaQueryWrapper<Notification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Notification::getId, notificationId)
                .eq(Notification::getUserId, userId);
        return this.remove(queryWrapper);
    }

    @Override
    public boolean sendMentionNotification(Long mentionedUserId, Long mentionUserId, Long noteId, String noteTitle) {
        try {
            // 获取@用户的信息
            User mentionUser = userMapper.selectById(mentionUserId);
            if (mentionUser == null) {
                log.warn("@用户不存在: {}", mentionUserId);
                return false;
            }

            // 创建@用户通知
            Notification notification = new Notification();
            notification.setUserId(mentionedUserId);
            notification.setSenderId(mentionUserId);
            notification.setType(6); // @用户通知
            notification.setTargetId(noteId);
            notification.setTargetType(3); // 照片笔记
            notification.setContent(String.format("%s 在照片笔记《%s》中@了你",
                mentionUser.getNickname(),
                noteTitle != null && !noteTitle.isEmpty() ? noteTitle : "无标题"));
            notification.setIsRead(0);
            notification.setCreatedAt(LocalDateTime.now());

            this.save(notification);

            // 推送WebSocket通知
            pushNotification(notification);

            log.info("发送@用户通知成功: 被@用户={}, @用户={}, 照片笔记={}",
                mentionedUserId, mentionUserId, noteId);

            return true;
        } catch (Exception e) {
            log.error("发送@用户通知失败: 被@用户={}, @用户={}, 照片笔记={}",
                mentionedUserId, mentionUserId, noteId, e);
            return false;
        }
    }
}
