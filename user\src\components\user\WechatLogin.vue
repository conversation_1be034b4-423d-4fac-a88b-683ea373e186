<template>
  <div class="wechat-login">
    <div class="flex flex-col items-center">
      <div v-if="type === 'mp'" class="qrcode-container">
        <div v-if="qrCodeUrl" class="qrcode">
          <img :src="qrCodeUrl" alt="微信扫码登录" />
        </div>
        <div v-else class="qrcode-loading">
          <van-loading type="spinner" size="24px" />
          <p class="mt-2">加载中...</p>
        </div>
        <p class="text-center mt-2 text-sm text-gray-500">请使用微信扫描二维码登录</p>
        <p class="text-center text-xs text-gray-400 mt-1">未绑定微信的用户将自动创建账号</p>
      </div>

      <div v-else-if="type === 'mini-app'" class="mini-app-login">
        <van-button
          round
          block
          type="primary"
          color="#07C160"
          :loading="loading"
          @click="miniAppLogin"
          size="large"
        >
          <template #icon>
            <van-icon name="wechat" />
          </template>
          微信一键登录
        </van-button>
        <p class="text-center mt-2 text-sm text-gray-500">点击按钮使用微信小程序登录</p>
        <p class="text-center text-xs text-gray-400 mt-1">未绑定微信的用户将自动创建账号</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { showToast, showSuccessToast, showFailToast } from 'vant';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import QRCode from 'qrcode';

const props = defineProps({
  // 登录类型：mp（公众号）、mini-app（小程序）
  type: {
    type: String,
    default: 'mp',
  },
});

const router = useRouter();
const userStore = useUserStore();

// 状态
const loading = ref(false);
const qrCodeUrl = ref('');
const authUrl = ref('');
let checkTimer: ReturnType<typeof setInterval> | null = null;
let stateCode = '';

// 生成随机状态码
const generateStateCode = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// 获取微信公众号授权URL
const getMpAuthUrl = async () => {
  try {
    loading.value = true;

    // 生成状态码
    stateCode = generateStateCode();

    // 获取授权URL
    const redirectUrl = `${window.location.origin}/wechat-callback`;
    const response = await fetch(`/api/wechat/mp/auth-url?redirectUrl=${encodeURIComponent(redirectUrl)}&state=${stateCode}`);
    const result = await response.json();

    if (result.code === 200) {
      authUrl.value = result.data.authUrl;

      // 生成二维码
      qrCodeUrl.value = await QRCode.toDataURL(authUrl.value, {
        width: 200,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      });

      // 开始轮询检查登录状态
      startCheckLoginStatus();
    } else {
      showFailToast(result.message || '获取微信授权失败');
    }
  } catch (error) {
    console.error('获取微信授权失败:', error);
    showFailToast('获取微信授权失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 开始检查登录状态
const startCheckLoginStatus = () => {
  // 清除之前的定时器
  if (checkTimer !== null) {
    clearInterval(checkTimer);
  }

  // 每3秒检查一次登录状态
  checkTimer = setInterval(async () => {
    try {
      // 检查是否有回调参数
      const params = new URLSearchParams(window.location.search);
      const code = params.get('code');
      const state = params.get('state');

      if (code && state && state === stateCode) {
        // 清除定时器
        if (checkTimer !== null) {
          clearInterval(checkTimer);
          checkTimer = null;
        }

        // 微信登录
        await mpLogin(code);

        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }
  }, 3000);
};

// 微信公众号登录/注册
const mpLogin = async (code: string) => {
  try {
    loading.value = true;

    // 登录/注册请求
    const response = await fetch('/api/wechat/mp/login-register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code,
      }),
    });

    const result = await response.json();

    if (result.code === 200) {
      // 登录成功
      const { token, user } = result.data;

      // 保存登录信息
      userStore.setToken(token);
      userStore.setUser(user);

      showSuccessToast('登录成功');

      // 跳转到首页
      router.push('/');
    } else {
      showFailToast(result.message || '登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
    showFailToast('登录失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 微信小程序登录
const miniAppLogin = () => {
  showToast('微信小程序登录功能暂未实现');
};

// 组件挂载时初始化
onMounted(() => {
  if (props.type === 'mp') {
    getMpAuthUrl();
  }
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (checkTimer !== null) {
    clearInterval(checkTimer);
  }
});
</script>

<style scoped>
.wechat-login {
  padding: 1rem;
}

.qrcode-container {
  width: 200px;
  margin: 0 auto;
}

.qrcode {
  width: 200px;
  height: 200px;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
}

.qrcode img {
  width: 100%;
  height: 100%;
}

.qrcode-loading {
  width: 200px;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #eee;
  border-radius: 4px;
}

.mini-app-login {
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
}
</style>
