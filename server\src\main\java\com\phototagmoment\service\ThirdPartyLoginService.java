package com.phototagmoment.service;

import com.phototagmoment.dto.AuthUserInfoDTO;
import com.phototagmoment.vo.TokenVO;
import me.zhyd.oauth.model.AuthCallback;

/**
 * 第三方登录服务接口
 */
public interface ThirdPartyLoginService {

    /**
     * 第三方登录
     *
     * @param source 第三方平台来源(qq, wechat等)
     * @param callback 授权回调参数
     * @return 登录结果
     */
    TokenVO login(String source, AuthCallback callback);

    /**
     * 绑定第三方账号
     *
     * @param userId 用户ID
     * @param source 第三方平台来源(qq, wechat等)
     * @param openId 第三方平台唯一标识
     * @param accessToken 访问令牌
     * @param userInfo 用户信息
     * @return 是否成功
     */
    boolean bind(Long userId, String source, String openId, String accessToken, AuthUserInfoDTO userInfo);

    /**
     * 解绑第三方账号
     *
     * @param userId 用户ID
     * @param source 第三方平台来源(qq, wechat等)
     * @return 是否成功
     */
    boolean unbind(Long userId, String source);

    /**
     * 查询用户绑定的第三方账号
     *
     * @param userId 用户ID
     * @return 绑定的第三方账号列表
     */
    java.util.List<String> getBindingList(Long userId);
}
