<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="标题/描述"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="审核状态"
        clearable
        style="width: 130px"
        class="filter-item"
      >
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        v-if="selectedPhotos.length > 0"
        class="filter-item"
        type="success"
        icon="el-icon-check"
        @click="handleBatchApprove"
      >
        批量通过
      </el-button>
      <el-button
        v-if="selectedPhotos.length > 0"
        class="filter-item"
        type="danger"
        icon="el-icon-close"
        @click="handleBatchReject"
      >
        批量拒绝
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
      />
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          <span>{{ scope.row.photoId }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="缩略图" width="120">
        <template #default="scope">
          <el-image
            :src="scope.row.thumbnailUrl"
            :preview-src-list="[scope.row.url]"
            fit="cover"
            style="width: 80px; height: 80px; border-radius: 4px;"
          />
        </template>
      </el-table-column>

      <el-table-column align="center" label="标题" min-width="150">
        <template #default="scope">
          <span>{{ scope.row.title }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="用户" width="150">
        <template #default="scope">
          <div class="user-info">
            <el-avatar :size="30" :src="scope.row.avatar"></el-avatar>
            <span>{{ scope.row.nickname }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="上传时间" width="180">
        <template #default="scope">
          <span>{{ formatTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="200">
        <template #default="scope">
          <el-button
            v-if="scope.row.status === 0"
            size="mini"
            type="success"
            @click="handleApprove(scope.row)"
          >
            通过
          </el-button>
          <el-button
            v-if="scope.row.status === 0"
            size="mini"
            type="danger"
            @click="handleReject(scope.row)"
          >
            拒绝
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 拒绝对话框 -->
    <el-dialog
      title="拒绝原因"
      :visible.sync="rejectDialogVisible"
      width="500px"
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝原因">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmReject">确认</el-button>
      </div>
    </el-dialog>

    <!-- 照片详情对话框 -->
    <el-dialog
      title="照片详情"
      :visible.sync="detailDialogVisible"
      width="800px"
    >
      <div v-if="currentPhoto" class="photo-detail">
        <div class="photo-main">
          <el-image
            :src="currentPhoto.url"
            fit="contain"
            style="width: 100%; max-height: 400px;"
          />
        </div>
        <div class="photo-info">
          <div class="info-item">
            <span class="label">ID:</span>
            <span>{{ currentPhoto.photoId }}</span>
          </div>
          <div class="info-item">
            <span class="label">标题:</span>
            <span>{{ currentPhoto.title }}</span>
          </div>
          <div class="info-item">
            <span class="label">描述:</span>
            <span>{{ currentPhoto.description }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户:</span>
            <span>{{ currentPhoto.nickname }} (ID: {{ currentPhoto.userId }})</span>
          </div>
          <div class="info-item">
            <span class="label">上传时间:</span>
            <span>{{ formatTime(currentPhoto.createdAt) }}</span>
          </div>
          <div class="info-item">
            <span class="label">状态:</span>
            <el-tag :type="getStatusType(currentPhoto.status)">
              {{ getStatusText(currentPhoto.status) }}
            </el-tag>
          </div>
          <div v-if="currentPhoto.status === 2" class="info-item">
            <span class="label">拒绝原因:</span>
            <span>{{ currentPhoto.rejectReason }}</span>
          </div>
          <div v-if="currentPhoto.lastAuditTime" class="info-item">
            <span class="label">最近审核时间:</span>
            <span>{{ formatTime(currentPhoto.lastAuditTime) }}</span>
          </div>
          <div v-if="currentPhoto.lastAuditType !== undefined" class="info-item">
            <span class="label">最近审核类型:</span>
            <span>{{ currentPhoto.lastAuditType === 0 ? '自动审核' : '人工审核' }}</span>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentPhoto && currentPhoto.status === 0"
          type="success"
          @click="handleApprove(currentPhoto)"
        >
          通过
        </el-button>
        <el-button
          v-if="currentPhoto && currentPhoto.status === 0"
          type="danger"
          @click="handleReject(currentPhoto)"
        >
          拒绝
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPendingAuditPhotos, auditPhoto, batchAuditPhotos } from '@/api/photo.ts'
import Pagination from '@/components/Pagination.vue'
import waves from '@/directive/waves'

export default {
  name: 'PhotoAudit',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        keyword: '',
        status: 0 // 默认查询待审核的照片
      },
      statusOptions: [
        { label: '待审核', value: 0 },
        { label: '已通过', value: 1 },
        { label: '已拒绝', value: 2 }
      ],
      selectedPhotos: [],
      rejectDialogVisible: false,
      rejectForm: {
        reason: '',
        photoId: null,
        isBatch: false
      },
      detailDialogVisible: false,
      currentPhoto: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getPendingAuditPhotos({
        page: this.listQuery.page,
        size: this.listQuery.limit,
        keyword: this.listQuery.keyword,
        status: this.listQuery.status
      }).then(response => {
        this.list = response.data.records
        this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.selectedPhotos = val
    },
    handleApprove(row) {
      this.$confirm('确认通过该照片?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }).then(() => {
        auditPhoto({
          photoId: row.photoId,
          approved: true
        }).then(response => {
          this.$message({
            type: 'success',
            message: '审核通过成功!'
          })
          this.getList()
          if (this.detailDialogVisible) {
            this.detailDialogVisible = false
          }
        })
      })
    },
    handleReject(row) {
      this.rejectForm.photoId = row.photoId
      this.rejectForm.isBatch = false
      this.rejectDialogVisible = true
    },
    handleBatchApprove() {
      if (this.selectedPhotos.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一张照片'
        })
        return
      }

      this.$confirm(`确认批量通过选中的 ${this.selectedPhotos.length} 张照片?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }).then(() => {
        const photoIds = this.selectedPhotos.map(item => item.photoId)
        batchAuditPhotos({
          photoIds,
          approved: true
        }).then(response => {
          this.$message({
            type: 'success',
            message: '批量审核通过成功!'
          })
          this.getList()
        })
      })
    },
    handleBatchReject() {
      if (this.selectedPhotos.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一张照片'
        })
        return
      }

      this.rejectForm.photoId = null
      this.rejectForm.isBatch = true
      this.rejectDialogVisible = true
    },
    confirmReject() {
      if (!this.rejectForm.reason) {
        this.$message({
          type: 'warning',
          message: '请输入拒绝原因'
        })
        return
      }

      if (this.rejectForm.isBatch) {
        // 批量拒绝
        const photoIds = this.selectedPhotos.map(item => item.photoId)
        batchAuditPhotos({
          photoIds,
          approved: false,
          rejectReason: this.rejectForm.reason
        }).then(response => {
          this.$message({
            type: 'success',
            message: '批量拒绝成功!'
          })
          this.rejectDialogVisible = false
          this.rejectForm.reason = ''
          this.getList()
        })
      } else {
        // 单个拒绝
        auditPhoto({
          photoId: this.rejectForm.photoId,
          approved: false,
          rejectReason: this.rejectForm.reason
        }).then(response => {
          this.$message({
            type: 'success',
            message: '拒绝成功!'
          })
          this.rejectDialogVisible = false
          this.rejectForm.reason = ''
          this.getList()
          if (this.detailDialogVisible) {
            this.detailDialogVisible = false
          }
        })
      }
    },
    handleDetail(row) {
      this.currentPhoto = row
      this.detailDialogVisible = true
    },
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString()
    },
    getStatusType(status) {
      switch (status) {
        case 0: return 'warning'
        case 1: return 'success'
        case 2: return 'danger'
        default: return 'info'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 0: return '待审核'
        case 1: return '已通过'
        case 2: return '已拒绝'
        default: return '未知'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-item {
  margin-right: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.photo-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.photo-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: flex-start;

  .label {
    font-weight: bold;
    width: 100px;
    flex-shrink: 0;
  }
}

@media (min-width: 768px) {
  .photo-detail {
    flex-direction: row;
  }

  .photo-main {
    flex: 1;
  }

  .photo-info {
    flex: 1;
    padding-left: 20px;
  }
}
</style>
