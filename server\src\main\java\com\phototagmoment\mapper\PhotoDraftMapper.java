package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.PhotoDraftDTO;
import com.phototagmoment.entity.PhotoDraft;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 照片草稿Mapper接口
 */
@Mapper
@Repository
public interface PhotoDraftMapper extends BaseMapper<PhotoDraft> {

    /**
     * 获取用户草稿列表
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @return 草稿列表
     */
    @Select("SELECT * FROM ptm_photo_draft WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY updated_at DESC")
    IPage<PhotoDraft> getUserDrafts(Page<PhotoDraft> page, @Param("userId") Long userId);

    /**
     * 获取草稿详情
     *
     * @param draftId 草稿ID
     * @param userId 用户ID
     * @return 草稿详情
     */
    @Select("SELECT * FROM ptm_photo_draft WHERE id = #{draftId} AND user_id = #{userId} AND is_deleted = 0")
    PhotoDraft getDraftDetail(@Param("draftId") Long draftId, @Param("userId") Long userId);
}
