package com.phototagmoment.controller.admin;

import com.phototagmoment.common.Result;
import com.phototagmoment.dto.AdminPermissionDTO;
import com.phototagmoment.entity.AdminPermission;
import com.phototagmoment.service.NewAdminService;
import com.phototagmoment.vo.AdminPermissionTreeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理员权限控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/permission")
@Tag(name = "管理员权限", description = "管理员权限相关接口")
public class AdminPermissionController {

    @Autowired
    private NewAdminService adminService;

    /**
     * 获取权限列表
     *
     * @return 权限列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取权限列表", description = "获取所有权限列表")
    public Result<List<AdminPermission>> list() {
        List<AdminPermission> permissions = adminService.getPermissionList();
        return Result.success(permissions);
    }

    /**
     * 获取权限树
     *
     * @return 权限树
     */
    @GetMapping("/tree")
    @Operation(summary = "获取权限树", description = "获取权限树形结构")
    public Result<List<AdminPermissionTreeVO>> tree() {
        List<AdminPermissionTreeVO> permissionTree = adminService.getPermissionTree();
        return Result.success(permissionTree);
    }

    /**
     * 获取权限详情
     *
     * @param id 权限ID
     * @return 权限详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取权限详情", description = "根据ID获取权限详情")
    public Result<AdminPermission> getById(@Parameter(description = "权限ID") @PathVariable Long id) {
        AdminPermission permission = adminService.getPermissionById(id);
        return Result.success(permission);
    }

    /**
     * 创建权限
     *
     * @param permissionDTO 权限信息
     * @return 权限ID
     */
    @PostMapping
    @Operation(summary = "创建权限", description = "创建新权限")
    public Result<Long> create(@RequestBody @Valid AdminPermissionDTO permissionDTO) {
        Long permissionId = adminService.createPermission(permissionDTO);
        return Result.success(permissionId);
    }

    /**
     * 更新权限
     *
     * @param id            权限ID
     * @param permissionDTO 权限信息
     * @return 是否成功
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新权限", description = "更新权限信息")
    public Result<Boolean> update(
            @Parameter(description = "权限ID") @PathVariable Long id,
            @RequestBody @Valid AdminPermissionDTO permissionDTO) {
        boolean result = adminService.updatePermission(id, permissionDTO);
        return Result.success(result);
    }

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除权限", description = "删除权限")
    public Result<Boolean> delete(@Parameter(description = "权限ID") @PathVariable Long id) {
        boolean result = adminService.deletePermission(id);
        return Result.success(result);
    }

    /**
     * 更新权限状态
     *
     * @param id     权限ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新权限状态", description = "更新权限状态")
    public Result<Boolean> updateStatus(
            @Parameter(description = "权限ID") @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam Integer status) {
        boolean result = adminService.updatePermissionStatus(id, status);
        return Result.success(result);
    }
}
