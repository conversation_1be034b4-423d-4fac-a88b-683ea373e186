package com.phototagmoment.service.impl;

import com.phototagmoment.entity.SensitiveWord;
import com.phototagmoment.mapper.SensitiveWordMapper;
import com.phototagmoment.service.SensitiveWordImportService;
import com.phototagmoment.service.SensitiveWordService;
import com.phototagmoment.service.parser.SensitiveWordFileParser;
import com.phototagmoment.service.parser.SensitiveWordParserFactory;
import com.phototagmoment.vo.SensitiveWordImportResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 敏感词导入服务实现类
 */
@Slf4j
@Service
public class SensitiveWordImportServiceImpl implements SensitiveWordImportService {

    @Autowired
    private SensitiveWordMapper sensitiveWordMapper;

    @Autowired
    private SensitiveWordService sensitiveWordService;

    @Autowired
    private SensitiveWordParserFactory parserFactory;

    private static final String DEFAULT_DIRECTORY = "Sensitive-lexicon";
    private static final String FILE_EXTENSION = ".txt";

    // 文件名类型映射
    private static final Map<String, String> FILE_TYPE_MAP = new HashMap<>();
    static {
        FILE_TYPE_MAP.put("政治", "政治");
        FILE_TYPE_MAP.put("色情", "色情");
        FILE_TYPE_MAP.put("暴力", "暴力");
        FILE_TYPE_MAP.put("贪腐", "贪腐");
        FILE_TYPE_MAP.put("毒品", "毒品");
        FILE_TYPE_MAP.put("民族", "民族");
        FILE_TYPE_MAP.put("宗教", "宗教");
        FILE_TYPE_MAP.put("网络", "网络");
        FILE_TYPE_MAP.put("其他", "其他");
    }

    // 文件名级别映射
    private static final Map<String, Integer> FILE_LEVEL_MAP = new HashMap<>();
    static {
        FILE_LEVEL_MAP.put("一般", 1);
        FILE_LEVEL_MAP.put("中等", 2);
        FILE_LEVEL_MAP.put("严重", 3);
        FILE_LEVEL_MAP.put("低", 1);
        FILE_LEVEL_MAP.put("中", 2);
        FILE_LEVEL_MAP.put("高", 3);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SensitiveWordImportResultVO importSensitiveWords() {
        return importSensitiveWords(DEFAULT_DIRECTORY);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SensitiveWordImportResultVO importSensitiveWords(String directoryPath) {
        log.info("开始导入敏感词，目录路径: {}", directoryPath);

        // 创建导入结果对象
        SensitiveWordImportResultVO result = new SensitiveWordImportResultVO();
        result.setTotalFiles(0);
        result.setSuccessFiles(0);
        result.setFailedFiles(0);
        result.setTotalWords(0);
        result.setSuccessWords(0);
        result.setFailedWords(0);
        result.setDuplicateWords(0);
        result.setTypeStats(new HashMap<>());
        result.setLevelStats(new HashMap<>());
        result.setFailedFileList(new ArrayList<>());

        // 获取目录
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            log.error("目录不存在或不是目录: {}", directoryPath);
            result.getFailedFileList().add(new SensitiveWordImportResultVO.FileError(directoryPath, "目录不存在或不是目录"));
            return result;
        }

        // 获取所有txt文件
        File[] files = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(FILE_EXTENSION));
        if (files == null || files.length == 0) {
            log.warn("目录中没有txt文件: {}", directoryPath);
            return result;
        }

        result.setTotalFiles(files.length);

        // 获取已存在的敏感词
        List<SensitiveWord> existingWords = sensitiveWordMapper.selectList(null);
        Set<String> existingWordSet = new HashSet<>();
        for (SensitiveWord word : existingWords) {
            existingWordSet.add(word.getWord());
        }

        // 处理每个文件
        for (File file : files) {
            try {
                log.info("处理文件: {}", file.getName());

                // 解析文件名，获取类型和级别
                String fileName = file.getName();
                String fileNameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

                String type = "其他";
                int level = 1;

                // 从文件名中提取类型
                for (Map.Entry<String, String> entry : FILE_TYPE_MAP.entrySet()) {
                    if (fileNameWithoutExt.contains(entry.getKey())) {
                        type = entry.getValue();
                        break;
                    }
                }

                // 从文件名中提取级别
                for (Map.Entry<String, Integer> entry : FILE_LEVEL_MAP.entrySet()) {
                    if (fileNameWithoutExt.contains(entry.getKey())) {
                        level = entry.getValue();
                        break;
                    }
                }

                // 读取文件内容
                List<String> words = readWordsFromFile(file);
                int totalWords = words.size();
                int successWords = 0;
                int failedWords = 0;
                int duplicateWords = 0;

                // 导入敏感词
                for (String word : words) {
                    if (!StringUtils.hasText(word)) {
                        continue;
                    }

                    // 检查是否已存在
                    if (existingWordSet.contains(word)) {
                        duplicateWords++;
                        continue;
                    }

                    try {
                        // 创建敏感词对象
                        SensitiveWord sensitiveWord = new SensitiveWord();
                        sensitiveWord.setWord(word);
                        sensitiveWord.setType(type);
                        sensitiveWord.setLevel(level);
                        sensitiveWord.setStatus(true);
                        sensitiveWord.setCreatedAt(LocalDateTime.now());
                        sensitiveWord.setUpdatedAt(LocalDateTime.now());

                        // 保存敏感词
                        sensitiveWordMapper.insert(sensitiveWord);

                        // 添加到已存在集合
                        existingWordSet.add(word);

                        successWords++;

                        // 更新类型统计
                        result.getTypeStats().put(type, result.getTypeStats().getOrDefault(type, 0) + 1);

                        // 更新级别统计
                        result.getLevelStats().put(level, result.getLevelStats().getOrDefault(level, 0) + 1);
                    } catch (Exception e) {
                        log.error("导入敏感词失败: {}, 错误: {}", word, e.getMessage());
                        failedWords++;
                    }
                }

                // 更新文件处理结果
                result.setSuccessFiles(result.getSuccessFiles() + 1);
                result.setTotalWords(result.getTotalWords() + totalWords);
                result.setSuccessWords(result.getSuccessWords() + successWords);
                result.setFailedWords(result.getFailedWords() + failedWords);
                result.setDuplicateWords(result.getDuplicateWords() + duplicateWords);

                log.info("文件处理完成: {}, 总词数: {}, 成功: {}, 失败: {}, 重复: {}",
                        file.getName(), totalWords, successWords, failedWords, duplicateWords);
            } catch (Exception e) {
                log.error("处理文件失败: {}, 错误: {}", file.getName(), e.getMessage(), e);
                result.setFailedFiles(result.getFailedFiles() + 1);
                result.getFailedFileList().add(new SensitiveWordImportResultVO.FileError(file.getName(), e.getMessage()));
            }
        }

        // 刷新敏感词缓存
        try {
            sensitiveWordService.refreshSensitiveWordMap();
            log.info("敏感词缓存刷新成功");
        } catch (Exception e) {
            log.error("敏感词缓存刷新失败: {}", e.getMessage(), e);
        }

        log.info("敏感词导入完成，总文件数: {}, 成功: {}, 失败: {}, 总词数: {}, 成功: {}, 失败: {}, 重复: {}",
                result.getTotalFiles(), result.getSuccessFiles(), result.getFailedFiles(),
                result.getTotalWords(), result.getSuccessWords(), result.getFailedWords(), result.getDuplicateWords());

        return result;
    }

    /**
     * 从文件中读取敏感词
     *
     * @param file 文件
     * @return 敏感词列表
     */
    private List<String> readWordsFromFile(File file) throws Exception {
        List<String> words = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 去除空白字符
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }

                // 处理可能的分隔符（逗号、空格、制表符等）
                String[] lineWords = line.split("[,\\s\\t]+");
                for (String word : lineWords) {
                    word = word.trim();
                    if (!word.isEmpty()) {
                        words.add(word);
                    }
                }
            }
        }

        return words;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SensitiveWordImportResultVO importFromFile(MultipartFile file) {
        log.info("开始从文件导入敏感词: {}", file.getOriginalFilename());

        // 创建导入结果对象
        SensitiveWordImportResultVO result = new SensitiveWordImportResultVO();
        result.setTotalFiles(1);
        result.setSuccessFiles(0);
        result.setFailedFiles(0);
        result.setTotalWords(0);
        result.setSuccessWords(0);
        result.setFailedWords(0);
        result.setDuplicateWords(0);
        result.setTypeStats(new HashMap<>());
        result.setLevelStats(new HashMap<>());
        result.setFailedFileList(new ArrayList<>());

        try {
            // 获取适合的解析器
            SensitiveWordFileParser parser = parserFactory.getParser(file);

            // 解析文件
            List<SensitiveWord> sensitiveWords = parser.parse(file);

            // 获取已存在的敏感词
            List<SensitiveWord> existingWords = sensitiveWordMapper.selectList(null);
            Set<String> existingWordSet = new HashSet<>();
            for (SensitiveWord word : existingWords) {
                existingWordSet.add(word.getWord());
            }

            // 导入敏感词
            int totalWords = sensitiveWords.size();
            int successWords = 0;
            int failedWords = 0;
            int duplicateWords = 0;

            for (SensitiveWord sensitiveWord : sensitiveWords) {
                try {
                    // 检查是否已存在
                    if (existingWordSet.contains(sensitiveWord.getWord())) {
                        duplicateWords++;
                        continue;
                    }

                    // 保存敏感词
                    sensitiveWordMapper.insert(sensitiveWord);

                    // 添加到已存在集合
                    existingWordSet.add(sensitiveWord.getWord());

                    successWords++;

                    // 更新类型统计
                    String type = sensitiveWord.getType();
                    result.getTypeStats().put(type, result.getTypeStats().getOrDefault(type, 0) + 1);

                    // 更新级别统计
                    int level = sensitiveWord.getLevel();
                    result.getLevelStats().put(level, result.getLevelStats().getOrDefault(level, 0) + 1);
                } catch (Exception e) {
                    log.error("导入敏感词失败: {}, 错误: {}", sensitiveWord.getWord(), e.getMessage());
                    failedWords++;
                }
            }

            // 更新处理结果
            result.setSuccessFiles(1);
            result.setTotalWords(totalWords);
            result.setSuccessWords(successWords);
            result.setFailedWords(failedWords);
            result.setDuplicateWords(duplicateWords);

            log.info("文件处理完成: {}, 总词数: {}, 成功: {}, 失败: {}, 重复: {}",
                    file.getOriginalFilename(), totalWords, successWords, failedWords, duplicateWords);

            // 刷新敏感词缓存
            try {
                sensitiveWordService.refreshSensitiveWordMap();
                log.info("敏感词缓存刷新成功");
            } catch (Exception e) {
                log.error("敏感词缓存刷新失败: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("处理文件失败: {}, 错误: {}", file.getOriginalFilename(), e.getMessage(), e);
            result.setFailedFiles(1);
            result.getFailedFileList().add(new SensitiveWordImportResultVO.FileError(file.getOriginalFilename(), e.getMessage()));
        }

        log.info("敏感词导入完成，总词数: {}, 成功: {}, 失败: {}, 重复: {}",
                result.getTotalWords(), result.getSuccessWords(), result.getFailedWords(), result.getDuplicateWords());

        return result;
    }
}
