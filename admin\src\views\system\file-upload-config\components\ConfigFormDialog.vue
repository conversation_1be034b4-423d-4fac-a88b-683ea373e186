<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑配置' : '新增配置'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="config-form"
    >
      <!-- 基本信息 -->
      <el-card class="form-section" header="基本信息">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="配置名称" prop="configName">
              <el-input
                v-model="formData.configName"
                placeholder="请输入配置名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存储类型" prop="storageType">
              <el-select
                v-model="formData.storageType"
                placeholder="请选择存储类型"
                style="width: 100%"
                @change="handleStorageTypeChange"
              >
                <el-option label="本地存储" value="LOCAL" />
                <el-option label="七牛云" value="QINIU" />
                <el-option label="阿里云OSS" value="ALIYUN_OSS" />
                <el-option label="腾讯云COS" value="TENCENT_COS" />
                <el-option label="AWS S3" value="AWS_S3" />
                <el-option label="MinIO" value="MINIO" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="启用状态">
              <el-switch v-model="formData.enabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设为默认">
              <el-switch v-model="formData.isDefault" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="配置描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-card>

      <!-- 存储配置 -->
      <el-card class="form-section" header="存储配置">
        <!-- 通用配置 -->
        <div class="config-group">
          <h4>通用配置</h4>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="访问域名">
                <el-input
                  v-model="formData.configParams.domain"
                  placeholder="请输入访问域名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用HTTPS">
                <el-switch v-model="formData.configParams.useHttps" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="连接超时(秒)">
                <el-input-number
                  v-model="formData.configParams.connectTimeout"
                  :min="1"
                  :max="300"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="读取超时(秒)">
                <el-input-number
                  v-model="formData.configParams.readTimeout"
                  :min="1"
                  :max="600"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 本地存储配置 -->
        <div v-if="formData.storageType === 'LOCAL'" class="config-group">
          <h4>本地存储配置</h4>
          <el-form-item label="存储路径">
            <el-input
              v-model="formData.configParams.localPath"
              placeholder="请输入本地存储路径"
            />
          </el-form-item>
        </div>

        <!-- 七牛云配置 -->
        <div v-if="formData.storageType === 'QINIU'" class="config-group">
          <h4>七牛云配置</h4>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="AccessKey" prop="configParams.qiniuAccessKey">
                <el-input
                  v-model="formData.configParams.qiniuAccessKey"
                  placeholder="请输入七牛云AccessKey"
                  show-password
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="SecretKey" prop="configParams.qiniuSecretKey">
                <el-input
                  v-model="formData.configParams.qiniuSecretKey"
                  placeholder="请输入七牛云SecretKey"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="存储空间" prop="configParams.qiniuBucket">
                <el-input
                  v-model="formData.configParams.qiniuBucket"
                  placeholder="请输入存储空间名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="存储区域">
                <el-select
                  v-model="formData.configParams.qiniuRegion"
                  placeholder="请选择存储区域"
                  style="width: 100%"
                >
                  <el-option label="华东-浙江" value="z0" />
                  <el-option label="华北-河北" value="z1" />
                  <el-option label="华南-广东" value="z2" />
                  <el-option label="北美-洛杉矶" value="na0" />
                  <el-option label="亚太-新加坡" value="as0" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 阿里云OSS配置 -->
        <div v-if="formData.storageType === 'ALIYUN_OSS'" class="config-group">
          <h4>阿里云OSS配置</h4>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="AccessKeyId" prop="configParams.aliyunAccessKeyId">
                <el-input
                  v-model="formData.configParams.aliyunAccessKeyId"
                  placeholder="请输入AccessKeyId"
                  show-password
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="AccessKeySecret" prop="configParams.aliyunAccessKeySecret">
                <el-input
                  v-model="formData.configParams.aliyunAccessKeySecret"
                  placeholder="请输入AccessKeySecret"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="Bucket名称" prop="configParams.aliyunBucket">
                <el-input
                  v-model="formData.configParams.aliyunBucket"
                  placeholder="请输入Bucket名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Endpoint" prop="configParams.aliyunEndpoint">
                <el-input
                  v-model="formData.configParams.aliyunEndpoint"
                  placeholder="请输入Endpoint"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 腾讯云COS配置 -->
        <div v-if="formData.storageType === 'TENCENT_COS'" class="config-group">
          <h4>腾讯云COS配置</h4>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="SecretId" prop="configParams.tencentSecretId">
                <el-input
                  v-model="formData.configParams.tencentSecretId"
                  placeholder="请输入SecretId"
                  show-password
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="SecretKey" prop="configParams.tencentSecretKey">
                <el-input
                  v-model="formData.configParams.tencentSecretKey"
                  placeholder="请输入SecretKey"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="Bucket名称" prop="configParams.tencentBucket">
                <el-input
                  v-model="formData.configParams.tencentBucket"
                  placeholder="请输入Bucket名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地域" prop="configParams.tencentRegion">
                <el-input
                  v-model="formData.configParams.tencentRegion"
                  placeholder="请输入地域，如：ap-beijing"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- AWS S3配置 -->
        <div v-if="formData.storageType === 'AWS_S3'" class="config-group">
          <h4>AWS S3配置</h4>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="AccessKey" prop="configParams.awsAccessKey">
                <el-input
                  v-model="formData.configParams.awsAccessKey"
                  placeholder="请输入AccessKey"
                  show-password
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="SecretKey" prop="configParams.awsSecretKey">
                <el-input
                  v-model="formData.configParams.awsSecretKey"
                  placeholder="请输入SecretKey"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="Bucket名称" prop="configParams.awsBucket">
                <el-input
                  v-model="formData.configParams.awsBucket"
                  placeholder="请输入Bucket名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地域" prop="configParams.awsRegion">
                <el-input
                  v-model="formData.configParams.awsRegion"
                  placeholder="请输入地域，如：us-east-1"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- MinIO配置 -->
        <div v-if="formData.storageType === 'MINIO'" class="config-group">
          <h4>MinIO配置</h4>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="服务器地址" prop="configParams.minioEndpoint">
                <el-input
                  v-model="formData.configParams.minioEndpoint"
                  placeholder="请输入MinIO服务器地址"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Bucket名称" prop="configParams.minioBucket">
                <el-input
                  v-model="formData.configParams.minioBucket"
                  placeholder="请输入Bucket名称"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="AccessKey" prop="configParams.minioAccessKey">
                <el-input
                  v-model="formData.configParams.minioAccessKey"
                  placeholder="请输入AccessKey"
                  show-password
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="SecretKey" prop="configParams.minioSecretKey">
                <el-input
                  v-model="formData.configParams.minioSecretKey"
                  placeholder="请输入SecretKey"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 上传限制配置 -->
      <UploadLimitsConfig v-model="formData.uploadLimits" />

      <!-- 路径配置 -->
      <PathConfig v-model="formData.pathConfig" />
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="info" @click="handleTest" :loading="testing">
          测试连接
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  createConfig,
  updateConfig,
  testConfigWithoutSave,
  checkConfigName,
  type FileUploadConfig
} from '@/api/fileUploadConfig'
import UploadLimitsConfig from './UploadLimitsConfig.vue'
import PathConfig from './PathConfig.vue'

// Props
interface Props {
  modelValue: boolean
  config?: FileUploadConfig | null
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  config: null,
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const testing = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive<FileUploadConfig>({
  configName: '',
  storageType: 'LOCAL',
  enabled: true,
  isDefault: false,
  description: '',
  configParams: {
    domain: '',
    useHttps: false,
    connectTimeout: 30,
    readTimeout: 60
  },
  uploadLimits: {
    maxFileSize: 50,
    maxFileCount: 10,
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    enableFileTypeCheck: true,
    enableContentCheck: false,
    enableVirusScan: false
  },
  pathConfig: {
    rootPath: 'uploads',
    fileNamingRule: 'UUID',
    directoryStructure: 'DATE_USER_TYPE',
    enableDateDirectory: true,
    enableUserDirectory: true,
    enableTypeDirectory: true,
    thumbnailDirectory: 'thumbnails',
    tempDirectory: 'temp'
  }
})

// 表单验证规则
const formRules = {
  configName: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 1, max: 50, message: '配置名称长度应在1-50个字符之间', trigger: 'blur' },
    { validator: validateConfigName, trigger: 'blur' }
  ],
  storageType: [
    { required: true, message: '请选择存储类型', trigger: 'change' }
  ],
  'configParams.qiniuAccessKey': [
    { required: true, message: '请输入七牛云AccessKey', trigger: 'blur' }
  ],
  'configParams.qiniuSecretKey': [
    { required: true, message: '请输入七牛云SecretKey', trigger: 'blur' }
  ],
  'configParams.qiniuBucket': [
    { required: true, message: '请输入存储空间名称', trigger: 'blur' }
  ],
  // 其他存储类型的验证规则...
}

// 方法
const resetFormData = () => {
  // 重置表单数据为默认值
  Object.assign(formData, {
    id: undefined,
    configName: '',
    storageType: 'LOCAL',
    enabled: true,
    isDefault: false,
    description: '',
    configParams: {
      domain: '',
      useHttps: false,
      connectTimeout: 30,
      readTimeout: 60
    },
    uploadLimits: {
      maxFileSize: 50,
      maxFileCount: 10,
      allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
      enableFileTypeCheck: true,
      enableContentCheck: false,
      enableVirusScan: false
    },
    pathConfig: {
      rootPath: 'uploads',
      fileNamingRule: 'UUID',
      directoryStructure: 'DATE_USER_TYPE',
      enableDateDirectory: true,
      enableUserDirectory: true,
      enableTypeDirectory: true,
      thumbnailDirectory: 'thumbnails',
      tempDirectory: 'temp'
    }
  })
}

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    // 编辑模式：加载现有配置数据
    Object.assign(formData, {
      ...newConfig,
      configParams: { ...formData.configParams, ...newConfig.configParams },
      uploadLimits: { ...formData.uploadLimits, ...newConfig.uploadLimits },
      pathConfig: { ...formData.pathConfig, ...newConfig.pathConfig }
    })
  } else {
    // 新增模式：重置为默认值
    resetFormData()
  }
}, { immediate: true, deep: true })

const handleStorageTypeChange = () => {
  // 清空存储相关配置
  const commonParams = {
    domain: formData.configParams.domain,
    useHttps: formData.configParams.useHttps,
    connectTimeout: formData.configParams.connectTimeout,
    readTimeout: formData.configParams.readTimeout
  }

  formData.configParams = commonParams
}

const handleTest = async () => {
  try {
    testing.value = true
    const response = await testConfigWithoutSave(formData)

    if ((response as any).code === 200) {
      const result = (response as any).data
      if (result.success) {
        ElMessage.success(`连接测试成功！响应时间：${result.responseTime}ms`)
      } else {
        ElMessage.error(`连接测试失败：${result.message}`)
      }
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('测试连接失败')
  } finally {
    testing.value = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    submitting.value = true

    let response
    if (props.isEdit) {
      response = await updateConfig(formData.id!, formData)
    } else {
      response = await createConfig(formData)
    }

    if ((response as any).code === 200) {
      ElMessage.success((response as any).message)
      emit('success')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  // 重置表单验证状态
  formRef.value?.resetFields()
  // 如果是新增模式，重置表单数据
  if (!props.isEdit) {
    resetFormData()
  }
  emit('update:modelValue', false)
}

// 验证配置名称
async function validateConfigName(rule: any, value: string, callback: any) {
  if (!value) {
    callback()
    return
  }

  try {
    const excludeId = props.isEdit ? formData.id : undefined
    const response = await checkConfigName(value, excludeId)

    if ((response as any).code === 200 && !(response as any).data) {
      callback(new Error('配置名称已存在'))
    } else {
      callback()
    }
  } catch (error) {
    callback()
  }
}
</script>

<style scoped>
.config-form {
  max-height: 600px;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 20px;
}

.config-group {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.config-group h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}
</style>
