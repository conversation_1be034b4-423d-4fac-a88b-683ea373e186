import request from '@/utils/request'
import { AxiosResponse } from 'axios'

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 用户登录
 * @param data 登录参数
 */
export function login(data: { username: string; password: string }) {
  console.log('调用登录API，请求数据:', data);
  return request({
    url: '/auth/login',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 用户注册
 * @param data 注册参数
 */
export function register(data: { username: string; password: string; nickname?: string }) {
  return request({
    url: '/register',
    method: 'post',
    data
  })
}

/**
 * 获取当前用户信息
 */
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

/**
 * 退出登录
 */
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

/**
 * 获取第三方授权URL
 * @param source 第三方平台来源(qq, wechat等)
 * @param state 状态参数，用于防止CSRF攻击
 */
export function getAuthUrl(source: string, state?: string): Promise<ApiResponse<{authUrl: string, state: string}>> {
  return request({
    url: `/auth/third-party/${source}/auth-url`,
    method: 'get',
    params: state ? { state } : undefined
  })
}

/**
 * 第三方登录
 * @param source 第三方平台来源(qq, wechat等)
 * @param code 授权码
 * @param state 状态参数，用于验证请求合法性
 */
export function loginByThirdParty(source: string, code: string, state?: string): Promise<ApiResponse<{token: string, user: any}>> {
  return request({
    url: `/auth/third-party/${source}/login`,
    method: 'post',
    params: state ? { code, state } : { code }
  })
}

/**
 * 获取QQ授权URL
 * @param state 状态参数，用于防止CSRF攻击
 * @deprecated 使用 getAuthUrl('qq', state) 代替
 */
export function getQQAuthUrl(state?: string): Promise<ApiResponse<{authUrl: string, state: string}>> {
  return getAuthUrl('qq', state);
}

/**
 * QQ登录
 * @param code 授权码
 * @param state 状态参数，用于验证请求合法性
 * @deprecated 使用 loginByThirdParty('qq', code, state) 代替
 */
export function loginByQQ(code: string, state?: string): Promise<ApiResponse<{token: string, user: any}>> {
  return loginByThirdParty('qq', code, state);
}

/**
 * 获取微信授权URL
 * @deprecated 使用 getAuthUrl('wechat', state) 代替
 */
export function getWechatAuthUrl(state?: string) {
  return getAuthUrl('wechat', state);
}

/**
 * 微信登录
 * @param code 授权码
 * @param state 状态参数，用于验证请求合法性
 * @deprecated 使用 loginByThirdParty('wechat', code, state) 代替
 */
export function loginByWechat(code: string, state?: string) {
  return loginByThirdParty('wechat', code, state);
}

/**
 * 微信小程序登录
 * @param code 授权码
 * @param encryptedData 加密数据
 * @param iv 加密算法的初始向量
 * @deprecated 使用专门的小程序登录接口
 */
export function loginByWechatMiniApp(code: string, encryptedData?: string, iv?: string) {
  return request({
    url: '/auth/third-party/wechat/mini-app/login',
    method: 'post',
    params: { code, encryptedData, iv }
  })
}

/**
 * 检查微信登录状态
 * @param state 状态ID
 */
export function checkWechatLoginStatus(state: string) {
  return request({
    url: '/wechat/mp/check-login',
    method: 'get',
    params: { state }
  })
}

/**
 * 绑定第三方账号
 * @param source 第三方平台来源(qq, wechat等)
 * @param openId 第三方平台唯一标识
 * @param accessToken 访问令牌
 */
export function bindThirdParty(source: string, openId: string, accessToken: string) {
  return request({
    url: `/auth/third-party/${source}/bind`,
    method: 'post',
    params: { openId, accessToken }
  })
}

/**
 * 绑定QQ账号
 * @param openId QQ OpenID
 * @param accessToken 访问令牌
 * @deprecated 使用 bindThirdParty('qq', openId, accessToken) 代替
 */
export function bindQQ(openId: string, accessToken: string) {
  return bindThirdParty('qq', openId, accessToken);
}

/**
 * 绑定微信账号
 * @param openId 微信OpenID
 * @param accessToken 访问令牌
 * @deprecated 使用 bindThirdParty('wechat', openId, accessToken) 代替
 */
export function bindWechat(openId: string, accessToken: string) {
  return bindThirdParty('wechat', openId, accessToken);
}

/**
 * 绑定微信小程序账号
 * @param openId 微信OpenID
 * @param sessionKey 会话密钥
 * @deprecated 使用 bindThirdParty('wechat_mini', openId, sessionKey) 代替
 */
export function bindWechatMiniApp(openId: string, sessionKey: string) {
  return bindThirdParty('wechat_mini', openId, sessionKey);
}

/**
 * 解绑第三方账号
 * @param source 第三方平台来源(qq, wechat等)
 */
export function unbindThirdParty(source: string) {
  return request({
    url: `/auth/third-party/${source}/unbind`,
    method: 'post'
  })
}

/**
 * 解绑QQ账号
 * @deprecated 使用 unbindThirdParty('qq') 代替
 */
export function unbindQQ() {
  return unbindThirdParty('qq');
}

/**
 * 解绑微信账号
 * @deprecated 使用 unbindThirdParty('wechat') 代替
 */
export function unbindWechat() {
  return unbindThirdParty('wechat');
}

/**
 * 解绑微信小程序账号
 * @deprecated 使用 unbindThirdParty('wechat_mini') 代替
 */
export function unbindWechatMiniApp() {
  return unbindThirdParty('wechat_mini');
}

/**
 * 获取绑定的第三方账号列表
 */
export function getBindingList() {
  return request({
    url: '/auth/third-party/binding-list',
    method: 'get'
  })
}
