<template>
  <component :is="type" v-bind="linkProps">
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { isExternal } from '../utils/validate'

const props = defineProps({
  to: {
    type: String,
    required: true
  }
})

// 链接类型
const type = computed(() => {
  return isExternal(props.to) ? 'a' : 'router-link'
})

// 链接属性
const linkProps = computed(() => {
  return isExternal(props.to)
    ? {
        href: props.to,
        target: '_blank',
        rel: 'noopener'
      }
    : {
        to: props.to
      }
})
</script>
