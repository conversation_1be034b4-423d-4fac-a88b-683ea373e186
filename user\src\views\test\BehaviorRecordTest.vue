<template>
  <div class="behavior-test">
    <van-nav-bar title="行为记录接口测试" left-arrow @click-left="$router.back()" />
    
    <div class="test-container">
      <van-cell-group>
        <van-cell title="后端状态" :value="backendStatusText" :label="currentTime" />
      </van-cell-group>

      <div class="test-section">
        <h3>测试用例</h3>
        
        <!-- 测试1: 使用noteId -->
        <van-button 
          type="primary" 
          block 
          @click="testWithNoteId"
          :loading="loading1"
          style="margin: 10px 0;"
        >
          测试1: 使用noteId字段
        </van-button>
        
        <!-- 测试2: 使用photoId -->
        <van-button 
          type="success" 
          block 
          @click="testWithPhotoId"
          :loading="loading2"
          style="margin: 10px 0;"
        >
          测试2: 使用photoId字段
        </van-button>
        
        <!-- 测试3: 同时发送两个字段 -->
        <van-button 
          type="warning" 
          block 
          @click="testWithBothFields"
          :loading="loading3"
          style="margin: 10px 0;"
        >
          测试3: 同时发送两个字段
        </van-button>
        
        <!-- 测试4: 使用前端API函数 -->
        <van-button 
          type="default" 
          block 
          @click="testWithApiFunction"
          :loading="loading4"
          style="margin: 10px 0;"
        >
          测试4: 使用前端API函数
        </van-button>
      </div>

      <div class="response-section">
        <h3>响应结果</h3>
        <van-cell-group>
          <van-cell title="最新响应" :value="latestResponse" />
        </van-cell-group>
        
        <div class="response-log">
          <h4>详细日志</h4>
          <div class="log-content">
            <div v-for="(log, index) in responseLogs" :key="index" class="log-item">
              <div class="log-time">{{ log.time }}</div>
              <div class="log-method">{{ log.method }}</div>
              <div class="log-status" :class="log.success ? 'success' : 'error'">
                {{ log.status }}
              </div>
              <div class="log-response">{{ log.response }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { recordUserBehavior } from '@/api/recommendation'
import request from '@/utils/request'

const router = useRouter()

// 响应式数据
const backendStatusText = ref('检测中...')
const currentTime = ref('')
const latestResponse = ref('暂无')
const responseLogs = ref([])
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)
const loading4 = ref(false)

// 定时器
let timer = null

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 检查后端状态
const checkBackendStatus = async () => {
  try {
    const response = await fetch('http://localhost:8081/api/recommendation/home?page=1&size=1', { 
      method: 'GET',
      mode: 'cors'
    })
    if (response.ok) {
      backendStatusText.value = '在线'
    } else {
      backendStatusText.value = `错误 ${response.status}`
    }
  } catch (error) {
    backendStatusText.value = '离线'
  }
}

// 添加日志
const addLog = (method, status, response, success = true) => {
  responseLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    method,
    status,
    response: JSON.stringify(response, null, 2),
    success
  })
  
  // 只保留最近10条日志
  if (responseLogs.value.length > 10) {
    responseLogs.value = responseLogs.value.slice(0, 10)
  }
  
  latestResponse.value = success ? '成功' : '失败'
}

// 测试1: 使用noteId字段
const testWithNoteId = async () => {
  loading1.value = true
  try {
    const response = await request({
      url: '/recommendation/record-behavior',
      method: 'post',
      data: {
        noteId: 31,
        behavior: 'view'
      }
    })
    addLog('POST noteId', '200', response, true)
  } catch (error) {
    addLog('POST noteId', 'ERROR', error.message, false)
  } finally {
    loading1.value = false
  }
}

// 测试2: 使用photoId字段
const testWithPhotoId = async () => {
  loading2.value = true
  try {
    const response = await request({
      url: '/recommendation/record-behavior',
      method: 'post',
      data: {
        photoId: 31,
        behavior: 'like'
      }
    })
    addLog('POST photoId', '200', response, true)
  } catch (error) {
    addLog('POST photoId', 'ERROR', error.message, false)
  } finally {
    loading2.value = false
  }
}

// 测试3: 同时发送两个字段
const testWithBothFields = async () => {
  loading3.value = true
  try {
    const response = await request({
      url: '/recommendation/record-behavior',
      method: 'post',
      data: {
        noteId: 31,
        photoId: 31,
        behavior: 'collect'
      }
    })
    addLog('POST both', '200', response, true)
  } catch (error) {
    addLog('POST both', 'ERROR', error.message, false)
  } finally {
    loading3.value = false
  }
}

// 测试4: 使用前端API函数
const testWithApiFunction = async () => {
  loading4.value = true
  try {
    const response = await recordUserBehavior({
      photoId: 31,
      behavior: 'share'
    })
    addLog('API Function', '200', response, true)
  } catch (error) {
    addLog('API Function', 'ERROR', error.message, false)
  } finally {
    loading4.value = false
  }
}

// 生命周期
onMounted(() => {
  updateTime()
  checkBackendStatus()
  
  // 每秒更新时间
  timer = setInterval(() => {
    updateTime()
  }, 1000)
  
  // 每10秒检查后端状态
  setInterval(checkBackendStatus, 10000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.behavior-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-container {
  padding: 16px;
}

.test-section {
  margin: 20px 0;
}

.test-section h3 {
  margin-bottom: 16px;
  color: #323233;
  font-size: 16px;
  font-weight: 600;
}

.response-section {
  margin-top: 30px;
}

.response-section h3 {
  margin-bottom: 16px;
  color: #323233;
  font-size: 16px;
  font-weight: 600;
}

.response-section h4 {
  margin: 16px 0 8px 0;
  color: #646566;
  font-size: 14px;
  font-weight: 500;
}

.response-log {
  margin-top: 16px;
}

.log-content {
  background: white;
  border-radius: 8px;
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  padding: 8px 0;
  border-bottom: 1px solid #ebedf0;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #969799;
  margin-bottom: 4px;
}

.log-method {
  color: #1989fa;
  font-weight: 500;
  margin-bottom: 4px;
}

.log-status {
  margin-bottom: 4px;
  font-weight: 500;
}

.log-status.success {
  color: #07c160;
}

.log-status.error {
  color: #ee0a24;
}

.log-response {
  background: #f7f8fa;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 11px;
  color: #323233;
}
</style>
