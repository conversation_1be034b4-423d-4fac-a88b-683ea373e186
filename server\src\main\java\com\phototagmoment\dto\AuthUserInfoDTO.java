package com.phototagmoment.dto;

import lombok.Data;
import me.zhyd.oauth.model.AuthUser;

/**
 * 第三方用户信息DTO
 */
@Data
public class AuthUserInfoDTO {

    /**
     * 用户OpenID
     */
    private String openId;

    /**
     * 用户UnionID
     */
    private String unionId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像(小)
     */
    private String avatarSmall;

    /**
     * 用户头像(中)
     */
    private String avatarMedium;

    /**
     * 用户头像(大)
     */
    private String avatarLarge;

    /**
     * 用户性别
     */
    private String gender;

    /**
     * 第三方平台来源
     */
    private String source;

    /**
     * 从AuthUser创建DTO
     */
    public static AuthUserInfoDTO fromAuthUser(AuthUser authUser) {
        if (authUser == null) {
            return null;
        }

        AuthUserInfoDTO dto = new AuthUserInfoDTO();
        dto.setOpenId(authUser.getUuid());
        dto.setNickname(authUser.getNickname());
        dto.setAvatarSmall(authUser.getAvatar());
        dto.setAvatarMedium(authUser.getAvatar());
        dto.setAvatarLarge(authUser.getAvatar());
        dto.setGender(authUser.getGender().getDesc());
        dto.setSource(authUser.getSource());
        return dto;
    }

    /**
     * 创建DTO
     */
    public static AuthUserInfoDTO create(String openId, String nickname, String avatarSmall,
                                         String avatarMedium, String avatarLarge, String gender, String source) {
        AuthUserInfoDTO dto = new AuthUserInfoDTO();
        dto.setOpenId(openId);
        dto.setNickname(nickname);
        dto.setAvatarSmall(avatarSmall);
        dto.setAvatarMedium(avatarMedium);
        dto.setAvatarLarge(avatarLarge);
        dto.setGender(gender);
        dto.setSource(source);
        return dto;
    }
}
