import request from '../utils/request'

// 管理员登录
export function login(data: any) {
  return request({
    url: '/admin/system/login',
    method: 'post',
    data
  })
}

// 获取管理员信息
export function getAdminInfo() {
  return request({
    url: '/admin/system/info',
    method: 'get'
  })
}

// 管理员登出
export function logout() {
  return request({
    url: '/admin/system/logout',
    method: 'post'
  })
}

// 获取用户列表
export function getUserList(params: any) {
  return request({
    url: '/admin/user/list',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(id: number) {
  return request({
    url: `/admin/user/${id}`,
    method: 'get'
  })
}

// 更新用户状态
export function updateUserStatus(id: number, status: number) {
  return request({
    url: `/admin/user/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取实名认证列表
export function getVerificationList(params: any) {
  return request({
    url: '/admin/user/verification/list',
    method: 'get',
    params
  })
}

// 审核实名认证
export function verifyUser(id: number, data: any) {
  return request({
    url: `/admin/user/verification/${id}`,
    method: 'put',
    data
  })
}
