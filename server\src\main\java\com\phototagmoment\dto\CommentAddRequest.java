package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 添加评论请求（完整版）
 * 支持普通评论、回复评论、标签、用户提及
 */
@Data
@Schema(description = "添加评论请求")
public class CommentAddRequest {

    @Schema(description = "照片ID")
    @NotNull(message = "照片ID不能为空")
    private Long photoId;

    @Schema(description = "评论内容")
    @NotBlank(message = "评论内容不能为空")
    private String content;

    @Schema(description = "父评论ID（回复时使用）")
    private Long parentId;

    @Schema(description = "回复的用户ID")
    private Long replyToUserId;

    @Schema(description = "回复的用户名")
    private String replyToUsername;

    @Schema(description = "标签列表")
    private List<String> tags;

    @Schema(description = "用户提及列表")
    private List<String> mentions;
}
