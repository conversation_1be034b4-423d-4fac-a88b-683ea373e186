/**
 * 日期时间格式化工具函数
 */

/**
 * 格式化日期时间
 * @param date 日期对象、字符串或时间戳
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(date: Date | string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) {
    return ''
  }
  
  let d: Date
  
  if (typeof date === 'number') {
    d = new Date(date)
  } else if (typeof date === 'string') {
    d = new Date(date)
  } else {
    d = date
  }
  
  if (isNaN(d.getTime())) {
    return ''
  }
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期
 * @param date 日期对象、字符串或时间戳
 * @returns 格式化后的日期字符串 (YYYY-MM-DD)
 */
export function formatDate(date: Date | string | number): string {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间
 * @param date 日期对象、字符串或时间戳
 * @returns 格式化后的时间字符串 (HH:mm:ss)
 */
export function formatTime(date: Date | string | number): string {
  return formatDateTime(date, 'HH:mm:ss')
}

/**
 * 格式化相对时间
 * @param date 日期对象、字符串或时间戳
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: Date | string | number): string {
  if (!date) {
    return ''
  }
  
  let d: Date
  
  if (typeof date === 'number') {
    d = new Date(date)
  } else if (typeof date === 'string') {
    d = new Date(date)
  } else {
    d = date
  }
  
  if (isNaN(d.getTime())) {
    return ''
  }
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  // 转换为秒
  const seconds = Math.floor(diff / 1000)
  
  if (seconds < 60) {
    return '刚刚'
  }
  
  // 转换为分钟
  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) {
    return `${minutes}分钟前`
  }
  
  // 转换为小时
  const hours = Math.floor(minutes / 60)
  if (hours < 24) {
    return `${hours}小时前`
  }
  
  // 转换为天
  const days = Math.floor(hours / 24)
  if (days < 7) {
    return `${days}天前`
  }
  
  // 转换为周
  const weeks = Math.floor(days / 7)
  if (weeks < 4) {
    return `${weeks}周前`
  }
  
  // 转换为月
  const months = Math.floor(days / 30)
  if (months < 12) {
    return `${months}个月前`
  }
  
  // 转换为年
  const years = Math.floor(days / 365)
  return `${years}年前`
}

/**
 * 判断是否为今天
 * @param date 日期对象、字符串或时间戳
 * @returns 是否为今天
 */
export function isToday(date: Date | string | number): boolean {
  if (!date) {
    return false
  }
  
  let d: Date
  
  if (typeof date === 'number') {
    d = new Date(date)
  } else if (typeof date === 'string') {
    d = new Date(date)
  } else {
    d = date
  }
  
  if (isNaN(d.getTime())) {
    return false
  }
  
  const today = new Date()
  
  return d.getFullYear() === today.getFullYear() &&
         d.getMonth() === today.getMonth() &&
         d.getDate() === today.getDate()
}

/**
 * 判断是否为昨天
 * @param date 日期对象、字符串或时间戳
 * @returns 是否为昨天
 */
export function isYesterday(date: Date | string | number): boolean {
  if (!date) {
    return false
  }
  
  let d: Date
  
  if (typeof date === 'number') {
    d = new Date(date)
  } else if (typeof date === 'string') {
    d = new Date(date)
  } else {
    d = date
  }
  
  if (isNaN(d.getTime())) {
    return false
  }
  
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  
  return d.getFullYear() === yesterday.getFullYear() &&
         d.getMonth() === yesterday.getMonth() &&
         d.getDate() === yesterday.getDate()
}

/**
 * 获取日期范围
 * @param days 天数
 * @returns 日期范围 [开始日期, 结束日期]
 */
export function getDateRange(days: number): [string, string] {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - days + 1)
  
  return [formatDate(start), formatDate(end)]
}

/**
 * 获取本周日期范围
 * @returns 本周日期范围 [周一, 周日]
 */
export function getThisWeekRange(): [string, string] {
  const now = new Date()
  const dayOfWeek = now.getDay()
  const monday = new Date(now)
  monday.setDate(now.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1))
  
  const sunday = new Date(monday)
  sunday.setDate(monday.getDate() + 6)
  
  return [formatDate(monday), formatDate(sunday)]
}

/**
 * 获取本月日期范围
 * @returns 本月日期范围 [月初, 月末]
 */
export function getThisMonthRange(): [string, string] {
  const now = new Date()
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  
  return [formatDate(firstDay), formatDate(lastDay)]
}
