package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件上传配置实体类
 */
@Data
@TableName("ptm_file_upload_config")
@Schema(description = "文件上传配置")
public class FileUploadConfig {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "配置ID")
    private Long id;

    @Schema(description = "配置名称")
    private String configName;

    @Schema(description = "存储服务商类型")
    private String storageType;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "是否为默认配置")
    private Boolean isDefault;

    @Schema(description = "配置参数JSON")
    private String configParams;

    @Schema(description = "上传限制配置JSON")
    private String uploadLimits;

    @Schema(description = "存储路径配置JSON")
    private String pathConfig;

    @Schema(description = "配置描述")
    private String description;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "配置状态：0-正常，1-禁用，2-异常")
    private Integer status;

    @Schema(description = "最后测试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastTestTime;

    @Schema(description = "最后测试结果")
    private String lastTestResult;

    @Schema(description = "创建者ID")
    private Long createdBy;

    @Schema(description = "更新者ID")
    private Long updatedBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "是否逻辑删除")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 存储服务商类型枚举
     */
    public static class StorageType {
        public static final String LOCAL = "LOCAL";           // 本地存储
        public static final String QINIU = "QINIU";           // 七牛云
        public static final String ALIYUN_OSS = "ALIYUN_OSS"; // 阿里云OSS
        public static final String TENCENT_COS = "TENCENT_COS"; // 腾讯云COS
        public static final String AWS_S3 = "AWS_S3";         // AWS S3
        public static final String MINIO = "MINIO";           // MinIO
    }

    /**
     * 配置状态枚举
     */
    public static class Status {
        public static final int NORMAL = 0;    // 正常
        public static final int DISABLED = 1;  // 禁用
        public static final int ERROR = 2;     // 异常
    }

    /**
     * 获取存储类型显示名称
     */
    public String getStorageTypeDisplayName() {
        switch (storageType) {
            case StorageType.LOCAL:
                return "本地存储";
            case StorageType.QINIU:
                return "七牛云";
            case StorageType.ALIYUN_OSS:
                return "阿里云OSS";
            case StorageType.TENCENT_COS:
                return "腾讯云COS";
            case StorageType.AWS_S3:
                return "AWS S3";
            case StorageType.MINIO:
                return "MinIO";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态显示名称
     */
    public String getStatusDisplayName() {
        switch (status) {
            case Status.NORMAL:
                return "正常";
            case Status.DISABLED:
                return "禁用";
            case Status.ERROR:
                return "异常";
            default:
                return "未知";
        }
    }

    /**
     * 判断是否为云存储
     */
    public boolean isCloudStorage() {
        return !StorageType.LOCAL.equals(storageType);
    }

    /**
     * 判断配置是否可用
     */
    public boolean isAvailable() {
        return enabled != null && enabled && 
               status != null && status == Status.NORMAL;
    }
}
