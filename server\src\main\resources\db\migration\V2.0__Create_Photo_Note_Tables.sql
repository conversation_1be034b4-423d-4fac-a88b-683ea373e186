-- 照片笔记功能数据库迁移脚本
-- 版本 2.0 - 重构照片发布为照片笔记功能

-- 创建照片笔记表
CREATE TABLE IF NOT EXISTS `ptm_photo_note` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '照片笔记ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `title` varchar(100) DEFAULT NULL COMMENT '标题（可选，最多100字符）',
  `content` text NOT NULL COMMENT '正文内容（必填，最多2000字符）',
  `photo_count` tinyint(4) NOT NULL DEFAULT 1 COMMENT '照片数量（1-9张）',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览量',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论数',
  `share_count` int(11) DEFAULT 0 COMMENT '分享数',
  `visibility` tinyint(1) DEFAULT 1 COMMENT '可见性: 0-私密, 1-公开, 2-好友可见',
  `allow_comment` tinyint(1) DEFAULT 1 COMMENT '是否允许评论: 0-不允许, 1-允许',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `location` varchar(100) DEFAULT NULL COMMENT '地理位置',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_like_count` (`like_count`),
  KEY `idx_view_count` (`view_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片笔记表';

-- 创建照片笔记图片关联表
CREATE TABLE IF NOT EXISTS `ptm_photo_note_image` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint(20) NOT NULL COMMENT '照片笔记ID',
  `photo_id` bigint(20) NOT NULL COMMENT '照片ID',
  `sort_order` tinyint(4) NOT NULL DEFAULT 1 COMMENT '排序顺序（1-9）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_note_photo` (`note_id`, `photo_id`),
  KEY `idx_note_id` (`note_id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片笔记图片关联表';

-- 创建照片笔记标签表
CREATE TABLE IF NOT EXISTS `ptm_photo_note_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint(20) NOT NULL COMMENT '照片笔记ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_note_tag` (`note_id`, `tag_name`),
  KEY `idx_note_id` (`note_id`),
  KEY `idx_tag_name` (`tag_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片笔记标签表';

-- 创建照片笔记@用户表
CREATE TABLE IF NOT EXISTS `ptm_photo_note_mention` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint(20) NOT NULL COMMENT '照片笔记ID',
  `mentioned_user_id` bigint(20) NOT NULL COMMENT '被@用户ID',
  `mention_user_id` bigint(20) NOT NULL COMMENT '@用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_note_mention` (`note_id`, `mentioned_user_id`),
  KEY `idx_note_id` (`note_id`),
  KEY `idx_mentioned_user_id` (`mentioned_user_id`),
  KEY `idx_mention_user_id` (`mention_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片笔记@用户表';

-- 创建标签统计表
CREATE TABLE IF NOT EXISTS `ptm_tag_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `use_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `note_count` int(11) DEFAULT 0 COMMENT '笔记数量',
  `total_view_count` bigint(20) DEFAULT 0 COMMENT '总浏览量',
  `total_like_count` bigint(20) DEFAULT 0 COMMENT '总点赞数',
  `hot_score` decimal(10,2) DEFAULT 0.00 COMMENT '热度分数',
  `last_used_at` datetime DEFAULT NULL COMMENT '最后使用时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`),
  KEY `idx_hot_score` (`hot_score`),
  KEY `idx_use_count` (`use_count`),
  KEY `idx_last_used_at` (`last_used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签统计表';

-- 创建照片笔记点赞表
CREATE TABLE IF NOT EXISTS `ptm_photo_note_like` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint(20) NOT NULL COMMENT '照片笔记ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_note_user` (`note_id`, `user_id`),
  KEY `idx_note_id` (`note_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片笔记点赞表';

-- 创建照片笔记收藏表
CREATE TABLE IF NOT EXISTS `ptm_photo_note_collection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `note_id` bigint(20) NOT NULL COMMENT '照片笔记ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_note_user` (`note_id`, `user_id`),
  KEY `idx_note_id` (`note_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片笔记收藏表';

-- 创建照片笔记评论表
CREATE TABLE IF NOT EXISTS `ptm_photo_note_comment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `note_id` bigint(20) NOT NULL COMMENT '照片笔记ID',
  `user_id` bigint(20) NOT NULL COMMENT '评论用户ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID（回复评论时使用）',
  `reply_to_user_id` bigint(20) DEFAULT NULL COMMENT '回复的用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-待审核，1-正常，2-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_note_id` (`note_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片笔记评论表';
