<template>
  <div class="profile-container">
    <div class="profile-header">
      <div class="user-info">
        <div class="avatar-container">
          <van-image
            round
            width="80"
            height="80"
            :src="userInfo?.avatar || 'https://randomuser.me/api/portraits/men/1.jpg'"
            fit="cover"
          />
          <div class="avatar-edit" @click="handleAvatarEdit">
            <van-icon name="photograph" />
          </div>
        </div>
        <div class="user-details">
          <h2 class="username">{{ userInfo?.nickname || userInfo?.username || '用户名' }}</h2>
          <p class="user-id">ID: {{ userInfo?.id || '未知' }}</p>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-value">{{ stats.photoCount }}</span>
              <span class="stat-label">照片</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ stats.followingCount }}</span>
              <span class="stat-label">关注</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ stats.followerCount }}</span>
              <span class="stat-label">粉丝</span>
            </div>
          </div>
        </div>
      </div>
      <div class="action-buttons">
        <van-button type="primary" size="small" icon="setting-o" to="/settings">编辑资料</van-button>
      </div>
    </div>

    <div class="profile-tabs">
      <van-tabs v-model:active="activeTab" animated swipeable>
        <van-tab title="我的照片">
          <div class="photo-grid" v-if="photos.length > 0">
            <div v-for="photo in photos" :key="photo.id" class="photo-item" @click="viewPhoto(photo.id)">
              <van-image
                :src="photo.thumbnailUrl"
                fit="cover"
                lazy-load
              />
            </div>
          </div>
          <div v-else class="empty-state">
            <van-empty description="暂无照片" />
            <van-button type="primary" to="/upload" class="upload-button">上传照片</van-button>
          </div>
        </van-tab>
        <van-tab title="收藏">
          <div class="photo-grid" v-if="collections.length > 0">
            <div v-for="photo in collections" :key="photo.id" class="photo-item" @click="viewPhoto(photo.id)">
              <van-image
                :src="photo.thumbnailUrl"
                fit="cover"
                lazy-load
              />
            </div>
          </div>
          <div v-else class="empty-state">
            <van-empty description="暂无收藏" />
          </div>
        </van-tab>
        <van-tab title="喜欢">
          <div class="photo-grid" v-if="likes.length > 0">
            <div v-for="photo in likes" :key="photo.id" class="photo-item" @click="viewPhoto(photo.id)">
              <van-image
                :src="photo.thumbnailUrl"
                fit="cover"
                lazy-load
              />
            </div>
          </div>
          <div v-else class="empty-state">
            <van-empty description="暂无喜欢" />
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { showToast } from 'vant';
import { getUserPhotos, getUserCollections } from '@/api/profile';

const router = useRouter();
const userStore = useUserStore();

// 用户信息
const userInfo = ref(userStore.userInfo);

// 统计数据
const stats = reactive({
  photoCount: 0,
  followingCount: 0,
  followerCount: 0
});

// 当前标签页
const activeTab = ref(0);

// 照片列表
const photos = ref<any[]>([]);
const collections = ref<any[]>([]);
const likes = ref<any[]>([]);

// 获取用户统计数据
const fetchUserStats = async () => {
  try {
    // 使用用户信息中的统计数据
    if (userInfo.value) {
      stats.photoCount = userInfo.value.photoCount || 0;
      stats.followingCount = userInfo.value.followingCount || 0;
      stats.followerCount = userInfo.value.followerCount || 0;
    }
  } catch (error) {
    console.error('获取用户统计数据失败', error);
  }
};

// 获取用户照片
const fetchUserPhotos = async () => {
  try {
    if (!userInfo.value?.id) return;

    const res = await getUserPhotos(userInfo.value.id, {
      page: 1,
      size: 9,
      type: 'all'
    });

    photos.value = res.data?.records || [];
  } catch (error) {
    console.error('获取用户照片失败', error);
    showToast('获取照片失败，请稍后重试');
  }
};

// 获取用户收藏
const fetchUserCollections = async () => {
  try {
    if (!userInfo.value?.id) return;

    const res = await getUserCollections(userInfo.value.id, {
      page: 1,
      size: 9
    });

    collections.value = res.data?.records || [];
  } catch (error) {
    console.error('获取用户收藏失败', error);
    showToast('获取收藏失败，请稍后重试');
  }
};

// 获取用户喜欢
const fetchUserLikes = async () => {
  try {
    if (!userInfo.value?.id) return;

    // 目前API中没有获取用户喜欢的照片的接口，暂时保留空数组
    likes.value = [];
  } catch (error) {
    console.error('获取用户喜欢失败', error);
    showToast('获取喜欢失败，请稍后重试');
  }
};

// 查看照片详情
const viewPhoto = (id: number) => {
  router.push(`/photo/${id}`);
};

// 编辑头像
const handleAvatarEdit = () => {
  showToast('头像编辑功能开发中');
};

// 生命周期钩子
onMounted(async () => {
  try {
    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      console.log('用户未登录，重定向到登录页面');
      router.push({
        path: '/auth/login',
        query: { redirect: '/user/profile' }
      });
      return;
    }

    // 获取最新的用户信息
    await userStore.fetchUserInfo();
    userInfo.value = userStore.userInfo;

    // 获取用户数据
    fetchUserStats();
    fetchUserPhotos();
    fetchUserCollections();
    fetchUserLikes();
  } catch (error) {
    console.error('初始化个人页面失败', error);
    showToast('加载用户信息失败，请稍后重试');
  }
});
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
}

.profile-header {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  position: relative;
  margin-right: 20px;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #3498db;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.username {
  margin: 0 0 5px;
  font-size: 18px;
}

.user-id {
  margin: 0 0 10px;
  font-size: 14px;
  color: #666;
}

.user-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-weight: bold;
  font-size: 16px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.profile-tabs {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  padding: 4px;
}

.photo-item {
  aspect-ratio: 1;
  overflow: hidden;
  cursor: pointer;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.upload-button {
  margin-top: 20px;
}

@media (min-width: 768px) {
  .profile-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .photo-grid {
    gap: 10px;
    padding: 10px;
  }
}
</style>
