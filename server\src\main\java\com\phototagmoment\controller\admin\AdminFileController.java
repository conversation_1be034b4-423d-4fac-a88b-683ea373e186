package com.phototagmoment.controller.admin;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.common.Result;
import com.phototagmoment.config.QiniuConfig;
import com.phototagmoment.dto.FileUploadConfigDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.AdminFileService;
import com.phototagmoment.service.FileUploadConfigService;
import com.phototagmoment.service.QiniuStorageService;
import com.phototagmoment.service.StorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 后台管理文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/file")
@Tag(name = "后台文件管理", description = "后台管理系统文件上传、管理相关接口")
@PreAuthorize("hasRole('ADMIN')")
public class AdminFileController {

    @Autowired
    private StorageService storageService;

    @Autowired
    private AdminFileService adminFileService;

    @Autowired(required = false)
    private QiniuStorageService qiniuStorageService;

    @Autowired(required = false)
    private QiniuConfig qiniuConfig;

    @Autowired(required = false)
    private FileUploadConfigService fileUploadConfigService;

    @Value("${storage.local.path:uploads}")
    private String localStoragePath;

    @Value("${storage.local.domain:}")
    private String localDomain;

    /**
     * 上传单个文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "管理员上传单个文件")
    public Result<Map<String, Object>> uploadFile(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "文件类型") @RequestParam(value = "type", defaultValue = "general") String type) {

        if (file.isEmpty()) {
            return Result.fail("上传文件不能为空");
        }

        try {
            // 验证文件类型和大小
            String validationResult = validateFile(file, type);
            if (validationResult != null) {
                return Result.fail(validationResult);
            }

            // 获取当前管理员ID
            Long adminId = SecurityUtil.getCurrentUserId();
            if (adminId == null) {
                return Result.fail("获取管理员信息失败");
            }

            // 生成文件名和路径
            String originalFilename = file.getOriginalFilename();
            String extension = getFileExtension(originalFilename);
            String fileName = generateFileName(extension);
            String filePath = generateFilePath(type, adminId, fileName);

            // 上传文件
            String fileUrl = storageService.uploadFile(file.getInputStream(), filePath, file.getContentType());
            if (fileUrl == null) {
                return Result.fail("文件上传失败");
            }

            // 记录文件上传到数据库
            Long fileRecordId = adminFileService.recordFileUpload(
                originalFilename, fileName, filePath, fileUrl,
                file.getSize(), file.getContentType(), adminId,
                "ADMIN", determineFileCategory(extension)
            );

            // 记录上传日志
            logFileUpload(adminId, originalFilename, filePath, file.getSize(), type);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("fileRecordId", fileRecordId);
            result.put("fileName", fileName);
            result.put("originalName", originalFilename);
            result.put("filePath", filePath);
            result.put("fileUrl", fileUrl);
            result.put("fileSize", file.getSize());
            result.put("fileType", file.getContentType());
            result.put("uploadTime", new Date());

            return Result.success(result, "文件上传成功");
        } catch (IOException e) {
            log.error("文件上传失败", e);
            return Result.fail("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/batch-upload")
    @Operation(summary = "批量上传文件", description = "管理员批量上传文件")
    public Result<Map<String, Object>> batchUploadFiles(
            @Parameter(description = "文件列表") @RequestParam("files") MultipartFile[] files,
            @Parameter(description = "文件类型") @RequestParam(value = "type", defaultValue = "general") String type) {

        if (files == null || files.length == 0) {
            return Result.fail("请选择要上传的文件");
        }

        if (files.length > 10) {
            return Result.fail("单次最多上传10个文件");
        }

        List<Map<String, Object>> results = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        Long adminId = SecurityUtil.getCurrentUserId();
        if (adminId == null) {
            return Result.fail("获取管理员信息失败");
        }

        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            try {
                if (file.isEmpty()) {
                    errors.add("文件" + (i + 1) + "为空");
                    continue;
                }

                // 验证文件
                String validationResult = validateFile(file, type);
                if (validationResult != null) {
                    errors.add("文件" + (i + 1) + "：" + validationResult);
                    continue;
                }

                // 上传文件
                String originalFilename = file.getOriginalFilename();
                String extension = getFileExtension(originalFilename);
                String fileName = generateFileName(extension);
                String filePath = generateFilePath(type, adminId, fileName);

                String fileUrl = storageService.uploadFile(file.getInputStream(), filePath, file.getContentType());
                if (fileUrl == null) {
                    errors.add("文件" + (i + 1) + "上传失败");
                    continue;
                }

                // 记录文件上传到数据库
                Long fileRecordId = adminFileService.recordFileUpload(
                    originalFilename, fileName, filePath, fileUrl,
                    file.getSize(), file.getContentType(), adminId,
                    "ADMIN", determineFileCategory(extension)
                );

                // 记录上传日志
                logFileUpload(adminId, originalFilename, filePath, file.getSize(), type);

                // 添加到结果列表
                Map<String, Object> result = new HashMap<>();
                result.put("fileRecordId", fileRecordId);
                result.put("fileName", fileName);
                result.put("originalName", originalFilename);
                result.put("filePath", filePath);
                result.put("fileUrl", fileUrl);
                result.put("fileSize", file.getSize());
                result.put("fileType", file.getContentType());
                result.put("uploadTime", new Date());
                results.add(result);

            } catch (IOException e) {
                log.error("文件上传失败", e);
                errors.add("文件" + (i + 1) + "上传失败：" + e.getMessage());
            }
        }

        if (!errors.isEmpty()) {
            log.warn("批量上传部分失败：{}", errors);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("successCount", results.size());
        response.put("failCount", errors.size());
        response.put("files", results);
        response.put("errors", errors);

        return Result.success(response, "批量上传完成");
    }

    /**
     * 获取上传凭证（七牛云）
     */
    @GetMapping("/upload-token")
    @Operation(summary = "获取上传凭证", description = "获取七牛云上传凭证")
    public Result<Map<String, Object>> getUploadToken(
            @Parameter(description = "文件类型") @RequestParam(value = "type", defaultValue = "general") String type) {

        if (qiniuStorageService == null || qiniuConfig == null) {
            return Result.fail("七牛云存储服务未初始化");
        }

        // 刷新配置确保最新状态
        qiniuConfig.refreshConfig();

        // 检查配置完整性
        String validationError = qiniuConfig.getConfigValidationError();
        if (validationError != null) {
            return Result.fail(validationError);
        }

        try {
            Long adminId = SecurityUtil.getCurrentUserId();
            if (adminId == null) {
                return Result.fail("获取管理员信息失败");
            }

            // 生成文件key
            String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String key = qiniuConfig.getUploadDir() + "/admin/" + type + "/" + adminId + "/" + datePath + "/" + uuid;

            // 获取上传凭证
            String uploadToken = qiniuStorageService.getUploadToken(key);
            if (uploadToken == null || uploadToken.trim().isEmpty()) {
                return Result.fail("生成上传凭证失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("uploadToken", uploadToken);
            result.put("key", key);
            result.put("domain", qiniuConfig.getDomain());
            result.put("uploadUrl", "https://upload.qiniuapi.com");

            return Result.success(result, "获取上传凭证成功");
        } catch (Exception e) {
            log.error("获取上传凭证失败", e);
            return Result.fail("获取上传凭证失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除文件", description = "删除指定文件")
    public Result<Boolean> deleteFile(
            @Parameter(description = "文件路径") @RequestParam("filePath") String filePath) {

        try {
            boolean success = storageService.deleteFile(filePath);
            if (success) {
                // 记录删除日志
                Long adminId = SecurityUtil.getCurrentUserId();
                log.info("管理员{}删除文件：{}", adminId, filePath);
                return Result.success(true, "文件删除成功");
            } else {
                return Result.fail("文件删除失败");
            }
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return Result.fail("删除文件失败：" + e.getMessage());
        }
    }

    /**
     * 验证文件
     */
    private String validateFile(MultipartFile file, String type) {
        // 检查文件大小
        long maxSize = getMaxFileSize(type);
        if (file.getSize() > maxSize) {
            return "文件大小超过限制（最大" + (maxSize / 1024 / 1024) + "MB）";
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            return "文件名不能为空";
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        Set<String> allowedExtensions = getAllowedExtensions(type);
        if (!allowedExtensions.contains(extension)) {
            return "不支持的文件类型：" + extension;
        }

        return null;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf(".");
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String extension) {
        return UUID.randomUUID().toString().replaceAll("-", "") +
               (extension.isEmpty() ? "" : "." + extension);
    }

    /**
     * 生成文件路径
     */
    private String generateFilePath(String type, Long adminId, String fileName) {
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return "admin/" + type + "/" + adminId + "/" + datePath + "/" + fileName;
    }

    /**
     * 获取最大文件大小
     */
    private long getMaxFileSize(String type) {
        switch (type) {
            case "image":
                return 10 * 1024 * 1024; // 10MB
            case "document":
                return 50 * 1024 * 1024; // 50MB
            case "video":
                return 100 * 1024 * 1024; // 100MB
            default:
                return 20 * 1024 * 1024; // 20MB
        }
    }

    /**
     * 获取允许的文件扩展名
     */
    private Set<String> getAllowedExtensions(String type) {
        switch (type) {
            case "image":
                return Set.of("jpg", "jpeg", "png", "gif", "bmp", "webp");
            case "document":
                return Set.of("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt");
            case "video":
                return Set.of("mp4", "avi", "mov", "wmv", "flv", "mkv");
            default:
                return Set.of("jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "txt");
        }
    }

    /**
     * 确定文件分类
     */
    private String determineFileCategory(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "other";
        }

        String ext = extension.toLowerCase();
        if (Set.of("jpg", "jpeg", "png", "gif", "bmp", "webp").contains(ext)) {
            return "image";
        } else if (Set.of("mp4", "avi", "mov", "wmv", "flv", "mkv").contains(ext)) {
            return "video";
        } else if (Set.of("mp3", "wav", "flac", "aac", "ogg").contains(ext)) {
            return "audio";
        } else if (Set.of("pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt").contains(ext)) {
            return "document";
        } else {
            return "other";
        }
    }

    /**
     * 记录文件上传日志
     */
    private void logFileUpload(Long adminId, String originalFilename, String filePath, long fileSize, String type) {
        log.info("管理员文件上传 - 管理员ID: {}, 原文件名: {}, 存储路径: {}, 文件大小: {}字节, 文件类型: {}",
                adminId, originalFilename, filePath, fileSize, type);
    }

    /**
     * 刷新七牛云配置
     */
    @PostMapping("/refresh-qiniu-config")
    @Operation(summary = "刷新七牛云配置", description = "从数据库重新加载七牛云配置")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> refreshQiniuConfig() {
        try {
            if (qiniuConfig != null) {
                qiniuConfig.refreshConfig();
                log.info("七牛云配置刷新成功");
                return Result.success("七牛云配置刷新成功");
            } else {
                return Result.fail("七牛云配置服务未初始化");
            }
        } catch (Exception e) {
            log.error("刷新七牛云配置失败: {}", e.getMessage(), e);
            return Result.fail("刷新七牛云配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取七牛云配置状态
     */
    @GetMapping("/qiniu-config-status")
    @Operation(summary = "获取七牛云配置状态", description = "获取七牛云配置的当前状态")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Map<String, Object>> getQiniuConfigStatus() {
        Map<String, Object> status = new HashMap<>();

        if (qiniuConfig == null) {
            status.put("initialized", false);
            status.put("message", "七牛云配置服务未初始化");
            return Result.success(status);
        }

        // 刷新配置
        qiniuConfig.refreshConfig();

        status.put("initialized", true);
        status.put("enabled", qiniuConfig.isEnabled());
        status.put("configComplete", qiniuConfig.isConfigComplete());
        status.put("configSource", qiniuConfig.getConfigSource());
        status.put("configStatusInfo", qiniuConfig.getConfigStatusInfo());

        String validationError = qiniuConfig.getConfigValidationError();
        if (validationError != null) {
            status.put("valid", false);
            status.put("error", validationError);
        } else {
            status.put("valid", true);
            status.put("bucket", qiniuConfig.getBucket());
            status.put("domain", qiniuConfig.getDomain());
            status.put("region", qiniuConfig.getRegion());
            status.put("uploadDir", qiniuConfig.getUploadDir());
            status.put("isPrivate", qiniuConfig.isPrivate());
        }

        return Result.success(status);
    }

    /**
     * 强制从文件上传配置表刷新七牛云配置
     */
    @PostMapping("/force-refresh-qiniu-config")
    @Operation(summary = "强制刷新七牛云配置", description = "强制从文件上传配置表重新加载七牛云配置")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> forceRefreshQiniuConfig() {
        try {
            if (qiniuConfig != null) {
                qiniuConfig.refreshConfig();

                // 记录配置状态信息
                String statusInfo = qiniuConfig.getConfigStatusInfo();
                log.info("强制刷新七牛云配置完成:\n{}", statusInfo);

                return Result.success("七牛云配置强制刷新成功\n" + statusInfo);
            } else {
                return Result.fail("七牛云配置服务未初始化");
            }
        } catch (Exception e) {
            log.error("强制刷新七牛云配置失败: {}", e.getMessage(), e);
            return Result.fail("强制刷新七牛云配置失败: " + e.getMessage());
        }
    }

    /**
     * 调试七牛云配置读取
     */
    @GetMapping("/debug-qiniu-config")
    @Operation(summary = "调试七牛云配置读取", description = "调试七牛云配置的读取过程，显示详细信息")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Map<String, Object>> debugQiniuConfig() {
        Map<String, Object> debugInfo = new HashMap<>();

        try {
            // 1. 检查文件上传配置表中的七牛云配置
            if (fileUploadConfigService != null) {
                FileUploadConfigDTO qiniuFileConfig = fileUploadConfigService.getConfigByStorageType("QINIU");
                Map<String, Object> fileConfigInfo = new HashMap<>();
                if (qiniuFileConfig != null) {
                    fileConfigInfo.put("exists", true);
                    fileConfigInfo.put("id", qiniuFileConfig.getId());
                    fileConfigInfo.put("configName", qiniuFileConfig.getConfigName());
                    fileConfigInfo.put("enabled", qiniuFileConfig.getEnabled());
                    fileConfigInfo.put("isDefault", qiniuFileConfig.getIsDefault());
                    fileConfigInfo.put("status", qiniuFileConfig.getStatus());

                    // 检查配置参数
                    if (qiniuFileConfig.getConfigParams() != null) {
                        FileUploadConfigDTO.StorageConfigParams params = qiniuFileConfig.getConfigParams();
                        Map<String, Object> paramsInfo = new HashMap<>();
                        paramsInfo.put("accessKeyConfigured", params.getQiniuAccessKey() != null && !params.getQiniuAccessKey().isEmpty());
                        paramsInfo.put("secretKeyConfigured", params.getQiniuSecretKey() != null && !params.getQiniuSecretKey().isEmpty());
                        paramsInfo.put("bucket", params.getQiniuBucket());
                        paramsInfo.put("region", params.getQiniuRegion());
                        paramsInfo.put("domain", params.getDomain());
                        fileConfigInfo.put("configParams", paramsInfo);
                    } else {
                        fileConfigInfo.put("configParams", "未配置");
                    }
                } else {
                    fileConfigInfo.put("exists", false);
                    fileConfigInfo.put("message", "文件上传配置表中没有七牛云配置");
                }
                debugInfo.put("fileUploadConfig", fileConfigInfo);
            } else {
                debugInfo.put("fileUploadConfig", "FileUploadConfigService未初始化");
            }

            // 2. 检查QiniuConfig当前状态
            if (qiniuConfig != null) {
                Map<String, Object> qiniuConfigInfo = new HashMap<>();
                qiniuConfigInfo.put("enabled", qiniuConfig.isEnabled());
                qiniuConfigInfo.put("configComplete", qiniuConfig.isConfigComplete());
                qiniuConfigInfo.put("configSource", qiniuConfig.getConfigSource());
                qiniuConfigInfo.put("accessKeyConfigured", qiniuConfig.getAccessKey() != null && !qiniuConfig.getAccessKey().isEmpty());
                qiniuConfigInfo.put("secretKeyConfigured", qiniuConfig.getSecretKey() != null && !qiniuConfig.getSecretKey().isEmpty());
                qiniuConfigInfo.put("bucket", qiniuConfig.getBucket());
                qiniuConfigInfo.put("region", qiniuConfig.getRegion());
                qiniuConfigInfo.put("domain", qiniuConfig.getDomain());

                String validationError = qiniuConfig.getConfigValidationError();
                qiniuConfigInfo.put("validationError", validationError);

                debugInfo.put("qiniuConfig", qiniuConfigInfo);
            } else {
                debugInfo.put("qiniuConfig", "QiniuConfig未初始化");
            }

            // 3. 执行配置刷新并记录结果
            if (qiniuConfig != null) {
                qiniuConfig.refreshConfig();
                debugInfo.put("refreshResult", "配置刷新完成");
                debugInfo.put("refreshTime", LocalDateTime.now());
            }

            return Result.success(debugInfo);
        } catch (Exception e) {
            log.error("调试七牛云配置失败: {}", e.getMessage(), e);
            debugInfo.put("error", e.getMessage());
            return Result.success(debugInfo);
        }
    }
}
