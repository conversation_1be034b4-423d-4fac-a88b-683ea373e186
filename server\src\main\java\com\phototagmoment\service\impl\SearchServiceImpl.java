package com.phototagmoment.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.TagDTO;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.entity.PhotoTag;
import com.phototagmoment.entity.User;
import com.phototagmoment.mapper.PhotoMapper;
import com.phototagmoment.mapper.PhotoTagMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.PhotoNoteService;
import com.phototagmoment.service.QiniuStorageService;
import com.phototagmoment.service.SearchService;
import com.phototagmoment.service.UserFollowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 搜索服务实现类
 */
@Slf4j
@Service
public class SearchServiceImpl implements SearchService {

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PhotoTagMapper photoTagMapper;

    @Autowired
    private UserFollowService userFollowService;

    @Autowired
    private QiniuStorageService qiniuStorageService;

    @Autowired
    private PhotoNoteService photoNoteService;

    @org.springframework.beans.factory.annotation.Value("${qiniu.is-private:false}")
    private boolean qiniuIsPrivate;

    @Override
    public IPage<PhotoDTO> searchPhotos(String keyword, int page, int size, String sort, Long userId) {
        Page<Photo> photoPage = new Page<>(page, size);
        LambdaQueryWrapper<Photo> queryWrapper = new LambdaQueryWrapper<>();

        // 添加搜索条件
        queryWrapper.like(Photo::getTitle, keyword)
                .or()
                .like(Photo::getDescription, keyword);

        // 只查询公开照片
        queryWrapper.eq(Photo::getVisibility, 0);
        queryWrapper.eq(Photo::getStatus, 1);

        // 排序
        if ("popular".equals(sort)) {
            queryWrapper.orderByDesc(Photo::getLikeCount);
        } else {
            queryWrapper.orderByDesc(Photo::getCreatedAt);
        }

        // 执行查询
        IPage<Photo> result = photoMapper.selectPage(photoPage, queryWrapper);

        // 转换为DTO
        IPage<PhotoDTO> dtoPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        List<PhotoDTO> dtoList = new ArrayList<>();

        for (Photo photo : result.getRecords()) {
            PhotoDTO dto = new PhotoDTO();
            BeanUtils.copyProperties(photo, dto);

            // 查询用户信息
            User user = userMapper.selectById(photo.getUserId());
            if (user != null) {
                dto.setUsername(user.getUsername());
                dto.setNickname(user.getNickname());
                dto.setAvatar(user.getAvatar());
            }

            // 检查当前用户是否已点赞
            if (userId != null) {
                dto.setIsLiked(photoMapper.checkUserLiked(photo.getId(), userId));
                dto.setIsCollected(photoMapper.checkUserCollected(photo.getId(), userId));
            }

            // 如果是七牛云私有空间，生成带下载凭证的URL
            if (qiniuIsPrivate) {
                processPrivateUrls(dto);
            }

            dtoList.add(dto);
        }

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public IPage<UserDTO> searchUsers(String keyword, int page, int size, Long userId) {
        Page<User> userPage = new Page<>(page, size);
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();

        // 添加搜索条件
        queryWrapper.like(User::getUsername, keyword)
                .or()
                .like(User::getNickname, keyword)
                .or()
                .like(User::getBio, keyword);

        // 只查询正常状态的用户
        queryWrapper.eq(User::getStatus, 1);
        queryWrapper.eq(User::getIsDeleted, 0);

        // 排序
        queryWrapper.orderByDesc(User::getFollowerCount);

        // 执行查询
        IPage<User> result = userMapper.selectPage(userPage, queryWrapper);

        // 转换为DTO
        IPage<UserDTO> dtoPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        List<UserDTO> dtoList = new ArrayList<>();

        for (User user : result.getRecords()) {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(user, dto);

            // 检查当前用户是否已关注
            if (userId != null) {
                dto.setIsFollowing(userFollowService.checkFollowing(userId, user.getId()));
            }

            dtoList.add(dto);
        }

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public List<TagDTO> searchTags(String keyword, int limit) {
        // 查询标签
        List<PhotoTag> tags = photoTagMapper.searchTags(keyword, limit);

        // 转换为DTO
        return tags.stream().map(tag -> {
            TagDTO dto = new TagDTO();
            dto.setName(tag.getName());
            dto.setCount(tag.getPhotoId() != null ? 1L : 0L); // 这里应该是从查询结果中获取数量，暂时使用1
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> search(String keyword, int page, int size, String sort, String type, Long userId) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();

        // 根据搜索类型执行不同的搜索
        if ("all".equals(type) || "photos".equals(type)) {
            IPage<PhotoDTO> photos = searchPhotos(keyword, page, size, sort, userId);
            result.put("photos", photos.getRecords());
            result.put("photoTotal", photos.getTotal());
        }

        if ("all".equals(type) || "users".equals(type)) {
            IPage<UserDTO> users = searchUsers(keyword, page, size, userId);
            result.put("users", users.getRecords());
            result.put("userTotal", users.getTotal());
        }

        if ("all".equals(type) || "tags".equals(type)) {
            List<TagDTO> tags = searchTags(keyword, 20);
            result.put("tags", tags);
            result.put("tagTotal", tags.size());
        }

        // 计算总结果数和搜索时间
        long totalResults = 0;
        if (result.containsKey("photoTotal")) {
            totalResults += (long) result.get("photoTotal");
        }
        if (result.containsKey("userTotal")) {
            totalResults += (long) result.get("userTotal");
        }
        if (result.containsKey("tagTotal")) {
            totalResults += (long) result.get("tagTotal");
        }

        result.put("totalResults", totalResults);
        result.put("searchTime", System.currentTimeMillis() - startTime);

        return result;
    }

    @Override
    public List<TagDTO> getPopularTags(int limit) {
        try {
            // 查询热门标签
            List<TagDTO> tags = photoTagMapper.getPopularTags(limit);

            // 如果数据库中没有标签数据，返回默认标签
            if (tags == null || tags.isEmpty()) {
                log.info("数据库中没有标签数据，返回默认标签");
                List<TagDTO> defaultTags = new ArrayList<>();
                String[] defaultTagNames = {"风景", "美食", "旅行", "人像", "宠物", "城市", "自然", "建筑", "艺术", "生活"};
                for (int i = 0; i < defaultTagNames.length && i < limit; i++) {
                    TagDTO tag = new TagDTO();
                    tag.setName(defaultTagNames[i]);
                    tag.setCount((long)(100 - i * 10)); // 模拟热度递减
                    defaultTags.add(tag);
                }
                return defaultTags;
            }

            return tags;
        } catch (Exception e) {
            log.error("获取热门标签失败: {}", e.getMessage(), e);
            // 返回默认标签
            List<TagDTO> defaultTags = new ArrayList<>();
            String[] defaultTagNames = {"风景", "美食", "旅行", "人像", "宠物", "城市", "自然", "建筑", "艺术", "生活"};
            for (int i = 0; i < defaultTagNames.length && i < limit; i++) {
                TagDTO tag = new TagDTO();
                tag.setName(defaultTagNames[i]);
                tag.setCount((long)(100 - i * 10)); // 模拟热度递减
                defaultTags.add(tag);
            }
            return defaultTags;
        }
    }

    @Override
    public List<UserDTO> getRecommendedUsers(int limit, Long userId) {
        // 查询推荐用户
        List<User> users = userMapper.getRecommendedUsers(limit, userId);

        // 转换为DTO
        List<UserDTO> dtoList = new ArrayList<>();
        for (User user : users) {
            UserDTO dto = new UserDTO();
            BeanUtils.copyProperties(user, dto);

            // 检查当前用户是否已关注
            if (userId != null) {
                dto.setIsFollowing(userFollowService.checkFollowing(userId, user.getId()));
            }

            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 处理私有空间URL，生成带下载凭证的URL
     * @param photoDTO 照片DTO
     */
    private void processPrivateUrls(PhotoDTO photoDTO) {
        try {
            // 处理照片URL
            if (photoDTO.getUrl() != null && !photoDTO.getUrl().isEmpty()) {
                String fileName = extractFileNameFromUrl(photoDTO.getUrl());
                String privateUrl = qiniuStorageService.getFileUrl(fileName);
                if (privateUrl != null && !privateUrl.isEmpty()) {
                    photoDTO.setUrl(privateUrl);
                }
            }

            // 处理缩略图URL
            if (photoDTO.getThumbnailUrl() != null && !photoDTO.getThumbnailUrl().isEmpty()) {
                String fileName = extractFileNameFromUrl(photoDTO.getThumbnailUrl());
                String privateUrl = qiniuStorageService.getFileUrl(fileName);
                if (privateUrl != null && !privateUrl.isEmpty()) {
                    photoDTO.setThumbnailUrl(privateUrl);
                }
            }

            // 处理用户头像URL
            if (photoDTO.getAvatar() != null && !photoDTO.getAvatar().isEmpty()) {
                String fileName = extractFileNameFromUrl(photoDTO.getAvatar());
                String privateUrl = qiniuStorageService.getFileUrl(fileName);
                if (privateUrl != null && !privateUrl.isEmpty()) {
                    photoDTO.setAvatar(privateUrl);
                }
            }
        } catch (Exception e) {
            log.error("处理私有空间URL失败", e);
            // 不影响业务，继续执行
        }
    }

    /**
     * 从URL中提取文件名
     * @param url URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        // 移除域名部分
        int domainEndIndex = url.indexOf("/", 8); // 跳过 "https://"
        if (domainEndIndex != -1) {
            return url.substring(domainEndIndex + 1);
        }

        return url;
    }

    @Override
    public IPage<PhotoNoteDTO> searchPhotoNotes(String keyword, int page, int size, String sort, Long userId) {
        // 使用PhotoNoteService的搜索方法
        IPage<PhotoNoteDTO> result = photoNoteService.searchPhotoNotes(keyword, page, size, userId);

        // 如果需要按热度排序，可以在这里进行额外处理
        if ("popular".equals(sort)) {
            // 这里可以添加按热度排序的逻辑
            // 目前先使用默认的搜索结果
        }

        return result;
    }

    @Override
    public Map<String, Object> searchV2(String keyword, int page, int size, String sort, String type, Long userId) {
        Map<String, Object> result = new HashMap<>();
        long startTime = System.currentTimeMillis();

        // 根据搜索类型执行不同的搜索
        if ("all".equals(type) || "photo_notes".equals(type)) {
            IPage<PhotoNoteDTO> photoNotes = searchPhotoNotes(keyword, page, size, sort, userId);
            result.put("photoNotes", photoNotes.getRecords());
            result.put("photoNoteTotal", photoNotes.getTotal());
        }

        if ("all".equals(type) || "users".equals(type)) {
            IPage<UserDTO> users = searchUsers(keyword, page, size, userId);
            result.put("users", users.getRecords());
            result.put("userTotal", users.getTotal());
        }

        if ("all".equals(type) || "tags".equals(type)) {
            // 使用照片笔记的标签搜索
            List<String> photoNoteTags = photoNoteService.searchTags(keyword, 20);
            List<TagDTO> tags = photoNoteTags.stream().map(tagName -> {
                TagDTO dto = new TagDTO();
                dto.setName(tagName);
                dto.setCount(1L); // 这里可以添加实际的统计逻辑
                return dto;
            }).collect(Collectors.toList());
            result.put("tags", tags);
            result.put("tagTotal", tags.size());
        }

        // 计算总结果数和搜索时间
        long totalResults = 0;
        if (result.containsKey("photoNoteTotal")) {
            totalResults += (long) result.get("photoNoteTotal");
        }
        if (result.containsKey("userTotal")) {
            totalResults += (long) result.get("userTotal");
        }
        if (result.containsKey("tagTotal")) {
            totalResults += (long) result.get("tagTotal");
        }

        result.put("totalResults", totalResults);
        result.put("searchTime", System.currentTimeMillis() - startTime);

        return result;
    }
}
