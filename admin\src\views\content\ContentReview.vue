<template>
  <div class="content-review-container">
    <el-card class="page-header">
      <div class="header-content">
        <h2>内容审核</h2>
        <p>审核用户发布的照片笔记内容，确保内容符合社区规范</p>
      </div>
    </el-card>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="审核状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已删除" value="deleted" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容类型">
          <el-select v-model="filterForm.contentType" placeholder="请选择类型" clearable>
            <el-option label="照片笔记" value="photo_note" />
            <el-option label="评论" value="comment" />
            <el-option label="用户资料" value="profile" />
          </el-select>
        </el-form-item>
        <el-form-item label="举报原因">
          <el-select v-model="filterForm.reportReason" placeholder="请选择原因" clearable>
            <el-option label="违法违规" value="illegal" />
            <el-option label="色情低俗" value="pornographic" />
            <el-option label="暴力血腥" value="violent" />
            <el-option label="垃圾广告" value="spam" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="16" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.pending }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.approved }}</div>
            <div class="stat-label">已通过</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.rejected }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总计</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 内容列表 -->
    <el-card class="content-list">
      <template #header>
        <div class="card-header">
          <span>内容列表</span>
          <div class="header-actions">
            <el-button size="small" @click="handleBatchApprove" :disabled="!selectedItems.length">
              批量通过
            </el-button>
            <el-button size="small" type="danger" @click="handleBatchReject" :disabled="!selectedItems.length">
              批量拒绝
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="contentList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="内容预览" width="200">
          <template #default="{ row }">
            <div class="content-preview">
              <img v-if="row.thumbnail" :src="row.thumbnail" class="preview-image" />
              <div class="preview-text">{{ row.content }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="contentType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getContentTypeTag(row.contentType)">
              {{ getContentTypeText(row.contentType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="reportReason" label="举报原因" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.reportReason" type="warning" size="small">
              {{ getReportReasonText(row.reportReason) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column prop="reviewedAt" label="审核时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">
              <el-icon><view /></el-icon>
              查看
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="success"
              @click="handleApprove(row)"
            >
              通过
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="danger"
              @click="handleReject(row)"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 内容详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="内容详情"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentContent" class="content-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="内容ID">{{ currentContent.id }}</el-descriptions-item>
          <el-descriptions-item label="内容类型">{{ getContentTypeText(currentContent.contentType) }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ currentContent.author }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTag(currentContent.status)">
              {{ getStatusText(currentContent.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentContent.createdAt }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{ currentContent.reviewedAt || '-' }}</el-descriptions-item>
        </el-descriptions>

        <div class="content-body">
          <h4>内容详情</h4>
          <div v-if="currentContent.images && currentContent.images.length" class="content-images">
            <img
              v-for="(image, index) in currentContent.images"
              :key="index"
              :src="image"
              class="content-image"
            />
          </div>
          <div class="content-text">{{ currentContent.content }}</div>
        </div>

        <div v-if="currentContent.reportReason" class="report-info">
          <h4>举报信息</h4>
          <p><strong>举报原因：</strong>{{ getReportReasonText(currentContent.reportReason) }}</p>
          <p><strong>举报详情：</strong>{{ currentContent.reportDetail || '-' }}</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentContent && currentContent.status === 'pending'"
            type="success"
            @click="handleApprove(currentContent)"
          >
            通过审核
          </el-button>
          <el-button
            v-if="currentContent && currentContent.status === 'pending'"
            type="danger"
            @click="handleReject(currentContent)"
          >
            拒绝审核
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, View } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const selectedItems = ref([])
const currentContent = ref(null)

const filterForm = reactive({
  status: '',
  contentType: '',
  reportReason: '',
  dateRange: []
})

const stats = reactive({
  pending: 0,
  approved: 0,
  rejected: 0,
  total: 0
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const contentList = ref([])

// 方法
const loadContentList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    contentList.value = [
      {
        id: 1,
        content: '今天天气真好，拍了一张美丽的风景照',
        contentType: 'photo_note',
        author: '用户001',
        status: 'pending',
        reportReason: null,
        thumbnail: 'https://via.placeholder.com/100x100',
        createdAt: '2025-05-23 10:30:00',
        reviewedAt: null,
        images: ['https://via.placeholder.com/400x300'],
        reportDetail: null
      },
      {
        id: 2,
        content: '这是一条可能包含敏感内容的评论',
        contentType: 'comment',
        author: '用户002',
        status: 'pending',
        reportReason: 'spam',
        thumbnail: null,
        createdAt: '2025-05-23 09:15:00',
        reviewedAt: null,
        images: [],
        reportDetail: '用户举报此评论为垃圾广告'
      }
    ]
    
    stats.pending = 25
    stats.approved = 156
    stats.rejected = 12
    stats.total = 193
    pagination.total = 50
    
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadContentList()
}

const handleReset = () => {
  Object.assign(filterForm, {
    status: '',
    contentType: '',
    reportReason: '',
    dateRange: []
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

const handleView = (row) => {
  currentContent.value = row
  detailDialogVisible.value = true
}

const handleApprove = async (row) => {
  try {
    await ElMessageBox.confirm('确认通过此内容的审核？', '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    row.status = 'approved'
    row.reviewedAt = new Date().toLocaleString()
    ElMessage.success('审核通过')
    
    if (detailDialogVisible.value) {
      detailDialogVisible.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleReject = async (row) => {
  try {
    await ElMessageBox.confirm('确认拒绝此内容的审核？', '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    row.status = 'rejected'
    row.reviewedAt = new Date().toLocaleString()
    ElMessage.success('审核拒绝')
    
    if (detailDialogVisible.value) {
      detailDialogVisible.value = false
    }
  } catch (error) {
    // 用户取消操作
  }
}

const handleBatchApprove = async () => {
  try {
    await ElMessageBox.confirm(`确认批量通过 ${selectedItems.value.length} 条内容的审核？`, '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    selectedItems.value.forEach(item => {
      item.status = 'approved'
      item.reviewedAt = new Date().toLocaleString()
    })
    
    ElMessage.success('批量审核通过')
    selectedItems.value = []
  } catch (error) {
    // 用户取消操作
  }
}

const handleBatchReject = async () => {
  try {
    await ElMessageBox.confirm(`确认批量拒绝 ${selectedItems.value.length} 条内容的审核？`, '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    selectedItems.value.forEach(item => {
      item.status = 'rejected'
      item.reviewedAt = new Date().toLocaleString()
    })
    
    ElMessage.success('批量审核拒绝')
    selectedItems.value = []
  } catch (error) {
    // 用户取消操作
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadContentList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadContentList()
}

const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentContent.value = null
}

// 辅助方法
const getContentTypeText = (type) => {
  const map = {
    photo_note: '照片笔记',
    comment: '评论',
    profile: '用户资料'
  }
  return map[type] || type
}

const getContentTypeTag = (type) => {
  const map = {
    photo_note: 'primary',
    comment: 'success',
    profile: 'info'
  }
  return map[type] || ''
}

const getStatusText = (status) => {
  const map = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    deleted: '已删除'
  }
  return map[status] || status
}

const getStatusTag = (status) => {
  const map = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    deleted: 'info'
  }
  return map[status] || ''
}

const getReportReasonText = (reason) => {
  const map = {
    illegal: '违法违规',
    pornographic: '色情低俗',
    violent: '暴力血腥',
    spam: '垃圾广告',
    other: '其他'
  }
  return map[reason] || reason
}

// 生命周期
onMounted(() => {
  loadContentList()
})
</script>

<style scoped>
.content-review-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.content-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.content-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.preview-text {
  flex: 1;
  font-size: 12px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.content-detail {
  max-height: 600px;
  overflow-y: auto;
}

.content-body {
  margin: 20px 0;
}

.content-body h4 {
  margin-bottom: 12px;
  color: #303133;
}

.content-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.content-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.content-text {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
}

.report-info {
  margin-top: 20px;
  padding: 16px;
  background-color: #fef0f0;
  border-radius: 4px;
  border-left: 4px solid #f56c6c;
}

.report-info h4 {
  margin-bottom: 12px;
  color: #f56c6c;
}

.report-info p {
  margin: 8px 0;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
