package com.phototagmoment.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 数据库工具类
 */
@Component
public class DatabaseUtil {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 检查表中是否存在指定列
     *
     * @param tableName 表名
     * @param columnName 列名
     * @return 是否存在
     */
    public boolean columnExists(String tableName, String columnName) {
        String sql = "SELECT COUNT(*) FROM information_schema.columns " +
                "WHERE table_schema = DATABASE() " +
                "AND table_name = ? " +
                "AND column_name = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName, columnName);
        return count != null && count > 0;
    }

    /**
     * 添加列
     *
     * @param tableName 表名
     * @param columnName 列名
     * @param columnDefinition 列定义
     */
    public void addColumn(String tableName, String columnName, String columnDefinition) {
        if (!columnExists(tableName, columnName)) {
            String sql = "ALTER TABLE " + tableName + " ADD COLUMN " + columnName + " " + columnDefinition;
            jdbcTemplate.execute(sql);
        }
    }
}
