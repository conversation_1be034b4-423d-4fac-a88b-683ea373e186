package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.PhotoLike;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 照片点赞Mapper接口
 */
@Mapper
@Repository
public interface PhotoLikeMapper extends BaseMapper<PhotoLike> {

    /**
     * 统计照片的点赞数
     *
     * @param photoId 照片ID
     * @return 点赞数
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_like WHERE photo_id = #{photoId} AND is_deleted = 0")
    int countByPhotoId(@Param("photoId") Long photoId);

    /**
     * 获取用户点赞的照片ID列表
     *
     * @param userId 用户ID
     * @return 照片ID列表
     */
    @Select("SELECT photo_id FROM ptm_photo_like WHERE user_id = #{userId} AND is_deleted = 0")
    List<Long> getLikedPhotoIds(@Param("userId") Long userId);

    /**
     * 获取照片的点赞用户ID列表
     *
     * @param photoId 照片ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM ptm_photo_like WHERE photo_id = #{photoId} AND is_deleted = 0")
    List<Long> getLikedUserIds(@Param("photoId") Long photoId);

    /**
     * 检查用户是否点赞了照片
     *
     * @param userId  用户ID
     * @param photoId 照片ID
     * @return 是否点赞
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_like WHERE user_id = #{userId} AND photo_id = #{photoId} AND is_deleted = 0")
    int checkUserLiked(@Param("userId") Long userId, @Param("photoId") Long photoId);
}
