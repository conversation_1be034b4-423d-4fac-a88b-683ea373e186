package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.UserVerification;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 用户实名认证Mapper接口
 */
@Repository
public interface UserVerificationMapper extends BaseMapper<UserVerification> {

    /**
     * 根据用户ID查询实名认证信息
     *
     * @param userId 用户ID
     * @return 实名认证信息
     */
    UserVerification selectByUserId(@Param("userId") Long userId);
}
