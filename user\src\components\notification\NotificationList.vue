<template>
  <div class="notification-list">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <!-- 使用普通div替代van-list，手动控制加载逻辑 -->
      <div>
        <div v-if="notifications.length > 0">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.isRead }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-avatar">
              <van-image
                round
                width="40"
                height="40"
                :src="notification.sender ? notification.sender.avatar : '/images/system-avatar.png'"
                :alt="notification.sender ? notification.sender.nickname : '系统通知'"
              />
            </div>
            <div class="notification-content">
              <div class="notification-header">
                <span class="sender-name" v-if="notification.sender">{{ notification.sender.nickname }}</span>
                <span class="sender-name" v-else>系统通知</span>
                <span class="notification-text">{{ notification.content }}</span>
              </div>
              <div class="notification-time">{{ formatTime(notification.createTime) }}</div>

              <!-- 目标内容预览 -->
              <div v-if="notification.target" class="notification-target">
                <div v-if="notification.target.type === 'photo'" class="target-photo">
                  <van-image
                    width="60"
                    height="60"
                    fit="cover"
                    :src="notification.target.thumbnailUrl"
                    :alt="notification.target.title"
                  />
                </div>
              </div>
            </div>
            <div class="notification-actions">
              <van-icon name="arrow" />
            </div>
          </div>
        </div>
        <div v-else-if="!loading" class="empty-state">
          <van-empty description="暂无消息" />
        </div>

        <!-- 加载状态和加载更多按钮 -->
        <div class="load-more-container">
          <van-loading v-if="loading" size="24px" vertical>加载中...</van-loading>
          <van-button
            v-else-if="!isFinished"
            size="small"
            type="primary"
            plain
            @click="onLoadMore"
          >
            加载更多
          </van-button>
          <div v-else class="finished-text">没有更多了</div>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';

const props = defineProps({
  notifications: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['load-more', 'refresh', 'mark-read']);

const router = useRouter();

// 列表状态
const refreshing = ref(false);
const isFinished = computed(() => {
  // 如果通知列表为空，则认为已加载完成
  if (props.notifications.length === 0) {
    return true;
  }

  // 如果通知列表数量不是10的倍数，可能已经加载完所有数据
  if (props.notifications.length > 0 && props.notifications.length % 10 !== 0) {
    return true;
  }

  // 否则根据父组件传递的加载状态判断
  return false;
});

// 下拉刷新
const onRefresh = () => {
  console.log('NotificationList: 触发下拉刷新');
  emit('refresh');
  refreshing.value = false;
};

// 加载更多
const onLoadMore = () => {
  // 如果已经加载完成，不再触发加载更多
  if (isFinished.value) {
    console.log('NotificationList: 已加载完所有数据，不再触发加载更多');
    return;
  }

  // 如果正在加载中，不再触发加载更多
  if (props.loading) {
    console.log('NotificationList: 正在加载中，不再触发加载更多');
    return;
  }

  console.log('NotificationList: 手动触发加载更多');
  emit('load-more');
};

// 处理通知点击
const handleNotificationClick = (notification: any) => {
  // 标记为已读
  notification.isRead = true;

  // 根据通知类型和目标跳转
  if (notification.target) {
    switch (notification.target.type) {
      case 'photo':
        router.push(`/photo/${notification.target.id}`);
        break;
      case 'comment':
        router.push(`/photo/${notification.target.photoId}?comment=${notification.target.id}`);
        break;
      default:
        break;
    }
  } else if (notification.type === 'follow' && notification.sender) {
    router.push(`/user/${notification.sender.id}`);
  } else if (notification.type === 'system') {
    // 系统通知通常不需要跳转，只需标记为已读
    emit('mark-read', notification.id);
  } else {
    showToast('无法跳转到相关内容');
  }
};

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚';
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`;
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
  }

  // 小于1周
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`;
  }

  // 格式化为年月日
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};
</script>

<style lang="scss" scoped>
.notification-list {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.notification-item {
  display: flex;
  padding: 16px;
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;

  &.unread {
    background-color: #f0f8ff;

    &::before {
      content: '';
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #3498db;
    }
  }

  &:active {
    background-color: #f9f9f9;
  }
}

.notification-avatar {
  margin-right: 12px;
}

.notification-content {
  flex: 1;
  overflow: hidden;
}

.notification-header {
  margin-bottom: 4px;

  .sender-name {
    font-weight: 500;
    margin-right: 4px;
  }

  .notification-text {
    color: #333;
  }
}

.notification-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.notification-target {
  margin-top: 8px;

  .target-photo {
    display: inline-block;
    border-radius: 4px;
    overflow: hidden;
  }
}

.notification-actions {
  display: flex;
  align-items: center;
  color: #999;
}

.empty-state {
  padding: 40px 0;
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.finished-text {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 16px 0;
}
</style>
