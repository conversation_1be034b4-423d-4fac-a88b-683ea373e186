USE phototag_moment;

-- 创建管理员用户
INSERT IGNORE INTO ptm_user (username, nickname, password, email, status, is_admin, is_verified, created_at, updated_at) 
VALUES ('admin', '系统管理员', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', '<EMAIL>', 1, 1, 1, NOW(), NOW());

-- 获取管理员ID
SET @admin_id = (SELECT id FROM ptm_user WHERE username = 'admin');

-- 创建管理员认证信息
INSERT IGNORE INTO ptm_user_auth (user_id, identity_type, identifier, credential, verified, created_at, updated_at)
VALUES (@admin_id, 'username', 'admin', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', 1, NOW(), NOW());

-- 创建测试用户
INSERT IGNORE INTO ptm_user (username, nickname, password, email, status, is_admin, is_verified, created_at, updated_at) 
VALUES ('testuser', '测试用户', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', '<EMAIL>', 1, 0, 1, NOW(), NOW());

-- 获取测试用户ID
SET @test_id = (SELECT id FROM ptm_user WHERE username = 'testuser');

-- 创建测试用户认证信息
INSERT IGNORE INTO ptm_user_auth (user_id, identity_type, identifier, credential, verified, created_at, updated_at)
VALUES (@test_id, 'username', 'testuser', '$2a$10$N.zmdr9k7uOCQb96VdqOVOBnw2i/3rjIhByJ5lAoW7yFDtUO7EWO6', 1, NOW(), NOW());

-- 创建测试照片笔记
INSERT IGNORE INTO ptm_photo_note (user_id, title, content, photo_count, status, visibility, allow_comment, created_at, updated_at)
VALUES 
(@test_id, '测试照片笔记1', '这是一条测试照片笔记 #测试#', 1, 1, 1, 1, NOW(), NOW()),
(@test_id, '待审核照片笔记', '这是一条待审核的照片笔记 #待审核#', 1, 0, 1, 1, NOW(), NOW());

-- 显示结果
SELECT 'Users created:' as result;
SELECT id, username, is_admin, status FROM ptm_user WHERE username IN ('admin', 'testuser');

SELECT 'Auth records created:' as result;
SELECT COUNT(*) as auth_count FROM ptm_user_auth;

SELECT 'Photo notes created:' as result;
SELECT id, title, status FROM ptm_photo_note;
