<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置详情"
    width="700px"
    :close-on-click-modal="false"
  >
    <div v-if="config" class="config-detail">
      <!-- 基本信息 -->
      <el-card class="detail-section" header="基本信息">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="配置名称">
            <span>{{ config.configName }}</span>
            <el-tag v-if="config.isDefault" type="success" size="small" style="margin-left: 8px;">
              默认
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="存储类型">
            <el-tag :type="getStorageTypeTagType(config.storageType)">
              {{ getStorageTypeLabel(config.storageType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="启用状态">
            <el-tag :type="config.enabled ? 'success' : 'danger'">
              {{ config.enabled ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="配置状态">
            <el-tag :type="getStatusTagType(config.status)">
              {{ getStatusLabel(config.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="配置描述" :span="2">
            {{ config.description || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 存储配置 -->
      <el-card class="detail-section" header="存储配置">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="访问域名">
            {{ config.configParams?.domain || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="使用HTTPS">
            <el-tag :type="config.configParams?.useHttps ? 'success' : 'info'">
              {{ config.configParams?.useHttps ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="连接超时">
            {{ config.configParams?.connectTimeout || '-' }}秒
          </el-descriptions-item>
          <el-descriptions-item label="读取超时">
            {{ config.configParams?.readTimeout || '-' }}秒
          </el-descriptions-item>
          
          <!-- 根据存储类型显示不同配置 -->
          <template v-if="config.storageType === 'LOCAL'">
            <el-descriptions-item label="存储路径" :span="2">
              {{ config.configParams?.localPath || '-' }}
            </el-descriptions-item>
          </template>
          
          <template v-if="config.storageType === 'QINIU'">
            <el-descriptions-item label="AccessKey">
              {{ maskSensitiveInfo(config.configParams?.qiniuAccessKey) }}
            </el-descriptions-item>
            <el-descriptions-item label="存储空间">
              {{ config.configParams?.qiniuBucket || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="存储区域" :span="2">
              {{ getQiniuRegionLabel(config.configParams?.qiniuRegion) }}
            </el-descriptions-item>
          </template>
          
          <template v-if="config.storageType === 'ALIYUN_OSS'">
            <el-descriptions-item label="AccessKeyId">
              {{ maskSensitiveInfo(config.configParams?.aliyunAccessKeyId) }}
            </el-descriptions-item>
            <el-descriptions-item label="Bucket名称">
              {{ config.configParams?.aliyunBucket || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="Endpoint" :span="2">
              {{ config.configParams?.aliyunEndpoint || '-' }}
            </el-descriptions-item>
          </template>
          
          <template v-if="config.storageType === 'TENCENT_COS'">
            <el-descriptions-item label="SecretId">
              {{ maskSensitiveInfo(config.configParams?.tencentSecretId) }}
            </el-descriptions-item>
            <el-descriptions-item label="Bucket名称">
              {{ config.configParams?.tencentBucket || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="地域" :span="2">
              {{ config.configParams?.tencentRegion || '-' }}
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </el-card>

      <!-- 上传限制 -->
      <el-card class="detail-section" header="上传限制">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="最大文件大小">
            {{ config.uploadLimits?.maxFileSize || '-' }}MB
          </el-descriptions-item>
          <el-descriptions-item label="单次最大数量">
            {{ config.uploadLimits?.maxFileCount || '-' }}个
          </el-descriptions-item>
          <el-descriptions-item label="允许的文件类型" :span="2">
            <div class="file-types">
              <el-tag
                v-for="type in config.uploadLimits?.allowedFileTypes"
                :key="type"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px;"
              >
                {{ type.toUpperCase() }}
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="禁止的文件类型" :span="2">
            <div v-if="config.uploadLimits?.forbiddenFileTypes?.length" class="file-types">
              <el-tag
                v-for="type in config.uploadLimits.forbiddenFileTypes"
                :key="type"
                type="danger"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px;"
              >
                {{ type.toUpperCase() }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="图片最大尺寸">
            {{ config.uploadLimits?.imageMaxDimensions?.maxWidth || '-' }} × 
            {{ config.uploadLimits?.imageMaxDimensions?.maxHeight || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="安全检查">
            <div class="security-checks">
              <el-tag 
                :type="config.uploadLimits?.enableFileTypeCheck ? 'success' : 'info'" 
                size="small"
              >
                文件类型检查
              </el-tag>
              <el-tag 
                :type="config.uploadLimits?.enableContentCheck ? 'success' : 'info'" 
                size="small"
                style="margin-left: 4px;"
              >
                内容检查
              </el-tag>
              <el-tag 
                :type="config.uploadLimits?.enableVirusScan ? 'success' : 'info'" 
                size="small"
                style="margin-left: 4px;"
              >
                病毒扫描
              </el-tag>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 路径配置 -->
      <el-card class="detail-section" header="路径配置">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="根目录">
            {{ config.pathConfig?.rootPath || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="文件命名规则">
            {{ getFileNamingRuleLabel(config.pathConfig?.fileNamingRule) }}
          </el-descriptions-item>
          <el-descriptions-item label="目录结构">
            {{ getDirectoryStructureLabel(config.pathConfig?.directoryStructure) }}
          </el-descriptions-item>
          <el-descriptions-item label="目录选项">
            <div class="directory-options">
              <el-tag 
                :type="config.pathConfig?.enableDateDirectory ? 'success' : 'info'" 
                size="small"
              >
                日期目录
              </el-tag>
              <el-tag 
                :type="config.pathConfig?.enableUserDirectory ? 'success' : 'info'" 
                size="small"
                style="margin-left: 4px;"
              >
                用户目录
              </el-tag>
              <el-tag 
                :type="config.pathConfig?.enableTypeDirectory ? 'success' : 'info'" 
                size="small"
                style="margin-left: 4px;"
              >
                类型目录
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="自定义前缀">
            {{ config.pathConfig?.customPrefix || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="缩略图目录">
            {{ config.pathConfig?.thumbnailDirectory || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="临时文件目录" :span="2">
            {{ config.pathConfig?.tempDirectory || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 测试信息 -->
      <el-card v-if="config.lastTestTime" class="detail-section" header="测试信息">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="最后测试时间">
            {{ formatDateTime(config.lastTestTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="测试结果">
            <el-tag :type="getTestResultTagType(config.lastTestResult)">
              {{ getTestResultLabel(config.lastTestResult) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="getTestResultDetails(config.lastTestResult)" label="测试详情" :span="2">
            <div class="test-details">
              {{ getTestResultDetails(config.lastTestResult) }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 创建信息 -->
      <el-card class="detail-section" header="创建信息">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(config.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(config.updatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { FileUploadConfig } from '@/api/fileUploadConfig'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  modelValue: boolean
  config?: FileUploadConfig | null
}

const props = withDefaults(defineProps<Props>(), {
  config: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const maskSensitiveInfo = (info?: string) => {
  if (!info) return '-'
  if (info.length <= 8) return '*'.repeat(info.length)
  return info.substring(0, 4) + '*'.repeat(info.length - 8) + info.substring(info.length - 4)
}

const getStorageTypeTagType = (storageType: string) => {
  const typeMap = {
    LOCAL: 'info',
    QINIU: 'success',
    ALIYUN_OSS: 'warning',
    TENCENT_COS: 'primary',
    AWS_S3: 'danger',
    MINIO: 'info'
  }
  return typeMap[storageType] || 'info'
}

const getStorageTypeLabel = (storageType: string) => {
  const labelMap = {
    LOCAL: '本地存储',
    QINIU: '七牛云',
    ALIYUN_OSS: '阿里云OSS',
    TENCENT_COS: '腾讯云COS',
    AWS_S3: 'AWS S3',
    MINIO: 'MinIO'
  }
  return labelMap[storageType] || '未知'
}

const getStatusTagType = (status?: number) => {
  const typeMap = {
    0: 'success',
    1: 'warning',
    2: 'danger'
  }
  return typeMap[status || 0] || 'info'
}

const getStatusLabel = (status?: number) => {
  const labelMap = {
    0: '正常',
    1: '禁用',
    2: '异常'
  }
  return labelMap[status || 0] || '未知'
}

const getQiniuRegionLabel = (region?: string) => {
  const regionMap = {
    z0: '华东-浙江',
    z1: '华北-河北',
    z2: '华南-广东',
    na0: '北美-洛杉矶',
    as0: '亚太-新加坡'
  }
  return regionMap[region || ''] || region || '-'
}

const getFileNamingRuleLabel = (rule?: string) => {
  const ruleMap = {
    UUID: 'UUID',
    TIMESTAMP: '时间戳',
    ORIGINAL: '原文件名',
    TIMESTAMP_ORIGINAL: '时间戳+原文件名',
    UUID_ORIGINAL: 'UUID+原文件名'
  }
  return ruleMap[rule || ''] || rule || '-'
}

const getDirectoryStructureLabel = (structure?: string) => {
  const structureMap = {
    FLAT: '扁平结构',
    DATE: '按日期分类',
    USER: '按用户分类',
    TYPE: '按文件类型分类',
    DATE_USER: '日期+用户',
    DATE_TYPE: '日期+类型',
    USER_TYPE: '用户+类型',
    DATE_USER_TYPE: '日期+用户+类型'
  }
  return structureMap[structure || ''] || structure || '-'
}

const getTestResultTagType = (testResult?: string) => {
  if (!testResult) return 'info'
  try {
    const result = JSON.parse(testResult)
    return result.success ? 'success' : 'danger'
  } catch {
    return 'info'
  }
}

const getTestResultLabel = (testResult?: string) => {
  if (!testResult) return '未测试'
  try {
    const result = JSON.parse(testResult)
    return result.success ? '成功' : '失败'
  } catch {
    return '未知'
  }
}

const getTestResultDetails = (testResult?: string) => {
  if (!testResult) return ''
  try {
    const result = JSON.parse(testResult)
    let details = `消息: ${result.message}`
    if (result.responseTime) {
      details += `\n响应时间: ${result.responseTime}ms`
    }
    if (result.errorDetails) {
      details += `\n错误详情: ${result.errorDetails}`
    }
    return details
  } catch {
    return ''
  }
}
</script>

<style scoped>
.config-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.file-types {
  line-height: 1.8;
}

.security-checks,
.directory-options {
  line-height: 1.8;
}

.test-details {
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-line;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}
</style>
