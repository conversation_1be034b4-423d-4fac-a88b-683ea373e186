package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.service.PhotoNoteService;
import com.phototagmoment.service.RecommendationService;
import com.phototagmoment.util.UserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 首页控制器
 */
@Slf4j
@RestController
@RequestMapping("/home")
@Tag(name = "首页接口", description = "首页照片笔记流相关接口")
@Validated
public class HomeController {

    @Autowired
    private PhotoNoteService photoNoteService;

    @Autowired
    private RecommendationService recommendationService;

    @GetMapping("/feed")
    @Operation(summary = "获取首页照片笔记流", description = "根据类型获取首页照片笔记流")
    public ApiResponse<IPage<PhotoNoteDTO>> getHomePhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "类型") @RequestParam(defaultValue = "recommend") String type,
            @Parameter(description = "最后一条记录ID") @RequestParam(required = false) Long lastId) {

        Long currentUserId = UserUtil.getCurrentUserId();

        IPage<PhotoNoteDTO> result;

        switch (type) {
            case "recommend":
                result = recommendationService.getRecommendedPhotoNotes(page, size, currentUserId, lastId);
                break;
            case "following":
                result = photoNoteService.getFollowingPhotoNotes(page, size, currentUserId, lastId);
                break;
            case "latest":
                result = photoNoteService.getLatestPhotoNotes(page, size, currentUserId, lastId);
                break;
            case "hot":
                result = photoNoteService.getHotPhotoNotes(page, size, currentUserId, lastId);
                break;
            default:
                result = recommendationService.getRecommendedPhotoNotes(page, size, currentUserId, lastId);
        }

        return ApiResponse.success(result);
    }

    @GetMapping("/recommend")
    @Operation(summary = "获取推荐照片笔记", description = "基于用户兴趣的个性化推荐")
    public ApiResponse<IPage<PhotoNoteDTO>> getRecommendedPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "最后一条记录ID") @RequestParam(required = false) Long lastId) {

        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> result = recommendationService.getRecommendedPhotoNotes(page, size, currentUserId, lastId);

        return ApiResponse.success(result);
    }

    @GetMapping("/following")
    @Operation(summary = "获取关注用户的照片笔记", description = "获取当前用户关注的用户发布的照片笔记")
    public ApiResponse<IPage<PhotoNoteDTO>> getFollowingPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "最后一条记录ID") @RequestParam(required = false) Long lastId) {

        Long currentUserId = UserUtil.getCurrentUserId();
        if (currentUserId == null) {
            return ApiResponse.failed("用户未登录");
        }

        IPage<PhotoNoteDTO> result = photoNoteService.getFollowingPhotoNotes(page, size, currentUserId, lastId);

        return ApiResponse.success(result);
    }

    @GetMapping("/latest")
    @Operation(summary = "获取最新照片笔记", description = "按时间倒序获取最新发布的照片笔记")
    public ApiResponse<IPage<PhotoNoteDTO>> getLatestPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "最后一条记录ID") @RequestParam(required = false) Long lastId) {

        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> result = photoNoteService.getLatestPhotoNotes(page, size, currentUserId, lastId);

        return ApiResponse.success(result);
    }

    @GetMapping("/hot")
    @Operation(summary = "获取热门照片笔记", description = "按热度排序获取热门照片笔记")
    public ApiResponse<IPage<PhotoNoteDTO>> getHotPhotoNotes(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer size,
            @Parameter(description = "最后一条记录ID") @RequestParam(required = false) Long lastId) {

        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoNoteDTO> result = photoNoteService.getHotPhotoNotes(page, size, currentUserId, lastId);

        return ApiResponse.success(result);
    }

    @PostMapping("/{noteId}/view")
    @Operation(summary = "增加照片笔记浏览量", description = "用户浏览照片笔记时调用")
    public ApiResponse<Boolean> incrementPhotoNoteView(
            @Parameter(description = "照片笔记ID") @PathVariable Long noteId) {

        Long currentUserId = UserUtil.getCurrentUserId();
        boolean result = photoNoteService.incrementViewCount(noteId, currentUserId);

        return ApiResponse.success(result);
    }

    @PostMapping("/{noteId}/report")
    @Operation(summary = "举报照片笔记", description = "用户举报不当内容")
    public ApiResponse<Boolean> reportPhotoNote(
            @Parameter(description = "照片笔记ID") @PathVariable Long noteId,
            @RequestBody ReportRequest request) {

        Long currentUserId = UserUtil.getCurrentUserId();
        if (currentUserId == null) {
            return ApiResponse.failed("用户未登录");
        }

        boolean result = photoNoteService.reportPhotoNote(noteId, currentUserId, request.getReason(), request.getDescription());

        return ApiResponse.success(result);
    }

    /**
     * 举报请求对象
     */
    public static class ReportRequest {
        private String reason;
        private String description;

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }
}
