<template>
  <div class="settings-container">
    <van-nav-bar
      title="设置"
      left-arrow
      @click-left="goBack"
      fixed
    />
    
    <div class="settings-content">
      <van-cell-group inset title="账号设置">
        <van-cell title="个人资料" is-link to="/settings/profile" />
        <van-cell title="账号安全" is-link to="/settings/security" />
        <van-cell title="隐私设置" is-link to="/settings/privacy" />
        <van-cell title="通知设置" is-link to="/settings/notifications" />
      </van-cell-group>
      
      <van-cell-group inset title="内容设置">
        <van-cell title="照片管理" is-link to="/settings/photos" />
        <van-cell title="收藏管理" is-link to="/settings/collections" />
        <van-cell title="黑名单管理" is-link to="/settings/blacklist" />
      </van-cell-group>
      
      <van-cell-group inset title="应用设置">
        <van-cell title="语言" is-link @click="showLanguagePopup = true">
          <template #value>
            <span>{{ languageOptions[currentLanguage] }}</span>
          </template>
        </van-cell>
        
        <van-cell title="主题">
          <template #right-icon>
            <van-switch v-model="isDarkMode" size="24" />
          </template>
        </van-cell>
        
        <van-cell title="清除缓存" @click="clearCache" />
      </van-cell-group>
      
      <van-cell-group inset title="关于">
        <van-cell title="关于我们" is-link to="/about" />
        <van-cell title="用户协议" is-link to="/terms" />
        <van-cell title="隐私政策" is-link to="/privacy" />
        <van-cell title="版本" :value="appVersion" />
      </van-cell-group>
      
      <div class="logout-button">
        <van-button round block type="danger" @click="showLogoutConfirm = true">退出登录</van-button>
      </div>
    </div>
    
    <!-- 语言选择弹窗 -->
    <van-popup
      v-model:show="showLanguagePopup"
      position="bottom"
      round
    >
      <van-picker
        :columns="Object.values(languageOptions)"
        @confirm="confirmLanguage"
        @cancel="showLanguagePopup = false"
        show-toolbar
        title="选择语言"
      />
    </van-popup>
    
    <!-- 退出登录确认弹窗 -->
    <van-dialog
      v-model:show="showLogoutConfirm"
      title="退出登录"
      show-cancel-button
      @confirm="logout"
    >
      <p class="logout-confirm-text">确定要退出登录吗？</p>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showSuccessToast } from 'vant';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();

// 检查登录状态
if (!userStore.isLoggedIn) {
  router.push('/auth/login?redirect=/settings');
}

// 应用版本
const appVersion = ref('1.0.0');

// 主题设置
const isDarkMode = ref(false);

// 语言设置
const languageOptions = {
  'zh-CN': '简体中文',
  'en-US': '英文',
  'ja-JP': '日文',
  'ko-KR': '韩文'
};
const currentLanguage = ref('zh-CN');
const showLanguagePopup = ref(false);

// 退出登录确认
const showLogoutConfirm = ref(false);

// 返回上一页
const goBack = () => {
  router.back();
};

// 确认语言选择
const confirmLanguage = ({ selectedOptions }) => {
  const selected = selectedOptions[0];
  const key = Object.keys(languageOptions).find(k => languageOptions[k] === selected);
  
  if (key) {
    currentLanguage.value = key;
    showToast(`语言已切换为${selected}`);
  }
  
  showLanguagePopup.value = false;
};

// 清除缓存
const clearCache = async () => {
  try {
    // 模拟清除缓存
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    showSuccessToast('缓存已清除');
  } catch (error) {
    console.error('清除缓存失败', error);
    showToast('清除缓存失败，请稍后重试');
  }
};

// 退出登录
const logout = () => {
  userStore.logoutAction();
  showSuccessToast('已退出登录');
  router.push('/');
};
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 46px; // 导航栏高度
}

.settings-content {
  padding: 16px;
  
  .van-cell-group {
    margin-bottom: 16px;
  }
}

.logout-button {
  margin: 24px 16px;
}

.logout-confirm-text {
  padding: 16px;
  text-align: center;
}
</style>
