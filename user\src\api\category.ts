import request from '@/utils/request'

/**
 * 分类
 */
export interface Category {
  id: number;
  name: string;
  value: string;
  icon?: string;
  count?: number;
  order?: number;
}

/**
 * 获取所有分类
 * @returns 分类列表
 */
export function getAllCategories() {
  return request({
    url: '/dict/data/code/photo_category',
    method: 'get'
  })
}

/**
 * 获取热门分类
 * @param limit 限制数量
 * @returns 热门分类列表
 */
export function getHotCategories(limit: number = 10) {
  // 由于没有专门的热门分类接口，我们使用相同的接口获取所有分类
  return getAllCategories()
}

/**
 * 获取分类详情
 * @param id 分类ID
 * @returns 分类详情
 */
export function getCategoryDetail(id: number) {
  return request({
    url: `/category/${id}`,
    method: 'get'
  })
}

/**
 * 获取分类内容
 * @param id 分类ID
 * @param page 页码
 * @param size 每页大小
 * @returns 分类内容
 */
export function getCategoryContent(id: number, page: number = 1, size: number = 10) {
  return request({
    url: `/category/${id}/content`,
    method: 'get',
    params: {
      page,
      size
    }
  })
}
