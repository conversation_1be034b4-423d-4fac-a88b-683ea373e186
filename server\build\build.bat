@echo off
chcp 65001 >nul

REM 解析命令行参数
set PROFILE=%1
if "%PROFILE%"=="" set PROFILE=dev

REM 验证环境参数
if not "%PROFILE%"=="dev" if not "%PROFILE%"=="test" if not "%PROFILE%"=="prod" (
    echo 错误: 无效的环境参数 "%PROFILE%"
    echo 支持的环境: dev, test, prod
    echo 用法: %0 [dev^|test^|prod]
    echo 示例: %0 prod
    pause
    exit /b 1
)

echo ==========================================
echo PhotoTagMoment 多环境打包脚本
echo ==========================================
echo 目标环境: %PROFILE%
echo ==========================================

REM 设置Java 17环境
set JAVA_HOME=C:\Program Files\Java\jdk-17
echo 使用Java版本: %JAVA_HOME%

REM 检查是否在项目根目录
if not exist "..\pom.xml" (
    echo 错误: 请在项目根目录运行此脚本
    echo 当前目录: %CD%
    echo 应该包含: ..\pom.xml
    pause
    exit /b 1
)

REM 检查环境配置文件是否存在
if not exist "..\src\main\resources\application-%PROFILE%.yml" (
    echo 警告: 环境配置文件不存在: application-%PROFILE%.yml
    echo 将使用默认配置文件
)

echo [1/4] 进入后端目录...
cd ..

echo [2/4] 清理之前的构建...
call mvn clean -q
if errorlevel 1 (
    echo 错误: Maven清理失败
    pause
    exit /b 1
)

echo [3/4] 编译项目...
call mvn compile -q
if errorlevel 1 (
    echo 错误: Maven编译失败
    pause
    exit /b 1
)

echo [4/4] 开始打包 (环境: %PROFILE%)...
call mvn package -DskipTests -Dspring.profiles.active=%PROFILE% -q
if errorlevel 1 (
    echo 错误: Maven打包失败
    pause
    exit /b 1
)

REM 获取JAR文件信息
set JAR_FILE=%CD%\target\phototagmoment-0.0.1-SNAPSHOT.jar
if not exist "%JAR_FILE%" (
    echo 错误: JAR文件未生成
    pause
    exit /b 1
)

REM 获取文件大小
for %%A in ("%JAR_FILE%") do set JAR_SIZE=%%~zA
set /a JAR_SIZE_MB=%JAR_SIZE%/1024/1024

echo.
echo ==========================================
echo 打包成功！
echo ==========================================
echo 环境配置: %PROFILE%
echo JAR文件位置: %JAR_FILE%
echo 文件大小: %JAR_SIZE_MB% MB
echo 生成时间: %date% %time%
echo.
echo 运行命令:
echo   java -jar target\phototagmoment-0.0.1-SNAPSHOT.jar
echo   或者指定环境:
echo   java -jar -Dspring.profiles.active=%PROFILE% target\phototagmoment-0.0.1-SNAPSHOT.jar
echo.
echo 配置文件: application-%PROFILE%.yml
echo API文档: http://localhost:8081/api/doc.html
echo ==========================================

pause
