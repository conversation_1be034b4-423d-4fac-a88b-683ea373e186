package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.DictType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 数据字典类型Mapper接口
 */
@Mapper
public interface DictTypeMapper extends BaseMapper<DictType> {

    /**
     * 根据字典类型获取字典类型对象
     *
     * @param dictType 字典类型
     * @return 字典类型对象
     */
    @Select("SELECT * FROM ptm_dict_type WHERE dict_type = #{dictType} AND is_deleted = 0")
    DictType getByDictType(@Param("dictType") String dictType);
}
