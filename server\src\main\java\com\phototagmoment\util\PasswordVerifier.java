package com.phototagmoment.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码验证工具类
 * 用于验证密码哈希是否匹配
 */
public class PasswordVerifier {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "123456";
        
        // 验证不同的哈希值
        String[] hashes = {
            "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iAt2s7JS", // 当前数据库中的哈希
            "$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm", // V1.4中的哈希
            "$2a$10$uXRGYXKj9Vw1Ckj6PRXoquuZGXqWYYnHXbf5yGLws.M8n0QWz6UHi", // V1.3中的哈希
            "$2a$10$EuWPZHzz32dJN7jexM34MOot3JkJMZZ4PcIZcT785SxkVuS38ZW2m"  // 我们生成的新哈希
        };
        
        for (String hash : hashes) {
            boolean matches = encoder.matches(password, hash);
            System.out.println("哈希值: " + hash);
            System.out.println("是否匹配123456: " + matches);
            System.out.println();
        }
        
        // 生成新的哈希值
        String newHash = encoder.encode(password);
        System.out.println("新生成的哈希值: " + newHash);
        System.out.println("是否匹配123456: " + encoder.matches(password, newHash));
    }
}
