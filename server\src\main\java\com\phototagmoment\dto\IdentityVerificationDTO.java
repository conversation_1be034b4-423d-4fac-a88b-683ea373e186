package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 实名认证DTO
 */
@Data
@Schema(description = "实名认证信息")
public class IdentityVerificationDTO {

    @Schema(description = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    @Schema(description = "身份证号")
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)", message = "身份证号格式不正确")
    private String idCard;

    @Schema(description = "身份证正面照片URL")
    private String idCardFrontUrl;

    @Schema(description = "身份证背面照片URL")
    private String idCardBackUrl;

    @Schema(description = "手持身份证照片URL")
    private String idCardHoldingUrl;

    @Schema(description = "人脸照片URL")
    private String faceImageUrl;

    @Schema(description = "认证方式：1-支付宝，2-微信，3-人脸识别，4-人工审核")
    private Integer verifyMethod;
}
