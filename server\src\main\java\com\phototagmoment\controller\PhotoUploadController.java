package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.config.QiniuConfig;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.QiniuStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 照片上传控制器
 * 提供获取上传凭证等功能
 */
@Slf4j
@RestController
@RequestMapping("/photo/upload")
@Tag(name = "照片上传接口", description = "照片上传相关接口")
public class PhotoUploadController {

    @Autowired
    private QiniuConfig qiniuConfig;

    @Autowired
    private QiniuStorageService qiniuStorageService;

    /**
     * 获取七牛云上传凭证
     * 用于前端直接上传到七牛云
     */
    @GetMapping("/token")
    @Operation(summary = "获取上传凭证", description = "获取七牛云上传凭证，用于前端直接上传到七牛云")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, Object>> getUploadToken() {
        // 刷新配置确保最新状态
        qiniuConfig.refreshConfig();

        // 检查配置完整性
        String validationError = qiniuConfig.getConfigValidationError();
        if (validationError != null) {
            return ApiResponse.failed(validationError);
        }

        try {
            // 获取当前用户ID
            Long userId = SecurityUtil.getCurrentUserId();
            if (userId == null) {
                return ApiResponse.failed("获取用户ID失败");
            }

            // 生成文件名
            String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String key = qiniuConfig.getUploadDir() + "/photos/" + userId + "/" + datePath + "/" + uuid;

            // 获取上传凭证
            String uploadToken = qiniuStorageService.getUploadToken(key);
            if (uploadToken == null || uploadToken.trim().isEmpty()) {
                return ApiResponse.failed("生成上传凭证失败");
            }

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("uploadToken", uploadToken);
            result.put("key", key);
            result.put("domain", qiniuConfig.getDomain());

            // 根据配置的区域选择上传域名
            String uploadUrl = "https://upload.qiniup.com"; // 默认华南区域
            if (qiniuConfig.getRegion() != null) {
                switch (qiniuConfig.getRegion().toLowerCase()) {
                    case "huadong":
                    case "z0":
                        uploadUrl = "https://upload.qiniup.com"; // 华东区域
                        break;
                    case "huabei":
                    case "z1":
                        uploadUrl = "https://upload-z1.qiniup.com"; // 华北区域
                        break;
                    case "huanan":
                    case "z2":
                        uploadUrl = "https://upload-z2.qiniup.com"; // 华南区域
                        break;
                    case "beimei":
                    case "na0":
                        uploadUrl = "https://upload-na0.qiniup.com"; // 北美区域
                        break;
                    case "xinjiapo":
                    case "as0":
                        uploadUrl = "https://upload-as0.qiniup.com"; // 新加坡区域
                        break;
                    default:
                        uploadUrl = "https://upload.qiniup.com"; // 默认华东区域
                        break;
                }
            }
            result.put("uploadUrl", uploadUrl);

            log.info("获取上传凭证成功，key: {}", key);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取上传凭证失败", e);
            return ApiResponse.failed("获取上传凭证失败: " + e.getMessage());
        }
    }

    /**
     * 获取多个七牛云上传凭证
     * 用于前端批量上传照片到七牛云
     */
    @GetMapping("/batch-token")
    @Operation(summary = "获取批量上传凭证", description = "获取多个七牛云上传凭证，用于前端批量上传照片到七牛云")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, Object>> getBatchUploadToken(
            @Parameter(description = "照片数量") @RequestParam(defaultValue = "1") Integer count) {
        // 刷新配置确保最新状态
        qiniuConfig.refreshConfig();

        // 检查配置完整性
        String validationError = qiniuConfig.getConfigValidationError();
        if (validationError != null) {
            return ApiResponse.failed(validationError);
        }

        if (count <= 0 || count > 9) {
            return ApiResponse.failed("照片数量必须在1-9之间");
        }

        try {
            // 获取当前用户ID
            Long userId = SecurityUtil.getCurrentUserId();
            if (userId == null) {
                return ApiResponse.failed("获取用户ID失败");
            }

            // 生成文件名和上传凭证
            String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            Map<String, String> keys = new HashMap<>();
            Map<String, String> tokens = new HashMap<>();

            for (int i = 0; i < count; i++) {
                String uuid = UUID.randomUUID().toString().replaceAll("-", "");
                String key = qiniuConfig.getUploadDir() + "/photos/" + userId + "/" + datePath + "/" + uuid;
                String uploadToken = qiniuStorageService.getUploadToken(key);

                if (uploadToken == null || uploadToken.trim().isEmpty()) {
                    return ApiResponse.failed("生成上传凭证失败，索引: " + i);
                }

                keys.put("key" + i, key);
                tokens.put("token" + i, uploadToken);
            }

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("keys", keys);
            result.put("tokens", tokens);
            result.put("domain", qiniuConfig.getDomain());

            // 根据配置的区域选择上传域名
            String uploadUrl = "https://upload.qiniup.com"; // 默认华南区域
            if (qiniuConfig.getRegion() != null) {
                switch (qiniuConfig.getRegion().toLowerCase()) {
                    case "huadong":
                    case "z0":
                        uploadUrl = "https://upload.qiniup.com"; // 华东区域
                        break;
                    case "huabei":
                    case "z1":
                        uploadUrl = "https://upload-z1.qiniup.com"; // 华北区域
                        break;
                    case "huanan":
                    case "z2":
                        uploadUrl = "https://upload-z2.qiniup.com"; // 华南区域
                        break;
                    case "beimei":
                    case "na0":
                        uploadUrl = "https://upload-na0.qiniup.com"; // 北美区域
                        break;
                    case "xinjiapo":
                    case "as0":
                        uploadUrl = "https://upload-as0.qiniup.com"; // 新加坡区域
                        break;
                    default:
                        uploadUrl = "https://upload.qiniup.com"; // 默认华东区域
                        break;
                }
            }
            result.put("uploadUrl", uploadUrl);

            log.info("获取批量上传凭证成功，数量: {}", count);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取批量上传凭证失败", e);
            return ApiResponse.failed("获取批量上传凭证失败: " + e.getMessage());
        }
    }
}
