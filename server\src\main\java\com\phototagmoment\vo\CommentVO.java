package com.phototagmoment.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论视图对象
 */
@Data
public class CommentVO {

    /**
     * 评论ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 照片ID
     */
    private Long photoId;

    /**
     * 父评论ID
     */
    private Long parentId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 是否点赞
     */
    private Boolean isLiked;

    /**
     * 回复数
     */
    private Integer replyCount;

    /**
     * 回复列表
     */
    private List<CommentVO> replies;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
