package com.phototagmoment.handler;

import com.phototagmoment.annotation.Encrypted;
import com.phototagmoment.service.EncryptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * 加密字段处理器
 * 拦截MyBatis查询结果，对标记为@Encrypted的字段进行解密
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
})
public class EncryptedFieldHandler implements Interceptor {

    // 使用静态ThreadLocal来跟踪已处理的对象，避免每次调用都创建新的ThreadLocal实例
    private static final ThreadLocal<Set<String>> PROCESSED_OBJECTS = ThreadLocal.withInitial(HashSet::new);

    /**
     * 获取处理对象的ThreadLocal实例
     *
     * @return ThreadLocal实例
     */
    private static ThreadLocal<Set<String>> getProcessedObjectsThreadLocal() {
        return PROCESSED_OBJECTS;
    }

    @Autowired
    private EncryptionService encryptionService;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取查询结果
        Object result = invocation.proceed();
        if (result == null) {
            return null;
        }

        try {
            // 清空ThreadLocal，确保每次拦截都是全新的状态
            PROCESSED_OBJECTS.get().clear();

            // 处理查询结果
            if (result instanceof Collection) {
                Collection<?> collection = (Collection<?>) result;
                if (!collection.isEmpty()) {
                    for (Object obj : collection) {
                        try {
                            decryptObject(obj);
                        } catch (Exception e) {
                            log.error("处理集合元素时出错: {}", e.getMessage());
                            // 继续处理下一个元素
                        }
                    }
                }
            } else {
                try {
                    decryptObject(result);
                } catch (Exception e) {
                    log.error("处理对象时出错: {}", e.getMessage());
                    // 返回原始结果
                }
            }

            return result;
        } finally {
            try {
                // 确保在方法结束时清空ThreadLocal，避免内存泄漏
                PROCESSED_OBJECTS.get().clear();
                // 移除ThreadLocal，彻底清理
                PROCESSED_OBJECTS.remove();
            } catch (Exception e) {
                log.error("清理ThreadLocal时出错: {}", e.getMessage());
            }
        }
    }

    /**
     * 解密对象中的加密字段
     *
     * @param obj 对象
     */
    private void decryptObject(Object obj) {
        if (obj == null) {
            return;
        }

        // 避免处理基本类型和常见的非实体类型
        Class<?> clazz = obj.getClass();
        if (clazz.isPrimitive() || clazz == String.class ||
            clazz == Integer.class || clazz == Long.class ||
            clazz == Boolean.class || clazz == Double.class ||
            clazz == Float.class || clazz == Byte.class ||
            clazz == Short.class || clazz == Character.class ||
            clazz.isArray() || clazz.isEnum() ||
            Collection.class.isAssignableFrom(clazz) ||
            Map.class.isAssignableFrom(clazz)) {
            return;
        }

        // 检查是否已经处理过该对象，防止循环引用导致的无限递归
        String objIdentity = System.identityHashCode(obj) + ":" + clazz.getName();

        // 使用静态的ThreadLocal实例，而不是每次调用都创建新的
        ThreadLocal<java.util.Set<String>> processedObjects = getProcessedObjectsThreadLocal();
        if (processedObjects.get().contains(objIdentity)) {
            return;
        }
        processedObjects.get().add(objIdentity);

        try {
            List<Field> fields = getAllFields(clazz);

            for (Field field : fields) {
                if (field.isAnnotationPresent(Encrypted.class) && field.getType() == String.class) {
                    field.setAccessible(true);
                    try {
                        String value = (String) field.get(obj);
                        if (StringUtils.hasText(value)) {
                            // 检查是否已经是解密后的值（例如，如果值包含特殊字符或格式）
                            if (value.contains("*") || !isBase64(value)) {
                                continue;
                            }

                            try {
                                String decryptedValue = encryptionService.decryptUserData(field.getName(), value);
                                field.set(obj, decryptedValue);
                            } catch (Exception e) {
                                // 解密失败，保留原值
                                log.warn("解密字段失败，保留原值: {}.{}: {}", clazz.getSimpleName(), field.getName(), e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理加密字段失败: {}.{}", clazz.getSimpleName(), field.getName(), e);
                    }
                }
            }
        } finally {
            // 处理完成后，从集合中移除对象标识
            getProcessedObjectsThreadLocal().get().remove(objIdentity);
        }
    }

    /**
     * 简单检查字符串是否可能是Base64编码
     *
     * @param str 待检查的字符串
     * @return 是否可能是Base64编码
     */
    private boolean isBase64(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        // Base64编码的字符串长度必须是4的倍数（可能有填充）
        if (str.length() % 4 != 0 && !str.endsWith("=") && !str.endsWith("==")) {
            return false;
        }

        // Base64编码的字符串只包含A-Z, a-z, 0-9, +, /, =
        return str.matches("^[A-Za-z0-9+/=]+$");
    }

    /**
     * 获取类的所有字段，包括父类字段
     *
     * @param clazz 类
     * @return 字段列表
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                fields.add(field);
            }
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 不需要额外配置
    }
}
