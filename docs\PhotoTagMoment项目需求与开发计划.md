# PhotoTagMoment 照片社交网站项目需求与开发计划

> 更新日期：2023-05-20

## 项目概述

PhotoTagMoment是一个以照片社交为主的网站应用，用户可以在平台上分享照片，进行点赞、评论、转发等社交互动。项目采用前后端分离架构，包含用户端（PC和移动端自适应）和管理端两个前端应用，以及一个统一的后端服务。

### 技术栈

**后端技术栈：**
- Java 17
- Spring Boot 框架
- MySQL 8 数据库
- Redis 缓存
- 七牛云对象存储
- Mybatis-Plus ORM框架
- Hutool 工具类
- OpenAPI 3 API文档规范
- JWT认证
- Spring Security
- WebSocket
- 数据加密（AES/RSA）
- 短信验证码服务
- 第三方内容审核API
- 第三方实名认证API

**用户端前端技术栈：**
- Vue 3
- Vant UI
- TailwindCSS
- 响应式设计（PC端和移动端适配）
- WebSocket客户端
- 微信登录SDK

**管理端前端技术栈：**
- Vue 3
- Element Plus
- ECharts
- WebSocket客户端

## 功能模块详细规划

### 1. 用户认证与授权模块

#### 1.1 用户注册
- 手机号+短信验证码注册（主要）
- 微信一键注册
- 密码强度校验
- 用户协议与隐私政策确认
- 注册信息加密存储

#### 1.2 用户登录
- 手机号+短信验证码登录（主要）
- 微信扫码登录
- 记住登录状态功能
- 登录日志记录
- 异常登录检测与通知

#### 1.3 实名认证
- 支付宝实名认证
- 微信实名认证
- 人脸识别验证
- 认证状态管理
- 未实名用户功能限制（不能评论和发布照片）
- 认证信息加密存储

#### 1.4 用户授权
- 基于JWT的认证机制
- Token刷新机制
- 权限控制
- 角色管理
- 访问控制列表（ACL）
- 数据传输加密

#### 1.5 账号安全
- 密码重置
- 账号找回
- 登录异常检测
- 敏感操作二次验证
- 个人数据加密存储

### 2. 用户个人中心模块

#### 2.1 个人资料
- 基本信息管理（昵称、头像、个性签名）
- 联系方式管理（手机、邮箱）
- 个人主页自定义
- 隐私设置

#### 2.2 账号设置
- 密码修改
- 绑定/解绑第三方账号
- 消息通知设置
- 黑名单管理

#### 2.3 内容管理
- 已发布照片管理
- 草稿箱
- 收藏夹
- 浏览历史

### 3. 照片发布与管理模块

#### 3.1 照片上传
- 多图上传（支持拖拽上传）
- 图片编辑（裁剪、滤镜、调色）
- 图片压缩与格式转换
- 水印添加
- 照片加密存储到七牛云
- 照片内容审核（色情、广告、恶心、违禁等）
- 实名认证用户才能上传照片

#### 3.2 照片信息
- 标题与描述
- 正文支持@用户功能（自动通知被@的用户）
- 正文支持#话题标签功能（可点击跳转到话题页）
- 地理位置标记
- 标签添加
- 人物标记

#### 3.3 发布设置
- 可见性设置（公开、好友可见、私密）
- 评论权限设置
- 定时发布
- 转载权限设置
- 保存草稿功能

#### 3.4 照片管理
- 照片编辑
- 照片删除
- 照片归档
- 照片分类与相册管理

### 4. 社交互动模块

#### 4.1 关注系统
- 关注/取消关注用户
- 粉丝管理
- 好友关系
- 关注分组

#### 4.2 点赞系统
- 照片点赞
- 评论点赞
- 点赞记录
- 点赞通知

#### 4.3 评论系统
- 发表评论（仅实名认证用户）
- 回复评论（仅实名认证用户）
- 评论管理（删除、举报）
- 评论通知
- 评论内容审核

#### 4.4 转发系统
- 照片转发
- 添加转发评论
- 转发记录
- 转发通知

#### 4.5 私信系统
- 发送私信
- 私信会话管理
- 私信通知
- 私信安全过滤

### 5. 内容发现模块

#### 5.1 首页推荐
- 个性化推荐算法
- 热门内容展示
- 关注用户内容流
- 新内容提醒

#### 5.2 搜索功能
- 照片搜索
- 用户搜索
- 标签搜索
- 地理位置搜索

#### 5.3 探索页面
- 分类浏览
- 热门标签
- 热门地点
- 活动与挑战

#### 5.4 内容推送
- 兴趣标签订阅
- 定制化内容推送
- 活动通知
- 系统公告

### 6. 通知与消息模块

#### 6.1 系统通知
- 账号相关通知
- 系统公告
- 活动通知
- 安全提醒
- 实时推送（WebSocket）

#### 6.2 互动通知
- 新粉丝通知
- 点赞通知
- 评论通知
- 转发通知
- @用户通知
- 实时推送（WebSocket）

#### 6.3 消息中心
- 消息汇总
- 消息状态管理
- 消息过滤
- 消息设置
- 消息加密存储
- 实时消息推送

### 7. 管理后台模块

#### 7.1 管理员系统
- 独立的管理员账户体系
- 管理员权限分级
- 管理员操作日志
- 管理员安全策略

#### 7.2 用户管理
- 用户列表与搜索
- 用户详情查看
- 用户状态管理（封禁/解封）
- 实名认证审核（支付宝/微信认证结果查看）

#### 7.3 内容管理
- 照片审核（接入第三方内容审核API）
- 评论审核
- 违规内容处理
- 内容推荐管理

#### 7.4 系统设置
- 基础参数配置
- 七牛云存储服务配置
- 第三方服务集成（短信、微信、支付宝等）
- 安全策略设置
- 加密策略配置

#### 7.5 数据统计
- 用户增长统计
- 内容发布统计
- 互动数据分析
- 热门内容分析
- 实时数据监控（WebSocket）

#### 7.6 运营管理
- 活动创建与管理
- 系统公告发布
- 推荐内容管理
- 用户反馈处理

## 开发计划

### 第一阶段：基础架构搭建（4周）✅

#### 周1-2：后端基础架构 ✅
- ✅ 搭建Spring Boot项目框架
- ✅ 配置MySQL、Redis环境
- ✅ 实现基础用户认证功能
- ✅ 设计数据库结构

#### 周3-4：前端基础架构 ✅
- ✅ 搭建Vue3用户端项目
- ✅ 搭建Vue3管理端项目
- ✅ 实现响应式布局基础
- ✅ 设计并实现基础UI组件

### 第二阶段：核心功能开发（8周）✅

#### 周5-6：用户认证与个人中心 ✅
- ✅ 完成用户注册、登录功能
- ✅ 实现JWT认证机制
- ✅ 开发个人资料管理功能
- ✅ 实现实名认证流程

#### 周7-8：照片上传与管理 ✅
- ✅ 实现本地存储和S3对象存储集成
- ✅ 开发照片上传与处理功能
- ✅ 实现照片信息管理
- ✅ 开发照片编辑功能

#### 周9-10：社交互动功能 ✅
- ✅ 实现关注系统
- ✅ 开发点赞、评论功能
- ✅ 实现照片收藏功能
- ⏳ 开发私信系统

#### 周11-12：内容发现与推送 ⏳
- ⏳ 实现首页推荐算法
- ✅ 开发搜索功能
- ⏳ 实现探索页面
- ✅ 开发通知系统

### 第三阶段：管理后台开发（4周）⏳

#### 周13-14：基础管理功能 ✅
- ✅ 实现管理员登录功能
- ✅ 实现用户管理功能
- ✅ 开发独立管理员账户体系
- ✅ 开发内容审核功能
- ✅ 实现系统设置功能
- ✅ 实现首页推荐算法
- ⏳ 开发数据统计功能

#### 周15-16：高级管理功能 ⏳
- ⏳ 实现运营管理功能
- ⏳ 开发报表与分析功能
- ⏳ 实现权限管理系统
- ⏳ 完善管理员操作日志
- ⏳ 实现WebSocket实时数据监控

### 第四阶段：新功能实现与优化（6周）

#### 周17-18：WebSocket与实时通信 ✅
- ✅ WebSocket服务端实现
- ✅ WebSocket客户端集成
- ✅ 实时消息推送
- ⏳ 实时数据监控

#### 周19-20：数据加密与安全 ✅
- ✅ 用户数据加密存储
- ✅ 照片加密存储到七牛云
- ✅ 敏感信息传输加密
- ⏳ 安全策略优化

#### 周21-22：第三方服务集成
- ✅ 短信验证码服务集成
- ✅ 微信登录SDK集成
- ✅ 第三方内容审核API集成框架
- ✅ 第三方实名认证集成

### 第五阶段：优化与测试（4周）

#### 周23-24：性能优化
- 数据库优化
- 缓存策略优化
- 前端性能优化
- API响应优化

#### 周25-26：测试与修复
- 功能测试
- 性能测试
- 安全测试
- Bug修复

### 第六阶段：部署与上线（2周）

#### 周27-28：部署与上线准备
- 环境部署
- 数据迁移
- 上线前测试
- 监控系统配置
- 安全审计

## 技术架构图

```
+------------------+    +------------------+
|   用户端 (Vue3)   |    |  管理端 (Vue3)   |
|  PC/移动端自适应  |    |   Element-UI    |
|  WebSocket客户端  |    |  WebSocket客户端 |
+--------+---------+    +--------+---------+
         |                       |
         v                       v
+------------------------------------------+
|              API网关层                    |
+------------------------------------------+
                   |
                   v
+------------------------------------------+
|           Spring Boot后端                 |
|                                          |
|  +-------------+    +----------------+   |
|  | 用户认证模块 |    | 照片管理模块    |   |
|  | (手机/微信)  |    | (七牛云加密存储) |   |
|  +-------------+    +----------------+   |
|                                          |
|  +-------------+    +----------------+   |
|  | 社交互动模块 |    | 内容发现模块    |   |
|  | (实名认证)   |    | (内容审核)      |   |
|  +-------------+    +----------------+   |
|                                          |
|  +-------------+    +----------------+   |
|  | 通知消息模块 |    | 管理后台模块    |   |
|  | (WebSocket)  |    | (独立账户体系)  |   |
|  +-------------+    +----------------+   |
+------------------------------------------+
                   |
                   v
+------------------------------------------+
|              数据存储层                   |
|                                          |
|  +--------+  +-----+  +----------------+ |
|  | MySQL8 |  |Redis|  |七牛云对象存储   | |
|  | (加密)  |  |     |  |(加密)          | |
|  +--------+  +-----+  +----------------+ |
+------------------------------------------+
                   |
                   v
+------------------------------------------+
|            第三方服务集成                 |
|                                          |
|  +--------+  +-----+  +----------------+ |
|  | 短信验证 |  |微信 |  |内容审核API    | |
|  | 服务     |  |登录 |  |               | |
|  +--------+  +-----+  +----------------+ |
|  +--------+  +-----+                     |
|  | 支付宝  |  |微信 |                    |
|  | 实名认证 |  |实名 |                    |
|  +--------+  +-----+                     |
+------------------------------------------+
```

## 当前进度与下一步行动计划

### 已完成
1. ✅ 确认需求文档，调整功能优先级
2. ✅ 设计数据库表结构（使用ptm前缀）
3. ✅ 搭建基础开发环境
4. ✅ 搭建Spring Boot后端基础架构
   - ✅ 配置MySQL、Redis环境
   - ✅ 设计数据库结构
   - ✅ 创建基础配置类和工具类
5. ✅ 搭建Vue3前端基础架构
   - ✅ 用户端项目结构和基础组件
   - ✅ 管理端项目结构和基础组件
   - ✅ 响应式布局基础
6. ✅ 实现用户认证与授权功能
   - ✅ 用户注册、登录功能
   - ✅ JWT认证机制
   - ✅ 权限控制
7. ✅ 开发个人中心功能
   - ✅ 个人资料管理
   - ✅ 账号设置
8. ✅ 开发照片上传与管理功能
   - ✅ 本地存储和S3对象存储集成
   - ✅ 照片上传与处理
   - ✅ 照片信息管理
   - ✅ 照片编辑与删除
   - ✅ 多图上传（支持拖拽上传）
   - ✅ 正文支持@用户功能
   - ✅ 正文支持#话题标签功能
   - ✅ 保存草稿功能
   - ✅ 前端直接上传到七牛云存储
9. ✅ 开发社交互动功能
   - ✅ 关注系统
   - ✅ 点赞功能
   - ✅ 评论功能
   - ✅ 收藏功能
10. ✅ 实现管理员登录和用户管理功能
11. ✅ 开发搜索功能
    - ✅ 照片搜索
    - ✅ 用户搜索
    - ✅ 标签搜索
12. ✅ 开发通知系统
    - ✅ 系统通知
    - ✅ 互动通知
    - ✅ @用户通知
    - ✅ 消息中心
13. ✅ 实现WebSocket实时通信
    - ✅ 服务端WebSocket配置
    - ✅ 客户端WebSocket集成
    - ✅ 实时消息推送
14. ✅ 实现数据加密存储
    - ✅ 用户数据加密
    - ✅ 照片加密存储到七牛云
    - ✅ 敏感信息传输加密
15. ✅ 实现手机号和微信登录
    - ✅ 短信验证码服务集成
    - ✅ 微信登录SDK集成
    - ✅ 登录流程优化
16. ✅ 实现内容审核功能
    - ✅ 内容审核服务接口设计
    - ✅ 本地内容审核服务实现（开发测试用）
    - ✅ 第三方内容审核API集成框架
    - ✅ 上传图片自动审核
    - ✅ 文本内容自动审核
    - ✅ 违规内容处理流程
    - ✅ 管理员手动审核功能
17. ✅ 实现第三方实名认证
    - ✅ 实名认证接口设计
    - ✅ 实名认证服务实现
    - ✅ 支付宝实名认证集成框架
    - ✅ 微信实名认证集成框架
    - ✅ 人脸识别实名认证框架
    - ✅ 未实名用户功能限制
18. ✅ 完善管理后台功能
    - ✅ 独立管理员账户体系
    - ✅ 管理员角色权限管理
    - ✅ 管理员操作日志
19. ✅ 实现系统设置功能
    - ✅ 系统参数配置
    - ✅ 敏感词管理
    - ✅ 数据字典管理
20. ✅ 实现首页推荐算法
    - ✅ 基于用户兴趣的推荐
    - ✅ 热门内容推荐
    - ✅ 关注用户内容流
    - ✅ 用户行为记录与分析
21. ✅ 优化用户界面
    - ✅ PC端搜索界面优化（弹出式搜索框）
    - ✅ 图文发布界面优化

### 下一步计划
1. ⏳ 开发私信系统
   - ⏳ 私信发送与接收
   - ⏳ 私信会话管理
   - ⏳ 私信安全过滤
2. ⏳ 实现探索页面
   - ⏳ 分类浏览
   - ⏳ 热门标签展示
   - ⏳ 热门地点展示
3. ⏳ 完善话题标签功能
   - ⏳ 话题详情页面
   - ⏳ 话题关注功能
   - ⏳ 话题推荐算法
4. ⏳ 优化@用户功能
   - ⏳ @用户消息聚合
   - ⏳ @用户权限控制
   - ⏳ @用户历史记录
