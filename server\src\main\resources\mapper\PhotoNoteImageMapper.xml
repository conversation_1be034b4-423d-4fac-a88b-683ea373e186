<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.PhotoNoteImageMapper">

    <!-- 批量插入照片笔记图片关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ptm_photo_note_image (note_id, photo_id, sort_order, created_at)
        VALUES
        <foreach collection="images" item="item" separator=",">
            (#{item.noteId}, #{item.photoId}, #{item.sortOrder}, #{item.createdAt})
        </foreach>
    </insert>

    <!-- 删除照片笔记的所有图片关联 -->
    <delete id="deleteByNoteId">
        DELETE FROM ptm_photo_note_image WHERE note_id = #{noteId}
    </delete>

</mapper>
