package com.phototagmoment.dto;

import lombok.Data;

/**
 * 微信登录DTO
 */
@Data
public class WechatLoginDTO {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 用户unionId
     */
    private String unionId;

    /**
     * 会话密钥
     */
    private String sessionKey;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;

    /**
     * 用户信息
     */
    private WechatUserInfoDTO userInfo;

    /**
     * 创建成功结果
     *
     * @param openId      用户openId
     * @param unionId     用户unionId
     * @param sessionKey  会话密钥
     * @param accessToken 访问令牌
     * @param expiresIn   过期时间
     * @return 登录结果
     */
    public static WechatLoginDTO success(String openId, String unionId, String sessionKey, String accessToken, Integer expiresIn) {
        WechatLoginDTO result = new WechatLoginDTO();
        result.setSuccess(true);
        result.setOpenId(openId);
        result.setUnionId(unionId);
        result.setSessionKey(sessionKey);
        result.setAccessToken(accessToken);
        result.setExpiresIn(expiresIn);
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param errorMsg 错误信息
     * @return 登录结果
     */
    public static WechatLoginDTO fail(String errorMsg) {
        WechatLoginDTO result = new WechatLoginDTO();
        result.setSuccess(false);
        result.setErrorMsg(errorMsg);
        return result;
    }
}
