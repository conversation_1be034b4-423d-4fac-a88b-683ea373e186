package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.service.EncryptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密控制器
 */
@Slf4j
@RestController
@RequestMapping("/encryption")
@Tag(name = "加密接口", description = "加密相关接口")
public class EncryptionController {

    @Autowired
    private EncryptionService encryptionService;

    /**
     * 获取加密公钥
     */
    @GetMapping("/public-key")
    @Operation(summary = "获取加密公钥", description = "获取用于前端加密敏感数据的RSA公钥")
    public ApiResponse<Map<String, String>> getPublicKey() {
        Map<String, String> result = new HashMap<>();
        result.put("publicKey", encryptionService.getRsaPublicKey());
        return ApiResponse.success(result);
    }

    /**
     * 加密测试
     */
    @PostMapping("/encrypt")
    @Operation(summary = "加密测试", description = "使用RSA公钥加密数据（仅用于测试）")
    public ApiResponse<Map<String, String>> encrypt(
            @Parameter(description = "明文数据") @RequestParam String data) {
        String encrypted = encryptionService.encryptWithPublicKey(data);
        Map<String, String> result = new HashMap<>();
        result.put("original", data);
        result.put("encrypted", encrypted);
        return ApiResponse.success(result);
    }

    /**
     * 解密测试
     */
    @PostMapping("/decrypt")
    @Operation(summary = "解密测试", description = "使用RSA私钥解密数据（仅用于测试）")
    public ApiResponse<Map<String, String>> decrypt(
            @Parameter(description = "加密数据") @RequestParam String data) {
        String decrypted = encryptionService.decryptWithPrivateKey(data);
        Map<String, String> result = new HashMap<>();
        result.put("encrypted", data);
        result.put("decrypted", decrypted);
        return ApiResponse.success(result);
    }
}
