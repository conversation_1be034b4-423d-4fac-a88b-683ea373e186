package com.phototagmoment.service;

import com.phototagmoment.vo.SensitiveWordImportResultVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 敏感词导入服务接口
 */
public interface SensitiveWordImportService {

    /**
     * 从目录导入敏感词
     *
     * @return 导入结果
     */
    SensitiveWordImportResultVO importSensitiveWords();

    /**
     * 从目录导入敏感词
     *
     * @param directoryPath 目录路径
     * @return 导入结果
     */
    SensitiveWordImportResultVO importSensitiveWords(String directoryPath);

    /**
     * 从文件导入敏感词
     *
     * @param file 文件
     * @return 导入结果
     */
    SensitiveWordImportResultVO importFromFile(MultipartFile file);
}
