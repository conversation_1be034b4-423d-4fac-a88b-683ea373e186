declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<any, any, any>
  export default component
}

// 扩展 Axios 类型
declare module 'axios' {
  export interface AxiosInstance {
    (config: AxiosRequestConfig): Promise<AxiosResponse>
    (url: string, config?: AxiosRequestConfig): Promise<AxiosResponse>
    create: (config?: any) => AxiosInstance
    post: (url: string, data?: any, config?: any) => Promise<AxiosResponse>
    get: (url: string, config?: any) => Promise<AxiosResponse>
    put: (url: string, data?: any, config?: any) => Promise<AxiosResponse>
    delete: (url: string, config?: any) => Promise<AxiosResponse>
    interceptors: {
      request: {
        use: (onFulfilled?: any, onRejected?: any) => number
      }
      response: {
        use: (onFulfilled?: any, onRejected?: any) => number
      }
    }
  }

  export interface AxiosResponse<T = any> {
    data: T
    status: number
    statusText: string
    headers: any
    config: any
    code?: number
    message?: string
  }

  export interface AxiosRequestConfig {
    url?: string
    method?: string
    baseURL?: string
    headers?: any
    params?: any
    data?: any
    timeout?: number
    withCredentials?: boolean
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
    validateStatus?: (status: number) => boolean
  }

  export interface InternalAxiosRequestConfig extends AxiosRequestConfig {
    headers: any
  }

  export interface AxiosProgressEvent {
    loaded: number
    total?: number
    progress?: number
    bytes: number
    estimated?: number
    rate?: number
    upload?: boolean
  }

  const axios: AxiosInstance
  export default axios
}

// 扩展 Vant 组件类型
declare module 'vant' {
  export interface ButtonProps {
    type?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'text'
  }

  // 导出所有 Vant 组件和函数
  export const Button: any
  export const NavBar: any
  export const Tabbar: any
  export const TabbarItem: any
  export const Icon: any
  export const Image: any
  export const Cell: any
  export const CellGroup: any
  export const Form: any
  export const Field: any
  export const Toast: any
  export const Dialog: any
  export const Uploader: any
  export const Swipe: any
  export const SwipeItem: any
  export const Lazyload: any
  export const PullRefresh: any
  export const List: any
  export const Tab: any
  export const Tabs: any
  export const Sticky: any
  export const Loading: any
  export const Empty: any
  export const Skeleton: any
  export const Tag: any
  export const Divider: any
  export const Grid: any
  export const GridItem: any
  export const Search: any
  export const ActionSheet: any
  export const Popup: any
  export const DropdownMenu: any
  export const DropdownItem: any
  export const Badge: any
  export const Picker: any
  export const DatePicker: any
  export const Radio: any
  export const RadioGroup: any
  export const Checkbox: any
  export const CheckboxGroup: any
  export const ActionBar: any
  export const ActionBarButton: any
  export const ActionBarIcon: any

  // 导出 Vant 函数
  export function showToast(options: any): any
  export function showSuccessToast(message: string): any
  export function showFailToast(message: string): any
  export function showLoadingToast(options: string | { message: string; forbidClick?: boolean; duration?: number }): any
  export function closeToast(): any
  export function showDialog(options: any): any
  export function showNotify(options: any): any
  export function showImagePreview(options: any): any
}
