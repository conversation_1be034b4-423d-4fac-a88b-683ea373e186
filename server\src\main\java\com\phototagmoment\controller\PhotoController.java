package com.phototagmoment.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.*;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.CommentService;
import com.phototagmoment.service.MentionService;
import com.phototagmoment.service.PhotoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 照片控制器
 */
@Slf4j
@RestController
@RequestMapping("/photo")
@Tag(name = "照片接口", description = "照片上传、查询、修改、删除接口")
public class PhotoController {

    @Autowired
    private PhotoService photoService;

    @Autowired
    private MentionService mentionService;

    @Autowired
    private CommentService commentService;

    @PostMapping("/upload")
    @Operation(summary = "上传照片", description = "上传单张照片")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Long> uploadPhoto(
            @Parameter(description = "照片文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "照片信息") @Validated @RequestPart("data") String dataJson) {
        Long userId = SecurityUtil.getCurrentUserId();

        // 解析JSON字符串
        PhotoUploadWithMentionsDTO uploadWithMentionsDTO = JSON.parseObject(dataJson, PhotoUploadWithMentionsDTO.class);

        // 转换为PhotoUploadDTO
        PhotoUploadDTO photoUploadDTO = new PhotoUploadDTO();
        BeanUtils.copyProperties(uploadWithMentionsDTO, photoUploadDTO);

        // 上传照片
        Long photoId = photoService.uploadPhoto(file, photoUploadDTO, userId);

        // 处理@用户
        if (uploadWithMentionsDTO.getMentions() != null && !uploadWithMentionsDTO.getMentions().isEmpty()) {
            mentionService.processPhotoMentions(photoId, uploadWithMentionsDTO.getMentions(), userId);
        }

        return ApiResponse.success(photoId);
    }

    /**
     * 保存照片信息（不包括文件上传）
     * 用于前端直接上传到七牛云后，保存照片信息
     */
    @PostMapping("/save-info")
    @Operation(summary = "保存照片信息", description = "保存照片信息（不包括文件上传），用于前端直接上传到七牛云后，保存照片信息")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Long> savePhotoInfo(
            @Parameter(description = "照片信息") @Validated @RequestBody PhotoUploadWithMentionsDTO uploadWithMentionsDTO) {
        // 直接使用新的savePhotoInfo方法，支持照片分组
        Long photoId = photoService.savePhotoInfo(uploadWithMentionsDTO);

        // 处理@用户
        if (uploadWithMentionsDTO.getMentions() != null && !uploadWithMentionsDTO.getMentions().isEmpty()) {
            Long userId = SecurityUtil.getCurrentUserId();
            mentionService.processPhotoMentions(photoId, uploadWithMentionsDTO.getMentions(), userId);
        }

        return ApiResponse.success(photoId);
    }

    @PostMapping("/batch-upload")
    @Operation(summary = "批量上传照片", description = "批量上传多张照片")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<List<Long>> batchUploadPhotos(
            @Parameter(description = "照片文件列表") @RequestParam("files") List<MultipartFile> files,
            @Parameter(description = "照片信息列表") @RequestPart("data") String dataJson) {
        Long userId = SecurityUtil.getCurrentUserId();

        // 解析JSON字符串
        List<PhotoUploadWithMentionsDTO> uploadWithMentionsDTOs = JSON.parseArray(dataJson, PhotoUploadWithMentionsDTO.class);

        // 转换为PhotoUploadDTO列表
        List<PhotoUploadDTO> photoUploadDTOs = new ArrayList<>();
        for (PhotoUploadWithMentionsDTO uploadWithMentionsDTO : uploadWithMentionsDTOs) {
            PhotoUploadDTO photoUploadDTO = new PhotoUploadDTO();
            BeanUtils.copyProperties(uploadWithMentionsDTO, photoUploadDTO);
            photoUploadDTOs.add(photoUploadDTO);
        }

        // 上传照片
        List<Long> photoIds = photoService.batchUploadPhotos(files, photoUploadDTOs, userId);

        // 处理@用户
        for (int i = 0; i < photoIds.size(); i++) {
            PhotoUploadWithMentionsDTO uploadWithMentionsDTO = uploadWithMentionsDTOs.get(i);
            if (uploadWithMentionsDTO.getMentions() != null && !uploadWithMentionsDTO.getMentions().isEmpty()) {
                mentionService.processPhotoMentions(photoIds.get(i), uploadWithMentionsDTO.getMentions(), userId);
            }
        }

        return ApiResponse.success(photoIds);
    }

    @GetMapping("/detail/{photoId}")
    @Operation(summary = "获取照片详情", description = "根据照片ID获取照片详情，包括同组照片信息")
    public ApiResponse<PhotoDTO> getPhotoDetail(
            @Parameter(description = "照片ID") @PathVariable Long photoId,
            @Parameter(description = "是否包含同组照片") @RequestParam(required = false, defaultValue = "true") Boolean includeGroupPhotos) {
        Long userId = SecurityUtil.getCurrentUserId();
        PhotoDTO photoDTO = photoService.getPhotoDetail(photoId, userId);

        // 如果需要包含同组照片信息，且照片有分组ID
        if (includeGroupPhotos && photoDTO != null && photoDTO.getGroupId() != null && !photoDTO.getGroupId().isEmpty()) {
            // 获取同组照片列表
            List<PhotoDTO> groupPhotos = photoService.getPhotosByGroupId(photoDTO.getGroupId(), userId);

            // 设置同组照片信息
            photoDTO.setGroupPhotos(groupPhotos);
            photoDTO.setGroupPhotoCount(groupPhotos.size());
            photoDTO.setIsGrouped(groupPhotos.size() > 1);

            // 设置同组照片ID列表
            List<Long> groupPhotoIds = new ArrayList<>();
            for (PhotoDTO photo : groupPhotos) {
                groupPhotoIds.add(photo.getId());
            }
            photoDTO.setGroupPhotoIds(groupPhotoIds);
        }

        return ApiResponse.success(photoDTO);
    }

    /**
     * 获取照片评论列表 - 转发到 CommentController
     * 此接口允许未登录用户访问
     */
    @GetMapping("/comments")
    @Operation(summary = "获取照片评论列表", description = "分页获取照片评论列表，允许未登录用户访问")
    public ApiResponse<IPage<CommentDTO>> getPhotoComments(
            @Parameter(description = "照片ID") @RequestParam Long photoId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        // 获取当前用户ID，可能为null（未登录用户）
        Long userId = null;
        try {
            // 尝试获取当前登录用户ID，如果未登录则捕获异常
            userId = SecurityUtil.getCurrentUserId();
        } catch (Exception e) {
            // 未登录用户，userId保持为null
            log.debug("未登录用户访问照片评论");
        }

        // 调用 CommentService 获取评论列表
        IPage<CommentDTO> commentPage = commentService.getPhotoComments(photoId, page, size, userId);
        return ApiResponse.success(commentPage);
    }

    @GetMapping("/list")
    @Operation(summary = "获取照片列表", description = "分页获取照片列表")
    public ApiResponse<IPage<PhotoDTO>> getPhotoList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        IPage<PhotoDTO> photoPage = photoService.getPhotoPage(page, size, userId, currentUserId);
        return ApiResponse.success(photoPage);
    }

    @PutMapping("/{photoId}")
    @Operation(summary = "更新照片信息", description = "更新照片信息")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> updatePhoto(
            @Parameter(description = "照片ID") @PathVariable Long photoId,
            @Parameter(description = "照片信息") @Validated @RequestBody PhotoUploadDTO photoUploadDTO) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.updatePhoto(photoId, photoUploadDTO, userId);
        return ApiResponse.success(result);
    }

    @DeleteMapping("/{photoId}")
    @Operation(summary = "删除照片", description = "删除照片")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> deletePhoto(
            @Parameter(description = "照片ID") @PathVariable Long photoId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.deletePhoto(photoId, userId);
        return ApiResponse.success(result);
    }

    @PostMapping("/like/{photoId}")
    @Operation(summary = "点赞照片", description = "点赞照片")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> likePhoto(
            @Parameter(description = "照片ID") @PathVariable Long photoId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.likePhoto(photoId, userId);
        return ApiResponse.success(result);
    }

    @PostMapping("/unlike/{photoId}")
    @Operation(summary = "取消点赞照片", description = "取消点赞照片")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> unlikePhoto(
            @Parameter(description = "照片ID") @PathVariable Long photoId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.unlikePhoto(photoId, userId);
        return ApiResponse.success(result);
    }

    @PostMapping("/collect/{photoId}")
    @Operation(summary = "收藏照片", description = "收藏照片")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> collectPhoto(
            @Parameter(description = "照片ID") @PathVariable Long photoId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.collectPhoto(photoId, userId);
        return ApiResponse.success(result);
    }

    @PostMapping("/uncollect/{photoId}")
    @Operation(summary = "取消收藏照片", description = "取消收藏照片")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> uncollectPhoto(
            @Parameter(description = "照片ID") @PathVariable Long photoId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.uncollectPhoto(photoId, userId);
        return ApiResponse.success(result);
    }

    @GetMapping("/liked/{photoId}")
    @Operation(summary = "检查是否已点赞照片", description = "检查当前用户是否已点赞照片")
    public ApiResponse<Boolean> checkLiked(
            @Parameter(description = "照片ID") @PathVariable Long photoId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.checkLiked(photoId, userId);
        return ApiResponse.success(result);
    }

    @GetMapping("/collected/{photoId}")
    @Operation(summary = "检查是否已收藏照片", description = "检查当前用户是否已收藏照片")
    public ApiResponse<Boolean> checkCollected(
            @Parameter(description = "照片ID") @PathVariable Long photoId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoService.checkCollected(photoId, userId);
        return ApiResponse.success(result);
    }

    @GetMapping("/collections")
    @Operation(summary = "获取用户收藏的照片列表", description = "分页获取当前用户收藏的照片列表")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<IPage<PhotoDTO>> getUserCollections(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        Long userId = SecurityUtil.getCurrentUserId();
        IPage<PhotoDTO> photoPage = photoService.getUserCollections(page, size, userId);
        return ApiResponse.success(photoPage);
    }

    @GetMapping("/group/{groupId}")
    @Operation(summary = "获取照片分组", description = "根据分组ID获取照片列表")
    public ApiResponse<List<PhotoDTO>> getPhotosByGroupId(
            @Parameter(description = "分组ID") @PathVariable String groupId) {
        Long userId = SecurityUtil.getCurrentUserId();
        List<PhotoDTO> photos = photoService.getPhotosByGroupId(groupId, userId);
        return ApiResponse.success(photos);
    }
}
