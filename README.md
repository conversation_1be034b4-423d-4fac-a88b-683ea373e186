# PhotoTagMoment 照片社交网站

PhotoTagMoment是一个以照片社交为主的网站应用，用户可以在平台上分享照片，进行点赞、评论、转发等社交互动。

## 项目结构

```
PhotoTagMoment/
├── backend/               # 后端项目
│   ├── src/               # 源代码
│   │   ├── main/
│   │   │   ├── java/      # Java代码
│   │   │   └── resources/ # 资源文件
│   │   └── test/          # 测试代码
│   └── pom.xml            # Maven配置文件
├── frontend-user/         # 用户端前端项目
│   ├── public/            # 静态资源
│   ├── src/               # 源代码
│   └── package.json       # NPM配置文件
├── frontend-admin/        # 管理端前端项目
│   ├── public/            # 静态资源
│   ├── src/               # 源代码
│   └── package.json       # NPM配置文件
└── README.md              # 项目说明文档
```

## 技术栈

### 后端技术栈
- Java 17
- Spring Boot 框架
- MySQL 8 数据库
- Redis 缓存
- S3对象存储（支持七牛云、腾讯云、阿里云）
- Mybatis-Plus ORM框架
- Hutool 工具类
- OpenAPI 3 API文档规范

### 用户端前端技术栈
- Vue 3
- Vant UI
- 响应式设计（PC端和移动端适配）
- TailwindCSS

### 管理端前端技术栈
- Vue 3
- Element-Plus
- ECharts

## 开发环境搭建

### 后端开发环境

1. 安装JDK 17
2. 安装Maven
3. 安装MySQL 8
4. 安装Redis

#### 数据库初始化

```bash
# 创建数据库和表结构
mysql -u root -p < backend/src/main/resources/db/schema.sql
```

#### 启动后端服务

```bash
cd backend
mvn spring-boot:run
```

### 前端开发环境

1. 安装Node.js (推荐v16+)
2. 安装pnpm (可选，也可使用npm或yarn)

#### 启动用户端前端服务

```bash
cd frontend-user
pnpm install
pnpm dev
```

#### 启动管理端前端服务

```bash
cd frontend-admin
pnpm install
pnpm dev
```

## 部署说明

### 后端部署

1. 打包后端项目

```bash
cd backend
mvn clean package -DskipTests
```

2. 运行JAR包

```bash
java -jar target/phototagmoment-0.0.1-SNAPSHOT.jar
```

### 前端部署

1. 构建用户端前端项目

```bash
cd frontend-user
pnpm build
```

2. 构建管理端前端项目

```bash
cd frontend-admin
pnpm build
```

3. 将构建产物部署到Web服务器

## 项目功能模块

- 用户认证与授权模块
- 用户个人中心模块
- 照片发布与管理模块
- 社交互动模块
- 内容发现模块
- 通知与消息模块
- 管理后台模块

详细功能请参考项目需求文档。

## 账号信息

### 用户端测试账号
- 用户名：user
- 密码：123456

### 管理端测试账号
- 用户名：admin
- 密码：123456

## 注意事项

- 所有文件编码统一使用UTF-8
- 数据库表名前缀统一使用ptm
- 处理大型操作时，应将其分解为较小的步骤，以避免输入大小限制
- 用户前端使用响应式设计，确保在移动设备上有良好的体验
- 代码提交前请确保通过代码规范检查

## 开发进度

当前已完成：
- 用户认证与授权模块
- 照片上传与管理功能
- 社交互动功能（关注、点赞、评论、收藏）
- 响应式布局和移动端适配

正在开发：
- 搜索功能
- 消息通知系统

详细开发计划请参考 [docs/development-plan.md](docs/development-plan.md)。
