package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.entity.DictData;
import com.phototagmoment.service.DictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户端数据字典控制器
 */
@Slf4j
@RestController
@RequestMapping("/dict")
@Tag(name = "数据字典接口", description = "用户端数据字典接口")
// 指定一个不同的 Bean 名称，避免与管理端的 DictController 冲突
@org.springframework.context.annotation.Primary
@org.springframework.stereotype.Component("userDictController")
public class DictController {

    @Autowired
    private DictService dictService;

    /**
     * 根据字典类型获取字典数据列表
     */
    @GetMapping("/data/code/{dictType}")
    @Operation(summary = "根据字典类型获取字典数据列表", description = "根据字典类型获取字典数据列表")
    public ApiResponse<List<DictData>> getDataByType(@Parameter(description = "字典类型") @PathVariable String dictType) {
        List<DictData> data = dictService.getDictDataByType(dictType);
        return ApiResponse.success(data);
    }

    /**
     * 根据字典类型获取字典数据Map
     */
    @GetMapping("/data/map/{dictType}")
    @Operation(summary = "根据字典类型获取字典数据Map", description = "根据字典类型获取字典数据Map，key为dictValue，value为dictLabel")
    public ApiResponse<Map<String, String>> getDataMapByType(@Parameter(description = "字典类型") @PathVariable String dictType) {
        Map<String, String> dataMap = dictService.getDictDataMapByType(dictType);
        return ApiResponse.success(dataMap);
    }
}
