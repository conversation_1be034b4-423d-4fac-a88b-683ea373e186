-- 检查ptm_system_config表结构
SET @column_exists_status = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_system_config' AND column_name = 'status');

-- 根据表结构选择不同的SQL语句
SET @insert_sql_with_status = 'INSERT IGNORE INTO `ptm_system_config` (`config_key`, `config_value`, `config_name`, `config_type`, `remark`, `is_system`, `status`) VALUES
-- 内容审核基本配置
(''content-moderation.enabled'', ''true'', ''启用内容审核'', ''boolean'', ''是否启用内容审核功能'', 1, 1),
(''content-moderation.image.provider'', ''baidu'', ''图像审核服务提供商'', ''string'', ''图像审核服务提供商：local, baidu'', 1, 1),
(''content-moderation.text.provider'', ''local'', ''文本审核服务提供商'', ''string'', ''文本审核服务提供商：local'', 1, 1),
(''content-moderation.auto-approve'', ''false'', ''自动通过审核'', ''boolean'', ''是否自动通过审核（用于测试）'', 1, 1),
(''content-moderation.mode'', ''mixed'', ''审核模式'', ''string'', ''审核模式：auto-自动审核, manual-人工审核, mixed-混合审核'', 1, 1),
(''content-moderation.sensitivity'', ''80'', ''自动审核敏感度'', ''number'', ''自动审核敏感度：0-100，值越大越严格'', 1, 1),
(''content-moderation.contact-info.filter'', ''true'', ''联系方式过滤'', ''boolean'', ''是否过滤联系方式'', 1, 1),

-- 百度内容审核配置
(''content-moderation.baidu.app-id'', '''', ''百度AppID'', ''string'', ''百度内容审核AppID'', 1, 1),
(''content-moderation.baidu.api-key'', '''', ''百度ApiKey'', ''string'', ''百度内容审核ApiKey'', 1, 1),
(''content-moderation.baidu.secret-key'', '''', ''百度SecretKey'', ''string'', ''百度内容审核SecretKey'', 1, 1),

-- 存储配置
(''storage.type'', ''qiniu'', ''存储类型'', ''string'', ''存储类型：local, qiniu, tencent, aliyun'', 1, 1),
(''storage.local.path'', ''/data/upload'', ''本地存储路径'', ''string'', ''本地存储路径'', 1, 1),
(''storage.local.domain'', ''http://localhost:8081/api/file'', ''本地存储域名'', ''string'', ''本地存储域名'', 1, 1),
(''storage.qiniu.access-key'', '''', ''七牛云AccessKey'', ''string'', ''七牛云AccessKey'', 1, 1),
(''storage.qiniu.secret-key'', '''', ''七牛云SecretKey'', ''string'', ''七牛云SecretKey'', 1, 1),
(''storage.qiniu.bucket'', '''', ''七牛云存储空间'', ''string'', ''七牛云存储空间'', 1, 1),
(''storage.qiniu.domain'', '''', ''七牛云域名'', ''string'', ''七牛云域名'', 1, 1),
(''storage.qiniu.region'', ''huanan'', ''七牛云区域'', ''string'', ''七牛云区域'', 1, 1),
(''storage.qiniu.upload-dir'', ''phototagmoment'', ''七牛云上传目录'', ''string'', ''七牛云上传目录'', 1, 1),
(''storage.qiniu.is-private'', ''true'', ''七牛云是否私有空间'', ''boolean'', ''七牛云是否私有空间'', 1, 1),
(''storage.qiniu.download-expires'', ''3600'', ''七牛云下载凭证有效期'', ''number'', ''七牛云下载凭证有效期（秒）'', 1, 1),

-- 敏感词过滤配置
(''sensitive.word.filter'', ''true'', ''敏感词过滤'', ''boolean'', ''是否启用敏感词过滤'', 1, 1),
(''sensitive.word.replace.char'', ''*'', ''敏感词替换字符'', ''string'', ''敏感词替换字符'', 1, 1)';

SET @insert_sql_without_status = 'INSERT IGNORE INTO `ptm_system_config` (`config_key`, `config_value`, `config_name`, `config_type`, `remark`, `is_system`) VALUES
-- 内容审核基本配置
(''content-moderation.enabled'', ''true'', ''启用内容审核'', ''boolean'', ''是否启用内容审核功能'', 1),
(''content-moderation.image.provider'', ''baidu'', ''图像审核服务提供商'', ''string'', ''图像审核服务提供商：local, baidu'', 1),
(''content-moderation.text.provider'', ''local'', ''文本审核服务提供商'', ''string'', ''文本审核服务提供商：local'', 1),
(''content-moderation.auto-approve'', ''false'', ''自动通过审核'', ''boolean'', ''是否自动通过审核（用于测试）'', 1),
(''content-moderation.mode'', ''mixed'', ''审核模式'', ''string'', ''审核模式：auto-自动审核, manual-人工审核, mixed-混合审核'', 1),
(''content-moderation.sensitivity'', ''80'', ''自动审核敏感度'', ''number'', ''自动审核敏感度：0-100，值越大越严格'', 1),
(''content-moderation.contact-info.filter'', ''true'', ''联系方式过滤'', ''boolean'', ''是否过滤联系方式'', 1),

-- 百度内容审核配置
(''content-moderation.baidu.app-id'', '''', ''百度AppID'', ''string'', ''百度内容审核AppID'', 1),
(''content-moderation.baidu.api-key'', '''', ''百度ApiKey'', ''string'', ''百度内容审核ApiKey'', 1),
(''content-moderation.baidu.secret-key'', '''', ''百度SecretKey'', ''string'', ''百度内容审核SecretKey'', 1),

-- 存储配置
(''storage.type'', ''qiniu'', ''存储类型'', ''string'', ''存储类型：local, qiniu, tencent, aliyun'', 1),
(''storage.local.path'', ''/data/upload'', ''本地存储路径'', ''string'', ''本地存储路径'', 1),
(''storage.local.domain'', ''http://localhost:8081/api/file'', ''本地存储域名'', ''string'', ''本地存储域名'', 1),
(''storage.qiniu.access-key'', '''', ''七牛云AccessKey'', ''string'', ''七牛云AccessKey'', 1),
(''storage.qiniu.secret-key'', '''', ''七牛云SecretKey'', ''string'', ''七牛云SecretKey'', 1),
(''storage.qiniu.bucket'', '''', ''七牛云存储空间'', ''string'', ''七牛云存储空间'', 1),
(''storage.qiniu.domain'', '''', ''七牛云域名'', ''string'', ''七牛云域名'', 1),
(''storage.qiniu.region'', ''huanan'', ''七牛云区域'', ''string'', ''七牛云区域'', 1),
(''storage.qiniu.upload-dir'', ''phototagmoment'', ''七牛云上传目录'', ''string'', ''七牛云上传目录'', 1),
(''storage.qiniu.is-private'', ''true'', ''七牛云是否私有空间'', ''boolean'', ''七牛云是否私有空间'', 1),
(''storage.qiniu.download-expires'', ''3600'', ''七牛云下载凭证有效期'', ''number'', ''七牛云下载凭证有效期（秒）'', 1),

-- 敏感词过滤配置
(''sensitive.word.filter'', ''true'', ''敏感词过滤'', ''boolean'', ''是否启用敏感词过滤'', 1),
(''sensitive.word.replace.char'', ''*'', ''敏感词替换字符'', ''string'', ''敏感词替换字符'', 1)';

-- 根据表结构选择SQL语句
SET @sql_to_execute = IF(@column_exists_status = 1, @insert_sql_with_status, @insert_sql_without_status);

-- 执行SQL语句
PREPARE stmt FROM @sql_to_execute;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查敏感词表结构
SET @table_exists_sensitive_word = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'ptm_sensitive_word');
SET @column_exists_status = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'ptm_sensitive_word' AND column_name = 'status');

-- 根据表结构选择不同的SQL语句
SET @insert_sql_with_status = 'INSERT IGNORE INTO `ptm_sensitive_word` (`word`, `type`, `level`, `status`, `created_at`, `updated_at`, `is_deleted`) VALUES
(''赌博'', ''违法'', 2, 1, NOW(), NOW(), 0),
(''博彩'', ''违法'', 2, 1, NOW(), NOW(), 0),
(''色情'', ''色情'', 2, 1, NOW(), NOW(), 0),
(''暴力'', ''暴力'', 2, 1, NOW(), NOW(), 0),
(''恐怖'', ''暴力'', 2, 1, NOW(), NOW(), 0),
(''政治'', ''政治'', 3, 1, NOW(), NOW(), 0),
(''广告'', ''广告'', 1, 1, NOW(), NOW(), 0),
(''推广'', ''广告'', 1, 1, NOW(), NOW(), 0),
(''联系'', ''广告'', 1, 1, NOW(), NOW(), 0),
(''价格'', ''广告'', 1, 1, NOW(), NOW(), 0)';

SET @insert_sql_without_status = 'INSERT IGNORE INTO `ptm_sensitive_word` (`word`, `type`, `level`, `created_at`, `updated_at`, `is_deleted`) VALUES
(''赌博'', ''违法'', 2, NOW(), NOW(), 0),
(''博彩'', ''违法'', 2, NOW(), NOW(), 0),
(''色情'', ''色情'', 2, NOW(), NOW(), 0),
(''暴力'', ''暴力'', 2, NOW(), NOW(), 0),
(''恐怖'', ''暴力'', 2, NOW(), NOW(), 0),
(''政治'', ''政治'', 3, NOW(), NOW(), 0),
(''广告'', ''广告'', 1, NOW(), NOW(), 0),
(''推广'', ''广告'', 1, NOW(), NOW(), 0),
(''联系'', ''广告'', 1, NOW(), NOW(), 0),
(''价格'', ''广告'', 1, NOW(), NOW(), 0)';

-- 如果敏感词表存在，则执行插入语句
SET @sql_to_execute = IF(@table_exists_sensitive_word > 0,
                         IF(@column_exists_status > 0, @insert_sql_with_status, @insert_sql_without_status),
                         'SELECT "敏感词表不存在，跳过插入"');

-- 执行SQL语句
PREPARE stmt FROM @sql_to_execute;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
