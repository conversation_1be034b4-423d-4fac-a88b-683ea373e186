# 简单的API测试脚本

Write-Host "=== PhotoTagMoment API测试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:8081/api"

# 测试1: 检查API是否可访问
Write-Host "`n--- 测试API可访问性 ---" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/v3/api-docs" -Method GET
    Write-Host "✅ API服务正常运行" -ForegroundColor Green
} catch {
    Write-Host "❌ API服务不可访问: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试2: 尝试登录获取token
Write-Host "`n--- 测试超级管理员登录 ---" -ForegroundColor Yellow
$loginData = @{
    username = "superadmin"
    password = "123456"
}

try {
    $loginJson = $loginData | ConvertTo-Json
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -Body $loginJson -ContentType "application/json"
    
    if ($loginResponse.code -eq 200) {
        Write-Host "✅ 超级管理员登录成功" -ForegroundColor Green
        $token = $loginResponse.data.token
        
        # 测试3: 获取配置列表
        Write-Host "`n--- 测试获取配置列表 ---" -ForegroundColor Yellow
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $configResponse = Invoke-RestMethod -Uri "$baseUrl/admin/file-upload-config/list?page=1&size=10" -Method GET -Headers $headers
        
        if ($configResponse.code -eq 200) {
            Write-Host "✅ 获取配置列表成功，共 $($configResponse.data.total) 个配置" -ForegroundColor Green
            
            # 测试4: 创建新配置
            Write-Host "`n--- 测试创建新配置 ---" -ForegroundColor Yellow
            $newConfig = @{
                configName = "测试配置-$(Get-Date -Format 'yyyyMMddHHmmss')"
                storageType = "LOCAL"
                configParams = '{"localPath":"test-uploads","domain":"http://localhost:8081","useHttps":false,"connectTimeout":30,"readTimeout":60}'
                uploadLimits = '{"maxFileSize":10,"maxFileCount":5,"allowedFileTypes":["jpg","png","pdf"],"enableFileTypeCheck":true}'
                pathConfig = '{"rootPath":"test-uploads","fileNamingRule":"UUID","directoryStructure":"DATE_TYPE","enableDateDirectory":true}'
                enabled = $true
                isDefault = $false
                description = "API测试创建的配置"
            }
            
            $createJson = $newConfig | ConvertTo-Json
            $createResponse = Invoke-RestMethod -Uri "$baseUrl/admin/file-upload-config/create" -Method POST -Body $createJson -Headers $headers
            
            if ($createResponse.code -eq 200) {
                Write-Host "✅ 创建配置成功，配置ID: $($createResponse.data.id)" -ForegroundColor Green
            } else {
                Write-Host "❌ 创建配置失败: $($createResponse.message)" -ForegroundColor Red
            }
            
        } else {
            Write-Host "❌ 获取配置列表失败: $($configResponse.message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ 超级管理员登录失败: $($loginResponse.message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP状态码: $statusCode" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
