<template>
  <div class="storage-config-container">
    <el-card class="page-header">
      <div class="header-content">
        <h2>存储配置</h2>
        <p>配置和管理文件存储服务，支持本地存储和云存储服务</p>
      </div>
    </el-card>

    <!-- 存储服务概览 -->
    <el-row :gutter="16" class="overview-cards">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#409eff"><folder /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ storageStats.totalFiles }}</div>
              <div class="metric-label">总文件数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#67c23a"><box /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ formatFileSize(storageStats.totalSize) }}</div>
              <div class="metric-label">总存储大小</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#e6a23c"><upload /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ storageStats.todayUploads }}</div>
              <div class="metric-label">今日上传</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon size="32" color="#f56c6c"><connection /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ storageStats.activeConfigs }}</div>
              <div class="metric-label">活跃配置</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 存储配置列表 -->
    <el-card class="config-list">
      <template #header>
        <div class="card-header">
          <span>存储配置</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><plus /></el-icon>
            新增配置
          </el-button>
        </div>
      </template>

      <el-table :data="configList" v-loading="loading" style="width: 100%">
        <el-table-column prop="name" label="配置名称" width="150" />
        <el-table-column prop="type" label="存储类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getStorageTypeTag(row.type)">
              {{ getStorageTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="endpoint" label="服务地址" width="200" />
        <el-table-column prop="bucket" label="存储桶/路径" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '正常' : '异常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认配置" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isDefault" type="primary" size="small">默认</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="usage" label="使用情况" width="150">
          <template #default="{ row }">
            <div class="usage-info">
              <div>{{ row.fileCount }} 个文件</div>
              <div>{{ formatFileSize(row.totalSize) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="lastTestTime" label="最后测试" width="160" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleTest(row)">
              <el-icon><connection /></el-icon>
              测试
            </el-button>
            <el-button size="small" @click="handleEdit(row)">
              <el-icon><edit /></el-icon>
              编辑
            </el-button>
            <el-button
              v-if="!row.isDefault"
              size="small"
              type="success"
              @click="handleSetDefault(row)"
            >
              设为默认
            </el-button>
            <el-button
              v-if="!row.isDefault"
              size="small"
              type="danger"
              @click="handleDelete(row)"
            >
              <el-icon><delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑存储配置' : '新增存储配置'"
      width="600px"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入配置名称" />
        </el-form-item>
        
        <el-form-item label="存储类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择存储类型" @change="handleTypeChange">
            <el-option label="本地存储" value="local" />
            <el-option label="七牛云" value="qiniu" />
            <el-option label="阿里云OSS" value="aliyun" />
            <el-option label="腾讯云COS" value="tencent" />
            <el-option label="AWS S3" value="aws" />
            <el-option label="MinIO" value="minio" />
          </el-select>
        </el-form-item>

        <!-- 本地存储配置 -->
        <template v-if="formData.type === 'local'">
          <el-form-item label="存储路径" prop="localPath">
            <el-input v-model="formData.localPath" placeholder="请输入本地存储路径" />
          </el-form-item>
          <el-form-item label="访问域名" prop="domain">
            <el-input v-model="formData.domain" placeholder="请输入访问域名" />
          </el-form-item>
        </template>

        <!-- 云存储配置 -->
        <template v-if="formData.type !== 'local'">
          <el-form-item label="Access Key" prop="accessKey">
            <el-input v-model="formData.accessKey" placeholder="请输入Access Key" show-password />
          </el-form-item>
          <el-form-item label="Secret Key" prop="secretKey">
            <el-input v-model="formData.secretKey" placeholder="请输入Secret Key" show-password />
          </el-form-item>
          <el-form-item label="存储桶" prop="bucket">
            <el-input v-model="formData.bucket" placeholder="请输入存储桶名称" />
          </el-form-item>
          <el-form-item label="服务地址" prop="endpoint">
            <el-input v-model="formData.endpoint" placeholder="请输入服务地址" />
          </el-form-item>
          <el-form-item label="访问域名" prop="domain">
            <el-input v-model="formData.domain" placeholder="请输入访问域名" />
          </el-form-item>
          <el-form-item v-if="formData.type === 'qiniu'" label="存储区域" prop="region">
            <el-select v-model="formData.region" placeholder="请选择存储区域">
              <el-option label="华东-浙江" value="z0" />
              <el-option label="华北-河北" value="z1" />
              <el-option label="华南-广东" value="z2" />
              <el-option label="北美-洛杉矶" value="na0" />
              <el-option label="亚太-新加坡" value="as0" />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="是否启用">
          <el-switch v-model="formData.enabled" />
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="formData.isDefault" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入配置描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Folder, Box, Upload, Connection, Plus, Edit, Delete } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

const storageStats = reactive({
  totalFiles: 0,
  totalSize: 0,
  todayUploads: 0,
  activeConfigs: 0
})

const configList = ref([])

const formData = reactive({
  id: null,
  name: '',
  type: '',
  localPath: '',
  accessKey: '',
  secretKey: '',
  bucket: '',
  endpoint: '',
  domain: '',
  region: '',
  enabled: true,
  isDefault: false,
  description: ''
})

const formRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择存储类型', trigger: 'change' }
  ],
  localPath: [
    { required: true, message: '请输入本地存储路径', trigger: 'blur' }
  ],
  accessKey: [
    { required: true, message: '请输入Access Key', trigger: 'blur' }
  ],
  secretKey: [
    { required: true, message: '请输入Secret Key', trigger: 'blur' }
  ],
  bucket: [
    { required: true, message: '请输入存储桶名称', trigger: 'blur' }
  ],
  endpoint: [
    { required: true, message: '请输入服务地址', trigger: 'blur' }
  ],
  domain: [
    { required: true, message: '请输入访问域名', trigger: 'blur' }
  ]
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    configList.value = [
      {
        id: 1,
        name: '默认本地存储',
        type: 'local',
        endpoint: 'localhost',
        bucket: '/uploads',
        status: 'active',
        isDefault: true,
        fileCount: 1250,
        totalSize: 2147483648, // 2GB
        lastTestTime: '2025-05-23 10:30:00'
      },
      {
        id: 2,
        name: '七牛云存储',
        type: 'qiniu',
        endpoint: 'up-z0.qiniup.com',
        bucket: 'phototagmoment',
        status: 'active',
        isDefault: false,
        fileCount: 856,
        totalSize: 1073741824, // 1GB
        lastTestTime: '2025-05-23 09:15:00'
      }
    ]
    
    storageStats.totalFiles = 2106
    storageStats.totalSize = 3221225472 // 3GB
    storageStats.todayUploads = 45
    storageStats.activeConfigs = 2
    
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleTest = async (row) => {
  try {
    ElMessage.info('正在测试连接...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    row.status = 'active'
    row.lastTestTime = new Date().toLocaleString()
    
    ElMessage.success('连接测试成功')
  } catch (error) {
    row.status = 'error'
    ElMessage.error('连接测试失败')
  }
}

const handleSetDefault = async (row) => {
  try {
    await ElMessageBox.confirm('确认将此配置设为默认存储配置？', '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 取消其他默认配置
    configList.value.forEach(item => {
      item.isDefault = false
    })
    
    row.isDefault = true
    ElMessage.success('默认配置设置成功')
  } catch (error) {
    // 用户取消操作
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除此存储配置？删除后无法恢复！', '确认删除', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const index = configList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      configList.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消操作
  }
}

const handleTypeChange = () => {
  // 清空相关字段
  formData.localPath = ''
  formData.accessKey = ''
  formData.secretKey = ''
  formData.bucket = ''
  formData.endpoint = ''
  formData.region = ''
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (isEdit.value) {
      // 更新配置
      const index = configList.value.findIndex(item => item.id === formData.id)
      if (index > -1) {
        Object.assign(configList.value[index], formData)
      }
      ElMessage.success('配置更新成功')
    } else {
      // 新增配置
      const newConfig = {
        ...formData,
        id: Date.now(),
        status: 'active',
        fileCount: 0,
        totalSize: 0,
        lastTestTime: new Date().toLocaleString()
      }
      configList.value.push(newConfig)
      ElMessage.success('配置创建成功')
    }
    
    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败', error)
  } finally {
    submitting.value = false
  }
}

const handleCloseDialog = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    name: '',
    type: '',
    localPath: '',
    accessKey: '',
    secretKey: '',
    bucket: '',
    endpoint: '',
    domain: '',
    region: '',
    enabled: true,
    isDefault: false,
    description: ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 辅助方法
const getStorageTypeText = (type) => {
  const map = {
    local: '本地存储',
    qiniu: '七牛云',
    aliyun: '阿里云OSS',
    tencent: '腾讯云COS',
    aws: 'AWS S3',
    minio: 'MinIO'
  }
  return map[type] || type
}

const getStorageTypeTag = (type) => {
  const map = {
    local: 'info',
    qiniu: 'primary',
    aliyun: 'success',
    tencent: 'warning',
    aws: 'danger',
    minio: 'info'
  }
  return map[type] || ''
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.storage-config-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.overview-cards {
  margin-bottom: 20px;
}

.metric-card {
  height: 120px;
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.metric-icon {
  margin-right: 16px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.config-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.usage-info {
  font-size: 12px;
  color: #606266;
}

.usage-info div {
  margin-bottom: 2px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
