<template>
  <div class="forgot-password-container">
    <div class="forgot-password-header">
      <h1 class="forgot-password-title">找回密码</h1>
      <p class="forgot-password-subtitle">请输入您的手机号，我们将发送验证码帮助您重置密码</p>
    </div>

    <div class="forgot-password-form">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入手机号"
            prefix-icon="el-icon-mobile-phone"
          ></el-input>
        </el-form-item>
        <el-form-item prop="code">
          <div class="sms-code-input">
            <el-input
              v-model="form.code"
              placeholder="请输入验证码"
              prefix-icon="el-icon-message"
            ></el-input>
            <el-button
              type="primary"
              :disabled="codeTimer > 0"
              @click="sendCode"
            >
              {{ codeTimer > 0 ? `${codeTimer}s后重新获取` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item prop="newPassword">
          <el-input
            v-model="form.newPassword"
            type="password"
            placeholder="请输入新密码"
            prefix-icon="el-icon-lock"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            prefix-icon="el-icon-lock"
            show-password
          ></el-input>
        </el-form-item>
        <el-button type="primary" class="submit-button" @click="resetPassword" :loading="loading">
          重置密码
        </el-button>
      </el-form>

      <div class="form-links">
        <router-link to="/auth/login">返回登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

const router = useRouter();
const formRef = ref<any>(null);

// 表单数据
interface FormData {
  phone: string;
  code: string;
  newPassword: string;
  confirmPassword: string;
}

const form = reactive<FormData>({
  phone: '',
  code: '',
  newPassword: '',
  confirmPassword: ''
});

// 验证规则
const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== form.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 状态
const loading = ref(false);
const codeTimer = ref(0);
let timerInterval: number | null = null;

// 发送验证码
const sendCode = async () => {
  if (!form.phone) {
    ElMessage.warning('请输入手机号');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(form.phone)) {
    ElMessage.warning('请输入正确的手机号');
    return;
  }

  try {
    // 发送验证码
    // await sendSmsVerifyCode(form.phone, 'reset_password');

    // 模拟发送成功
    ElMessage.success('验证码已发送，请注意查收');

    // 开始倒计时
    codeTimer.value = 60;
    timerInterval = window.setInterval(() => {
      codeTimer.value--;
      if (codeTimer.value <= 0 && timerInterval !== null) {
        window.clearInterval(timerInterval);
        timerInterval = null;
      }
    }, 1000);
  } catch (error) {
    console.error('发送验证码失败', error);
    ElMessage.error('发送验证码失败，请稍后重试');
  }
};

// 重置密码
const resetPassword = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true;

        // 调用重置密码API
        // await resetPasswordApi({
        //   phone: form.phone,
        //   code: form.code,
        //   newPassword: form.newPassword
        // });

        // 模拟重置成功
        setTimeout(() => {
          loading.value = false;
          ElMessage.success('密码重置成功，请使用新密码登录');
          router.push('/auth/login');
        }, 1500);
      } catch (error) {
        loading.value = false;
        console.error('重置密码失败', error);
        ElMessage.error('重置密码失败，请稍后重试');
      }
    }
  });
};

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timerInterval !== null) {
    window.clearInterval(timerInterval);
  }
});
</script>

<style lang="scss" scoped>
.forgot-password-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.forgot-password-header {
  text-align: center;
  margin-bottom: 30px;
}

.forgot-password-title {
  font-size: 28px;
  color: #409eff;
  margin-bottom: 10px;
}

.forgot-password-subtitle {
  font-size: 16px;
  color: #666;
}

.forgot-password-form {
  width: 100%;
  max-width: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.sms-code-input {
  display: flex;
  gap: 10px;
}

.sms-code-input .el-input {
  flex: 1;
}

.submit-button {
  width: 100%;
  margin-top: 10px;
}

.form-links {
  text-align: center;
  margin-top: 20px;
}

.form-links a {
  color: #409eff;
  text-decoration: none;
}

@media (max-width: 768px) {
  .forgot-password-form {
    padding: 20px;
  }

  .forgot-password-title {
    font-size: 24px;
  }
}
</style>
