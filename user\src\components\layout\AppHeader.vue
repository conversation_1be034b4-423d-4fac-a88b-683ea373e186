<template>
  <div>
    <!-- 移动端导航栏 -->
    <van-nav-bar
      v-if="isMobile && showNavBar"
      :title="navBarTitle"
      :left-text="showBackArrow ? '返回' : ''"
      :left-arrow="showBackArrow"
      :right-text="navBarRightText"
      fixed
      placeholder
      @click-left="onClickLeft"
      @click-right="onClickRight"
    >
      <template #right v-if="!isLoggedIn && !navBarRightText">
        <div class="login-buttons">
          <van-button type="primary" size="small" to="/auth/login">登录</van-button>
        </div>
      </template>
    </van-nav-bar>

    <!-- 桌面版导航栏 -->
    <div v-if="!isMobile" class="desktop-navbar">
      <div class="navbar-container">
        <div class="navbar-logo">
          <router-link to="/">PhotoTagMoment</router-link>
        </div>
        <div class="navbar-links">
          <router-link to="/" class="navbar-link" :class="{ active: route.path === '/' }">首页</router-link>
          <router-link to="/explore" class="navbar-link" :class="{ active: route.path === '/explore' }">发现</router-link>
          <router-link to="/search" class="navbar-link" :class="{ active: route.path === '/search' }">搜索</router-link>
        </div>
        <div class="navbar-actions">
          <template v-if="isLoggedIn">
            <router-link to="/notifications" class="navbar-icon">
              <van-badge :content="unreadCount > 0 ? unreadCount : ''" :max="99">
                <van-icon name="chat-o" size="24" />
              </van-badge>
            </router-link>
            <router-link to="/user/profile" class="navbar-avatar">
              <van-image
                round
                width="32"
                height="32"
                :src="userAvatar"
                fit="cover"
              />
            </router-link>
          </template>
          <template v-else>
            <router-link to="/auth/login" class="navbar-button login-button">登录/注册</router-link>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';

const props = defineProps({
  showNavBar: {
    type: Boolean,
    default: true
  }
});

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 是否已登录
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 用户头像
const userAvatar = computed(() => {
  return userStore.userInfo?.avatar || 'https://randomuser.me/api/portraits/men/1.jpg';
});

// 未读消息数量
const unreadCount = computed(() => userStore.unreadCount);

// 是否为移动设备
const isMobile = ref(false);

// 导航栏标题
const navBarTitle = computed(() => {
  const routeTitleMap: Record<string, string> = {
    '/': '首页',
    '/explore': '发现',
    '/upload': '上传照片',
    '/search': '搜索',
    '/notifications': '消息通知',
    '/user/profile': '个人中心',
    '/settings': '设置',
    '/photo': '照片详情',
    '/album': '相册详情',
    '/user': '用户主页',
  };

  // 获取当前路由的基本路径
  const basePath = '/' + route.path.split('/')[1];
  return routeTitleMap[basePath] || route.meta.title as string || 'PhotoTagMoment';
});

// 是否显示返回箭头
const showBackArrow = computed(() => {
  return route.path !== '/' && route.path !== '/explore' &&
         route.path !== '/notifications' && route.path !== '/user/profile';
});

// 导航栏右侧文本
const navBarRightText = computed(() => {
  if (route.path.includes('/photo/')) return '分享';
  if (route.path === '/user/profile') return '设置';
  return '';
});

// 导航栏左侧按钮点击事件
const onClickLeft = () => {
  if (showBackArrow.value) {
    router.back();
  }
};

// 导航栏右侧按钮点击事件
const onClickRight = () => {
  if (route.path.includes('/photo/')) {
    // 分享照片
    console.log('分享照片');
  } else if (route.path === '/user/profile') {
    // 跳转到设置页面
    router.push('/settings');
  }
};

// 检测设备类型
const checkDeviceType = () => {
  isMobile.value = window.innerWidth < 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkDeviceType();
  window.addEventListener('resize', checkDeviceType);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkDeviceType);
});
</script>

<style lang="scss" scoped>
.desktop-navbar {
  height: 64px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;

  .navbar-container {
    max-width: 1200px;
    height: 100%;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .navbar-logo {
    font-size: 1.25rem;
    font-weight: 600;

    a {
      color: #333;
      text-decoration: none;
    }
  }

  .navbar-links {
    display: flex;
    gap: 1.5rem;

    .navbar-link {
      color: #666;
      text-decoration: none;
      font-size: 1rem;
      padding: 0.5rem 0;
      position: relative;

      &.active {
        color: #3b82f6;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #3b82f6;
        }
      }

      &:hover {
        color: #3b82f6;
      }
    }
  }

  .navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;

    .navbar-button {
      padding: 0.5rem 1rem;
      border-radius: 9999px;
      font-size: 0.875rem;
      text-decoration: none;
      white-space: nowrap;
      overflow: visible;

      &.upload-button {
        background-color: #3b82f6;
        color: white;
      }

      &.login-button {
        background-color: #3b82f6;
        color: white;
        font-weight: 500;
        display: inline-block;
        text-align: center;
      }
    }

    .navbar-icon {
      color: #666;
    }

    .navbar-avatar {
      display: block;
    }
  }
}
</style>
