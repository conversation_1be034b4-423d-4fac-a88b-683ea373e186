package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.entity.SystemConfig;
import com.phototagmoment.mapper.SystemConfigMapper;
import com.phototagmoment.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 系统参数配置Service实现类
 */
@Slf4j
@Service
public class SystemConfigServiceImpl implements SystemConfigService {

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    // 本地缓存
    private static final Map<String, String> CONFIG_CACHE = new ConcurrentHashMap<>();

    @Override
    public List<SystemConfig> listAllConfigs() {
        return systemConfigMapper.selectList(null);
    }

    @Override
    public IPage<SystemConfig> pageConfigs(int page, int pageSize, String keyword) {
        Page<SystemConfig> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<SystemConfig> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(SystemConfig::getConfigKey, keyword)
                    .or().like(SystemConfig::getConfigName, keyword)
                    .or().like(SystemConfig::getRemark, keyword);
        }

        queryWrapper.orderByAsc(SystemConfig::getConfigKey);

        return systemConfigMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public SystemConfig getConfigById(Long id) {
        return systemConfigMapper.selectById(id);
    }

    @Override
    @Cacheable(value = "system_config", key = "#configKey", unless = "#result == null")
    public String getConfigValue(String configKey) {
        // 先从本地缓存获取
        String cachedValue = CONFIG_CACHE.get(configKey);
        if (cachedValue != null) {
            return cachedValue;
        }

        try {
            // 从数据库获取
            String value = systemConfigMapper.getConfigValueByKey(configKey);
            if (value != null) {
                CONFIG_CACHE.put(configKey, value);
            }
            return value;
        } catch (Exception e) {
            log.warn("获取配置值失败: {}, 错误: {}", configKey, e.getMessage());
            return null;
        }
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? value : defaultValue;
    }

    @Override
    public boolean getBooleanValue(String configKey) {
        String value = getConfigValue(configKey);
        return value != null && (value.equalsIgnoreCase("true") || value.equals("1"));
    }

    @Override
    public boolean getBooleanValue(String configKey, boolean defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? (value.equalsIgnoreCase("true") || value.equals("1")) : defaultValue;
    }

    @Override
    public int getIntValue(String configKey) {
        String value = getConfigValue(configKey);
        return value != null ? Integer.parseInt(value) : 0;
    }

    @Override
    public int getIntValue(String configKey, int defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? Integer.parseInt(value) : defaultValue;
    }

    @Override
    public long getLongValue(String configKey) {
        String value = getConfigValue(configKey);
        return value != null ? Long.parseLong(value) : 0L;
    }

    @Override
    public long getLongValue(String configKey, long defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? Long.parseLong(value) : defaultValue;
    }

    @Override
    public double getDoubleValue(String configKey) {
        String value = getConfigValue(configKey);
        return value != null ? Double.parseDouble(value) : 0.0;
    }

    @Override
    public double getDoubleValue(String configKey, double defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? Double.parseDouble(value) : defaultValue;
    }

    @Override
    public <T> T getJsonValue(String configKey, Class<T> clazz) {
        String value = getConfigValue(configKey);
        if (value == null) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(value, clazz);
        } catch (Exception e) {
            log.error("解析JSON配置失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "system_config", key = "#config.configKey")
    public boolean saveConfig(SystemConfig config) {
        // 检查配置键是否已存在
        SystemConfig existConfig = systemConfigMapper.getConfigByKey(config.getConfigKey());
        if (existConfig != null) {
            return false;
        }

        int result = systemConfigMapper.insert(config);
        if (result > 0) {
            // 更新本地缓存
            CONFIG_CACHE.put(config.getConfigKey(), config.getConfigValue());
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean batchSaveConfig(List<SystemConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return false;
        }

        for (SystemConfig config : configs) {
            saveConfig(config);
        }

        return true;
    }

    @Override
    @Transactional
    public boolean batchSaveConfig(Map<String, String> configMap) {
        if (configMap == null || configMap.isEmpty()) {
            return false;
        }

        List<SystemConfig> configs = new ArrayList<>();
        configMap.forEach((key, value) -> {
            SystemConfig config = new SystemConfig();
            config.setConfigKey(key);
            config.setConfigValue(value);
            config.setConfigName(key);
            config.setConfigType("string");
            config.setIsSystem(false);
            config.setStatus(true);
            configs.add(config);
        });

        return batchSaveConfig(configs);
    }

    @Override
    @Transactional
    @CacheEvict(value = "system_config", key = "#config.configKey")
    public boolean updateConfig(SystemConfig config) {
        int result = systemConfigMapper.updateById(config);
        if (result > 0) {
            // 更新本地缓存
            CONFIG_CACHE.put(config.getConfigKey(), config.getConfigValue());
        }

        return result > 0;
    }

    @Override
    @Transactional
    @CacheEvict(value = "system_config", key = "#configKey")
    public boolean updateConfigValue(String configKey, String configValue) {
        SystemConfig config = systemConfigMapper.getConfigByKey(configKey);
        if (config == null) {
            return false;
        }

        config.setConfigValue(configValue);
        int result = systemConfigMapper.updateById(config);
        if (result > 0) {
            // 更新本地缓存
            CONFIG_CACHE.put(configKey, configValue);
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteConfig(Long id) {
        SystemConfig config = systemConfigMapper.selectById(id);
        if (config == null) {
            return false;
        }

        // 系统内置配置不允许删除
        if (config.getIsSystem()) {
            return false;
        }

        int result = systemConfigMapper.deleteById(id);
        if (result > 0) {
            // 清除本地缓存
            CONFIG_CACHE.remove(config.getConfigKey());
        }

        return result > 0;
    }

    @Override
    @CacheEvict(value = "system_config", allEntries = true)
    public void refreshCache() {
        // 清空本地缓存
        CONFIG_CACHE.clear();

        // 重新加载所有配置
        List<SystemConfig> configs = systemConfigMapper.selectList(null);
        for (SystemConfig config : configs) {
            CONFIG_CACHE.put(config.getConfigKey(), config.getConfigValue());
        }

        log.info("系统配置缓存已刷新，共加载{}条配置", configs.size());
    }

    @Override
    public Map<String, String> batchGetConfigValues(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return new ConcurrentHashMap<>();
        }

        Map<String, String> result = new ConcurrentHashMap<>();
        for (String key : keys) {
            String value = getConfigValue(key);
            if (value != null) {
                result.put(key, value);
            }
        }

        return result;
    }
}
