<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <h1 class="page-title">控制台</h1>
      </el-col>
    </el-row>

    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="data-overview">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="data-card">
          <div class="data-card-content">
            <div class="data-icon users-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="data-info">
              <div class="data-value">{{ stats.userCount.toLocaleString() }}</div>
              <div class="data-label">用户总数</div>
            </div>
          </div>
          <div class="data-trend">
            <span class="trend-value" :class="{ 'up': stats.userTrend > 0, 'down': stats.userTrend < 0 }">
              {{ stats.userTrend > 0 ? '+' : '' }}{{ stats.userTrend }}%
            </span>
            <span class="trend-period">较上周</span>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="data-card">
          <div class="data-card-content">
            <div class="data-icon photos-icon">
              <el-icon><Picture /></el-icon>
            </div>
            <div class="data-info">
              <div class="data-value">{{ stats.photoCount.toLocaleString() }}</div>
              <div class="data-label">照片总数</div>
            </div>
          </div>
          <div class="data-trend">
            <span class="trend-value" :class="{ 'up': stats.photoTrend > 0, 'down': stats.photoTrend < 0 }">
              {{ stats.photoTrend > 0 ? '+' : '' }}{{ stats.photoTrend }}%
            </span>
            <span class="trend-period">较上周</span>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="data-card">
          <div class="data-card-content">
            <div class="data-icon comments-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="data-info">
              <div class="data-value">{{ stats.commentCount.toLocaleString() }}</div>
              <div class="data-label">评论总数</div>
            </div>
          </div>
          <div class="data-trend">
            <span class="trend-value" :class="{ 'up': stats.commentTrend > 0, 'down': stats.commentTrend < 0 }">
              {{ stats.commentTrend > 0 ? '+' : '' }}{{ stats.commentTrend }}%
            </span>
            <span class="trend-period">较上周</span>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="data-card">
          <div class="data-card-content">
            <div class="data-icon storage-icon">
              <el-icon><Coin /></el-icon>
            </div>
            <div class="data-info">
              <div class="data-value">{{ stats.storageUsedFormatted }}</div>
              <div class="data-label">存储用量</div>
            </div>
          </div>
          <div class="data-trend">
            <span class="trend-value" :class="{ 'up': stats.storageTrend > 0, 'down': stats.storageTrend < 0 }">
              {{ stats.storageTrend > 0 ? '+' : '' }}{{ stats.storageTrend }}%
            </span>
            <span class="trend-period">较上周</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
              <div class="header-actions">
                <el-radio-group v-model="userChartPeriod" size="small">
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                  <el-radio-button label="year">年</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container" ref="userChartRef"></div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>内容分布</span>
            </div>
          </template>
          <div class="chart-container" ref="contentChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新动态 -->
    <el-row :gutter="20" class="activity-section">
      <el-col :xs="24" :md="12">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最新用户</span>
              <el-button type="text" @click="viewAllUsers">查看全部</el-button>
            </div>
          </template>
          <div class="user-list">
            <div v-for="user in latestUsers" :key="user.id" class="user-item">
              <el-avatar :size="40" :src="user.avatar"></el-avatar>
              <div class="user-info">
                <div class="user-name">{{ user.nickname }}</div>
                <div class="user-meta">
                  <span>注册于 {{ formatDate(user.createdAt) }}</span>
                </div>
              </div>
              <div class="user-actions">
                <el-button size="small" @click="viewUserDetail(user.id)">查看</el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :md="12">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最新照片</span>
              <el-button type="text" @click="viewAllPhotos">查看全部</el-button>
            </div>
          </template>
          <div class="photo-list">
            <div v-for="photo in latestPhotos" :key="photo.id" class="photo-item">
              <div class="photo-thumbnail">
                <img :src="photo.thumbnailUrl" :alt="photo.title" />
              </div>
              <div class="photo-info">
                <div class="photo-title">{{ photo.title }}</div>
                <div class="photo-meta">
                  <span>{{ photo.user.nickname }}</span>
                  <span>{{ formatDate(photo.createdAt) }}</span>
                </div>
              </div>
              <div class="photo-actions">
                <el-button size="small" @click="viewPhotoDetail(photo.id)">查看</el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  getDashboardStats,
  getUserGrowthTrend,
  getContentDistribution,
  getLatestUsers,
  getLatestPhotos,
  type DashboardStats,
  type LatestUser,
  type LatestPhoto
} from '@/api/dashboard'
import { formatDateTime } from '@/utils/date'

const router = useRouter()

// 图表引用
const userChartRef = ref<HTMLElement | null>(null)
const contentChartRef = ref<HTMLElement | null>(null)
let userChart: echarts.ECharts | null = null
let contentChart: echarts.ECharts | null = null

// 加载状态
const loading = ref(false)

// 用户图表周期
const userChartPeriod = ref('week')

// 统计数据
const stats = ref<DashboardStats>({
  userCount: 0,
  userTrend: 0,
  photoCount: 0,
  photoTrend: 0,
  commentCount: 0,
  commentTrend: 0,
  storageUsed: 0,
  storageUsedFormatted: '0 B',
  storageTrend: 0,
  todayUsers: 0,
  todayPhotos: 0,
  todayComments: 0,
  todayStorage: 0,
  activeUsers: 0,
  pendingPhotos: 0,
  pendingComments: 0,
  systemRunDays: 0
})

// 最新用户
const latestUsers = ref<LatestUser[]>([])

// 最新照片
const latestPhotos = ref<LatestPhoto[]>([])

// 图表数据
const userGrowthData = ref<any>(null)
const contentDistributionData = ref<any[]>([])

// 加载统计数据
const loadDashboardStats = async () => {
  try {
    const response = await getDashboardStats()
    stats.value = response.data
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 加载用户增长趋势
const loadUserGrowthTrend = async () => {
  try {
    const response = await getUserGrowthTrend(userChartPeriod.value)
    userGrowthData.value = response.data
    updateUserChart()
  } catch (error) {
    console.error('获取用户增长趋势失败:', error)
    ElMessage.error('获取用户增长趋势失败')
  }
}

// 加载内容分布
const loadContentDistribution = async () => {
  try {
    const response = await getContentDistribution()
    contentDistributionData.value = response.data
    updateContentChart()
  } catch (error) {
    console.error('获取内容分布失败:', error)
    ElMessage.error('获取内容分布失败')
  }
}

// 加载最新用户
const loadLatestUsers = async () => {
  try {
    const response = await getLatestUsers(5)
    latestUsers.value = response.data
  } catch (error) {
    console.error('获取最新用户失败:', error)
    ElMessage.error('获取最新用户失败')
  }
}

// 加载最新照片
const loadLatestPhotos = async () => {
  try {
    const response = await getLatestPhotos(5)
    latestPhotos.value = response.data
  } catch (error) {
    console.error('获取最新照片失败:', error)
    ElMessage.error('获取最新照片失败')
  }
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadDashboardStats(),
      loadUserGrowthTrend(),
      loadContentDistribution(),
      loadLatestUsers(),
      loadLatestPhotos()
    ])
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return formatDateTime(dateString, 'MM-DD')
}

// 初始化用户增长趋势图表
const initUserChart = () => {
  if (!userChartRef.value) return
  userChart = echarts.init(userChartRef.value)
}

// 更新用户增长趋势图表
const updateUserChart = () => {
  if (!userChart || !userGrowthData.value) return

  const { newUsers, activeUsers, labels } = userGrowthData.value

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增用户', '活跃用户']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: labels
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新增用户',
        type: 'line',
        data: newUsers.map((item: any) => item.count),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#3498db'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(52, 152, 219, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(52, 152, 219, 0.1)'
              }
            ]
          }
        }
      },
      {
        name: '活跃用户',
        type: 'line',
        data: activeUsers.map((item: any) => item.count),
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#2ecc71'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(46, 204, 113, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(46, 204, 113, 0.1)'
              }
            ]
          }
        }
      }
    ]
  }

  userChart.setOption(option)
}

// 初始化内容分布图表
const initContentChart = () => {
  if (!contentChartRef.value) return
  contentChart = echarts.init(contentChartRef.value)
}

// 更新内容分布图表
const updateContentChart = () => {
  if (!contentChart || !contentDistributionData.value.length) return

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: contentDistributionData.value.map(item => item.name)
    },
    series: [
      {
        name: '照片分类',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: contentDistributionData.value
      }
    ]
  }

  contentChart.setOption(option)
}

// 查看所有用户
const viewAllUsers = () => {
  router.push('/user/list')
}

// 查看用户详情
const viewUserDetail = (userId: number) => {
  router.push(`/user/detail/${userId}`)
}

// 查看所有照片
const viewAllPhotos = () => {
  router.push('/content/photo')
}

// 查看照片详情
const viewPhotoDetail = (photoId: number) => {
  router.push(`/content/photo/detail/${photoId}`)
}

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  userChart?.resize()
  contentChart?.resize()
}

// 监听用户图表周期变化
watch(userChartPeriod, () => {
  loadUserGrowthTrend()
})

// 组件挂载时初始化图表
onMounted(() => {
  // 初始化图表
  initUserChart()
  initContentChart()

  // 加载数据
  loadAllData()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  userChart?.dispose()
  contentChart?.dispose()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;

  .page-title {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 24px;
  }

  .data-overview {
    margin-bottom: 20px;

    .data-card {
      margin-bottom: 20px;

      .data-card-content {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .data-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 48px;
          height: 48px;
          border-radius: 8px;
          margin-right: 15px;

          .el-icon {
            font-size: 24px;
            color: white;
          }

          &.users-icon {
            background-color: #3498db;
          }

          &.photos-icon {
            background-color: #2ecc71;
          }

          &.comments-icon {
            background-color: #f39c12;
          }

          &.storage-icon {
            background-color: #9b59b6;
          }
        }

        .data-info {
          .data-value {
            font-size: 24px;
            font-weight: bold;
            line-height: 1.2;
          }

          .data-label {
            font-size: 14px;
            color: #666;
          }
        }
      }

      .data-trend {
        font-size: 12px;

        .trend-value {
          &.up {
            color: #2ecc71;
          }

          &.down {
            color: #e74c3c;
          }
        }

        .trend-period {
          margin-left: 5px;
          color: #666;
        }
      }
    }
  }

  .chart-section {
    margin-bottom: 20px;

    .chart-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chart-container {
        height: 350px;
      }
    }
  }

  .activity-section {
    .activity-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .user-list, .photo-list {
        .user-item, .photo-item {
          display: flex;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .user-info, .photo-info {
            flex: 1;
            margin: 0 15px;

            .user-name, .photo-title {
              font-weight: 500;
              margin-bottom: 5px;
            }

            .user-meta, .photo-meta {
              font-size: 12px;
              color: #666;

              span {
                margin-right: 10px;
              }
            }
          }
        }

        .photo-item {
          .photo-thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }
    }
  }
}
</style>
