package com.phototagmoment.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通知DTO
 */
@Data
public class NotificationDTO {

    /**
     * 通知ID
     */
    private Long id;

    /**
     * 接收用户ID
     */
    private Long userId;

    /**
     * 发送用户ID
     */
    private Long senderId;

    /**
     * 发送用户名称
     */
    private String senderName;

    /**
     * 发送用户头像
     */
    private String senderAvatar;

    /**
     * 通知类型：1关注，2点赞，3评论，4回复，5系统
     */
    private Integer type;

    /**
     * 目标ID
     */
    private Long targetId;

    /**
     * 目标类型：1照片，2评论，3用户
     */
    private Integer targetType;

    /**
     * 目标标题或内容
     */
    private String targetContent;

    /**
     * 目标缩略图
     */
    private String targetThumbnail;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 是否已读：0未读，1已读
     */
    private Integer isRead;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
