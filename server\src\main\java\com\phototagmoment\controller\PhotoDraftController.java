package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.PhotoDraftDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.PhotoDraftService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 照片草稿控制器
 */
@Slf4j
@RestController
@RequestMapping("/photo/draft")
@Tag(name = "照片草稿接口", description = "照片草稿保存、获取、发布接口")
public class PhotoDraftController {

    @Autowired
    private PhotoDraftService photoDraftService;

    @PostMapping("/save")
    @Operation(summary = "保存草稿", description = "保存照片草稿")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Long> saveDraft(
            @Parameter(description = "照片文件列表") @RequestParam(value = "files", required = false) List<MultipartFile> files,
            @Parameter(description = "草稿信息") @Validated @RequestPart("data") PhotoDraftDTO photoDraftDTO) {
        Long userId = SecurityUtil.getCurrentUserId();
        Long draftId = photoDraftService.saveDraft(photoDraftDTO, files, userId);
        return ApiResponse.success(draftId);
    }

    @PutMapping("/{draftId}")
    @Operation(summary = "更新草稿", description = "更新照片草稿")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> updateDraft(
            @Parameter(description = "草稿ID") @PathVariable Long draftId,
            @Parameter(description = "照片文件列表") @RequestParam(value = "files", required = false) List<MultipartFile> files,
            @Parameter(description = "草稿信息") @Validated @RequestPart("data") PhotoDraftDTO photoDraftDTO) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoDraftService.updateDraft(draftId, photoDraftDTO, files, userId);
        return ApiResponse.success(result);
    }

    @GetMapping("/{draftId}")
    @Operation(summary = "获取草稿详情", description = "获取照片草稿详情")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<PhotoDraftDTO> getDraftDetail(
            @Parameter(description = "草稿ID") @PathVariable Long draftId) {
        Long userId = SecurityUtil.getCurrentUserId();
        PhotoDraftDTO draftDTO = photoDraftService.getDraftDetail(draftId, userId);
        return ApiResponse.success(draftDTO);
    }

    @GetMapping("/list")
    @Operation(summary = "获取草稿列表", description = "获取用户草稿列表")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<IPage<PhotoDraftDTO>> getUserDrafts(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        Long userId = SecurityUtil.getCurrentUserId();
        IPage<PhotoDraftDTO> draftPage = photoDraftService.getUserDrafts(page, size, userId);
        return ApiResponse.success(draftPage);
    }

    @DeleteMapping("/{draftId}")
    @Operation(summary = "删除草稿", description = "删除照片草稿")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> deleteDraft(
            @Parameter(description = "草稿ID") @PathVariable Long draftId) {
        Long userId = SecurityUtil.getCurrentUserId();
        boolean result = photoDraftService.deleteDraft(draftId, userId);
        return ApiResponse.success(result);
    }

    @PostMapping("/publish/{draftId}")
    @Operation(summary = "发布草稿", description = "发布照片草稿")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<List<Long>> publishDraft(
            @Parameter(description = "草稿ID") @PathVariable Long draftId) {
        Long userId = SecurityUtil.getCurrentUserId();
        List<Long> photoIds = photoDraftService.publishDraft(draftId, userId);
        return ApiResponse.success(photoIds);
    }
}
