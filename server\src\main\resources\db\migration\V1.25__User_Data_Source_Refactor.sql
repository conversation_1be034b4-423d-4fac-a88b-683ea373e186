-- PhotoTagMoment用户端数据源重构
-- 将用户端照片展示功能从传统照片数据切换到照片笔记数据
-- 创建时间: 2025-05-27
-- 版本: V1.25

-- 1. 更新推荐记录表，支持照片笔记推荐
-- 添加照片笔记ID字段到推荐记录表
ALTER TABLE ptm_recommendation_record 
ADD COLUMN note_id BIGINT NULL COMMENT '照片笔记ID' AFTER photo_id;

-- 添加索引
ALTER TABLE ptm_recommendation_record 
ADD INDEX idx_note_id (note_id);

-- 2. 创建照片笔记推荐记录表（专门用于照片笔记推荐）
CREATE TABLE IF NOT EXISTS ptm_photo_note_recommendation_record (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    note_id BIGINT NOT NULL COMMENT '照片笔记ID',
    recommendation_type VARCHAR(20) NOT NULL COMMENT '推荐类型（interest, hot, following）',
    recommendation_time DATETIME NOT NULL COMMENT '推荐时间',
    is_clicked TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否被点击',
    score DECIMAL(10, 4) NULL DEFAULT NULL COMMENT '推荐分数',
    created_at DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE INDEX uk_user_note_time (user_id, note_id, recommendation_time),
    INDEX idx_user_id (user_id),
    INDEX idx_note_id (note_id),
    INDEX idx_recommendation_type (recommendation_type),
    INDEX idx_recommendation_time (recommendation_time),
    INDEX idx_is_clicked (is_clicked),
    INDEX idx_score (score)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记推荐记录表';

-- 3. 创建用户照片笔记行为记录表
CREATE TABLE IF NOT EXISTS ptm_user_photo_note_behavior (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    note_id BIGINT NOT NULL COMMENT '照片笔记ID',
    behavior_type VARCHAR(20) NOT NULL COMMENT '行为类型（view, like, comment, collect, share）',
    behavior_time DATETIME NOT NULL COMMENT '行为时间',
    weight DECIMAL(5, 2) NULL DEFAULT 1.00 COMMENT '行为权重',
    created_at DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_note_id (note_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_behavior_time (behavior_time),
    INDEX idx_weight (weight)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户照片笔记行为表';

-- 4. 更新用户兴趣表，支持照片笔记标签
-- 添加来源字段，区分是来自照片还是照片笔记
ALTER TABLE ptm_user_interest 
ADD COLUMN source_type VARCHAR(20) NULL DEFAULT 'photo_note' COMMENT '来源类型：photo, photo_note' AFTER tag_name;

-- 添加索引
ALTER TABLE ptm_user_interest 
ADD INDEX idx_source_type (source_type);

-- 5. 创建照片笔记搜索记录表
CREATE TABLE IF NOT EXISTS ptm_photo_note_search_record (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NULL COMMENT '用户ID（可为空，支持匿名搜索）',
    keyword VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    result_count INT NULL DEFAULT 0 COMMENT '搜索结果数量',
    search_time DATETIME NOT NULL COMMENT '搜索时间',
    ip_address VARCHAR(50) NULL COMMENT 'IP地址',
    user_agent VARCHAR(500) NULL COMMENT '用户代理',
    created_at DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    INDEX idx_user_id (user_id),
    INDEX idx_keyword (keyword),
    INDEX idx_search_time (search_time),
    INDEX idx_result_count (result_count)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '照片笔记搜索记录表';

-- 6. 添加系统配置项，支持数据源切换配置
INSERT INTO ptm_system_config (config_key, config_value, config_name, config_type, remark, is_system, description) VALUES
('user.data.source', 'photo_note', '用户端数据源', 'string', '用户端展示数据源：photo-传统照片，photo_note-照片笔记', 1, '控制用户端页面显示的数据来源'),
('recommendation.algorithm.version', '2.0', '推荐算法版本', 'string', '推荐算法版本号', 1, '用于标识当前使用的推荐算法版本'),
('search.default.type', 'photo_note', '默认搜索类型', 'string', '默认搜索类型：photo, photo_note, all', 1, '用户搜索时的默认搜索范围');

-- 7. 更新现有数据，将传统照片的用户行为迁移到照片笔记行为（如果需要的话）
-- 注意：这里只是创建表结构，实际的数据迁移需要根据业务需求单独处理

-- 8. 添加数据完整性约束
-- 为照片笔记推荐记录表添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE ptm_photo_note_recommendation_record 
-- ADD CONSTRAINT fk_pnrr_user_id FOREIGN KEY (user_id) REFERENCES ptm_user(id) ON DELETE CASCADE,
-- ADD CONSTRAINT fk_pnrr_note_id FOREIGN KEY (note_id) REFERENCES ptm_photo_note(id) ON DELETE CASCADE;

-- 为用户照片笔记行为表添加外键约束（可选）
-- ALTER TABLE ptm_user_photo_note_behavior 
-- ADD CONSTRAINT fk_upnb_user_id FOREIGN KEY (user_id) REFERENCES ptm_user(id) ON DELETE CASCADE,
-- ADD CONSTRAINT fk_upnb_note_id FOREIGN KEY (note_id) REFERENCES ptm_photo_note(id) ON DELETE CASCADE;

-- 9. 创建视图，方便查询用户端展示数据
CREATE OR REPLACE VIEW v_user_display_content AS
SELECT 
    'photo_note' as content_type,
    pn.id as content_id,
    pn.user_id,
    pn.title,
    pn.content as description,
    pn.view_count,
    pn.like_count,
    pn.comment_count,
    pn.share_count,
    pn.created_at,
    pn.updated_at,
    u.username,
    u.nickname,
    u.avatar,
    -- 获取第一张照片作为封面
    (SELECT p.url FROM ptm_photo_note_image pni 
     LEFT JOIN ptm_photo p ON pni.photo_id = p.id 
     WHERE pni.note_id = pn.id 
     ORDER BY pni.sort_order ASC 
     LIMIT 1) as cover_image_url
FROM ptm_photo_note pn
LEFT JOIN ptm_user u ON pn.user_id = u.id
WHERE pn.is_deleted = 0 AND pn.status = 1 AND pn.visibility = 1;

-- 10. 添加注释说明
ALTER TABLE ptm_recommendation_record 
MODIFY COLUMN photo_id BIGINT NULL COMMENT '照片ID（兼容旧版本）';

-- 添加表注释更新
ALTER TABLE ptm_recommendation_record 
COMMENT = '推荐记录表（支持照片和照片笔记推荐）';

-- 完成重构标记
INSERT INTO ptm_system_config (config_key, config_value, config_name, config_type, remark, is_system, description) VALUES
('system.refactor.user_data_source', 'completed', '用户端数据源重构状态', 'string', '标记用户端数据源重构完成状态', 1, '用于标识用户端数据源重构的完成状态');
