package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.PhotoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户照片控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Tag(name = "用户照片接口", description = "用户照片相关接口")
public class UserPhotoController {

    @Autowired
    private PhotoService photoService;

    /**
     * 获取用户照片列表
     */
    @GetMapping("/{userId}/photos")
    @Operation(summary = "获取用户照片列表", description = "分页获取指定用户的照片列表")
    public ApiResponse<IPage<PhotoDTO>> getUserPhotos(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "照片类型：public-公开，private-私密，all-全部") @RequestParam(required = false, defaultValue = "all") String type,
            HttpServletRequest request) {

        log.info("获取用户照片列表: userId={}, page={}, size={}, type={}", userId, page, size, type);

        // 获取当前用户ID
        Long currentUserId = SecurityUtil.getCurrentUserId();
        log.info("当前用户ID: {}", currentUserId);

        // 如果未登录，使用0作为当前用户ID
        if (currentUserId == null) {
            currentUserId = 0L;
        }

        // 打印请求头信息，用于调试
        log.info("请求头信息:");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            log.info("  {} = {}", headerName, request.getHeader(headerName));
        }

        // 调用服务获取照片列表
        IPage<PhotoDTO> photoPage = photoService.getPhotoPage(page, size, userId, currentUserId);

        return ApiResponse.success(photoPage);
    }

    /**
     * 获取用户收藏的照片列表
     */
    @GetMapping("/{userId}/collections")
    @Operation(summary = "获取用户收藏的照片列表", description = "分页获取指定用户收藏的照片列表")
    public ApiResponse<IPage<PhotoDTO>> getUserCollections(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {

        log.info("获取用户收藏的照片列表: userId={}, page={}, size={}", userId, page, size);

        // 获取当前用户ID
        Long currentUserId = SecurityUtil.getCurrentUserId();
        log.info("当前用户ID: {}", currentUserId);

        // 打印请求头信息，用于调试
        log.info("请求头信息:");
        java.util.Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            log.info("  {} = {}", headerName, request.getHeader(headerName));
        }

        // 如果查看的是当前用户的收藏，直接调用服务
        if (currentUserId != null && userId.equals(currentUserId)) {
            IPage<PhotoDTO> photoPage = photoService.getUserCollections(page, size, userId);
            return ApiResponse.success(photoPage);
        }

        // 如果查看的是其他用户的收藏，需要检查权限
        // 目前简单处理，只返回空结果
        // 实际应用中可能需要根据业务需求调整
        IPage<PhotoDTO> emptyPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page, size);
        return ApiResponse.success(emptyPage);
    }
}
