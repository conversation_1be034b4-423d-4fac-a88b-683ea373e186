<template>
  <div>
    <!-- 移动端底部标签栏 -->
    <van-tabbar v-if="isMobile && showTabBar" route fixed placeholder @change="handleTabChange">
      <van-tabbar-item to="/" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/explore" icon="fire-o">发现</van-tabbar-item>
      <van-tabbar-item to="/upload" icon="plus">上传</van-tabbar-item>
      <van-tabbar-item to="/notifications" icon="chat-o" :badge="unreadCount > 0 ? unreadCount : ''">消息</van-tabbar-item>
      <van-tabbar-item name="profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>

    <!-- 桌面版底部 -->
    <footer v-if="!isMobile" class="desktop-footer">
      <div class="footer-container">
        <div class="footer-section">
          <h3 class="footer-title">PhotoTagMoment</h3>
          <p class="footer-description">分享你的精彩瞬间</p>
        </div>
        <div class="footer-section">
          <h4 class="footer-subtitle">关于我们</h4>
          <ul class="footer-links">
            <li><a href="#">关于我们</a></li>
            <li><a href="#">联系我们</a></li>
            <li><a href="#">加入我们</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4 class="footer-subtitle">帮助中心</h4>
          <ul class="footer-links">
            <li><a href="#">使用指南</a></li>
            <li><a href="#">常见问题</a></li>
            <li><a href="#">意见反馈</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h4 class="footer-subtitle">关注我们</h4>
          <div class="social-links">
            <a href="#" class="social-link">
              <van-icon name="wechat" size="24" />
            </a>
            <a href="#" class="social-link">
              <van-icon name="weibo" size="24" />
            </a>
            <a href="#" class="social-link">
              <van-icon name="friends-o" size="24" />
            </a>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>© {{ currentYear }} PhotoTagMoment. 保留所有权利</p>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';

const props = defineProps({
  showTabBar: {
    type: Boolean,
    default: true
  }
});

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 未读消息数量
const unreadCount = computed(() => userStore.unreadCount);

// 是否为移动设备
const isMobile = ref(false);

// 当前年份
const currentYear = computed(() => new Date().getFullYear());

// 检测设备类型
const checkDeviceType = () => {
  isMobile.value = window.innerWidth < 768;
};

// 监听窗口大小变化
onMounted(() => {
  checkDeviceType();
  window.addEventListener('resize', checkDeviceType);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkDeviceType);
});

// 处理标签栏切换
const handleTabChange = (name: string | number) => {
  // 如果点击的是"我的"标签
  if (name === 'profile') {
    // 检查用户是否已登录
    if (!userStore.isLoggedIn) {
      // 如果未登录，跳转到登录页面
      router.push({
        path: '/auth/login',
        query: { redirect: '/user/profile' }
      });
      return false; // 阻止默认导航
    } else {
      // 如果已登录，正常导航到个人页面
      router.push('/user/profile');
    }
  }
};
</script>

<style lang="scss" scoped>
.desktop-footer {
  background-color: #f9fafb;
  padding: 3rem 0 1rem;
  margin-top: 2rem;

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 2rem;
  }

  .footer-section {
    flex: 1;
    min-width: 200px;
  }

  .footer-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
  }

  .footer-description {
    color: #666;
    margin-bottom: 1rem;
  }

  .footer-subtitle {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: #333;
  }

  .footer-links {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 0.5rem;
    }

    a {
      color: #666;
      text-decoration: none;
      transition: color 0.2s;

      &:hover {
        color: #3b82f6;
      }
    }
  }

  .social-links {
    display: flex;
    gap: 1rem;

    .social-link {
      color: #666;
      transition: color 0.2s;

      &:hover {
        color: #3b82f6;
      }
    }
  }

  .footer-bottom {
    max-width: 1200px;
    margin: 2rem auto 0;
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    text-align: center;
    color: #9ca3af;
    font-size: 0.875rem;
  }
}
</style>
