-- 创建照片审核记录表
CREATE TABLE IF NOT EXISTS ptm_photo_audit (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '审核记录ID',
    photo_id BIGINT NOT NULL COMMENT '照片ID',
    audit_type TINYINT NOT NULL COMMENT '审核类型: 0-自动审核, 1-人工审核',
    audit_result TINYINT NOT NULL COMMENT '审核结果: 0-待审核, 1-通过, 2-拒绝',
    reject_reason VARCHAR(255) DEFAULT NULL COMMENT '拒绝原因',
    auditor_id BIGINT DEFAULT NULL COMMENT '审核人ID（人工审核时）',
    audit_time DATETIME NOT NULL COMMENT '审核时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_photo_id (photo_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片审核记录表';

-- 确保 ptm_photo 表中有必要的状态字段
-- 检查 status 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'status';

-- 如果 status 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN status TINYINT NOT NULL DEFAULT 0 COMMENT \'状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除\'',
    'SELECT \'status column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 reject_reason 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'reject_reason';

-- 如果 reject_reason 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN reject_reason VARCHAR(255) DEFAULT NULL COMMENT \'拒绝原因\'',
    'SELECT \'reject_reason column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
