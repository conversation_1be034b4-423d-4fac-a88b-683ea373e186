<template>
  <div class="login-container">
    <div class="login-header">
      <h1 class="login-title">PhotoTagMoment</h1>
      <p class="login-subtitle">分享你的精彩瞬间</p>
    </div>

    <div class="login-form">
      <div class="login-tabs">
        <div
          :class="['tab-item', { active: activeTab === 'password' }]"
          @click="activeTab = 'password'"
        >
          密码登录
        </div>
        <div
          :class="['tab-item', { active: activeTab === 'sms' }]"
          @click="activeTab = 'sms'"
        >
          短信登录
        </div>
        <div
          :class="['tab-item', { active: activeTab === 'wechat' }]"
          @click="activeTab = 'wechat'"
        >
          微信登录
        </div>
      </div>

      <!-- 密码登录 -->
      <div v-if="activeTab === 'password'" class="login-form-content">
        <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef">
          <el-form-item prop="username">
            <el-input
              v-model="passwordForm.username"
              placeholder="用户名/手机号"
              prefix-icon="el-icon-user"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="passwordForm.password"
              type="password"
              placeholder="密码"
              prefix-icon="el-icon-lock"
              show-password
            ></el-input>
          </el-form-item>
          <div class="form-actions">
            <el-checkbox v-model="passwordForm.remember">记住我</el-checkbox>
            <el-button type="text" @click="forgotPassword">忘记密码？</el-button>
          </div>
          <el-button type="primary" class="login-button" @click="handlePasswordLogin" :loading="loading">
            登录
          </el-button>
        </el-form>
      </div>

      <!-- 短信登录 -->
      <div v-if="activeTab === 'sms'" class="login-form-content">
        <PhoneLogin @login-success="onLoginSuccess" />
      </div>

      <!-- 微信登录 -->
      <div v-if="activeTab === 'wechat'" class="login-form-content wechat-login">
        <WechatLogin @login-success="onLoginSuccess" />
      </div>

      <div class="register-link">
        还没有账号？<el-button type="text" @click="goToRegister">立即注册</el-button>
      </div>
    </div>

    <div class="login-footer">
      <p>© {{ new Date().getFullYear() }} PhotoTagMoment. All Rights Reserved.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import PhoneLogin from '@/components/user/PhoneLogin.vue'
import WechatLogin from '@/components/user/WechatLogin.vue'
import { login as authLogin } from '@/api/auth'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 登录方式
const activeTab = ref<string>('password')

// 加载状态
const loading = ref<boolean>(false)

// 密码登录表单
const passwordFormRef = ref<any>(null)
interface PasswordForm {
  username: string
  password: string
  remember: boolean
}
const passwordForm = reactive<PasswordForm>({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const passwordRules = {
  username: [
    { required: true, message: '请输入用户名/手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ]
}

// 生命周期钩子
onMounted(() => {
  // 如果已经登录，跳转到首页
  if (userStore.isLoggedIn) {
    const redirect = (route.query.redirect as string) || '/'
    router.replace(redirect)
  }
})

// 密码登录
const handlePasswordLogin = async (): Promise<void> => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true
        console.log('提交登录表单:', passwordForm);

        // 使用导入的authLogin函数，确保使用统一的登录路径
        console.log('使用auth.ts中的login函数，路径为/auth/login');

        const res = await authLogin({
          username: passwordForm.username,
          password: passwordForm.password
        });

        console.log('登录响应:', res);

        if (res && res.code === 200 && res.data) {
          console.log('登录成功，获取到token:', res.data.token);

          // 保存登录信息
          userStore.setToken(res.data.token);
          if (res.data.user) {
            userStore.setUser(res.data.user);
          }

          ElMessage.success('登录成功');
          const redirect = (route.query.redirect as string) || '/';
          router.replace(redirect);
        } else {
          console.error('登录失败，响应:', res);
          ElMessage.error(res && res.message ? res.message : '登录失败，请检查用户名和密码');
        }
      } catch (error) {
        console.error('登录失败', error);

        if (error instanceof Error) {
          console.error('错误详情:', error.message);
          console.error('错误堆栈:', error.stack);
        }

        ElMessage.error('登录失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    }
  })
}

// 登录成功回调
const onLoginSuccess = (): void => {
  ElMessage.success('登录成功')
  const redirect = (route.query.redirect as string) || '/'
  router.replace(redirect)
}

// 忘记密码
const forgotPassword = (): void => {
  router.push('/auth/forgot-password')
}

// 跳转到注册页
const goToRegister = (): void => {
  router.push('/auth/register')
}
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 40px;
}

.login-title {
  font-size: 36px;
  color: #409eff;
  margin-bottom: 10px;
}

.login-subtitle {
  font-size: 16px;
  color: #666;
}

.login-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.login-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  cursor: pointer;
  color: #666;
  transition: all 0.3s;
}

.tab-item.active {
  color: #409eff;
  border-bottom: 2px solid #409eff;
}

.login-form-content {
  margin-top: 20px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
}

.register-link {
  text-align: center;
  margin-top: 20px;
}

.login-footer {
  margin-top: auto;
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form {
    padding: 20px;
  }

  .login-title {
    font-size: 28px;
  }
}
</style>
