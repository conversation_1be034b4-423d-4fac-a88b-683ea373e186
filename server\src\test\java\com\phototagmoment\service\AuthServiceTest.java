package com.phototagmoment.service;

import com.phototagmoment.dto.AuthLoginDTO;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 第三方登录服务测试类
 */
@Slf4j
@SpringBootTest
public class AuthServiceTest {

    @Autowired
    private AuthService authService;

    /**
     * 测试获取QQ授权URL
     */
    @Test
    public void testGetQQAuthUrl() {
        // 生成随机状态参数
        String state = java.util.UUID.randomUUID().toString();
        
        // 获取授权URL
        AuthLoginDTO authLoginDTO = authService.getAuthUrl("qq", state);
        
        // 验证结果
        assertNotNull(authLoginDTO);
        assertTrue(authLoginDTO.isSuccess());
        assertNotNull(authLoginDTO.getAuthUrl());
        assertNotNull(authLoginDTO.getState());
        assertEquals(state, authLoginDTO.getState());
        assertEquals("qq", authLoginDTO.getSource());
        
        log.info("QQ授权URL: {}", authLoginDTO.getAuthUrl());
        log.info("状态参数: {}", authLoginDTO.getState());
    }

    /**
     * 测试获取微信授权URL
     */
    @Test
    public void testGetWechatAuthUrl() {
        // 生成随机状态参数
        String state = java.util.UUID.randomUUID().toString();
        
        // 获取授权URL
        AuthLoginDTO authLoginDTO = authService.getAuthUrl("wechat", state);
        
        // 验证结果
        assertNotNull(authLoginDTO);
        
        if (authLoginDTO.isSuccess()) {
            assertNotNull(authLoginDTO.getAuthUrl());
            assertNotNull(authLoginDTO.getState());
            assertEquals(state, authLoginDTO.getState());
            assertEquals("wechat", authLoginDTO.getSource());
            
            log.info("微信授权URL: {}", authLoginDTO.getAuthUrl());
            log.info("状态参数: {}", authLoginDTO.getState());
        } else {
            log.info("微信登录未启用或配置不完整: {}", authLoginDTO.getErrorMsg());
        }
    }

    /**
     * 测试不支持的第三方平台
     */
    @Test
    public void testUnsupportedSource() {
        // 生成随机状态参数
        String state = java.util.UUID.randomUUID().toString();
        
        // 获取授权URL
        AuthLoginDTO authLoginDTO = authService.getAuthUrl("unsupported", state);
        
        // 验证结果
        assertNotNull(authLoginDTO);
        assertFalse(authLoginDTO.isSuccess());
        assertNotNull(authLoginDTO.getErrorMsg());
        
        log.info("错误信息: {}", authLoginDTO.getErrorMsg());
    }

    /**
     * 测试登录回调
     * 注意：此测试需要有效的授权码，通常需要手动获取
     * 因此默认跳过此测试
     */
    @Test
    public void testLogin() {
        // 创建授权回调对象
        AuthCallback callback = new AuthCallback();
        callback.setCode("test_code"); // 替换为有效的授权码
        callback.setState("test_state"); // 替换为有效的状态参数
        
        // 处理登录
        AuthLoginDTO loginResult = authService.login("qq", callback);
        
        // 验证结果
        assertNotNull(loginResult);
        
        if (loginResult.isSuccess()) {
            assertNotNull(loginResult.getOpenId());
            assertNotNull(loginResult.getAccessToken());
            assertTrue(loginResult.getExpiresIn() > 0);
            assertEquals("qq", loginResult.getSource());
            
            log.info("登录成功: openId={}, accessToken={}, expiresIn={}", 
                    loginResult.getOpenId(), loginResult.getAccessToken(), loginResult.getExpiresIn());
        } else {
            log.info("登录失败: {}", loginResult.getErrorMsg());
        }
    }
}
