package com.phototagmoment.service.parser;

import com.phototagmoment.entity.SensitiveWord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TXT文本敏感词解析器
 */
@Slf4j
@Component
public class TxtSensitiveWordParser implements SensitiveWordFileParser {

    private static final String SUPPORTED_EXTENSION = "txt";
    
    // 匹配格式：敏感词[类型:级别]
    private static final Pattern PATTERN = Pattern.compile("^(.+?)(?:\\[(.*?)(?::(\\d+))?\\])?$");

    @Override
    public List<SensitiveWord> parse(MultipartFile file) throws Exception {
        log.info("解析TXT文件: {}", file.getOriginalFilename());
        List<SensitiveWord> sensitiveWords = new ArrayList<>();
        
        // 从文件名中提取类型和级别信息
        String fileName = file.getOriginalFilename();
        String defaultType = "其他";
        int defaultLevel = 1;
        
        if (fileName != null) {
            // 从文件名中提取类型
            if (fileName.contains("政治")) {
                defaultType = "政治";
            } else if (fileName.contains("色情")) {
                defaultType = "色情";
            } else if (fileName.contains("暴力")) {
                defaultType = "暴力";
            } else if (fileName.contains("贪腐")) {
                defaultType = "贪腐";
            } else if (fileName.contains("毒品")) {
                defaultType = "毒品";
            } else if (fileName.contains("民族")) {
                defaultType = "民族";
            } else if (fileName.contains("宗教")) {
                defaultType = "宗教";
            } else if (fileName.contains("网络")) {
                defaultType = "网络";
            }
            
            // 从文件名中提取级别
            if (fileName.contains("高级") || fileName.contains("严重")) {
                defaultLevel = 3;
            } else if (fileName.contains("中级") || fileName.contains("中等")) {
                defaultLevel = 2;
            } else if (fileName.contains("低级") || fileName.contains("一般")) {
                defaultLevel = 1;
            }
        }
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }
                
                // 处理可能的分隔符（逗号、空格、制表符等）
                String[] lineWords = line.split("[,\\s\\t]+");
                for (String wordText : lineWords) {
                    wordText = wordText.trim();
                    if (wordText.isEmpty()) {
                        continue;
                    }
                    
                    try {
                        // 解析敏感词、类型和级别
                        Matcher matcher = PATTERN.matcher(wordText);
                        if (matcher.find()) {
                            String word = matcher.group(1).trim();
                            String type = matcher.group(2);
                            String levelStr = matcher.group(3);
                            
                            SensitiveWord sensitiveWord = new SensitiveWord();
                            sensitiveWord.setWord(word);
                            sensitiveWord.setType(type != null && !type.isEmpty() ? type : defaultType);
                            
                            int level = defaultLevel;
                            if (levelStr != null && !levelStr.isEmpty()) {
                                try {
                                    level = Integer.parseInt(levelStr);
                                } catch (NumberFormatException e) {
                                    // 使用默认级别
                                }
                            }
                            sensitiveWord.setLevel(level);
                            
                            sensitiveWord.setReplaceWord("");
                            sensitiveWord.setStatus(true);
                            sensitiveWord.setCreatedAt(LocalDateTime.now());
                            sensitiveWord.setUpdatedAt(LocalDateTime.now());
                            
                            sensitiveWords.add(sensitiveWord);
                        }
                    } catch (Exception e) {
                        log.error("解析TXT行数据失败: {}", e.getMessage(), e);
                    }
                }
            }
        }
        
        log.info("TXT文件解析完成，共解析出{}个敏感词", sensitiveWords.size());
        return sensitiveWords;
    }

    @Override
    public boolean supports(String fileExtension) {
        return SUPPORTED_EXTENSION.equalsIgnoreCase(fileExtension);
    }
}
