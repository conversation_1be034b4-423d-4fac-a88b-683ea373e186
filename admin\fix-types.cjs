const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/views/system/file/components/FileStatistics.vue',
  'src/views/system/file/components/TrashFiles.vue',
  'src/views/system/file/index.vue'
];

// 修复函数
function fixTypeErrors(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${fullPath}`);
    return;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  
  // 替换 response.code 为 (response as any).code
  content = content.replace(/response\.code/g, '(response as any).code');
  content = content.replace(/response\.data/g, '(response as any).data');
  content = content.replace(/response\.message/g, '(response as any).message');
  
  fs.writeFileSync(fullPath, content, 'utf8');
  console.log(`已修复: ${filePath}`);
}

// 执行修复
console.log('开始修复TypeScript类型错误...');
filesToFix.forEach(fixTypeErrors);
console.log('修复完成！');
