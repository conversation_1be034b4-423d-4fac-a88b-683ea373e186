# PhotoTagMoment 后台管理系统

这是 PhotoTagMoment 项目的后台管理系统，基于 Vue3 + TypeScript + Element Plus 开发。

## 项目说明

本项目是 PhotoTagMoment 的活跃后台管理系统，用于管理用户、照片、系统配置等功能。

## 技术栈

- Vue 3
- TypeScript
- Element Plus
- Axios
- Vue Router
- Pinia

## 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 项目结构

```
frontend-admin/
├── src/                    # 源代码
│   ├── api/                # API 请求
│   ├── assets/             # 静态资源
│   ├── components/         # 公共组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── index.html              # HTML 模板
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript 配置
└── vite.config.ts          # Vite 配置
```

## API 路径说明

所有 API 请求都应使用 `/api/admin/...` 前缀，与后端控制器路径保持一致。例如：

- 管理员登录: `/api/admin/system/login`
- 系统配置: `/api/admin/system/config/...`
- 用户管理: `/api/admin/user/...`

## 注意事项

1. 本项目是当前活跃的后台管理系统，旧版本的 `admin` 目录已被归档到 `archive/admin-old` 目录。
2. 所有新功能开发和维护应在本项目中进行。
3. API 请求路径必须与后端控制器路径保持一致，使用 `/api/admin/...` 前缀。
