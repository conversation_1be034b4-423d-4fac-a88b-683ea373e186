package com.phototagmoment.service;

import com.phototagmoment.dto.LoginDTO;
import com.phototagmoment.dto.RegisterDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.vo.TokenVO;
import com.phototagmoment.vo.UserVO;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 用户ID
     */
    Long register(RegisterDTO registerDTO);

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return Token信息
     */
    TokenVO login(LoginDTO loginDTO);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User getUserByUsername(String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    User getUserByEmail(String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    User getUserByPhone(String phone);

    /**
     * 根据昵称查询用户
     *
     * @param nickname 昵称
     * @return 用户信息
     */
    User getUserByNickname(String nickname);

    /**
     * 根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    User getUserById(Long id);

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    UserVO getCurrentUser();

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUser(User user);

    /**
     * 更新用户密码
     *
     * @param userId      用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean updatePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 重置密码
     *
     * @param email    邮箱
     * @param password 新密码
     * @return 是否成功
     */
    boolean resetPassword(String email, String password);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean checkUsernameExists(String username);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    boolean checkEmailExists(String email);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @return 是否存在
     */
    boolean checkPhoneExists(String phone);

    /**
     * 用户登出
     *
     * @return 是否成功
     */
    boolean logout();

    /**
     * 将User转换为UserVO
     *
     * @param user 用户信息
     * @return 用户VO
     */
    UserVO convertToUserVO(User user);

    /**
     * 根据微信OpenID查询用户
     *
     * @param openId 微信OpenID
     * @return 用户信息
     */
    User findByWechatOpenId(String openId);

    /**
     * 根据微信UnionID查询用户
     *
     * @param unionId 微信UnionID
     * @return 用户信息
     */
    User findByWechatUnionId(String unionId);

    /**
     * 手机号登录
     *
     * @param phone 手机号
     * @param code  验证码
     * @return Token信息
     */
    TokenVO loginByPhone(String phone, String code);

    /**
     * 保存用户
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean save(User user);

    /**
     * 根据ID更新用户
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateById(User user);
}
