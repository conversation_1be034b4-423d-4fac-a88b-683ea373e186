<template>
  <div class="qq-login-container">
    <div v-if="!isMobile" class="qq-login-button" @click="handleQQLogin">
      <img src="@/assets/images/qq-logo.png" alt="QQ登录" class="qq-logo" />
      <span>QQ登录</span>
    </div>
    <div v-else class="qq-login-button mobile" @click="handleQQLogin">
      <img src="@/assets/images/qq-logo.png" alt="QQ登录" class="qq-logo" />
      <span>QQ登录</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getQQAuthUrl, ApiResponse } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { showToast, showLoadingToast, closeToast } from 'vant'

const router = useRouter()
const userStore = useUserStore()

// 是否为移动设备
const isMobile = ref(false)
// 是否正在处理登录
const isLoggingIn = ref(false)

// 检测设备类型
onMounted(() => {
  isMobile.value = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
})

// 处理QQ登录
const handleQQLogin = async () => {
  // 防止重复点击
  if (isLoggingIn.value) {
    return
  }

  try {
    isLoggingIn.value = true
    showLoadingToast({
      message: '正在获取QQ授权...',
      forbidClick: true,
      duration: 0
    })

    // 生成随机状态码，防止CSRF攻击
    const state = generateRandomState()

    // 获取QQ授权URL
    const res = await getQQAuthUrl(state)
    closeToast()

    if (res && res.code === 200 && res.data && res.data.authUrl) {
      // 保存状态，用于回调验证
      localStorage.setItem('qq_login_state', res.data.state)

      // 记录登录时间
      localStorage.setItem('qq_login_timestamp', Date.now().toString())

      console.log('获取QQ授权URL成功，准备跳转...')

      // 跳转到QQ授权页面
      window.location.href = res.data.authUrl
    } else {
      showToast({
        message: '获取QQ授权URL失败',
        type: 'fail'
      })
      console.error('获取QQ授权URL失败', res)
    }
  } catch (error) {
    closeToast()
    console.error('QQ登录失败', error)
    showToast({
      message: 'QQ登录失败，请稍后再试',
      type: 'fail'
    })
  } finally {
    isLoggingIn.value = false
  }
}

// 生成随机状态码
const generateRandomState = (): string => {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15)
}
</script>

<style scoped>
.qq-login-container {
  margin: 10px 0;
}

.qq-login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  background-color: #12b7f5;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.qq-login-button:hover {
  background-color: #0e9fd9;
}

.qq-login-button.mobile {
  width: 100%;
  padding: 12px 15px;
}

.qq-logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
</style>
