package com.phototagmoment.config;

import com.phototagmoment.mapper.AdminMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.security.JwtAuthenticationFilter;
import com.phototagmoment.security.JwtAuthenticationEntryPoint;
import com.phototagmoment.security.JwtAccessDeniedHandler;
import com.phototagmoment.security.CustomUserDetailsService;
import com.phototagmoment.security.JwtTokenProvider;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security配置
 */
@Slf4j
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAccessDeniedHandler jwtAccessDeniedHandler;
    private final UserMapper userMapper;
    private final AdminMapper adminMapper;

    // 使用 @Autowired 注入 JwtTokenProvider
    @org.springframework.beans.factory.annotation.Autowired
    private JwtTokenProvider jwtTokenProvider;

    public SecurityConfig(JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint,
                          JwtAccessDeniedHandler jwtAccessDeniedHandler,
                          UserMapper userMapper,
                          AdminMapper adminMapper) {
        this.jwtAuthenticationEntryPoint = jwtAuthenticationEntryPoint;
        this.jwtAccessDeniedHandler = jwtAccessDeniedHandler;
        this.userMapper = userMapper;
        this.adminMapper = adminMapper;
    }

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        log.info("创建密码编码器: BCryptPasswordEncoder");
        return new BCryptPasswordEncoder();
    }

    /**
     * 认证管理器
     *
     * 注意：我们完全禁用了Spring Security的认证管理器，以避免循环依赖问题
     * 所有认证都通过自定义的方式进行，不使用Spring Security的认证机制
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        log.info("创建自定义的AuthenticationManager，完全禁用Spring Security的认证机制");

        // 返回一个不执行任何操作的AuthenticationManager
        // 这个实现永远不会被调用，因为我们使用自定义的认证方式
        return authentication -> {
            log.warn("尝试使用已禁用的认证管理器，这是一个错误");
            throw new UnsupportedOperationException("认证管理器已禁用，请使用自定义认证方法");
        };
    }

    /**
     * 用户详情服务
     */
    @Bean
    public UserDetailsService userDetailsService() {
        return new CustomUserDetailsService();
    }

    /**
     * 安全过滤器链
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        log.info("配置安全过滤器链");

        http
            // 禁用CSRF
            .csrf().disable()
            // 禁用HTTP基本认证
            .httpBasic().disable()
            // 配置表单登录
            .formLogin()
            .loginPage("/auth/login")
            .loginProcessingUrl("/auth/login")
            .permitAll()
            .and()
            // 禁用默认登出
            .logout().disable()
            // 允许匿名用户
            .anonymous().and()
            // 配置会话管理 - 使用无状态会话，不创建HttpSession
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS) // 无状态会话，适用于JWT认证
            .and()
            // 异常处理
            .exceptionHandling()
            .authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .accessDeniedHandler(jwtAccessDeniedHandler)
            .and()
            // 请求授权
            .authorizeRequests()
            // 明确放行后台管理相关接口
            .antMatchers("/api/admin/system/permission/**").permitAll()
            .antMatchers("/api/admin/system/role/**").permitAll()
            .antMatchers("/api/admin/system/sensitive-word/**").permitAll()
            .antMatchers("/api/admin/system/admin/**").permitAll()
            .antMatchers("/api/admin/system/log/**").permitAll()
            .antMatchers("/api/admin/file/**").permitAll() // 添加后台文件上传接口
            .antMatchers("/api/admin/file-manage/**").permitAll() // 添加后台文件管理接口
            .antMatchers("/api/admin/file-upload-config/**").permitAll() // 添加后台文件上传配置接口
            // 同时放行不带/api前缀的路径，以兼容可能的路径映射
            .antMatchers("/admin/system/permission/**").permitAll()
            .antMatchers("/admin/system/role/**").permitAll()
            .antMatchers("/admin/system/system-role/**").permitAll() // 添加新的路径
            .antMatchers("/admin/system/sensitive-word/**").permitAll()
            .antMatchers("/admin/system/admin/**").permitAll()
            .antMatchers("/admin/system/log/**").permitAll()
            .antMatchers("/admin/file/**").permitAll() // 添加后台文件上传接口
            .antMatchers("/admin/file-manage/**").permitAll() // 添加后台文件管理接口
            .antMatchers("/admin/file-upload-config/**").permitAll() // 添加后台文件上传配置接口
            // 允许所有人访问的路径（包括匿名用户）
            .antMatchers(
                "/auth/**",
                "/admin/system/login",
                "/admin/system/info", // 添加管理员信息接口
//                "/admin/system/emergency-reset", // 添加紧急密码重置接口
//                "/admin/system/emergency-token", // 添加紧急令牌接口
//                "/admin/system/config/batch", // 添加系统配置批量接口
//                "/admin/system/config/**", // 添加系统配置相关接口
//                "/admin/password/reset", // 添加密码重置接口
//                "/admin/password/reset-page", // 添加密码重置页面
//                "/static/**", // 添加静态资源
                "/recommendation/**", // 允许匿名访问所有推荐接口
                "/search/**", // 允许匿名访问所有搜索接口
                "/photo/list/**", // 允许匿名访问照片列表
                "/photo/detail/**", // 允许匿名访问照片详情
                "/photo/comments/**", // 允许匿名访问照片评论
                "/photo-notes/list/**", // 允许匿名访问照片笔记列表
                "/photo-notes/detail/**", // 允许匿名访问照片笔记详情
                "/photo-notes/*/comments/**", // 允许匿名访问照片笔记评论
                "/tag/**", // 允许匿名访问标签相关接口
                "/user/profile/**", // 允许匿名访问用户资料
//                "/user/*/photos/**", // 允许匿名访问用户照片列表
//                "/user/*/collections/**", // 允许匿名访问用户收藏列表
//                "/qq/auth-url", // 允许匿名访问QQ授权URL
                "/photo/group/**", // 允许匿名访问照片分组
                "/dict/**", // 允许匿名访问字典数据
                "/notification/**", // 允许匿名访问通知相关接口
                "/v3/api-docs/**",
                "/swagger-ui/**",
                "/swagger-ui.html",
                "/swagger-resources/**",
                "/webjars/**",
                "/api/doc.html",
                "/doc.html"
            ).permitAll()
            // 其他所有请求需要认证
            .anyRequest().authenticated();

        log.info("添加JWT过滤器");
        // 创建并添加JWT过滤器 - 确保只添加一次
        JwtAuthenticationFilter jwtAuthenticationFilter = new JwtAuthenticationFilter(userMapper, adminMapper);
        jwtAuthenticationFilter.setJwtTokenProvider(jwtTokenProvider);

        // 确保JWT过滤器在UsernamePasswordAuthenticationFilter之前执行，且只添加一次
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        log.info("JWT过滤器添加完成");

        log.info("安全过滤器链配置完成");
        return http.build();
    }
}
