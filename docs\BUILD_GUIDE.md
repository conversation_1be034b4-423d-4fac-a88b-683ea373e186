# PhotoTagMoment 多环境打包指南

本指南介绍如何使用PhotoTagMoment项目的多环境打包脚本。

## 环境配置

项目支持三种环境配置：

### 开发环境 (dev)
- **端口**: 8081
- **数据库**: 本地MySQL (端口31160)
- **Redis**: 本地Redis (端口6379)
- **缓存**: 简单内存缓存
- **日志级别**: DEBUG
- **API文档**: 启用，无需认证
- **配置文件**: `application-dev.yml`

### 测试环境 (test)
- **端口**: 8082
- **数据库**: 测试MySQL (端口3306)
- **Redis**: 测试Redis (端口6379)
- **缓存**: Redis缓存
- **日志级别**: INFO
- **API文档**: 启用，需要认证
- **配置文件**: `application-test.yml`

### 生产环境 (prod)
- **端口**: 8081
- **数据库**: 生产MySQL (端口13306)
- **Redis**: 生产Redis (端口16379)
- **缓存**: Redis缓存
- **日志级别**: WARN
- **API文档**: 关闭
- **配置文件**: `application-prod.yml`

## 打包脚本

### 1. 基础多环境脚本

#### Windows批处理脚本 (`build.bat`)
```bash
# 默认开发环境
.\build.bat

# 指定环境
.\build.bat dev
.\build.bat test
.\build.bat prod
```

#### PowerShell脚本 (`build.ps1`)
```powershell
# 默认开发环境
.\build.ps1

# 指定环境
.\build.ps1 dev
.\build.ps1 test
.\build.ps1 prod
```

### 2. 高级多环境脚本

#### Windows高级脚本 (`build-advanced.bat`)
```bash
# 基本用法
.\build-advanced.bat [环境] [是否运行测试]

# 示例
.\build-advanced.bat prod false    # 生产环境，跳过测试
.\build-advanced.bat test true     # 测试环境，运行测试
.\build-advanced.bat               # 默认开发环境，跳过测试
```

#### PowerShell高级脚本 (`build-advanced.ps1`)
```powershell
# 基本用法
.\build-advanced.ps1 [-Environment <env>] [-RunTests] [-Clean] [-Verbose]

# 示例
.\build-advanced.ps1 -Environment prod                    # 生产环境
.\build-advanced.ps1 -Environment test -RunTests          # 测试环境并运行测试
.\build-advanced.ps1 -Environment dev -Verbose            # 开发环境，详细输出
.\build-advanced.ps1 -Environment prod -RunTests -Verbose # 生产环境，运行测试，详细输出
```

## 脚本功能特性

### 基础脚本功能
- ✅ 自动设置Java 17环境
- ✅ 多环境配置支持
- ✅ 错误处理和验证
- ✅ 跳过测试快速打包
- ✅ 显示JAR文件信息
- ✅ 环境特定的运行说明

### 高级脚本功能
- ✅ 所有基础功能
- ✅ 可选运行测试
- ✅ Java和Maven环境检查
- ✅ 详细输出控制
- ✅ 清理构建选项
- ✅ 环境特定JAR文件生成
- ✅ 环境特性说明

## 输出文件

打包完成后会生成以下文件：

```
backend/target/
├── phototagmoment-0.0.1-SNAPSHOT.jar           # 标准JAR文件
├── phototagmoment-{环境}-0.0.1-SNAPSHOT.jar     # 环境特定JAR文件
├── classes/                                     # 编译后的类文件
└── surefire-reports/                           # 测试报告（如果运行了测试）
```

## 运行应用

### 方式1：使用标准JAR
```bash
cd backend
java -jar target/phototagmoment-0.0.1-SNAPSHOT.jar
```

### 方式2：使用环境特定JAR
```bash
cd backend
java -jar target/phototagmoment-dev-0.0.1-SNAPSHOT.jar
```

### 方式3：指定环境配置
```bash
cd backend
java -jar -Dspring.profiles.active=prod target/phototagmoment-0.0.1-SNAPSHOT.jar
```

### 方式4：使用JVM参数
```bash
cd backend
java -Xms512m -Xmx2g -XX:+UseG1GC -Dspring.profiles.active=prod -jar target/phototagmoment-0.0.1-SNAPSHOT.jar
```

## 环境变量

生产环境支持以下环境变量：

```bash
# 数据库配置
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# Redis配置
REDIS_HOST=your_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret

# 加密配置
ENCRYPTION_USER_DATA_KEY=your_encryption_key
ENCRYPTION_PHOTO_KEY=your_photo_key
ENCRYPTION_RSA_PUBLIC_KEY=your_rsa_public_key
ENCRYPTION_RSA_PRIVATE_KEY=your_rsa_private_key

# API文档配置
API_DOC_USERNAME=admin
API_DOC_PASSWORD=your_secure_password
```

## 故障排除

### 常见问题

1. **Java版本错误**
   ```
   错误: 无效的目标发行版: 17
   ```
   **解决方案**: 确保JAVA_HOME指向Java 17安装目录

2. **Maven未找到**
   ```
   错误: Maven环境未正确配置
   ```
   **解决方案**: 确保Maven已安装并添加到PATH环境变量

3. **配置文件不存在**
   ```
   警告: 环境配置文件不存在: application-test.yml
   ```
   **解决方案**: 检查配置文件是否存在于`backend/src/main/resources/`目录

4. **测试失败**
   ```
   错误: 测试失败
   ```
   **解决方案**: 使用`-DskipTests`参数跳过测试，或修复测试问题

### 日志查看

应用运行时的日志文件位置：
- 开发环境: `logs/phototagmoment-dev.log`
- 测试环境: `logs/phototagmoment-test.log`
- 生产环境: `/var/log/phototagmoment/application.log`

## API文档访问

根据环境配置，API文档访问地址：

- **开发环境**: http://localhost:8081/api/doc.html
- **测试环境**: http://localhost:8082/api/doc.html (需要认证)
- **生产环境**: 已关闭

## 性能建议

### 开发环境
- 使用简单内存缓存以便调试
- 启用详细日志便于开发

### 测试环境
- 使用Redis缓存模拟生产环境
- 适中的日志级别

### 生产环境
- 优化JVM参数
- 使用Redis缓存
- 最小化日志输出
- 启用安全特性
