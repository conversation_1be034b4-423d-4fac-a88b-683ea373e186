-- 创建照片笔记举报表
CREATE TABLE ptm_photo_note_report (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '举报ID',
    note_id BIGINT NOT NULL COMMENT '照片笔记ID',
    report_user_id BIGINT NOT NULL COMMENT '举报用户ID',
    reason VARCHAR(500) NOT NULL COMMENT '举报原因',
    description TEXT COMMENT '详细描述',
    report_type VARCHAR(50) NOT NULL DEFAULT 'other' COMMENT '举报类型：illegal-违法违规, pornographic-色情低俗, violent-暴力血腥, spam-垃圾广告, harassment-恶意骚扰, other-其他',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '处理状态：0-待处理, 1-已处理, 2-已忽略',
    process_result TEXT COMMENT '处理结果',
    process_user_id BIGINT COMMENT '处理人ID',
    process_time DATETIME COMMENT '处理时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除, 1-已删除',
    
    INDEX idx_note_id (note_id),
    INDEX idx_report_user_id (report_user_id),
    INDEX idx_status (status),
    INDEX idx_report_type (report_type),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_note_user (note_id, report_user_id, is_deleted) COMMENT '同一用户对同一照片笔记只能举报一次'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='照片笔记举报表';

-- 添加外键约束
ALTER TABLE ptm_photo_note_report 
ADD CONSTRAINT fk_report_note_id 
FOREIGN KEY (note_id) REFERENCES ptm_photo_note(id) ON DELETE CASCADE;

ALTER TABLE ptm_photo_note_report 
ADD CONSTRAINT fk_report_user_id 
FOREIGN KEY (report_user_id) REFERENCES ptm_user(id) ON DELETE CASCADE;

ALTER TABLE ptm_photo_note_report 
ADD CONSTRAINT fk_process_user_id 
FOREIGN KEY (process_user_id) REFERENCES ptm_user(id) ON DELETE SET NULL;
