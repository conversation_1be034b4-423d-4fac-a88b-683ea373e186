package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 照片草稿实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ptm_photo_draft")
public class PhotoDraft implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 草稿ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 草稿标题
     */
    private String title;

    /**
     * 草稿描述
     */
    private String description;

    /**
     * 拍摄地点
     */
    private String location;

    /**
     * 标签，JSON格式
     */
    private String tags;

    /**
     * 提及用户，JSON格式
     */
    private String mentions;

    /**
     * 可见性: 0-私密, 1-公开, 2-好友可见
     */
    private Integer visibility;

    /**
     * 是否允许评论: 0-不允许, 1-允许
     */
    private Integer allowComment;

    /**
     * 是否允许下载: 0-不允许, 1-允许
     */
    private Integer allowDownload;

    /**
     * 临时文件路径，JSON格式
     */
    private String tempFilePaths;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    @TableLogic
    private Integer isDeleted;
}
