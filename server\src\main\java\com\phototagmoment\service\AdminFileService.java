package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.FileInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * 后台文件管理服务接口
 */
public interface AdminFileService {

    /**
     * 获取文件列表
     * @param page 分页参数
     * @param fileType 文件类型
     * @param uploaderId 上传者ID
     * @param keyword 关键词
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 文件列表
     */
    IPage<FileInfoDTO> getFileList(Page<FileInfoDTO> page, String fileType, Long uploaderId, 
                                   String keyword, String startDate, String endDate);

    /**
     * 获取文件详情
     * @param fileId 文件ID
     * @return 文件详情
     */
    FileInfoDTO getFileDetail(Long fileId);

    /**
     * 批量删除文件
     * @param fileIds 文件ID列表
     * @return 删除结果
     */
    Map<String, Object> batchDeleteFiles(List<Long> fileIds);

    /**
     * 重命名文件
     * @param fileId 文件ID
     * @param newName 新文件名
     * @return 是否成功
     */
    boolean renameFile(Long fileId, String newName);

    /**
     * 移动文件到回收站
     * @param fileId 文件ID
     * @return 是否成功
     */
    boolean moveToTrash(Long fileId);

    /**
     * 从回收站恢复文件
     * @param fileId 文件ID
     * @return 是否成功
     */
    boolean restoreFile(Long fileId);

    /**
     * 获取回收站文件列表
     * @param page 分页参数
     * @return 回收站文件列表
     */
    IPage<FileInfoDTO> getTrashFiles(Page<FileInfoDTO> page);

    /**
     * 清空回收站
     * @return 清空结果
     */
    Map<String, Object> clearTrash();

    /**
     * 获取文件统计信息
     * @return 统计信息
     */
    Map<String, Object> getFileStatistics();

    /**
     * 获取存储空间使用情况
     * @return 使用情况
     */
    Map<String, Object> getStorageUsage();

    /**
     * 获取文件类型分布统计
     * @return 类型分布
     */
    List<Map<String, Object>> getFileTypeDistribution();

    /**
     * 检查文件完整性
     * @param fileIds 文件ID列表（为空则检查所有文件）
     * @return 检查结果
     */
    Map<String, Object> checkFileIntegrity(List<Long> fileIds);

    /**
     * 生成文件访问链接
     * @param fileId 文件ID
     * @param expireSeconds 有效期（秒）
     * @return 访问链接信息
     */
    Map<String, Object> generateAccessUrl(Long fileId, Integer expireSeconds);

    /**
     * 批量移动文件
     * @param fileIds 文件ID列表
     * @param targetPath 目标路径
     * @return 移动结果
     */
    Map<String, Object> batchMoveFiles(List<Long> fileIds, String targetPath);

    /**
     * 搜索文件
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param fileType 文件类型
     * @param minSize 最小文件大小
     * @param maxSize 最大文件大小
     * @return 搜索结果
     */
    IPage<FileInfoDTO> searchFiles(Page<FileInfoDTO> page, String keyword, String fileType, 
                                   Long minSize, Long maxSize);

    /**
     * 记录文件上传
     * @param originalName 原始文件名
     * @param fileName 存储文件名
     * @param filePath 文件路径
     * @param fileUrl 文件URL
     * @param fileSize 文件大小
     * @param mimeType MIME类型
     * @param uploaderId 上传者ID
     * @param uploaderType 上传者类型
     * @param category 文件分类
     * @return 文件记录ID
     */
    Long recordFileUpload(String originalName, String fileName, String filePath, String fileUrl,
                         Long fileSize, String mimeType, Long uploaderId, String uploaderType, String category);

    /**
     * 更新文件访问记录
     * @param fileId 文件ID
     */
    void updateFileAccess(Long fileId);

    /**
     * 获取文件上传统计
     * @param days 统计天数
     * @return 上传统计
     */
    List<Map<String, Object>> getUploadStatistics(Integer days);

    /**
     * 获取热门文件
     * @param limit 数量限制
     * @return 热门文件列表
     */
    List<FileInfoDTO> getPopularFiles(Integer limit);

    /**
     * 获取最近上传的文件
     * @param limit 数量限制
     * @return 最近文件列表
     */
    List<FileInfoDTO> getRecentFiles(Integer limit);

    /**
     * 清理过期的临时文件
     * @return 清理结果
     */
    Map<String, Object> cleanExpiredTempFiles();

    /**
     * 同步文件存储状态
     * @return 同步结果
     */
    Map<String, Object> syncFileStorageStatus();
}
