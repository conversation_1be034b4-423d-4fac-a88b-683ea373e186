package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.TagDTO;

import java.util.List;

/**
 * 标签服务接口
 */
public interface TagService {

    /**
     * 获取标签详情
     *
     * @param tagName 标签名称
     * @return 标签详情
     */
    TagDTO getTagDetail(String tagName);

    /**
     * 获取标签相关内容
     *
     * @param tagName      标签名称
     * @param page         页码
     * @param size         每页大小
     * @param currentUserId 当前用户ID
     * @return 照片分页结果
     */
    IPage<PhotoDTO> getTagContent(String tagName, Integer page, Integer size, Long currentUserId);

    /**
     * 获取热门标签
     *
     * @param limit 数量限制
     * @return 热门标签列表
     */
    List<TagDTO> getHotTags(Integer limit);
}
