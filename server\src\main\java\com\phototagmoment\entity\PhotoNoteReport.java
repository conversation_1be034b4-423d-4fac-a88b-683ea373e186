package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 照片笔记举报实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ptm_photo_note_report")
public class PhotoNoteReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 举报ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 照片笔记ID
     */
    private Long noteId;

    /**
     * 举报用户ID
     */
    private Long reportUserId;

    /**
     * 举报原因
     */
    private String reason;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 举报类型：illegal-违法违规, pornographic-色情低俗, violent-暴力血腥, spam-垃圾广告, harassment-恶意骚扰, other-其他
     */
    private String reportType;

    /**
     * 处理状态：0-待处理, 1-已处理, 2-已忽略
     */
    private Integer status;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 处理人ID
     */
    private Long processUserId;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDeleted;
}
