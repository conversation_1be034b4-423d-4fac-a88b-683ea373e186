# PhotoTagMoment 用户前端

PhotoTagMoment 是一个照片分享社交网站的用户前端应用，基于 Vue 3 和 TypeScript 开发。

## 项目结构

```
src/
├── api/                  # API 调用，按领域组织
├── assets/               # 静态资源（图片、字体等）
├── components/           # 共享组件
│   ├── common/           # 通用 UI 组件
│   ├── layout/           # 布局组件
│   ├── photo/            # 照片相关组件
│   ├── user/             # 用户相关组件
│   └── notification/     # 通知组件
├── composables/          # Vue 3 组合式函数
├── router/               # 路由配置
├── stores/               # Pinia 状态管理
├── services/             # 服务（如 WebSocket）
├── utils/                # 工具函数
├── views/                # 页面组件
│   ├── home/             # 首页
│   ├── photo/            # 照片相关页面
│   ├── user/             # 用户相关页面
│   │   ├── profile/      # 个人中心
│   │   └── detail/       # 用户详情
│   ├── search/           # 搜索页面
│   ├── auth/             # 认证页面
│   │   ├── login/        # 登录
│   │   ├── register/     # 注册
│   │   └── forgot-password/ # 忘记密码
│   └── error/            # 错误页面
├── App.vue               # 根组件
└── main.ts               # 应用程序入口点
```

## 技术栈

- Vue 3
- TypeScript (100% TypeScript 代码库)
- Vant UI (移动端)
- Element Plus (桌面端)
- Pinia (状态管理)
- Vue Router
- Sass/SCSS
- Vite

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 代码规范

- 组件文件名：PascalCase（例如：PhotoCard.vue）
- 视图文件名：index.vue 或特定功能名称（例如：Detail.vue）
- API 和工具函数文件名：camelCase（例如：photoApi.ts, formatDate.ts）
- 常量：UPPER_SNAKE_CASE
- 变量和函数：camelCase
- 类型和接口：PascalCase

### 组件开发规范

1. 使用 Vue 3 组合式 API（`<script setup>` 语法）
2. 为所有 props 和函数参数添加类型定义
3. 使用 `defineProps` 和 `defineEmits` 定义组件接口
4. 使用 `ref` 和 `reactive` 管理状态
5. 使用 `computed` 派生状态
6. 使用 `watch` 和 `watchEffect` 处理副作用
7. 使用 `onMounted` 和 `onUnmounted` 处理生命周期

### 路由规范

1. 路由配置按功能模块组织
2. 使用嵌套路由表示功能层次
3. 使用路由元数据（meta）定义页面属性
4. 使用路由守卫处理权限控制

### 状态管理规范

1. 使用 Pinia 进行状态管理
2. 按领域划分 store
3. 使用 TypeScript 类型定义 store 状态和操作
4. 使用 `defineStore` 创建 store
5. 使用 `useStore` 在组件中访问 store

## 功能模块

- 用户认证（登录、注册、找回密码）
- 个人中心（个人资料、照片管理）
- 照片浏览与搜索
- 社交互动（关注、点赞、评论）
- 消息通知
- 设置

## 响应式设计

应用采用响应式设计，同时支持移动端和桌面端：

- 移动端：使用 Vant UI 组件库
- 桌面端：使用自定义样式和部分 Element Plus 组件

## TypeScript 支持

本项目已完全迁移到 TypeScript，提供以下优势：

- **类型安全**：编译时类型检查，减少运行时错误
- **更好的开发体验**：IDE 智能提示和自动补全
- **代码质量**：强类型约束提高代码可维护性
- **API 类型定义**：所有 API 函数都有完整的类型注解

详细的迁移文档请参考：[TypeScript 迁移文档](docs/typescript-migration.md)

## 注意事项

- 所有文件使用 UTF-8 编码
- 遵循 ESLint 和 Prettier 代码风格
- 添加适当的注释和文档
- 使用 TypeScript 类型定义
- 使用 Vue 3 组合式 API
- 项目已100%迁移到TypeScript，不再包含JavaScript文件
