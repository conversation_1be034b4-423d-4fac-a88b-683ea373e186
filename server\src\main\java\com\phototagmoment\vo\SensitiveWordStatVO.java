package com.phototagmoment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 敏感词统计VO
 */
@Data
@Schema(description = "敏感词统计信息")
public class SensitiveWordStatVO {

    /**
     * 敏感词总数
     */
    @Schema(description = "敏感词总数")
    private Integer total;

    /**
     * 启用的敏感词数量
     */
    @Schema(description = "启用的敏感词数量")
    private Integer enabledCount;

    /**
     * 禁用的敏感词数量
     */
    @Schema(description = "禁用的敏感词数量")
    private Integer disabledCount;

    /**
     * 按类型统计
     */
    @Schema(description = "按类型统计")
    private List<TypeStat> typeStats;

    /**
     * 按级别统计
     */
    @Schema(description = "按级别统计")
    private List<LevelStat> levelStats;

    /**
     * 类型统计
     */
    @Data
    @Schema(description = "类型统计")
    public static class TypeStat {
        /**
         * 类型
         */
        @Schema(description = "类型")
        private String type;

        /**
         * 数量
         */
        @Schema(description = "数量")
        private Integer count;
    }

    /**
     * 级别统计
     */
    @Data
    @Schema(description = "级别统计")
    public static class LevelStat {
        /**
         * 级别
         */
        @Schema(description = "级别")
        private Integer level;

        /**
         * 级别名称
         */
        @Schema(description = "级别名称")
        private String levelName;

        /**
         * 数量
         */
        @Schema(description = "数量")
        private Integer count;
    }
}
