-- 添加 file_size 字段到 ptm_photo 表
-- 检查 file_size 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'file_size';

-- 如果 file_size 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN file_size BIGINT NULL COMMENT \'文件大小(字节)\'',
    'SELECT \'file_size column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 original_filename 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'original_filename';

-- 如果 original_filename 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN original_filename VARCHAR(255) NULL COMMENT \'原始文件名\'',
    'SELECT \'original_filename column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
