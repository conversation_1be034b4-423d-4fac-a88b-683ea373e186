package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 实名认证实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ptm_identity_verification")
public class IdentityVerification implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 认证ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 身份证正面照片URL
     */
    private String idCardFrontUrl;

    /**
     * 身份证背面照片URL
     */
    private String idCardBackUrl;

    /**
     * 手持身份证照片URL
     */
    private String idCardHoldingUrl;

    /**
     * 人脸照片URL
     */
    private String faceImageUrl;

    /**
     * 认证方式：1-支付宝，2-微信，3-人脸识别，4-人工审核
     */
    private Integer verifyMethod;

    /**
     * 认证状态：0-待审核，1-已认证，2-认证失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 认证时间
     */
    private LocalDateTime verifiedAt;

    /**
     * 审核人ID
     */
    private Long reviewerId;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
