<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.UserInterestTagMapper">

    <!-- 批量插入或更新用户兴趣标签 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO ptm_user_interest (user_id, tag_name, interest_score, created_at, updated_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.tagName}, #{item.interestScore}, #{item.createdAt}, #{item.updatedAt})
        </foreach>
        ON DUPLICATE KEY UPDATE
        interest_score = VALUES(interest_score),
        updated_at = VALUES(updated_at)
    </insert>

    <!-- 删除用户的所有兴趣标签 -->
    <delete id="deleteByUserId">
        DELETE FROM ptm_user_interest WHERE user_id = #{userId}
    </delete>

</mapper>
