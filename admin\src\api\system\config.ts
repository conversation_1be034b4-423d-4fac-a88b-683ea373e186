import request from '@/utils/request'

// 系统配置数据接口
interface SystemConfigData {
  id?: number
  configKey: string
  configValue: string
  configName: string
  configType: string
  remark?: string
  isSystem?: boolean
  description?: string
  [key: string]: any
}

// 配置查询参数接口
interface ConfigListParams {
  page?: number
  pageSize?: number
  keyword?: string
  configType?: string
  isSystem?: boolean
  [key: string]: any
}

/**
 * 获取系统配置（批量）
 * @param keys 配置键数组
 * @returns 配置值对象
 */
export function getSystemConfig(keys: string[]) {
  return request({
    url: '/admin/system/config/batch',
    method: 'post',
    data: { keys }
  }).then((res: any) => {
    if (res.code === 200) {
      return res.data
    }
    return {}
  })
}

/**
 * 更新系统配置（批量）
 * @param data 配置数据对象
 * @returns 更新结果
 */
export function updateSystemConfig(data: Record<string, string>) {
  return request({
    url: '/admin/system/config/batch',
    method: 'put',
    data
  })
}

/**
 * 获取所有系统配置
 * @returns 所有配置列表
 */
export function getAllSystemConfig() {
  return request({
    url: '/admin/system/config/list',
    method: 'get'
  })
}

/**
 * 分页获取系统配置
 * @param params 查询参数
 * @returns 分页配置列表
 */
export function getSystemConfigPage(params: ConfigListParams) {
  return request({
    url: '/admin/system/config/page',
    method: 'get',
    params
  })
}

/**
 * 获取配置详情
 * @param id 配置ID
 * @returns 配置详情
 */
export function getConfigDetail(id: number) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'get'
  })
}

/**
 * 根据配置键获取配置值
 * @param configKey 配置键
 * @returns 配置值
 */
export function getConfigValue(configKey: string) {
  return request({
    url: `/admin/system/config/value/${configKey}`,
    method: 'get'
  })
}

/**
 * 添加配置
 * @param data 配置数据
 * @returns 添加结果
 */
export function addConfig(data: SystemConfigData) {
  return request({
    url: '/admin/system/config',
    method: 'post',
    data
  })
}

/**
 * 保存系统配置
 * @param data 配置数据
 * @returns 保存结果
 */
export function saveSystemConfig(data: SystemConfigData) {
  return request({
    url: '/admin/system/config',
    method: 'post',
    data
  })
}

/**
 * 更新配置
 * @param id 配置ID
 * @param data 配置数据
 * @returns 更新结果
 */
export function updateConfig(id: number, data: SystemConfigData) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'put',
    data
  })
}

/**
 * 更新配置值
 * @param configKey 配置键
 * @param configValue 配置值
 * @returns 更新结果
 */
export function updateConfigValue(configKey: string, configValue: string) {
  return request({
    url: '/admin/system/config/value',
    method: 'put',
    params: {
      configKey,
      configValue
    }
  })
}

/**
 * 删除配置
 * @param id 配置ID
 * @returns 删除结果
 */
export function deleteConfig(id: number) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'delete'
  })
}

/**
 * 刷新配置缓存
 * @returns 刷新结果
 */
export function refreshConfigCache() {
  return request({
    url: '/admin/system/config/refresh',
    method: 'post'
  })
}

// 导出类型定义
export type {
  SystemConfigData,
  ConfigListParams
}
