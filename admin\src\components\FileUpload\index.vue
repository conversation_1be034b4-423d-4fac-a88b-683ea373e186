<template>
  <div class="file-upload-container">
    <!-- 上传区域 -->
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :limit="limit"
      :accept="accept"
      :before-upload="beforeUpload"
      :on-success="onSuccess"
      :on-error="onError"
      :on-progress="onProgress"
      :on-exceed="onExceed"
      :on-remove="onRemove"
      :file-list="fileList"
      :auto-upload="autoUpload"
      :show-file-list="showFileList"
      :drag="drag"
      :disabled="disabled"
    >
      <template v-if="drag">
        <div class="upload-dragger">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <div class="el-upload__tip" v-if="tip">
            {{ tip }}
          </div>
        </div>
      </template>
      <template v-else>
        <el-button type="primary" :loading="uploading">
          <el-icon><upload-filled /></el-icon>
          {{ uploading ? '上传中...' : '选择文件' }}
        </el-button>
        <div class="upload-tip" v-if="tip">
          {{ tip }}
        </div>
      </template>
    </el-upload>

    <!-- 上传进度 -->
    <div v-if="showProgress && uploadProgress.length > 0" class="upload-progress">
      <div v-for="(progress, index) in uploadProgress" :key="index" class="progress-item">
        <div class="progress-info">
          <span class="file-name">{{ progress.fileName }}</span>
          <span class="progress-text">{{ progress.percentage }}%</span>
        </div>
        <el-progress
          :percentage="progress.percentage"
          :status="progress.status"
          :stroke-width="6"
        />
      </div>
    </div>

    <!-- 上传结果 -->
    <div v-if="showResult && uploadResult.length > 0" class="upload-result">
      <h4>上传结果</h4>
      <div v-for="(result, index) in uploadResult" :key="index" class="result-item">
        <el-icon :class="result.success ? 'success-icon' : 'error-icon'">
          <check v-if="result.success" />
          <close v-else />
        </el-icon>
        <span class="file-name">{{ result.fileName }}</span>
        <span class="result-text" :class="result.success ? 'success-text' : 'error-text'">
          {{ result.message }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Check, Close } from '@element-plus/icons-vue'
import { uploadFile, batchUploadFiles, getUploadToken } from '@/api/file'
import { getToken } from '@/utils/auth'

// Props定义
interface Props {
  // 文件类型
  fileType?: string
  // 是否支持多选
  multiple?: boolean
  // 最大上传数量
  limit?: number
  // 接受的文件类型
  accept?: string
  // 是否自动上传
  autoUpload?: boolean
  // 是否显示文件列表
  showFileList?: boolean
  // 是否支持拖拽
  drag?: boolean
  // 是否禁用
  disabled?: boolean
  // 提示文本
  tip?: string
  // 是否显示上传进度
  showProgress?: boolean
  // 是否显示上传结果
  showResult?: boolean
  // 最大文件大小（MB）
  maxSize?: number
  // 使用七牛云上传
  useQiniu?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  fileType: 'general',
  multiple: true,
  limit: 10,
  accept: '',
  autoUpload: true,
  showFileList: true,
  drag: true,
  disabled: false,
  tip: '支持jpg、png、pdf等格式，单个文件不超过20MB',
  showProgress: true,
  showResult: true,
  maxSize: 20,
  useQiniu: false
})

// Emits定义
const emit = defineEmits<{
  success: [files: any[]]
  error: [error: any]
  progress: [progress: any]
}>()

// 响应式数据
const uploadRef = ref()
const uploading = ref(false)
const fileList = ref([])
const uploadProgress = ref([])
const uploadResult = ref([])

// 计算属性
const uploadAction = computed(() => {
  return props.useQiniu ? '' : '/api/admin/file/upload'
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${getToken()}`
  }
})

const uploadData = computed(() => {
  return {
    type: props.fileType
  }
})

// 上传前检查
const beforeUpload = (file: File) => {
  // 检查文件大小
  const maxSizeBytes = props.maxSize * 1024 * 1024
  if (file.size > maxSizeBytes) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 检查文件类型
  if (props.accept) {
    const allowedTypes = props.accept.split(',').map(type => type.trim())
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!allowedTypes.includes(fileExtension)) {
      ElMessage.error('不支持的文件类型')
      return false
    }
  }

  uploading.value = true
  
  // 添加到进度列表
  if (props.showProgress) {
    uploadProgress.value.push({
      fileName: file.name,
      percentage: 0,
      status: 'uploading'
    })
  }

  return true
}

// 上传成功
const onSuccess = (response: any, file: any) => {
  uploading.value = false
  
  // 更新进度
  if (props.showProgress) {
    const progressIndex = uploadProgress.value.findIndex(p => p.fileName === file.name)
    if (progressIndex !== -1) {
      uploadProgress.value[progressIndex].percentage = 100
      uploadProgress.value[progressIndex].status = 'success'
    }
  }

  // 添加到结果列表
  if (props.showResult) {
    uploadResult.value.push({
      fileName: file.name,
      success: true,
      message: '上传成功',
      data: response.data
    })
  }

  ElMessage.success('文件上传成功')
  emit('success', [response.data])
}

// 上传失败
const onError = (error: any, file: any) => {
  uploading.value = false
  
  // 更新进度
  if (props.showProgress) {
    const progressIndex = uploadProgress.value.findIndex(p => p.fileName === file.name)
    if (progressIndex !== -1) {
      uploadProgress.value[progressIndex].status = 'exception'
    }
  }

  // 添加到结果列表
  if (props.showResult) {
    uploadResult.value.push({
      fileName: file.name,
      success: false,
      message: error.message || '上传失败'
    })
  }

  ElMessage.error('文件上传失败')
  emit('error', error)
}

// 上传进度
const onProgress = (event: any, file: any) => {
  if (props.showProgress) {
    const progressIndex = uploadProgress.value.findIndex(p => p.fileName === file.name)
    if (progressIndex !== -1) {
      uploadProgress.value[progressIndex].percentage = Math.round(event.percent)
    }
  }
  
  emit('progress', { event, file })
}

// 超出文件数量限制
const onExceed = (files: any[], fileList: any[]) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
}

// 移除文件
const onRemove = (file: any, fileList: any[]) => {
  // 从进度列表中移除
  if (props.showProgress) {
    const progressIndex = uploadProgress.value.findIndex(p => p.fileName === file.name)
    if (progressIndex !== -1) {
      uploadProgress.value.splice(progressIndex, 1)
    }
  }
}

// 清空上传列表
const clearFiles = () => {
  uploadRef.value?.clearFiles()
  uploadProgress.value = []
  uploadResult.value = []
}

// 手动上传
const submit = () => {
  uploadRef.value?.submit()
}

// 暴露方法
defineExpose({
  clearFiles,
  submit
})
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

.upload-dragger {
  padding: 40px;
  text-align: center;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-dragger:hover {
  border-color: #409eff;
}

.upload-dragger .el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.upload-progress {
  margin-top: 16px;
}

.progress-item {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 14px;
}

.file-name {
  color: #606266;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.progress-text {
  color: #909399;
  margin-left: 8px;
}

.upload-result {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.upload-result h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.result-item:last-child {
  margin-bottom: 0;
}

.success-icon {
  color: #67c23a;
  margin-right: 8px;
}

.error-icon {
  color: #f56c6c;
  margin-right: 8px;
}

.result-text {
  margin-left: auto;
  font-size: 12px;
}

.success-text {
  color: #67c23a;
}

.error-text {
  color: #f56c6c;
}
</style>
