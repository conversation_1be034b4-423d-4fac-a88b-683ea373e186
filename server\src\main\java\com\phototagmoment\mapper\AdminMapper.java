package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.Admin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 管理员Mapper接口
 */
@Mapper
public interface AdminMapper extends BaseMapper<Admin> {

    /**
     * 根据用户名查询管理员
     *
     * @param username 用户名
     * @return 管理员信息
     */
    @Select("SELECT * FROM ptm_admin WHERE username = #{username} AND is_deleted = 0")
    Admin selectByUsername(@Param("username") String username);

    /**
     * 更新管理员状态
     *
     * @param id     管理员ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("UPDATE ptm_admin SET status = #{status}, updated_at = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新管理员密码
     *
     * @param id       管理员ID
     * @param password 密码
     * @return 影响行数
     */
    @Update("UPDATE ptm_admin SET password = #{password}, updated_at = NOW() WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password);

    /**
     * 更新管理员最后登录信息
     *
     * @param id      管理员ID
     * @param loginIp 登录IP
     * @return 影响行数
     */
    @Update("UPDATE ptm_admin SET last_login_time = NOW(), last_login_ip = #{loginIp}, updated_at = NOW() WHERE id = #{id}")
    int updateLastLogin(@Param("id") Long id, @Param("loginIp") String loginIp);
}
