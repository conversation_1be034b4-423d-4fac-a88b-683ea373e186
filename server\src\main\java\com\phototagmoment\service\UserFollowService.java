package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.entity.UserFollow;

/**
 * 用户关注服务接口
 */
public interface UserFollowService extends IService<UserFollow> {

    /**
     * 关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean followUser(Long followerId, Long followingId);

    /**
     * 取消关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否成功
     */
    boolean unfollowUser(Long followerId, Long followingId);

    /**
     * 检查是否已关注用户
     *
     * @param followerId 关注者ID
     * @param followingId 被关注者ID
     * @return 是否已关注
     */
    boolean checkFollowing(Long followerId, Long followingId);

    /**
     * 获取用户关注列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 关注列表
     */
    IPage<UserDTO> getFollowingList(Long userId, int page, int size);

    /**
     * 获取用户粉丝列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 粉丝列表
     */
    IPage<UserDTO> getFollowerList(Long userId, int page, int size);

    /**
     * 获取用户关注数量
     *
     * @param userId 用户ID
     * @return 关注数量
     */
    int getFollowingCount(Long userId);

    /**
     * 获取用户粉丝数量
     *
     * @param userId 用户ID
     * @return 粉丝数量
     */
    int getFollowerCount(Long userId);
}
