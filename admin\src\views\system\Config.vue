<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>系统配置</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基础配置" name="basic">
          <el-form
            ref="basicFormRef"
            :model="basicForm"
            :rules="basicRules"
            label-width="120px"
            class="config-form"
          >
            <el-form-item label="网站名称" prop="siteName">
              <el-input v-model="basicForm.siteName" placeholder="请输入网站名称" />
            </el-form-item>
            
            <el-form-item label="网站描述" prop="siteDescription">
              <el-input
                v-model="basicForm.siteDescription"
                type="textarea"
                :rows="3"
                placeholder="请输入网站描述"
              />
            </el-form-item>
            
            <el-form-item label="网站关键词" prop="siteKeywords">
              <el-input v-model="basicForm.siteKeywords" placeholder="请输入网站关键词，多个关键词用逗号分隔" />
            </el-form-item>
            
            <el-form-item label="网站Logo">
              <el-upload
                class="avatar-uploader"
                action="#"
                :http-request="uploadLogo"
                :show-file-list="false"
                :before-upload="beforeLogoUpload"
              >
                <img v-if="basicForm.siteLogo" :src="basicForm.siteLogo" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：200px * 60px，支持jpg、png格式</div>
            </el-form-item>
            
            <el-form-item label="网站Favicon">
              <el-upload
                class="avatar-uploader"
                action="#"
                :http-request="uploadFavicon"
                :show-file-list="false"
                :before-upload="beforeFaviconUpload"
              >
                <img v-if="basicForm.siteFavicon" :src="basicForm.siteFavicon" class="avatar favicon" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：32px * 32px，支持ico、png格式</div>
            </el-form-item>
            
            <el-form-item label="备案信息" prop="siteBeian">
              <el-input v-model="basicForm.siteBeian" placeholder="请输入备案信息，如：京ICP备xxxxxxxx号" />
            </el-form-item>
            
            <el-form-item label="版权信息" prop="siteCopyright">
              <el-input v-model="basicForm.siteCopyright" placeholder="请输入版权信息，如：© 2023 PhotoTagMoment" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveBasicConfig">保存配置</el-button>
              <el-button @click="resetBasicForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="上传配置" name="upload">
          <el-form
            ref="uploadFormRef"
            :model="uploadForm"
            :rules="uploadRules"
            label-width="120px"
            class="config-form"
          >
            <el-form-item label="存储方式" prop="storageType">
              <el-radio-group v-model="uploadForm.storageType">
                <el-radio label="local">本地存储</el-radio>
                <el-radio label="qiniu">七牛云</el-radio>
                <el-radio label="aliyun">阿里云OSS</el-radio>
                <el-radio label="tencent">腾讯云COS</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <!-- 本地存储配置 -->
            <template v-if="uploadForm.storageType === 'local'">
              <el-form-item label="上传路径" prop="localPath">
                <el-input v-model="uploadForm.localPath" placeholder="请输入本地上传路径" />
              </el-form-item>
              
              <el-form-item label="访问域名" prop="localDomain">
                <el-input v-model="uploadForm.localDomain" placeholder="请输入本地访问域名" />
              </el-form-item>
            </template>
            
            <!-- 七牛云配置 -->
            <template v-if="uploadForm.storageType === 'qiniu'">
              <el-form-item label="AccessKey" prop="qiniuAccessKey">
                <el-input v-model="uploadForm.qiniuAccessKey" placeholder="请输入七牛云AccessKey" />
              </el-form-item>
              
              <el-form-item label="SecretKey" prop="qiniuSecretKey">
                <el-input v-model="uploadForm.qiniuSecretKey" type="password" placeholder="请输入七牛云SecretKey" show-password />
              </el-form-item>
              
              <el-form-item label="Bucket" prop="qiniuBucket">
                <el-input v-model="uploadForm.qiniuBucket" placeholder="请输入七牛云Bucket" />
              </el-form-item>
              
              <el-form-item label="域名" prop="qiniuDomain">
                <el-input v-model="uploadForm.qiniuDomain" placeholder="请输入七牛云域名" />
              </el-form-item>
            </template>
            
            <!-- 阿里云OSS配置 -->
            <template v-if="uploadForm.storageType === 'aliyun'">
              <el-form-item label="AccessKeyId" prop="aliyunAccessKeyId">
                <el-input v-model="uploadForm.aliyunAccessKeyId" placeholder="请输入阿里云AccessKeyId" />
              </el-form-item>
              
              <el-form-item label="AccessKeySecret" prop="aliyunAccessKeySecret">
                <el-input v-model="uploadForm.aliyunAccessKeySecret" type="password" placeholder="请输入阿里云AccessKeySecret" show-password />
              </el-form-item>
              
              <el-form-item label="Endpoint" prop="aliyunEndpoint">
                <el-input v-model="uploadForm.aliyunEndpoint" placeholder="请输入阿里云Endpoint" />
              </el-form-item>
              
              <el-form-item label="Bucket" prop="aliyunBucket">
                <el-input v-model="uploadForm.aliyunBucket" placeholder="请输入阿里云Bucket" />
              </el-form-item>
              
              <el-form-item label="域名" prop="aliyunDomain">
                <el-input v-model="uploadForm.aliyunDomain" placeholder="请输入阿里云域名" />
              </el-form-item>
            </template>
            
            <!-- 腾讯云COS配置 -->
            <template v-if="uploadForm.storageType === 'tencent'">
              <el-form-item label="SecretId" prop="tencentSecretId">
                <el-input v-model="uploadForm.tencentSecretId" placeholder="请输入腾讯云SecretId" />
              </el-form-item>
              
              <el-form-item label="SecretKey" prop="tencentSecretKey">
                <el-input v-model="uploadForm.tencentSecretKey" type="password" placeholder="请输入腾讯云SecretKey" show-password />
              </el-form-item>
              
              <el-form-item label="Region" prop="tencentRegion">
                <el-input v-model="uploadForm.tencentRegion" placeholder="请输入腾讯云Region" />
              </el-form-item>
              
              <el-form-item label="Bucket" prop="tencentBucket">
                <el-input v-model="uploadForm.tencentBucket" placeholder="请输入腾讯云Bucket" />
              </el-form-item>
              
              <el-form-item label="域名" prop="tencentDomain">
                <el-input v-model="uploadForm.tencentDomain" placeholder="请输入腾讯云域名" />
              </el-form-item>
            </template>
            
            <el-divider />
            
            <el-form-item label="图片最大尺寸" prop="imageMaxSize">
              <el-input-number v-model="uploadForm.imageMaxSize" :min="1" :max="100" />
              <span class="unit-label">MB</span>
            </el-form-item>
            
            <el-form-item label="允许的图片类型" prop="imageAllowedTypes">
              <el-select
                v-model="uploadForm.imageAllowedTypes"
                multiple
                placeholder="请选择允许的图片类型"
              >
                <el-option label="JPG" value="jpg" />
                <el-option label="JPEG" value="jpeg" />
                <el-option label="PNG" value="png" />
                <el-option label="GIF" value="gif" />
                <el-option label="BMP" value="bmp" />
                <el-option label="WEBP" value="webp" />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveUploadConfig">保存配置</el-button>
              <el-button @click="resetUploadForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="邮件配置" name="email">
          <el-form
            ref="emailFormRef"
            :model="emailForm"
            :rules="emailRules"
            label-width="120px"
            class="config-form"
          >
            <el-form-item label="SMTP服务器" prop="smtpHost">
              <el-input v-model="emailForm.smtpHost" placeholder="请输入SMTP服务器地址" />
            </el-form-item>
            
            <el-form-item label="SMTP端口" prop="smtpPort">
              <el-input-number v-model="emailForm.smtpPort" :min="1" :max="65535" />
            </el-form-item>
            
            <el-form-item label="是否启用SSL">
              <el-switch v-model="emailForm.smtpSsl" />
            </el-form-item>
            
            <el-form-item label="发件人邮箱" prop="smtpUsername">
              <el-input v-model="emailForm.smtpUsername" placeholder="请输入发件人邮箱" />
            </el-form-item>
            
            <el-form-item label="发件人密码" prop="smtpPassword">
              <el-input v-model="emailForm.smtpPassword" type="password" placeholder="请输入发件人密码或授权码" show-password />
            </el-form-item>
            
            <el-form-item label="发件人名称" prop="smtpSender">
              <el-input v-model="emailForm.smtpSender" placeholder="请输入发件人名称" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveEmailConfig">保存配置</el-button>
              <el-button @click="resetEmailForm">重置</el-button>
              <el-button type="success" @click="testEmail">测试邮件</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 测试邮件对话框 -->
    <el-dialog v-model="testEmailDialogVisible" title="发送测试邮件" width="500px">
      <el-form :model="testEmailForm" label-width="100px">
        <el-form-item label="收件人邮箱" prop="to">
          <el-input v-model="testEmailForm.to" placeholder="请输入收件人邮箱" />
        </el-form-item>
        <el-form-item label="邮件主题" prop="subject">
          <el-input v-model="testEmailForm.subject" placeholder="请输入邮件主题" />
        </el-form-item>
        <el-form-item label="邮件内容" prop="content">
          <el-input
            v-model="testEmailForm.content"
            type="textarea"
            :rows="5"
            placeholder="请输入邮件内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testEmailDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="sendTestEmail">发送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// 当前激活的标签页
const activeTab = ref('basic')

// 表单引用
const basicFormRef = ref<FormInstance>()
const uploadFormRef = ref<FormInstance>()
const emailFormRef = ref<FormInstance>()

// 基础配置表单
const basicForm = reactive({
  siteName: 'PhotoTagMoment',
  siteDescription: '一个照片分享社交网站',
  siteKeywords: '照片,分享,社交,摄影',
  siteLogo: '',
  siteFavicon: '',
  siteBeian: '',
  siteCopyright: '© 2023 PhotoTagMoment'
})

// 上传配置表单
const uploadForm = reactive({
  storageType: 'local',
  localPath: '/data/upload',
  localDomain: 'http://localhost:8080/api/file',
  qiniuAccessKey: '',
  qiniuSecretKey: '',
  qiniuBucket: '',
  qiniuDomain: '',
  aliyunAccessKeyId: '',
  aliyunAccessKeySecret: '',
  aliyunEndpoint: '',
  aliyunBucket: '',
  aliyunDomain: '',
  tencentSecretId: '',
  tencentSecretKey: '',
  tencentRegion: '',
  tencentBucket: '',
  tencentDomain: '',
  imageMaxSize: 10,
  imageAllowedTypes: ['jpg', 'jpeg', 'png', 'gif']
})

// 邮件配置表单
const emailForm = reactive({
  smtpHost: '',
  smtpPort: 465,
  smtpSsl: true,
  smtpUsername: '',
  smtpPassword: '',
  smtpSender: 'PhotoTagMoment'
})

// 测试邮件表单
const testEmailForm = reactive({
  to: '',
  subject: 'PhotoTagMoment测试邮件',
  content: '这是一封测试邮件，如果您收到这封邮件，说明邮件配置正确。'
})

// 测试邮件对话框可见性
const testEmailDialogVisible = ref(false)

// 基础配置表单校验规则
const basicRules = reactive<FormRules>({
  siteName: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ],
  siteDescription: [
    { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
  ],
  siteKeywords: [
    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
  ]
})

// 上传配置表单校验规则
const uploadRules = reactive<FormRules>({
  storageType: [
    { required: true, message: '请选择存储方式', trigger: 'change' }
  ],
  imageMaxSize: [
    { required: true, message: '请输入图片最大尺寸', trigger: 'blur' }
  ],
  imageAllowedTypes: [
    { required: true, message: '请选择允许的图片类型', trigger: 'change' }
  ]
})

// 邮件配置表单校验规则
const emailRules = reactive<FormRules>({
  smtpHost: [
    { required: true, message: '请输入SMTP服务器地址', trigger: 'blur' }
  ],
  smtpPort: [
    { required: true, message: '请输入SMTP端口', trigger: 'blur' }
  ],
  smtpUsername: [
    { required: true, message: '请输入发件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  smtpPassword: [
    { required: true, message: '请输入发件人密码或授权码', trigger: 'blur' }
  ],
  smtpSender: [
    { required: true, message: '请输入发件人名称', trigger: 'blur' }
  ]
})

// 上传Logo前的校验
const beforeLogoUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg'
  const isPNG = file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG && !isPNG) {
    ElMessage.error('Logo只能是JPG或PNG格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('Logo大小不能超过2MB!')
    return false
  }
  return true
}

// 上传Favicon前的校验
const beforeFaviconUpload = (file: File) => {
  const isPNG = file.type === 'image/png'
  const isICO = file.type === 'image/x-icon' || file.type === 'image/vnd.microsoft.icon'
  const isLt1M = file.size / 1024 / 1024 < 1

  if (!isPNG && !isICO) {
    ElMessage.error('Favicon只能是ICO或PNG格式!')
    return false
  }
  if (!isLt1M) {
    ElMessage.error('Favicon大小不能超过1MB!')
    return false
  }
  return true
}

// 上传Logo
const uploadLogo = (options: any) => {
  // 这里应该调用实际的API上传Logo
  console.log('上传Logo', options.file)
  
  // 模拟上传成功
  setTimeout(() => {
    // 使用FileReader读取文件内容
    const reader = new FileReader()
    reader.readAsDataURL(options.file)
    reader.onload = () => {
      basicForm.siteLogo = reader.result as string
      ElMessage.success('Logo上传成功')
    }
  }, 500)
}

// 上传Favicon
const uploadFavicon = (options: any) => {
  // 这里应该调用实际的API上传Favicon
  console.log('上传Favicon', options.file)
  
  // 模拟上传成功
  setTimeout(() => {
    // 使用FileReader读取文件内容
    const reader = new FileReader()
    reader.readAsDataURL(options.file)
    reader.onload = () => {
      basicForm.siteFavicon = reader.result as string
      ElMessage.success('Favicon上传成功')
    }
  }, 500)
}

// 保存基础配置
const saveBasicConfig = async () => {
  if (!basicFormRef.value) return
  
  await basicFormRef.value.validate((valid) => {
    if (valid) {
      // 这里应该调用实际的API保存基础配置
      console.log('保存基础配置', basicForm)
      
      // 模拟保存成功
      ElMessage.success('基础配置保存成功')
    }
  })
}

// 重置基础配置表单
const resetBasicForm = () => {
  if (!basicFormRef.value) return
  basicFormRef.value.resetFields()
}

// 保存上传配置
const saveUploadConfig = async () => {
  if (!uploadFormRef.value) return
  
  await uploadFormRef.value.validate((valid) => {
    if (valid) {
      // 这里应该调用实际的API保存上传配置
      console.log('保存上传配置', uploadForm)
      
      // 模拟保存成功
      ElMessage.success('上传配置保存成功')
    }
  })
}

// 重置上传配置表单
const resetUploadForm = () => {
  if (!uploadFormRef.value) return
  uploadFormRef.value.resetFields()
}

// 保存邮件配置
const saveEmailConfig = async () => {
  if (!emailFormRef.value) return
  
  await emailFormRef.value.validate((valid) => {
    if (valid) {
      // 这里应该调用实际的API保存邮件配置
      console.log('保存邮件配置', emailForm)
      
      // 模拟保存成功
      ElMessage.success('邮件配置保存成功')
    }
  })
}

// 重置邮件配置表单
const resetEmailForm = () => {
  if (!emailFormRef.value) return
  emailFormRef.value.resetFields()
}

// 测试邮件
const testEmail = () => {
  // 验证邮件配置是否已填写
  if (!emailForm.smtpHost || !emailForm.smtpUsername || !emailForm.smtpPassword) {
    ElMessage.warning('请先完善邮件配置')
    return
  }
  
  // 显示测试邮件对话框
  testEmailDialogVisible.value = true
}

// 发送测试邮件
const sendTestEmail = () => {
  // 验证收件人邮箱
  if (!testEmailForm.to) {
    ElMessage.warning('请输入收件人邮箱')
    return
  }
  
  // 这里应该调用实际的API发送测试邮件
  console.log('发送测试邮件', testEmailForm)
  
  // 模拟发送成功
  setTimeout(() => {
    ElMessage.success('测试邮件发送成功')
    testEmailDialogVisible.value = false
  }, 1000)
}

// 获取配置
const getConfig = () => {
  // 这里应该调用实际的API获取配置
  console.log('获取配置')
  
  // 模拟获取配置
  setTimeout(() => {
    // 这里应该用实际的API返回数据替换
    // 基础配置
    basicForm.siteName = 'PhotoTagMoment'
    basicForm.siteDescription = '一个照片分享社交网站'
    basicForm.siteKeywords = '照片,分享,社交,摄影'
    basicForm.siteLogo = 'https://via.placeholder.com/200x60'
    basicForm.siteFavicon = 'https://via.placeholder.com/32x32'
    basicForm.siteBeian = '京ICP备xxxxxxxx号'
    basicForm.siteCopyright = '© 2023 PhotoTagMoment'
    
    // 上传配置
    uploadForm.storageType = 'local'
    uploadForm.localPath = '/data/upload'
    uploadForm.localDomain = 'http://localhost:8080/api/file'
    uploadForm.imageMaxSize = 10
    uploadForm.imageAllowedTypes = ['jpg', 'jpeg', 'png', 'gif']
    
    // 邮件配置
    emailForm.smtpHost = 'smtp.example.com'
    emailForm.smtpPort = 465
    emailForm.smtpSsl = true
    emailForm.smtpUsername = '<EMAIL>'
    emailForm.smtpPassword = '********'
    emailForm.smtpSender = 'PhotoTagMoment'
  }, 500)
}

// 组件挂载时获取配置
onMounted(() => {
  getConfig()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .config-form {
    max-width: 800px;
    margin: 20px 0;
  }
  
  .avatar-uploader {
    .avatar {
      width: 200px;
      height: 60px;
      display: block;
      object-fit: contain;
      
      &.favicon {
        width: 32px;
        height: 32px;
      }
    }
    
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 200px;
      height: 60px;
      line-height: 60px;
      text-align: center;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      
      &.favicon {
        width: 32px;
        height: 32px;
        line-height: 32px;
        font-size: 16px;
      }
    }
  }
  
  .upload-tip {
    font-size: 12px;
    color: #606266;
    margin-top: 5px;
  }
  
  .unit-label {
    margin-left: 10px;
  }
}
</style>
