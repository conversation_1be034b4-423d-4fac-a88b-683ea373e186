<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置统计信息"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="statistics-container">
      <!-- 基础统计 -->
      <el-card class="stat-card">
        <template #header>
          <div class="card-header">
            <span>基础统计</span>
            <el-button size="small" @click="loadStatistics">
              <el-icon><refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">总配置数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.enabledCount || 0 }}</div>
              <div class="stat-label">启用配置</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.normalCount || 0 }}</div>
              <div class="stat-label">正常配置</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.errorCount || 0 }}</div>
              <div class="stat-label">异常配置</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 存储类型分布 -->
      <el-card class="stat-card">
        <template #header>
          <span>存储类型分布</span>
        </template>
        <div v-if="storageTypeStats.length > 0" class="storage-stats">
          <div v-for="item in storageTypeStats" :key="item.storage_type" class="storage-item">
            <div class="storage-info">
              <el-tag :type="getStorageTypeTagType(item.storage_type)">
                {{ getStorageTypeLabel(item.storage_type) }}
              </el-tag>
              <span class="storage-count">{{ item.count }} 个配置</span>
            </div>
            <div class="storage-progress">
              <el-progress
                :percentage="getStoragePercentage(item.count)"
                :stroke-width="8"
                :show-text="false"
                :color="getStorageProgressColor(item.storage_type)"
              />
            </div>
          </div>
        </div>
        <el-empty v-else description="暂无数据" />
      </el-card>

      <!-- 健康状态 -->
      <el-card class="stat-card">
        <template #header>
          <span>健康状态</span>
        </template>
        <div v-if="healthStatus" class="health-status">
          <div class="health-score">
            <div class="score-circle">
              <el-progress
                type="circle"
                :percentage="healthStatus.healthScore || 0"
                :color="getHealthScoreColor(healthStatus.healthScore)"
                :width="120"
              />
            </div>
            <div class="score-label">健康分数</div>
          </div>

          <div class="health-details">
            <div class="health-item">
              <span class="health-label">最近失败配置：</span>
              <span class="health-value">{{ healthStatus.recentFailedConfigs?.length || 0 }} 个</span>
            </div>
            <div class="health-item">
              <span class="health-label">未测试配置：</span>
              <span class="health-value">{{ healthStatus.untestedConfigs?.length || 0 }} 个</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 最近失败的配置 -->
      <el-card v-if="healthStatus?.recentFailedConfigs?.length" class="stat-card">
        <template #header>
          <span>最近失败的配置</span>
        </template>
        <div class="failed-configs">
          <div
            v-for="config in healthStatus.recentFailedConfigs"
            :key="config.id"
            class="failed-config-item"
          >
            <div class="config-info">
              <span class="config-name">{{ config.configName }}</span>
              <el-tag :type="getStorageTypeTagType(config.storageType)" size="small">
                {{ getStorageTypeLabel(config.storageType) }}
              </el-tag>
            </div>
            <div class="config-time">
              {{ formatDateTime(config.lastTestTime) }}
            </div>
          </div>
        </div>
      </el-card>

      <!-- 未测试的配置 -->
      <el-card v-if="healthStatus?.untestedConfigs?.length" class="stat-card">
        <template #header>
          <span>长时间未测试的配置</span>
        </template>
        <div class="untested-configs">
          <div
            v-for="config in healthStatus.untestedConfigs"
            :key="config.id"
            class="untested-config-item"
          >
            <div class="config-info">
              <span class="config-name">{{ config.configName }}</span>
              <el-tag :type="getStorageTypeTagType(config.storageType)" size="small">
                {{ getStorageTypeLabel(config.storageType) }}
              </el-tag>
            </div>
            <div class="config-time">
              {{ config.lastTestTime ? formatDateTime(config.lastTestTime) : '从未测试' }}
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import {
  getConfigStatistics,
  getStorageTypeStatistics,
  getConfigHealthStatus
} from '@/api/fileUploadConfig'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const statistics = reactive({
  totalCount: 0,
  enabledCount: 0,
  normalCount: 0,
  errorCount: 0
})
const storageTypeStats = ref([])
const healthStatus = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框显示
watch(dialogVisible, (visible) => {
  if (visible) {
    loadStatistics()
    loadStorageTypeStats()
    loadHealthStatus()
  }
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await getConfigStatistics()
    if ((response as any).code === 200) {
      Object.assign(statistics, (response as any).data)
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
    ElMessage.error('加载统计信息失败')
  }
}

const loadStorageTypeStats = async () => {
  try {
    const response = await getStorageTypeStatistics()
    if ((response as any).code === 200) {
      storageTypeStats.value = (response as any).data
    }
  } catch (error) {
    console.error('加载存储类型统计失败:', error)
    ElMessage.error('加载存储类型统计失败')
  }
}

const loadHealthStatus = async () => {
  try {
    const response = await getConfigHealthStatus()
    if ((response as any).code === 200) {
      healthStatus.value = (response as any).data
    }
  } catch (error) {
    console.error('加载健康状态失败:', error)
    ElMessage.error('加载健康状态失败')
  }
}

// 工具方法
const getStorageTypeTagType = (storageType: string) => {
  const typeMap = {
    LOCAL: 'info',
    QINIU: 'success',
    ALIYUN_OSS: 'warning',
    TENCENT_COS: 'primary',
    AWS_S3: 'danger',
    MINIO: 'info'
  }
  return typeMap[storageType] || 'info'
}

const getStorageTypeLabel = (storageType: string) => {
  const labelMap = {
    LOCAL: '本地存储',
    QINIU: '七牛云',
    ALIYUN_OSS: '阿里云OSS',
    TENCENT_COS: '腾讯云COS',
    AWS_S3: 'AWS S3',
    MINIO: 'MinIO'
  }
  return labelMap[storageType] || '未知'
}

const getStoragePercentage = (count: number) => {
  const total = statistics.totalCount || 1
  return Math.round((count / total) * 100)
}

const getStorageProgressColor = (storageType: string) => {
  const colorMap = {
    LOCAL: '#909399',
    QINIU: '#67c23a',
    ALIYUN_OSS: '#e6a23c',
    TENCENT_COS: '#409eff',
    AWS_S3: '#f56c6c',
    MINIO: '#909399'
  }
  return colorMap[storageType] || '#909399'
}

const getHealthScoreColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}
</script>

<style scoped>
.statistics-container {
  max-height: 600px;
  overflow-y: auto;
}

.stat-card {
  margin-bottom: 16px;
}

.stat-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 16px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.storage-stats {
  padding: 16px;
}

.storage-item {
  margin-bottom: 16px;
}

.storage-item:last-child {
  margin-bottom: 0;
}

.storage-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.storage-count {
  color: #606266;
  font-size: 14px;
}

.health-status {
  display: flex;
  align-items: center;
  padding: 16px;
}

.health-score {
  text-align: center;
  margin-right: 32px;
}

.score-label {
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.health-details {
  flex: 1;
}

.health-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.health-item:last-child {
  margin-bottom: 0;
}

.health-label {
  color: #606266;
}

.health-value {
  color: #303133;
  font-weight: 500;
}

.failed-configs,
.untested-configs {
  padding: 16px;
}

.failed-config-item,
.untested-config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
}

.failed-config-item:last-child,
.untested-config-item:last-child {
  margin-bottom: 0;
}

.config-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-name {
  font-weight: 500;
  color: #303133;
}

.config-time {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
