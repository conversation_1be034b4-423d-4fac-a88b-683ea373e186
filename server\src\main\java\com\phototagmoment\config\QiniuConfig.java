package com.phototagmoment.config;

import com.phototagmoment.dto.FileUploadConfigDTO;
import com.phototagmoment.service.FileUploadConfigService;
import com.phototagmoment.service.SystemConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

/**
 * 七牛云配置类
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "storage.qiniu")
public class QiniuConfig {

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private FileUploadConfigService fileUploadConfigService;

    /**
     * 是否启用七牛云存储
     */
    private boolean enabled = false;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 存储空间名称
     */
    private String bucket;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 访问域名
     */
    private String domain;

    /**
     * 上传目录
     */
    private String uploadDir = "uploads";

    /**
     * 是否启用加密存储
     */
    private boolean encryptEnabled = true;

    /**
     * 是否为私有空间
     */
    private boolean isPrivate = true;

    /**
     * 下载凭证有效期（秒）
     */
    private long downloadExpires = 3600;

    /**
     * 初始化后从数据库加载配置
     */
    @PostConstruct
    public void loadConfigFromDatabase() {
        try {
            refreshConfig();
            log.info("七牛云配置初始化完成，enabled: {}", enabled);
        } catch (Exception e) {
            log.warn("从数据库加载七牛云配置失败，使用默认配置: {}", e.getMessage());
        }
    }

    /**
     * 从数据库刷新配置
     * 优先级：文件上传配置表 > 系统配置表
     */
    public void refreshConfig() {
        try {
            // 优先从文件上传配置表读取七牛云配置
            boolean configLoaded = loadFromFileUploadConfig();

            if (!configLoaded) {
                // 如果文件上传配置表中没有启用的七牛云配置，则从系统配置表读取
                loadFromSystemConfig();
                log.debug("从系统配置表加载七牛云配置");
            } else {
                log.debug("从文件上传配置表加载七牛云配置");
            }

            log.debug("七牛云配置刷新完成: enabled={}, bucket={}, domain={}", enabled, bucket, domain);
        } catch (Exception e) {
            log.error("刷新七牛云配置失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从文件上传配置表加载七牛云配置
     * @return 是否成功加载配置
     */
    private boolean loadFromFileUploadConfig() {
        if (fileUploadConfigService == null) {
            log.warn("FileUploadConfigService未初始化，跳过文件上传配置表读取");
            return false;
        }

        try {
            // 获取启用的七牛云配置
            FileUploadConfigDTO qiniuConfig = fileUploadConfigService.getConfigByStorageType("QINIU");

            if (qiniuConfig == null || !Boolean.TRUE.equals(qiniuConfig.getEnabled())) {
                log.debug("文件上传配置表中没有启用的七牛云配置");
                return false;
            }

            // 设置启用状态
            this.enabled = true;

            // 从配置参数中读取七牛云具体配置
            FileUploadConfigDTO.StorageConfigParams configParams = qiniuConfig.getConfigParams();
            if (configParams != null) {
                this.accessKey = configParams.getQiniuAccessKey();
                this.secretKey = configParams.getQiniuSecretKey();
                this.bucket = configParams.getQiniuBucket();
                this.region = configParams.getQiniuRegion();
                this.domain = configParams.getDomain();

                // 设置其他配置项的默认值
                this.uploadDir = "phototagmoment";
                this.isPrivate = true;
                this.downloadExpires = 3600L;
                this.encryptEnabled = true;
            }

            log.info("从文件上传配置表成功加载七牛云配置: configId={}, configName={}",
                    qiniuConfig.getId(), qiniuConfig.getConfigName());
            return true;
        } catch (Exception e) {
            log.error("从文件上传配置表加载七牛云配置失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从系统配置表加载七牛云配置（兜底方案）
     */
    private void loadFromSystemConfig() {
        if (systemConfigService == null) {
            log.warn("SystemConfigService未初始化，跳过系统配置表读取");
            return;
        }

        try {
            // 读取存储类型配置，判断是否启用七牛云
            String storageType = systemConfigService.getConfigValue("storage.type", "local");
            this.enabled = "qiniu".equals(storageType);

            // 读取七牛云具体配置
            this.accessKey = systemConfigService.getConfigValue("storage.qiniu.access-key", "");
            this.secretKey = systemConfigService.getConfigValue("storage.qiniu.secret-key", "");
            this.bucket = systemConfigService.getConfigValue("storage.qiniu.bucket", "");
            this.region = systemConfigService.getConfigValue("storage.qiniu.region", "");
            this.domain = systemConfigService.getConfigValue("storage.qiniu.domain", "");
            this.uploadDir = systemConfigService.getConfigValue("storage.qiniu.upload-dir", "uploads");
            this.isPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", true);
            this.downloadExpires = systemConfigService.getLongValue("storage.qiniu.download-expires", 3600L);
        } catch (Exception e) {
            log.error("从系统配置表加载七牛云配置失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查配置是否完整
     */
    public boolean isConfigComplete() {
        if (!enabled) {
            return false;
        }

        return StringUtils.hasText(accessKey) &&
               StringUtils.hasText(secretKey) &&
               StringUtils.hasText(bucket) &&
               StringUtils.hasText(region) &&
               StringUtils.hasText(domain);
    }

    /**
     * 获取配置验证错误信息
     */
    public String getConfigValidationError() {
        if (!enabled) {
            return "七牛云存储未启用";
        }

        if (!StringUtils.hasText(accessKey)) {
            return "七牛云AccessKey未配置";
        }

        if (!StringUtils.hasText(secretKey)) {
            return "七牛云SecretKey未配置";
        }

        if (!StringUtils.hasText(bucket)) {
            return "七牛云存储空间未配置";
        }

        if (!StringUtils.hasText(region)) {
            return "七牛云存储区域未配置";
        }

        if (!StringUtils.hasText(domain)) {
            return "七牛云访问域名未配置";
        }

        return null;
    }

    /**
     * 获取配置来源信息
     * @return 配置来源描述
     */
    public String getConfigSource() {
        try {
            // 检查文件上传配置表中是否有启用的七牛云配置
            if (fileUploadConfigService != null) {
                FileUploadConfigDTO qiniuConfig = fileUploadConfigService.getConfigByStorageType("QINIU");
                if (qiniuConfig != null && Boolean.TRUE.equals(qiniuConfig.getEnabled())) {
                    return "文件上传配置表 (ID: " + qiniuConfig.getId() + ", 名称: " + qiniuConfig.getConfigName() + ")";
                }
            }

            // 检查系统配置表
            if (systemConfigService != null) {
                String storageType = systemConfigService.getConfigValue("storage.type", "local");
                if ("qiniu".equals(storageType)) {
                    return "系统配置表 (storage.type = qiniu)";
                }
            }

            return "未找到有效配置";
        } catch (Exception e) {
            return "配置来源检查失败: " + e.getMessage();
        }
    }

    /**
     * 获取详细的配置状态信息
     * @return 配置状态信息
     */
    public String getConfigStatusInfo() {
        StringBuilder info = new StringBuilder();
        info.append("七牛云配置状态:\n");
        info.append("- 启用状态: ").append(enabled).append("\n");
        info.append("- 配置来源: ").append(getConfigSource()).append("\n");
        info.append("- AccessKey: ").append(StringUtils.hasText(accessKey) ? "已配置" : "未配置").append("\n");
        info.append("- SecretKey: ").append(StringUtils.hasText(secretKey) ? "已配置" : "未配置").append("\n");
        info.append("- Bucket: ").append(StringUtils.hasText(bucket) ? bucket : "未配置").append("\n");
        info.append("- Region: ").append(StringUtils.hasText(region) ? region : "未配置").append("\n");
        info.append("- Domain: ").append(StringUtils.hasText(domain) ? domain : "未配置").append("\n");
        info.append("- 配置完整性: ").append(isConfigComplete() ? "完整" : "不完整").append("\n");

        String validationError = getConfigValidationError();
        if (validationError != null) {
            info.append("- 验证错误: ").append(validationError).append("\n");
        }

        return info.toString();
    }
}
