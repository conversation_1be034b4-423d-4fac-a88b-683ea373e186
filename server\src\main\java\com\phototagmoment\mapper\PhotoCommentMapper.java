package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.PhotoComment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 照片评论Mapper接口
 */
@Mapper
@Repository
public interface PhotoCommentMapper extends BaseMapper<PhotoComment> {

    /**
     * 统计照片的评论数
     *
     * @param photoId 照片ID
     * @return 评论数
     */
    @Select("SELECT COUNT(*) FROM ptm_photo_comment WHERE photo_id = #{photoId} AND is_deleted = 0")
    int countByPhotoId(@Param("photoId") Long photoId);

    /**
     * 获取照片的评论列表
     *
     * @param photoId 照片ID
     * @return 评论列表
     */
    @Select("SELECT * FROM ptm_photo_comment WHERE photo_id = #{photoId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<PhotoComment> getCommentsByPhotoId(@Param("photoId") Long photoId);

    /**
     * 获取用户的评论列表
     *
     * @param userId 用户ID
     * @return 评论列表
     */
    @Select("SELECT * FROM ptm_photo_comment WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<PhotoComment> getCommentsByUserId(@Param("userId") Long userId);

    /**
     * 获取照片的热门评论
     *
     * @param photoId 照片ID
     * @param limit   限制数量
     * @return 评论列表
     */
    @Select("SELECT * FROM ptm_photo_comment WHERE photo_id = #{photoId} AND is_deleted = 0 ORDER BY like_count DESC LIMIT #{limit}")
    List<PhotoComment> getHotCommentsByPhotoId(@Param("photoId") Long photoId, @Param("limit") int limit);
}
