package com.phototagmoment.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.common.Result;
import com.phototagmoment.common.ResultCode;
import com.phototagmoment.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证拦截器
 * 注意：此拦截器已被 JwtAuthenticationFilter 替代，不再使用
 * 保留此类仅作为参考
 */
@Slf4j
@Component
@Deprecated
public class JwtAuthenticationInterceptor implements HandlerInterceptor {

    @Value("${jwt.header}")
    private String tokenHeader;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 检查请求URI是否为白名单
        String requestURI = request.getRequestURI();
        if (requestURI.contains("/login") ||
            requestURI.contains("/auth/") ||
            requestURI.contains("/swagger") ||
            requestURI.contains("/v3/api-docs") ||
            requestURI.contains("/doc.html") ||
            requestURI.contains("/emergency-token") ||
            requestURI.contains("/emergency-reset") ||
            requestURI.contains("/webjars") ||
            requestURI.contains("/search") ||
            requestURI.contains("/recommendation") ||
            requestURI.contains("/photo/list") ||
            requestURI.contains("/photo/detail") ||
            requestURI.contains("/tag") ||
            requestURI.contains("/user/profile") ||
            requestURI.contains("/user/photos") ||
            requestURI.contains("/user/collections") ||
            requestURI.contains("/dict") ||
            requestURI.contains("/notification")) {
            // 白名单请求，直接放行
            return true;
        }

        try {
            // 从请求头中获取JWT
            String jwt = getJwtFromRequest(request);

            // 如果JWT不为空且有效
            if (StringUtils.hasText(jwt) && jwtTokenProvider.validateToken(jwt)) {
                // 获取用户名
                String username = jwtTokenProvider.getUsernameFromToken(jwt);

                // 加载用户详情
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                // 创建认证对象
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                // 设置认证信息
                SecurityContextHolder.getContext().setAuthentication(authentication);
                return true;
            } else if (StringUtils.hasText(jwt)) {
                // JWT无效
                handleAuthenticationError(response, ResultCode.UNAUTHORIZED);
                return false;
            }
        } catch (Exception e) {
            log.error("JWT认证拦截器处理异常: {}", e.getMessage());
            // 清除认证信息，确保安全
            SecurityContextHolder.clearContext();
        }

        // 没有JWT，放行，由Spring Security处理
        return true;
    }

    /**
     * 从请求头中获取JWT
     *
     * @param request 请求
     * @return JWT
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader(tokenHeader);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(tokenPrefix + " ")) {
            return bearerToken.substring(tokenPrefix.length() + 1);
        }
        return null;
    }

    /**
     * 处理认证错误
     *
     * @param response 响应
     * @param resultCode 结果码
     * @throws IOException IO异常
     */
    private void handleAuthenticationError(HttpServletResponse response, ResultCode resultCode) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(objectMapper.writeValueAsString(Result.fail(resultCode)));
    }
}
