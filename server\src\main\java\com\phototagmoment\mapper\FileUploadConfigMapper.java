package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.FileUploadConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件上传配置Mapper接口
 */
@Mapper
public interface FileUploadConfigMapper extends BaseMapper<FileUploadConfig> {

    /**
     * 获取默认配置
     */
    @Select("SELECT * FROM ptm_file_upload_config WHERE is_default = 1 AND is_deleted = 0 LIMIT 1")
    FileUploadConfig selectDefaultConfig();

    /**
     * 获取启用的配置列表
     */
    @Select("SELECT * FROM ptm_file_upload_config WHERE enabled = 1 AND is_deleted = 0 ORDER BY sort_order ASC, created_at ASC")
    List<FileUploadConfig> selectEnabledConfigs();

    /**
     * 根据存储类型获取配置
     */
    @Select("SELECT * FROM ptm_file_upload_config WHERE storage_type = #{storageType} AND enabled = 1 AND is_deleted = 0 ORDER BY sort_order ASC LIMIT 1")
    FileUploadConfig selectByStorageType(@Param("storageType") String storageType);

    /**
     * 检查配置名称是否存在
     */
    @Select("SELECT COUNT(*) FROM ptm_file_upload_config WHERE config_name = #{configName} AND id != #{excludeId} AND is_deleted = 0")
    int countByConfigName(@Param("configName") String configName, @Param("excludeId") Long excludeId);

    /**
     * 更新默认配置（先查询再更新，避免BlockAttackInnerInterceptor限制）
     */
    @Select("SELECT id FROM ptm_file_upload_config WHERE is_default = 1 AND is_deleted = 0")
    List<Long> selectDefaultConfigIds();

    /**
     * 清除指定配置的默认状态
     */
    @Update("UPDATE ptm_file_upload_config SET is_default = 0, updated_at = NOW() WHERE id = #{configId} AND is_deleted = 0")
    int clearDefaultConfigById(@Param("configId") Long configId);

    /**
     * 设置默认配置（避免触发器冲突）
     */
    @Update("UPDATE ptm_file_upload_config SET is_default = 1, updated_by = #{updatedBy}, updated_at = #{updateTime} WHERE id = #{configId} AND is_deleted = 0")
    int setDefaultConfig(@Param("configId") Long configId,
                        @Param("updatedBy") Long updatedBy,
                        @Param("updateTime") LocalDateTime updateTime);

    /**
     * 安全设置默认配置（分步操作避免触发器冲突）
     */
    @Update("UPDATE ptm_file_upload_config SET is_default = CASE WHEN id = #{configId} THEN 1 ELSE 0 END, updated_by = #{updatedBy}, updated_at = #{updateTime} WHERE is_deleted = 0")
    int setDefaultConfigSafely(@Param("configId") Long configId,
                              @Param("updatedBy") Long updatedBy,
                              @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新配置状态
     */
    @Update("UPDATE ptm_file_upload_config SET status = #{status}, updated_at = #{updateTime} WHERE id = #{configId} AND is_deleted = 0")
    int updateConfigStatus(@Param("configId") Long configId, @Param("status") Integer status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新启用状态（避免触发器冲突）
     */
    @Update("UPDATE ptm_file_upload_config SET enabled = #{enabled}, updated_by = #{updatedBy}, updated_at = #{updateTime} WHERE id = #{configId} AND is_deleted = 0")
    int updateEnabledStatus(@Param("configId") Long configId,
                           @Param("enabled") Boolean enabled,
                           @Param("updatedBy") Long updatedBy,
                           @Param("updateTime") LocalDateTime updateTime);

    /**
     * 选择性更新配置（避免触发器冲突，不更新is_default字段）
     */
    @Update("UPDATE ptm_file_upload_config SET " +
            "config_name = #{configName}, " +
            "storage_type = #{storageType}, " +
            "enabled = #{enabled}, " +
            "config_params = #{configParams}, " +
            "upload_limits = #{uploadLimits}, " +
            "path_config = #{pathConfig}, " +
            "description = #{description}, " +
            "sort_order = #{sortOrder}, " +
            "status = #{status}, " +
            "updated_by = #{updatedBy}, " +
            "updated_at = #{updatedAt} " +
            "WHERE id = #{configId} AND is_deleted = 0")
    int updateConfigSelectively(@Param("configId") Long configId,
                               @Param("configName") String configName,
                               @Param("storageType") String storageType,
                               @Param("enabled") Boolean enabled,
                               @Param("configParams") String configParams,
                               @Param("uploadLimits") String uploadLimits,
                               @Param("pathConfig") String pathConfig,
                               @Param("description") String description,
                               @Param("sortOrder") Integer sortOrder,
                               @Param("status") Integer status,
                               @Param("updatedBy") Long updatedBy,
                               @Param("updatedAt") LocalDateTime updatedAt);

    /**
     * 更新测试结果
     */
    @Update("UPDATE ptm_file_upload_config SET last_test_time = #{testTime}, last_test_result = #{testResult}, updated_at = #{updateTime} WHERE id = #{configId} AND is_deleted = 0")
    int updateTestResult(@Param("configId") Long configId,
                        @Param("testTime") LocalDateTime testTime,
                        @Param("testResult") String testResult,
                        @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新启用状态
     */
    @Update("UPDATE ptm_file_upload_config SET enabled = #{enabled}, updated_at = #{updateTime} WHERE id IN (${configIds}) AND is_deleted = 0")
    int batchUpdateEnabled(@Param("configIds") String configIds,
                          @Param("enabled") Boolean enabled,
                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取配置统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalCount, " +
            "COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabledCount, " +
            "COUNT(CASE WHEN status = 0 THEN 1 END) as normalCount, " +
            "COUNT(CASE WHEN status = 2 THEN 1 END) as errorCount " +
            "FROM ptm_file_upload_config WHERE is_deleted = 0")
    java.util.Map<String, Object> selectConfigStatistics();

    /**
     * 根据存储类型统计配置数量
     */
    @Select("SELECT storage_type, COUNT(*) as count " +
            "FROM ptm_file_upload_config " +
            "WHERE is_deleted = 0 " +
            "GROUP BY storage_type " +
            "ORDER BY count DESC")
    List<java.util.Map<String, Object>> selectStorageTypeStatistics();

    /**
     * 获取最近测试失败的配置
     */
    @Select("SELECT * FROM ptm_file_upload_config " +
            "WHERE status = 2 AND is_deleted = 0 " +
            "ORDER BY last_test_time DESC " +
            "LIMIT #{limit}")
    List<FileUploadConfig> selectRecentFailedConfigs(@Param("limit") Integer limit);

    /**
     * 获取长时间未测试的配置
     */
    @Select("SELECT * FROM ptm_file_upload_config " +
            "WHERE enabled = 1 AND is_deleted = 0 " +
            "AND (last_test_time IS NULL OR last_test_time < #{beforeTime}) " +
            "ORDER BY last_test_time ASC " +
            "LIMIT #{limit}")
    List<FileUploadConfig> selectUntestedConfigs(@Param("beforeTime") LocalDateTime beforeTime,
                                                @Param("limit") Integer limit);

    /**
     * 根据创建者查询配置
     */
    @Select("SELECT * FROM ptm_file_upload_config " +
            "WHERE created_by = #{createdBy} AND is_deleted = 0 " +
            "ORDER BY created_at DESC")
    List<FileUploadConfig> selectByCreatedBy(@Param("createdBy") Long createdBy);

    /**
     * 搜索配置
     */
    @Select("<script>" +
            "SELECT * FROM ptm_file_upload_config " +
            "WHERE is_deleted = 0 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (config_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='storageType != null and storageType != \"\"'>" +
            "AND storage_type = #{storageType} " +
            "</if>" +
            "<if test='enabled != null'>" +
            "AND enabled = #{enabled} " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "ORDER BY sort_order ASC, created_at DESC" +
            "</script>")
    List<FileUploadConfig> searchConfigs(@Param("keyword") String keyword,
                                        @Param("storageType") String storageType,
                                        @Param("enabled") Boolean enabled,
                                        @Param("status") Integer status);
}
