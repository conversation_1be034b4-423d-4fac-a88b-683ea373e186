package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件记录实体类
 */
@Data
@TableName("ptm_file_record")
@Schema(description = "文件记录")
public class FileRecord {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "文件记录ID")
    private Long id;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "存储文件名")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "MIME类型")
    private String mimeType;

    @Schema(description = "文件扩展名")
    private String extension;

    @Schema(description = "文件分类")
    private String category;

    @Schema(description = "上传者ID")
    private Long uploaderId;

    @Schema(description = "上传者类型")
    private String uploaderType;

    @Schema(description = "存储类型")
    private String storageType;

    @Schema(description = "文件状态：0-正常，1-回收站，2-已删除")
    private Integer status;

    @Schema(description = "文件标签")
    private String tags;

    @Schema(description = "文件描述")
    private String description;

    @Schema(description = "访问次数")
    private Integer accessCount;

    @Schema(description = "最后访问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;

    @Schema(description = "文件MD5值")
    private String md5Hash;

    @Schema(description = "文件SHA1值")
    private String sha1Hash;

    @Schema(description = "是否为临时文件")
    private Boolean isTemp;

    @Schema(description = "临时文件过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tempExpireTime;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    @Schema(description = "是否逻辑删除")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 文件状态枚举
     */
    public static class Status {
        public static final int NORMAL = 0;     // 正常
        public static final int TRASH = 1;      // 回收站
        public static final int DELETED = 2;    // 已删除
    }

    /**
     * 上传者类型枚举
     */
    public static class UploaderType {
        public static final String USER = "USER";   // 普通用户
        public static final String ADMIN = "ADMIN"; // 管理员
        public static final String SYSTEM = "SYSTEM"; // 系统
    }

    /**
     * 存储类型枚举
     */
    public static class StorageType {
        public static final String LOCAL = "LOCAL";   // 本地存储
        public static final String QINIU = "QINIU";   // 七牛云
        public static final String ALIYUN = "ALIYUN"; // 阿里云
        public static final String TENCENT = "TENCENT"; // 腾讯云
    }

    /**
     * 文件分类枚举
     */
    public static class Category {
        public static final String IMAGE = "image";       // 图片
        public static final String VIDEO = "video";       // 视频
        public static final String AUDIO = "audio";       // 音频
        public static final String DOCUMENT = "document"; // 文档
        public static final String ARCHIVE = "archive";   // 压缩包
        public static final String OTHER = "other";       // 其他
        public static final String AVATAR = "avatar";     // 头像
        public static final String COVER = "cover";       // 封面
        public static final String TEMP = "temp";         // 临时文件
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (originalName == null || originalName.isEmpty()) {
            return "";
        }
        int lastDotIndex = originalName.lastIndexOf(".");
        return lastDotIndex > 0 ? originalName.substring(lastDotIndex + 1).toLowerCase() : "";
    }

    /**
     * 判断是否为图片文件
     */
    public boolean isImage() {
        String ext = getFileExtension();
        return ext.equals("jpg") || ext.equals("jpeg") || ext.equals("png") || 
               ext.equals("gif") || ext.equals("bmp") || ext.equals("webp");
    }

    /**
     * 判断是否为视频文件
     */
    public boolean isVideo() {
        String ext = getFileExtension();
        return ext.equals("mp4") || ext.equals("avi") || ext.equals("mov") || 
               ext.equals("wmv") || ext.equals("flv") || ext.equals("mkv");
    }

    /**
     * 判断是否为音频文件
     */
    public boolean isAudio() {
        String ext = getFileExtension();
        return ext.equals("mp3") || ext.equals("wav") || ext.equals("flac") || 
               ext.equals("aac") || ext.equals("ogg");
    }

    /**
     * 判断是否为文档文件
     */
    public boolean isDocument() {
        String ext = getFileExtension();
        return ext.equals("pdf") || ext.equals("doc") || ext.equals("docx") || 
               ext.equals("xls") || ext.equals("xlsx") || ext.equals("ppt") || 
               ext.equals("pptx") || ext.equals("txt");
    }

    /**
     * 格式化文件大小
     */
    public String getFileSizeFormatted() {
        if (fileSize == null) {
            return "0 B";
        }
        
        long size = fileSize;
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 判断是否在回收站
     */
    public boolean isInTrash() {
        return status != null && status == Status.TRASH;
    }

    /**
     * 判断是否已删除
     */
    public boolean isDeleted() {
        return status != null && status == Status.DELETED;
    }

    /**
     * 判断是否为临时文件且已过期
     */
    public boolean isTempExpired() {
        return isTemp != null && isTemp && tempExpireTime != null && 
               tempExpireTime.isBefore(LocalDateTime.now());
    }
}
