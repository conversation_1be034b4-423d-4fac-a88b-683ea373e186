import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import router from '../router'

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: '/api', // API基础URL
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 从localStorage获取token
    const token = localStorage.getItem('admin_token')

    // 如果有token则添加到请求头
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`

      // 特殊处理 /api/admin/system/info 和 /api/admin/system/config/batch 请求
      if (config.url && (
          config.url.includes('/admin/system/info') ||
          config.url.includes('/admin/system/config/batch')
        )) {
        console.log(`特殊处理请求 ${config.url}，确保添加Authorization头: Bearer ${token.substring(0, 10)}...`)

        // 确保这些请求总是带上token
        if (!config.headers['Authorization']) {
          config.headers['Authorization'] = `Bearer ${token}`
        }
      } else {
        console.log(`请求 ${config.url} 添加Authorization头: Bearer ${token.substring(0, 10)}...`)
      }
    } else {
      console.log(`请求 ${config.url} 没有添加Authorization头`)
    }

    return config
  },
  (error) => {
    console.error('请求错误', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data

    // 如果返回的状态码不是200，说明接口请求失败
    if (res.code !== 200) {
      ElMessage({
        message: res.message || '系统错误',
        type: 'error',
        duration: 5 * 1000
      })

      // 401: 未登录或token过期
      if (res.code === 401) {
        // 询问是否重新登录
        ElMessageBox.confirm('您已登出，可以取消继续留在该页面，或者重新登录', '确认登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 清除本地token
          localStorage.removeItem('admin_token')

          // 跳转到登录页
          router.replace({
            path: '/login',
            query: { redirect: router.currentRoute.value.fullPath }
          })
        })
      }

      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res
    }
  },
  (error) => {
    console.error('响应错误', error)

    // 处理网络错误
    let message = '网络错误，请稍后重试'
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求错误'
          break
        case 401:
          message = '未授权，请登录'
          // 清除本地token
          localStorage.removeItem('admin_token')
          // 跳转到登录页
          router.replace({
            path: '/login',
            query: { redirect: router.currentRoute.value.fullPath }
          })
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误${error.response.status}`
      }
    }

    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(error)
  }
)

export default service
