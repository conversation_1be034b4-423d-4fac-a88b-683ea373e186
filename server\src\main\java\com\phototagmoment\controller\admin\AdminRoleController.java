package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.dto.AdminRoleDTO;
import com.phototagmoment.entity.AdminRole;
import com.phototagmoment.service.NewAdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理员角色控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/role")
@Tag(name = "管理员角色", description = "管理员角色相关接口")
public class AdminRoleController {

    @Autowired
    private NewAdminService adminService;

    /**
     * 获取角色列表
     *
     * @param page     页码
     * @param pageSize 每页条数
     * @param keyword  关键字
     * @return 角色列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取角色列表", description = "获取角色列表")
    public Result<IPage<AdminRole>> list(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        IPage<AdminRole> roles = adminService.getRoleList(page, pageSize, keyword);
        return Result.success(roles);
    }

    /**
     * 获取角色详情
     *
     * @param id 角色ID
     * @return 角色详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取角色详情", description = "根据ID获取角色详情")
    public Result<AdminRole> getById(@Parameter(description = "角色ID") @PathVariable Long id) {
        AdminRole role = adminService.getRoleById(id);
        return Result.success(role);
    }

    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 角色ID
     */
    @PostMapping
    @Operation(summary = "创建角色", description = "创建新角色")
    public Result<Long> create(@RequestBody @Valid AdminRoleDTO roleDTO) {
        Long roleId = adminService.createRole(roleDTO);
        return Result.success(roleId);
    }

    /**
     * 更新角色
     *
     * @param id      角色ID
     * @param roleDTO 角色信息
     * @return 是否成功
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新角色", description = "更新角色信息")
    public Result<Boolean> update(
            @Parameter(description = "角色ID") @PathVariable Long id,
            @RequestBody @Valid AdminRoleDTO roleDTO) {
        boolean result = adminService.updateRole(id, roleDTO);
        return Result.success(result);
    }

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色", description = "删除角色")
    public Result<Boolean> delete(@Parameter(description = "角色ID") @PathVariable Long id) {
        boolean result = adminService.deleteRole(id);
        return Result.success(result);
    }

    /**
     * 更新角色状态
     *
     * @param id     角色ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新角色状态", description = "更新角色状态")
    public Result<Boolean> updateStatus(
            @Parameter(description = "角色ID") @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用") @RequestParam Integer status) {
        boolean result = adminService.updateRoleStatus(id, status);
        return Result.success(result);
    }

    /**
     * 获取角色权限
     *
     * @param id 角色ID
     * @return 权限ID列表
     */
    @GetMapping("/{id}/permissions")
    @Operation(summary = "获取角色权限", description = "获取角色权限ID列表")
    public Result<List<Long>> getRolePermissions(@Parameter(description = "角色ID") @PathVariable Long id) {
        List<Long> permissionIds = adminService.getRolePermissionIds(id);
        return Result.success(permissionIds);
    }

    /**
     * 分配角色权限
     *
     * @param id            角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    @PostMapping("/{id}/permissions")
    @Operation(summary = "分配角色权限", description = "分配角色权限")
    public Result<Boolean> assignPermissions(
            @Parameter(description = "角色ID") @PathVariable Long id,
            @RequestBody List<Long> permissionIds) {
        boolean result = adminService.assignRolePermissions(id, permissionIds);
        return Result.success(result);
    }
}
