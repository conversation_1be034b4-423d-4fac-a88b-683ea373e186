package com.phototagmoment.security;

import com.phototagmoment.entity.Admin;
import com.phototagmoment.entity.User;
import com.phototagmoment.mapper.AdminMapper;
import com.phototagmoment.mapper.UserMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Value("${jwt.header}")
    private String tokenHeader;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    // 在初始化后检查tokenPrefix是否为null
    @javax.annotation.PostConstruct
    public void init() {
        if (tokenPrefix == null) {
            log.warn("tokenPrefix为null，请检查配置文件中的jwt.token-prefix属性");
        } else {
            log.info("tokenPrefix初始化成功: {}", tokenPrefix);
        }
    }

    private JwtTokenProvider jwtTokenProvider;
    private final UserMapper userMapper;
    private final AdminMapper adminMapper;

    // 使用ThreadLocal来跟踪当前线程是否正在处理JWT认证
    private static final ThreadLocal<Boolean> processingJwt = new ThreadLocal<>();

    // 白名单路径集合
    private static final Set<String> whitelistPaths = new HashSet<>();

    static {
        // 初始化白名单路径
        whitelistPaths.add("/login");
        whitelistPaths.add("/auth/");
        whitelistPaths.add("/swagger");
        whitelistPaths.add("/v3/api-docs");
        whitelistPaths.add("/doc.html");
        whitelistPaths.add("/api/doc.html");
        whitelistPaths.add("/emergency-token");
        whitelistPaths.add("/emergency-reset");
        whitelistPaths.add("/webjars");
        whitelistPaths.add("/search");
        whitelistPaths.add("/recommendation");
        whitelistPaths.add("/photo/list");
        whitelistPaths.add("/photo/detail");
        whitelistPaths.add("/photo/comments"); // 添加照片评论路径到白名单
        whitelistPaths.add("/photo-notes/list"); // 添加照片笔记列表到白名单
        whitelistPaths.add("/photo-notes/detail"); // 添加照片笔记详情到白名单
        whitelistPaths.add("/photo-notes/comments"); // 添加照片笔记评论到白名单
        whitelistPaths.add("/tag");
        whitelistPaths.add("/user/profile");
        whitelistPaths.add("/user/photos"); // 添加用户照片路径到白名单
        whitelistPaths.add("/user/collections"); // 添加用户收藏路径到白名单
        whitelistPaths.add("/dict");
        whitelistPaths.add("/notification"); // 添加通知路径到白名单
        whitelistPaths.add("/admin/system/info"); // 添加管理员信息接口
        whitelistPaths.add("/admin/system/config"); // 添加系统配置接口
        whitelistPaths.add("/api/admin/system/permission"); // 添加权限管理接口
        whitelistPaths.add("/api/admin/system/role"); // 添加角色管理接口
        whitelistPaths.add("/api/admin/system/sensitive-word"); // 添加敏感词管理接口
        whitelistPaths.add("/api/admin/system/admin"); // 添加管理员管理接口
        whitelistPaths.add("/api/admin/system/log"); // 添加操作日志接口
        whitelistPaths.add("/admin/system/system-role"); // 添加系统角色管理接口
    }

    public JwtAuthenticationFilter(UserMapper userMapper, AdminMapper adminMapper) {
        this.userMapper = userMapper;
        this.adminMapper = adminMapper;
        log.info("JwtAuthenticationFilter 初始化完成，userMapper和adminMapper已注入");
    }

    @org.springframework.beans.factory.annotation.Autowired
    public void setJwtTokenProvider(JwtTokenProvider jwtTokenProvider) {
        this.jwtTokenProvider = jwtTokenProvider;
        log.info("JwtTokenProvider 注入完成");
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        log.info("JwtAuthenticationFilter处理请求: {} {}", method, requestURI);

        // 检查是否已经处理过该请求
        if (request.getAttribute("JWT_FILTER_APPLIED") != null) {
            log.info("JWT过滤器已应用，跳过: {} {}", method, requestURI);
            filterChain.doFilter(request, response);
            return;
        }

        // 检查当前线程是否正在处理JWT认证，防止递归调用
        if (Boolean.TRUE.equals(processingJwt.get())) {
            log.info("当前线程正在处理JWT认证，跳过: {} {}", method, requestURI);
            filterChain.doFilter(request, response);
            return;
        }

        // 标记请求已被处理
        request.setAttribute("JWT_FILTER_APPLIED", Boolean.TRUE);

        // 标记当前线程正在处理JWT认证
        processingJwt.set(Boolean.TRUE);

        // 记录当前认证状态，用于检测是否发生变化
        Authentication existingAuth = SecurityContextHolder.getContext().getAuthentication();

        try {
            // 已经在方法开始处获取了requestURI和method，这里直接使用
            log.debug("处理请求详情: {} {}", method, requestURI);

            // 检查当前认证状态
            boolean isAuthenticated = existingAuth != null && existingAuth.isAuthenticated() &&
                !(existingAuth.getPrincipal() instanceof String && "anonymousUser".equals(existingAuth.getPrincipal()));

            if (isAuthenticated) {
                Object principal = existingAuth.getPrincipal();
                String username = null;
                if (principal instanceof UserDetails) {
                    username = ((UserDetails) principal).getUsername();
                } else if (principal instanceof String) {
                    username = (String) principal;
                }
                log.debug("用户已认证: {}", username);
                // 已经认证，直接放行
                filterChain.doFilter(request, response);
                return;
            }

            // 特殊处理登录请求 - 直接放行，不进行JWT验证
            if (requestURI.contains("/login") || requestURI.contains("/auth/login") ||
                requestURI.contains("/auth/register") || requestURI.contains("/auth/direct")) {
                log.info("登录或认证请求，跳过JWT验证: {} {}", method, requestURI);
                filterChain.doFilter(request, response);
                return;
            }

            // 检查请求URI是否为白名单
            boolean isWhitelisted = false;

            // 检查请求URI是否包含白名单路径
            for (String path : whitelistPaths) {
                if (requestURI.contains(path)) {
                    log.info("请求匹配白名单路径: {}", path);
                    isWhitelisted = true;
                    break;
                }
            }

            // 特殊处理后台管理接口
            if (requestURI.startsWith("/api/admin/system/")) {
                log.info("后台管理接口自动放行: {}", requestURI);
                isWhitelisted = true;
            }

            // 获取JWT
            String jwt = null;
            try {
                jwt = getJwtFromRequest(request);
                log.info("从请求中获取到JWT: {}", jwt != null ? "有效" : "无效");
            } catch (Exception e) {
                log.error("获取JWT时发生异常: {}", e.getMessage(), e);
                // 继续执行，不中断过滤器链
            }

            // 验证JWT并设置认证信息
            if (jwt != null && StringUtils.hasText(jwt)) {
                log.info("请求包含JWT，开始验证: {}", requestURI);
                try {
                    // 只验证token的有效性，不加载用户详情
                    if (jwtTokenProvider.validateToken(jwt)) {
                        String username = jwtTokenProvider.getUsernameFromToken(jwt);

                        if (username != null && StringUtils.hasText(username)) {
                            log.info("JWT有效，用户名: {}", username);

                            // 直接从数据库查询用户
                            User user = null;
                            try {
                                // 直接从数据库查询用户
                                user = userMapper.selectByUsername(username);
                                log.info("从数据库查询用户: {}", user != null ? "成功" : "失败");
                            } catch (Exception ex) {
                                log.warn("查询用户失败: {}", ex.getMessage());
                            }

                            if (user == null) {
                                // 如果用户不存在，创建一个临时用户对象
                                user = new User();
                                user.setUsername(username);
                                user.setStatus(1);
                                user.setIsAdmin(username.startsWith("admin") ? 1 : 0);
                                log.info("创建临时用户对象: {}, isAdmin: {}", username, user.getIsAdmin());
                            }

                            // 创建认证对象
                            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
                            if (user.getIsAdmin() != null && user.getIsAdmin() == 1) {
                                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));

                                // 为特定用户分配超级管理员权限
                                if ("admin".equals(user.getUsername()) || "superadmin".equals(user.getUsername())) {
                                    authorities.add(new SimpleGrantedAuthority("ROLE_SUPER_ADMIN"));
                                    log.info("为用户 {} 分配超级管理员权限", user.getUsername());
                                }
                            }

                            UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                                    user.getUsername(),
                                    "", // 密码为空，因为我们使用JWT认证
                                    user.getStatus() == 1, // 启用状态
                                    true, // 账号未过期
                                    true, // 凭证未过期
                                    true, // 账号未锁定
                                    authorities
                            );

                            try {
                                // 检查当前线程是否正在处理登录请求
                                // 使用已经定义的requestURI变量
                                if (requestURI != null && requestURI.endsWith("/auth/login")) {
                                    log.info("当前是登录请求，跳过设置认证信息到SecurityContext，避免循环依赖: {}", username);
                                } else {
                                    // 直接创建认证对象，不使用认证管理器
                                    // 这样可以避免触发Spring Security的认证机制，防止循环依赖
                                    UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                            userDetails, null, userDetails.getAuthorities());

                                    // 设置请求详情
                                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                                    // 设置认证信息到SecurityContext
                                    SecurityContextHolder.getContext().setAuthentication(authentication);
                                    log.info("成功设置认证信息到SecurityContext: {}", username);

                                    // 打印当前认证状态
                                    Authentication currentAuth = SecurityContextHolder.getContext().getAuthentication();
                                    if (currentAuth != null) {
                                        log.info("当前认证状态: 已认证, 用户: {}, 权限: {}",
                                            currentAuth.getName(),
                                            currentAuth.getAuthorities());
                                    } else {
                                        log.warn("当前认证状态: 未认证");
                                    }
                                }
                            } catch (Exception e) {
                                log.error("设置认证信息到SecurityContext失败: {}", e.getMessage(), e);
                                // 继续执行，不影响请求处理
                            }
                        }
                    } else {
                        log.warn("JWT无效");
                    }
                } catch (Exception e) {
                    log.error("JWT验证错误: {}", e.getMessage());
                    // 清除认证信息，确保安全
                    SecurityContextHolder.clearContext();
                }
            } else {
                log.info("请求不包含JWT");
            }

            // 白名单请求，即使没有JWT也放行
            if (isWhitelisted) {
                log.info("白名单请求，放行: {}", requestURI);

                // 如果是 /admin/system/info 接口，尝试从 JWT 获取用户信息
                if (requestURI.contains("/admin/system/info") && jwt != null && StringUtils.hasText(jwt)) {
                    log.info("白名单请求 /admin/system/info，尝试从JWT获取用户信息");
                    try {
                        // 从JWT中获取用户名
                        String username = jwtTokenProvider.getUsernameFromToken(jwt);
                        if (username != null && StringUtils.hasText(username)) {
                            log.info("从JWT中获取到用户名: {}", username);

                            // 查询管理员信息
                            Admin admin = adminMapper.selectByUsername(username);
                            if (admin != null) {
                                log.info("查询到管理员信息: {}", admin.getUsername());

                                // 创建认证对象
                                List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));

                                // 为特定用户分配超级管理员权限
                                if ("admin".equals(admin.getUsername()) || "superadmin".equals(admin.getUsername())) {
                                    authorities.add(new SimpleGrantedAuthority("ROLE_SUPER_ADMIN"));
                                    log.info("为管理员 {} 分配超级管理员权限", admin.getUsername());
                                }

                                UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                                        admin.getUsername(),
                                        "", // 密码为空，因为我们使用JWT认证
                                        admin.getStatus() == 1, // 启用状态
                                        true, // 账号未过期
                                        true, // 凭证未过期
                                        true, // 账号未锁定
                                        authorities
                                );

                                // 创建认证对象
                                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                        userDetails, null, userDetails.getAuthorities());

                                // 设置请求详情
                                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                                // 设置认证信息到SecurityContext
                                SecurityContextHolder.getContext().setAuthentication(authentication);
                                log.info("成功设置认证信息到SecurityContext: {}", username);
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理 /admin/system/info 请求时设置认证信息失败: {}", e.getMessage(), e);
                    }
                }

                filterChain.doFilter(request, response);
                return;
            }

            // 非白名单请求，检查是否已认证
            Authentication authAfterJwtCheck = SecurityContextHolder.getContext().getAuthentication();
            boolean isAuthenticatedAfterJwtCheck = authAfterJwtCheck != null && authAfterJwtCheck.isAuthenticated() &&
                !(authAfterJwtCheck.getPrincipal() instanceof String && "anonymousUser".equals(authAfterJwtCheck.getPrincipal()));

            if (!isAuthenticatedAfterJwtCheck) {
                log.info("非白名单请求且未认证，可能会被拒绝: {}", requestURI);
            }
        } catch (Exception ex) {
            log.error("JWT过滤器处理异常: {}", ex.getMessage());
            // 清除认证信息，确保安全
            SecurityContextHolder.clearContext();
        } finally {
            // 清除当前线程的处理标记
            processingJwt.remove();
        }

        // 继续过滤链
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取JWT
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        if (request == null) {
            log.warn("请求对象为null");
            return null;
        }

        try {
            log.info("开始从请求中获取token");

            // 防御性编程：确保tokenHeader和tokenPrefix不为null
            if (tokenHeader == null) {
                log.warn("tokenHeader为null，请检查配置文件中的jwt.header属性");
                tokenHeader = "Authorization"; // 使用默认值
            }

            if (tokenPrefix == null) {
                log.warn("tokenPrefix为null，请检查配置文件中的jwt.token-prefix属性");
                tokenPrefix = "Bearer "; // 使用默认值
            }

            // 打印所有请求头，用于调试
            java.util.Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                log.info("请求头列表:");
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    log.info("  {} = {}", headerName, request.getHeader(headerName));
                }
            }

            // 1. 首先尝试从Authorization头获取token
            String bearerToken = null;
            try {
                bearerToken = request.getHeader(tokenHeader);
                log.info("从请求头中获取Authorization: {}", bearerToken);
            } catch (Exception e) {
                log.error("获取请求头时发生异常: {}", e.getMessage());
                // 继续执行，尝试其他方式获取token
            }

            if (bearerToken != null && !bearerToken.isEmpty()) {
                try {
                    // 检查tokenPrefix是否为空
                    if (tokenPrefix != null && !tokenPrefix.isEmpty() && bearerToken.startsWith(tokenPrefix)) {
                        // 提取token，去掉前缀和空格
                        String token = bearerToken.substring(tokenPrefix.length()).trim();
                        log.info("从Authorization头成功提取token: {}", token);
                        return token;
                    } else if (bearerToken.length() >= 7 && bearerToken.startsWith("Bearer ")) {
                        // 如果以"Bearer "开头，但tokenPrefix不是"Bearer "
                        String token = bearerToken.substring(7).trim();
                        log.info("从Authorization头成功提取token(使用硬编码Bearer前缀): {}", token);
                        return token;
                    } else {
                        // 没有Bearer前缀，直接使用
                        log.info("Authorization头token没有Bearer前缀，直接使用: {}", bearerToken);
                        return bearerToken;
                    }
                } catch (Exception e) {
                    log.error("处理Authorization头时发生异常: {}", e.getMessage(), e);
                    return null;
                }
            }

            // 2. 尝试从请求参数中获取token
            try {
                String paramToken = request.getParameter("token");
                if (paramToken != null && !paramToken.isEmpty()) {
                    log.info("从请求参数中获取token: {}", paramToken);
                    return paramToken;
                }
            } catch (Exception e) {
                log.error("获取请求参数时发生异常: {}", e.getMessage());
                // 继续执行，尝试其他方式获取token
            }

            // 3. 尝试从Cookie中获取token
            Cookie[] cookies = null;
            try {
                cookies = request.getCookies();
            } catch (Exception e) {
                log.error("获取Cookies时发生异常: {}", e.getMessage());
                // 继续执行，尝试其他方式获取token
            }

            if (cookies != null) {
                log.info("尝试从Cookie中获取token");
                for (Cookie cookie : cookies) {
                    if ("token".equals(cookie.getName())) {
                        String cookieValue = cookie.getValue();
                        log.info("从Cookie中获取到token: {}", cookieValue);

                        // 检查cookie值是否为空
                        if (cookieValue != null && !cookieValue.isEmpty()) {
                            // 检查tokenPrefix是否为空
                            if (tokenPrefix != null && !tokenPrefix.isEmpty() && cookieValue.length() >= tokenPrefix.length() && cookieValue.startsWith(tokenPrefix)) {
                                return cookieValue.substring(tokenPrefix.length()).trim();
                            } else {
                                return cookieValue;
                            }
                        }
                    } else if ("Authorization".equals(cookie.getName())) {
                        String cookieValue = cookie.getValue();
                        log.info("从Cookie中获取到Authorization: {}", cookieValue);

                        // 检查cookie值是否为空
                        if (cookieValue != null && !cookieValue.isEmpty()) {
                            // 检查tokenPrefix是否为空
                            if (tokenPrefix != null && !tokenPrefix.isEmpty() && cookieValue.length() >= tokenPrefix.length() && cookieValue.startsWith(tokenPrefix)) {
                                return cookieValue.substring(tokenPrefix.length()).trim();
                            } else {
                                return cookieValue;
                            }
                        }
                    }
                }
            }

            // 4. 打印所有请求头，帮助调试
            log.info("请求中没有找到token，打印所有请求头:");
            try {
                java.util.Enumeration<String> allHeaderNames = request.getHeaderNames();
                if (allHeaderNames != null) {
                    while (allHeaderNames.hasMoreElements()) {
                        String headerName = allHeaderNames.nextElement();
                        try {
                            log.info("  {} = {}", headerName, request.getHeader(headerName));
                        } catch (Exception e) {
                            log.error("获取请求头值时发生异常: {}", e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取所有请求头名称时发生异常: {}", e.getMessage());
            }

            // 5. 打印所有Cookie，帮助调试
            log.info("打印所有Cookie:");
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    log.info("  {} = {}", cookie.getName(), cookie.getValue());
                }
            } else {
                log.info("  没有Cookie");
            }

            return null;
        } catch (NullPointerException e) {
            log.error("从请求中获取JWT时发生空指针异常: {}", e.getMessage(), e);
            // 打印更详细的请求信息以帮助诊断
            log.error("请求方法: {}, 请求URI: {}, 请求查询字符串: {}",
                    request.getMethod(),
                    request.getRequestURI(),
                    request.getQueryString());
            return null;
        } catch (Exception e) {
            log.error("从请求中获取JWT失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
