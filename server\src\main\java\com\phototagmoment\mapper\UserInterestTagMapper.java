package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.UserInterestTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户兴趣标签Mapper接口
 */
@Mapper
public interface UserInterestTagMapper extends BaseMapper<UserInterestTag> {

    /**
     * 获取用户兴趣标签列表（按兴趣分数降序）
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 兴趣标签列表
     */
    @Select("SELECT tag_name FROM ptm_user_interest WHERE user_id = #{userId} ORDER BY interest_score DESC LIMIT #{limit}")
    List<String> selectUserInterestTags(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取用户对指定标签的兴趣分数
     *
     * @param userId 用户ID
     * @param tagName 标签名称
     * @return 兴趣分数
     */
    @Select("SELECT interest_score FROM ptm_user_interest WHERE user_id = #{userId} AND tag_name = #{tagName}")
    Double selectInterestScore(@Param("userId") Long userId, @Param("tagName") String tagName);

    /**
     * 批量插入或更新用户兴趣标签
     *
     * @param userInterestTags 用户兴趣标签列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<UserInterestTag> userInterestTags);

    /**
     * 删除用户的所有兴趣标签
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);
}
