package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.entity.IdentityVerification;

/**
 * 扩展的实名认证服务接口
 */
public interface ExtendedIdentityVerificationService extends IdentityVerificationService, IService<IdentityVerification> {

    /**
     * 分页查询实名认证记录
     *
     * @param page         分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<IdentityVerification> page(Page<IdentityVerification> page, LambdaQueryWrapper<IdentityVerification> queryWrapper);

    /**
     * 根据ID查询实名认证记录
     *
     * @param id 认证记录ID
     * @return 实名认证记录
     */
    IdentityVerification getById(Long id);
}
