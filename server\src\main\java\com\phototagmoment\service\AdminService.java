package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.dto.LoginDTO;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.vo.AdminVO;
import com.phototagmoment.vo.TokenVO;

/**
 * 管理员服务接口
 */
public interface AdminService {

    /**
     * 管理员登录
     *
     * @param loginDTO 登录信息
     * @return Token信息
     */
    TokenVO login(LoginDTO loginDTO);

    /**
     * 获取当前管理员信息
     *
     * @return 管理员信息
     */
    AdminVO getCurrentAdmin();

    /**
     * 管理员登出
     *
     * @return 是否成功
     */
    boolean logout();

    /**
     * 获取用户列表
     *
     * @param page     页码
     * @param size     每页大小
     * @param username 用户名
     * @param phone    手机号
     * @param email    邮箱
     * @param status   状态
     * @return 用户列表
     */
    IPage<UserDTO> getUserList(int page, int size, String username, String phone, String email, Integer status);

    /**
     * 获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserDTO getUserDetail(Long id);

    /**
     * 更新用户状态
     *
     * @param id     用户ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    boolean updateUserStatus(Long id, Integer status);

    /**
     * 获取实名认证列表
     *
     * @param page   页码
     * @param size   每页大小
     * @param status 状态：0-待审核，1-已通过，2-已拒绝
     * @return 实名认证列表
     */
    IPage<UserDTO> getVerificationList(int page, int size, Integer status);

    /**
     * 审核实名认证
     *
     * @param id     用户ID
     * @param status 状态：1-通过，2-拒绝
     * @param reason 拒绝理由
     * @return 是否成功
     */
    boolean verifyUser(Long id, Integer status, String reason);

    /**
     * 将User转换为AdminVO
     *
     * @param user 用户信息
     * @return 管理员VO
     */
    AdminVO convertToAdminVO(User user);
}
