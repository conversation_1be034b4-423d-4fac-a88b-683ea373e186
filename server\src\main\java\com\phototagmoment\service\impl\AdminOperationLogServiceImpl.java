package com.phototagmoment.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.entity.AdminOperationLog;
import com.phototagmoment.mapper.AdminOperationLogMapper;
import com.phototagmoment.service.AdminOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 管理员操作日志服务实现类
 */
@Slf4j
@Service
public class AdminOperationLogServiceImpl implements AdminOperationLogService {

    @Autowired
    private AdminOperationLogMapper adminOperationLogMapper;

    @Override
    public IPage<AdminOperationLog> getOperationLogList(Integer page, Integer pageSize, String keyword,
                                                      String module, String operation, String startDate, String endDate) {
        Page<AdminOperationLog> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<AdminOperationLog> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StrUtil.isNotBlank(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(AdminOperationLog::getUsername, keyword)
                    .or()
                    .like(AdminOperationLog::getContent, keyword));
        }

        if (StrUtil.isNotBlank(module)) {
            queryWrapper.eq(AdminOperationLog::getModule, module);
        }

        if (StrUtil.isNotBlank(operation)) {
            queryWrapper.eq(AdminOperationLog::getOperation, operation);
        }

        if (StrUtil.isNotBlank(startDate)) {
            LocalDateTime startDateTime = LocalDate.parse(startDate).atStartOfDay();
            queryWrapper.ge(AdminOperationLog::getCreatedAt, startDateTime);
        }

        if (StrUtil.isNotBlank(endDate)) {
            LocalDateTime endDateTime = LocalDate.parse(endDate).plusDays(1).atStartOfDay();
            queryWrapper.lt(AdminOperationLog::getCreatedAt, endDateTime);
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(AdminOperationLog::getCreatedAt);

        return adminOperationLogMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public void exportOperationLog(String keyword, String module, String operation,
                                  String startDate, String endDate, HttpServletResponse response) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<AdminOperationLog> queryWrapper = new LambdaQueryWrapper<>();

            if (StrUtil.isNotBlank(keyword)) {
                queryWrapper.and(wrapper -> wrapper
                        .like(AdminOperationLog::getUsername, keyword)
                        .or()
                        .like(AdminOperationLog::getContent, keyword));
            }

            if (StrUtil.isNotBlank(module)) {
                queryWrapper.eq(AdminOperationLog::getModule, module);
            }

            if (StrUtil.isNotBlank(operation)) {
                queryWrapper.eq(AdminOperationLog::getOperation, operation);
            }

            if (StrUtil.isNotBlank(startDate)) {
                LocalDateTime startDateTime = LocalDate.parse(startDate).atStartOfDay();
                queryWrapper.ge(AdminOperationLog::getCreatedAt, startDateTime);
            }

            if (StrUtil.isNotBlank(endDate)) {
                LocalDateTime endDateTime = LocalDate.parse(endDate).plusDays(1).atStartOfDay();
                queryWrapper.lt(AdminOperationLog::getCreatedAt, endDateTime);
            }

            // 按创建时间降序排序
            queryWrapper.orderByDesc(AdminOperationLog::getCreatedAt);

            // 查询数据
            List<AdminOperationLog> logs = adminOperationLogMapper.selectList(queryWrapper);

            // 创建Excel写入器
            ExcelWriter writer = ExcelUtil.getWriter();

            // 设置列宽
            writer.setColumnWidth(0, 10);
            writer.setColumnWidth(1, 15);
            writer.setColumnWidth(2, 15);
            writer.setColumnWidth(3, 15);
            writer.setColumnWidth(4, 40);
            writer.setColumnWidth(5, 15);
            writer.setColumnWidth(6, 40);
            writer.setColumnWidth(7, 20);

            // 设置表头
            writer.addHeaderAlias("id", "ID");
            writer.addHeaderAlias("username", "用户名");
            writer.addHeaderAlias("module", "模块");
            writer.addHeaderAlias("operation", "操作类型");
            writer.addHeaderAlias("content", "操作内容");
            writer.addHeaderAlias("ip", "IP地址");
            writer.addHeaderAlias("userAgent", "User-Agent");
            writer.addHeaderAlias("createdAt", "操作时间");

            // 写入数据
            writer.write(logs, true);

            // 设置响应头
            String fileName = URLEncoder.encode("操作日志_" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss"), StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 输出
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            out.close();
        } catch (IOException e) {
            log.error("导出操作日志失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearOperationLog() {
        try {
            adminOperationLogMapper.delete(null);
            return true;
        } catch (Exception e) {
            log.error("清空操作日志失败", e);
            return false;
        }
    }

    @Override
    public void recordOperationLog(Long adminId, String username, String module,
                                  String operation, String content, String ip, String userAgent) {
        try {
            AdminOperationLog log = new AdminOperationLog();
            log.setAdminId(adminId);
            log.setUsername(username);
            log.setModule(module);
            log.setOperation(operation);
            log.setContent(content);
            log.setIp(ip);
            log.setUserAgent(userAgent);
            log.setCreatedAt(LocalDateTime.now());
            adminOperationLogMapper.insert(log);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }
}
