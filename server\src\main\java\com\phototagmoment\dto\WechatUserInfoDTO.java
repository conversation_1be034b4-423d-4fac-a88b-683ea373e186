package com.phototagmoment.dto;

import lombok.Data;

/**
 * 微信用户信息DTO
 */
@Data
public class WechatUserInfoDTO {

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 用户unionId
     */
    private String unionId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String headImgUrl;

    /**
     * 用户性别（1：男，2：女，0：未知）
     */
    private Integer sex;

    /**
     * 用户所在国家
     */
    private String country;

    /**
     * 用户所在省份
     */
    private String province;

    /**
     * 用户所在城市
     */
    private String city;

    /**
     * 用户语言
     */
    private String language;

    /**
     * 用户特权信息
     */
    private String[] privilege;

    /**
     * 是否关注公众号
     */
    private Boolean subscribe;

    /**
     * 关注时间
     */
    private Long subscribeTime;

    /**
     * 关注场景
     */
    private String subscribeScene;

    /**
     * 二维码扫码场景
     */
    private String qrScene;

    /**
     * 二维码扫码场景描述
     */
    private String qrSceneStr;
}
