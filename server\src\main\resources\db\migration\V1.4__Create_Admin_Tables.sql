-- 创建管理员表
CREATE TABLE IF NOT EXISTS `ptm_admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 创建管理员角色表
CREATE TABLE IF NOT EXISTS `ptm_admin_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统内置：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色表';

-- 创建管理员权限表
CREATE TABLE IF NOT EXISTS `ptm_admin_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限编码',
  `description` varchar(255) DEFAULT NULL COMMENT '权限描述',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '权限类型：1-菜单，2-按钮，3-接口',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父权限ID',
  `path` varchar(255) DEFAULT NULL COMMENT '路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统内置：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员权限表';

-- 创建管理员角色权限关联表
CREATE TABLE IF NOT EXISTS `ptm_admin_role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`,`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色权限关联表';

-- 创建管理员操作日志表
CREATE TABLE IF NOT EXISTS `ptm_admin_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` bigint(20) NOT NULL COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `module` varchar(50) NOT NULL COMMENT '模块',
  `operation` varchar(50) NOT NULL COMMENT '操作',
  `content` varchar(500) DEFAULT NULL COMMENT '内容',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- 插入默认权限数据
INSERT INTO `ptm_admin_permission` (`name`, `code`, `description`, `type`, `parent_id`, `path`, `icon`, `sort`, `status`, `is_system`)
VALUES
-- 系统管理
('系统管理', 'system', '系统管理', 1, NULL, '/system', 'setting', 1, 1, 1),
-- 管理员管理
('管理员管理', 'system:admin', '管理员管理', 1, 1, '/system/admin', 'user', 1, 1, 1),
('管理员列表', 'system:admin:list', '管理员列表', 2, 2, NULL, NULL, 1, 1, 1),
('管理员创建', 'system:admin:create', '管理员创建', 2, 2, NULL, NULL, 2, 1, 1),
('管理员编辑', 'system:admin:edit', '管理员编辑', 2, 2, NULL, NULL, 3, 1, 1),
('管理员删除', 'system:admin:delete', '管理员删除', 2, 2, NULL, NULL, 4, 1, 1),
('管理员状态修改', 'system:admin:status', '管理员状态修改', 2, 2, NULL, NULL, 5, 1, 1),
('管理员密码重置', 'system:admin:reset-password', '管理员密码重置', 2, 2, NULL, NULL, 6, 1, 1),
-- 角色管理
('角色管理', 'system:role', '角色管理', 1, 1, '/system/role', 'team', 2, 1, 1),
('角色列表', 'system:role:list', '角色列表', 2, 10, NULL, NULL, 1, 1, 1),
('角色创建', 'system:role:create', '角色创建', 2, 10, NULL, NULL, 2, 1, 1),
('角色编辑', 'system:role:edit', '角色编辑', 2, 10, NULL, NULL, 3, 1, 1),
('角色删除', 'system:role:delete', '角色删除', 2, 10, NULL, NULL, 4, 1, 1),
('角色权限设置', 'system:role:permission', '角色权限设置', 2, 10, NULL, NULL, 5, 1, 1),
-- 日志管理
('日志管理', 'system:log', '日志管理', 1, 1, '/system/log', 'file-text', 3, 1, 1),
('操作日志', 'system:log:operation', '操作日志', 2, 16, NULL, NULL, 1, 1, 1);

-- 插入超级管理员角色
INSERT INTO `ptm_admin_role` (`name`, `code`, `description`, `status`, `is_system`)
VALUES ('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1, 1);

-- 获取超级管理员角色ID
SET @super_admin_role_id = LAST_INSERT_ID();

-- 插入超级管理员
INSERT INTO `ptm_admin` (`username`, `password`, `name`, `email`, `phone`, `status`, `role_id`)
VALUES ('admin', '$2a$10$ySG2lkvjFHY5O0./CPIE1OI8VJsuKYEzOYzqIa7AJR6sEgSzUFOAm', '系统管理员', '<EMAIL>', '13800138000', 1, @super_admin_role_id);

-- 获取所有权限ID
INSERT INTO `ptm_admin_role_permission` (`role_id`, `permission_id`)
SELECT @super_admin_role_id, id FROM `ptm_admin_permission`;
