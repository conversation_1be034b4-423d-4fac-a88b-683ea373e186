package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.config.IdentityVerificationConfig;
import com.phototagmoment.dto.IdentityVerificationDTO;
import com.phototagmoment.entity.IdentityVerification;
import com.phototagmoment.entity.User;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.IdentityVerificationMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.IdentityVerificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 支付宝实名认证服务实现类
 * 
 * 注意：此类为示例实现，实际使用时需要引入支付宝SDK并完善代码
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "identity-verification.provider", havingValue = "alipay")
public class AlipayIdentityVerificationServiceImpl extends ServiceImpl<IdentityVerificationMapper, IdentityVerification> implements IdentityVerificationService {

    @Autowired
    private IdentityVerificationConfig config;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitVerification(Long userId, IdentityVerificationDTO dto) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查是否已经实名认证
        if (isUserVerified(userId)) {
            throw new BusinessException("用户已完成实名认证");
        }

        // 创建实名认证记录
        IdentityVerification verification = new IdentityVerification();
        verification.setUserId(userId);
        verification.setRealName(dto.getRealName());
        verification.setIdCard(dto.getIdCard());
        verification.setIdCardFrontUrl(dto.getIdCardFrontUrl());
        verification.setIdCardBackUrl(dto.getIdCardBackUrl());
        verification.setIdCardHoldingUrl(dto.getIdCardHoldingUrl());
        verification.setFaceImageUrl(dto.getFaceImageUrl());
        verification.setVerifyMethod(1); // 支付宝认证
        verification.setStatus(0); // 待审核

        // 保存实名认证记录
        this.save(verification);

        // 调用支付宝实名认证
        verifyByAlipay(userId, dto.getRealName(), dto.getIdCard());

        return verification.getId();
    }

    @Override
    public IdentityVerification getUserVerification(Long userId) {
        return baseMapper.selectLatestByUserId(userId);
    }

    @Override
    public boolean isUserVerified(Long userId) {
        return baseMapper.countVerifiedByUserId(userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyByAlipay(Long userId, String name, String idCard) {
        log.info("通过支付宝进行实名认证，用户ID：{}，姓名：{}，身份证号：{}", userId, name, idCard);

        // 获取最新的实名认证记录
        IdentityVerification verification = baseMapper.selectLatestByUserId(userId);
        if (verification == null) {
            throw new BusinessException("未找到实名认证记录");
        }

        try {
            // 调用支付宝实名认证API
            // 注意：此处为示例代码，实际使用时需要引入支付宝SDK并完善代码
            boolean passed = callAlipayVerification(name, idCard);

            if (passed) {
                // 更新认证状态为已认证
                verification.setStatus(1);
                verification.setVerifiedAt(LocalDateTime.now());
                this.updateById(verification);

                // 更新用户实名认证状态
                User user = userMapper.selectById(userId);
                user.setIsVerified(1);
                user.setRealName(name);
                userMapper.updateById(user);

                log.info("实名认证通过，用户ID：{}", userId);
            } else {
                // 更新认证状态为认证失败
                verification.setStatus(2);
                verification.setFailReason("支付宝实名认证失败");
                this.updateById(verification);

                log.info("实名认证失败，用户ID：{}", userId);
            }

            return passed;
        } catch (Exception e) {
            log.error("调用支付宝实名认证失败", e);
            
            // 更新认证状态为认证失败
            verification.setStatus(2);
            verification.setFailReason("调用支付宝实名认证失败：" + e.getMessage());
            this.updateById(verification);
            
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyByWechat(Long userId, String name, String idCard) {
        throw new BusinessException("不支持微信实名认证");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyByFaceRecognition(Long userId, String name, String idCard, String faceImageUrl) {
        throw new BusinessException("不支持人脸识别实名认证");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewVerification(Long verificationId, Integer status, String reason) {
        log.info("管理员审核实名认证，认证ID：{}，状态：{}，原因：{}", verificationId, status, reason);

        // 获取实名认证记录
        IdentityVerification verification = this.getById(verificationId);
        if (verification == null) {
            throw new BusinessException("未找到实名认证记录");
        }

        // 检查认证状态
        if (verification.getStatus() != 0) {
            throw new BusinessException("该认证记录已审核");
        }

        // 更新认证状态
        verification.setStatus(status);
        if (status == 1) {
            // 认证通过
            verification.setVerifiedAt(LocalDateTime.now());
            this.updateById(verification);

            // 更新用户实名认证状态
            User user = userMapper.selectById(verification.getUserId());
            user.setIsVerified(1);
            user.setRealName(verification.getRealName());
            userMapper.updateById(user);

            log.info("实名认证审核通过，用户ID：{}", verification.getUserId());
        } else {
            // 认证失败
            verification.setFailReason(reason);
            this.updateById(verification);

            log.info("实名认证审核不通过，用户ID：{}", verification.getUserId());
        }

        return true;
    }

    /**
     * 调用支付宝实名认证API
     * 
     * @param name   真实姓名
     * @param idCard 身份证号
     * @return 认证结果
     */
    private boolean callAlipayVerification(String name, String idCard) {
        // 此处为示例代码，实际使用时需要引入支付宝SDK并完善代码
        log.info("调用支付宝实名认证API，姓名：{}，身份证号：{}", name, idCard);
        
        // 模拟认证结果
        return true;
    }
}
