package com.phototagmoment.service.impl;

import com.phototagmoment.service.ContentModerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Random;

/**
 * 本地内容审核服务实现类
 * 仅用于开发和测试环境，随机返回审核结果
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "content-moderation.provider", havingValue = "local", matchIfMissing = true)
public class LocalContentModerationServiceImpl implements ContentModerationService {

    private final Random random = new Random();
    private String failReason;

    @Override
    public boolean moderateImage(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            failReason = "文件为空";
            return false;
        }

        try {
            return moderateImage(file.getInputStream(), file.getContentType());
        } catch (IOException e) {
            log.error("读取图片文件失败", e);
            failReason = "读取图片文件失败";
            return false;
        }
    }

    @Override
    public boolean moderateImage(InputStream inputStream, String contentType) {
        if (inputStream == null) {
            failReason = "图片输入流为空";
            return false;
        }

        if (contentType == null || !contentType.startsWith("image/")) {
            failReason = "不支持的文件类型";
            return false;
        }

        // 本地模式下，随机返回审核结果，通过率为90%
        boolean passed = random.nextInt(100) < 90;
        if (!passed) {
            // 随机生成审核失败原因
            String[] reasons = {
                "图片包含色情内容",
                "图片包含暴力内容",
                "图片包含政治敏感内容",
                "图片包含广告内容",
                "图片包含违禁品内容"
            };
            failReason = reasons[random.nextInt(reasons.length)];
            log.info("图片审核不通过，原因：{}", failReason);
        } else {
            log.info("图片审核通过");
        }

        return passed;
    }

    @Override
    public boolean moderateImageByUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            failReason = "图片URL为空";
            return false;
        }

        // 本地模式下，随机返回审核结果，通过率为90%
        boolean passed = random.nextInt(100) < 90;
        if (!passed) {
            // 随机生成审核失败原因
            String[] reasons = {
                "图片包含色情内容",
                "图片包含暴力内容",
                "图片包含政治敏感内容",
                "图片包含广告内容",
                "图片包含违禁品内容"
            };
            failReason = reasons[random.nextInt(reasons.length)];
            log.info("图片审核不通过，原因：{}", failReason);
        } else {
            log.info("图片审核通过");
        }

        return passed;
    }

    @Override
    public boolean moderateText(String text) {
        if (text == null || text.isEmpty()) {
            failReason = "文本内容为空";
            return false;
        }

        // 本地模式下，随机返回审核结果，通过率为95%
        boolean passed = random.nextInt(100) < 95;
        if (!passed) {
            // 随机生成审核失败原因
            String[] reasons = {
                "文本包含敏感词汇",
                "文本包含广告内容",
                "文本包含政治敏感内容",
                "文本包含违禁品内容"
            };
            failReason = reasons[random.nextInt(reasons.length)];
            log.info("文本审核不通过，原因：{}", failReason);
        } else {
            log.info("文本审核通过");
        }

        return passed;
    }

    @Override
    public String getFailReason() {
        return failReason;
    }
}
