package com.phototagmoment.service.parser;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.entity.SensitiveWord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * JSON敏感词解析器
 */
@Slf4j
@Component
public class JsonSensitiveWordParser implements SensitiveWordFileParser {

    private static final String SUPPORTED_EXTENSION = "json";
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<SensitiveWord> parse(MultipartFile file) throws Exception {
        log.info("解析JSON文件: {}", file.getOriginalFilename());
        List<SensitiveWord> sensitiveWords = new ArrayList<>();

        try (InputStream is = file.getInputStream()) {
            JsonNode rootNode = objectMapper.readTree(is);
            
            // 处理两种可能的JSON格式：数组或对象
            if (rootNode.isArray()) {
                // 数组格式：[{"word": "敏感词1", "type": "类型", "level": 1}, ...]
                List<Map<String, Object>> wordList = objectMapper.convertValue(
                        rootNode, new TypeReference<List<Map<String, Object>>>() {});
                
                for (Map<String, Object> wordMap : wordList) {
                    try {
                        SensitiveWord sensitiveWord = parseSensitiveWordFromMap(wordMap);
                        if (sensitiveWord != null) {
                            sensitiveWords.add(sensitiveWord);
                        }
                    } catch (Exception e) {
                        log.error("解析JSON数组项失败: {}", e.getMessage(), e);
                    }
                }
            } else if (rootNode.isObject()) {
                // 对象格式：{"words": [{"word": "敏感词1", ...}, ...]}
                JsonNode wordsNode = rootNode.get("words");
                if (wordsNode != null && wordsNode.isArray()) {
                    List<Map<String, Object>> wordList = objectMapper.convertValue(
                            wordsNode, new TypeReference<List<Map<String, Object>>>() {});
                    
                    for (Map<String, Object> wordMap : wordList) {
                        try {
                            SensitiveWord sensitiveWord = parseSensitiveWordFromMap(wordMap);
                            if (sensitiveWord != null) {
                                sensitiveWords.add(sensitiveWord);
                            }
                        } catch (Exception e) {
                            log.error("解析JSON对象项失败: {}", e.getMessage(), e);
                        }
                    }
                } else {
                    // 简单对象格式：{"word": "敏感词1", "type": "类型", "level": 1}
                    try {
                        Map<String, Object> wordMap = objectMapper.convertValue(
                                rootNode, new TypeReference<Map<String, Object>>() {});
                        SensitiveWord sensitiveWord = parseSensitiveWordFromMap(wordMap);
                        if (sensitiveWord != null) {
                            sensitiveWords.add(sensitiveWord);
                        }
                    } catch (Exception e) {
                        log.error("解析JSON单个对象失败: {}", e.getMessage(), e);
                    }
                }
            }
        }
        
        log.info("JSON文件解析完成，共解析出{}个敏感词", sensitiveWords.size());
        return sensitiveWords;
    }

    @Override
    public boolean supports(String fileExtension) {
        return SUPPORTED_EXTENSION.equalsIgnoreCase(fileExtension);
    }
    
    /**
     * 从Map中解析敏感词对象
     */
    private SensitiveWord parseSensitiveWordFromMap(Map<String, Object> wordMap) {
        // 获取敏感词
        Object wordObj = wordMap.get("word");
        if (wordObj == null) {
            return null;
        }
        String word = wordObj.toString().trim();
        if (word.isEmpty()) {
            return null;
        }
        
        SensitiveWord sensitiveWord = new SensitiveWord();
        sensitiveWord.setWord(word);
        
        // 获取类型
        Object typeObj = wordMap.get("type");
        String type = typeObj != null ? typeObj.toString().trim() : "";
        sensitiveWord.setType(type.isEmpty() ? "其他" : type);
        
        // 获取级别
        Object levelObj = wordMap.get("level");
        int level = 1;
        if (levelObj != null) {
            try {
                if (levelObj instanceof Number) {
                    level = ((Number) levelObj).intValue();
                } else {
                    String levelStr = levelObj.toString().trim();
                    if (!levelStr.isEmpty()) {
                        level = Integer.parseInt(levelStr);
                    }
                }
            } catch (NumberFormatException e) {
                // 使用默认级别
            }
        }
        sensitiveWord.setLevel(level);
        
        // 获取替换词
        Object replaceWordObj = wordMap.get("replaceWord");
        String replaceWord = replaceWordObj != null ? replaceWordObj.toString() : "";
        sensitiveWord.setReplaceWord(replaceWord);
        
        // 获取状态
        Object statusObj = wordMap.get("status");
        boolean status = true;
        if (statusObj != null) {
            if (statusObj instanceof Boolean) {
                status = (Boolean) statusObj;
            } else {
                String statusStr = statusObj.toString().trim().toLowerCase();
                status = !("false".equals(statusStr) || "0".equals(statusStr) || "no".equals(statusStr));
            }
        }
        sensitiveWord.setStatus(status);
        
        // 设置时间
        sensitiveWord.setCreatedAt(LocalDateTime.now());
        sensitiveWord.setUpdatedAt(LocalDateTime.now());
        
        return sensitiveWord;
    }
}
