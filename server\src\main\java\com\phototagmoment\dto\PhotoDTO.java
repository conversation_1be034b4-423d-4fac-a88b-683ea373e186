package com.phototagmoment.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 照片DTO
 */
@Data
public class PhotoDTO {

    /**
     * 照片ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 照片分组ID
     */
    private String groupId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 照片标题
     */
    private String title;

    /**
     * 照片描述
     */
    private String description;

    /**
     * 照片URL
     */
    private String url;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 照片宽度
     */
    private Integer width;

    /**
     * 照片高度
     */
    private Integer height;

    /**
     * 拍摄地点
     */
    private String location;

    /**
     * 拍摄时间
     */
    private LocalDateTime takenTime;

    /**
     * 可见性: 0-私密, 1-公开, 2-好友可见
     */
    private Integer visibility;

    /**
     * 是否允许评论: 0-不允许, 1-允许
     */
    private Integer allowComment;

    /**
     * 是否允许下载: 0-不允许, 1-允许
     */
    private Integer allowDownload;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 浏览数
     */
    private Integer viewCount;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 是否已点赞
     */
    private Boolean isLiked;

    /**
     * 是否已收藏
     */
    private Boolean isCollected;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 是否是分组照片
     */
    private Boolean isGrouped = false;

    /**
     * 同组照片列表
     * 使用 @JsonIgnore 防止循环引用导致的无限递归
     */
    @JsonIgnore
    private List<PhotoDTO> groupPhotos = new ArrayList<>();

    /**
     * 同组照片数量
     */
    private Integer groupPhotoCount = 0;

    /**
     * 同组照片ID列表
     */
    private List<Long> groupPhotoIds = new ArrayList<>();
}
