package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.config.QiniuConfig;
import com.phototagmoment.service.QiniuStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 七牛云存储控制器
 */
@Slf4j
@RestController
@RequestMapping("/qiniu")
@Tag(name = "七牛云存储接口", description = "七牛云存储相关接口")
public class QiniuStorageController {

    @Autowired
    private QiniuStorageService qiniuStorageService;

    @Autowired
    private QiniuConfig qiniuConfig;

    /**
     * 获取上传凭证
     */
    @GetMapping("/upload-token")
    @Operation(summary = "获取上传凭证", description = "获取七牛云上传凭证")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, String>> getUploadToken(
            @Parameter(description = "文件名") @RequestParam(required = false) String fileName) {
        // 刷新配置确保最新状态
        qiniuConfig.refreshConfig();

        // 检查配置完整性
        String validationError = qiniuConfig.getConfigValidationError();
        if (validationError != null) {
            return ApiResponse.failed(validationError);
        }

        String token = qiniuStorageService.getUploadToken(fileName);
        Map<String, String> result = new HashMap<>();
        result.put("uploadToken", token);
        result.put("domain", qiniuConfig.getDomain());
        result.put("region", qiniuConfig.getRegion());
        result.put("bucket", qiniuConfig.getBucket());
        result.put("uploadDir", qiniuConfig.getUploadDir());
        result.put("encryptEnabled", String.valueOf(qiniuConfig.isEncryptEnabled()));
        return ApiResponse.success(result);
    }

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "上传文件到七牛云")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Map<String, String>> uploadFile(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "文件名") @RequestParam(required = false) String fileName) {
        // 刷新配置确保最新状态
        qiniuConfig.refreshConfig();

        // 检查配置完整性
        String validationError = qiniuConfig.getConfigValidationError();
        if (validationError != null) {
            return ApiResponse.failed(validationError);
        }

        if (file.isEmpty()) {
            return ApiResponse.failed("文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (fileName == null || fileName.isEmpty()) {
            fileName = originalFilename;
        }

        String fileUrl = qiniuStorageService.uploadFile(file, fileName);
        if (fileUrl == null) {
            return ApiResponse.failed("上传文件失败");
        }

        Map<String, String> result = new HashMap<>();
        result.put("url", fileUrl);
        result.put("originalFilename", originalFilename);
        result.put("size", String.valueOf(file.getSize()));
        result.put("contentType", file.getContentType());
        return ApiResponse.success(result);
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除文件", description = "从七牛云删除文件")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Boolean> deleteFile(
            @Parameter(description = "文件URL") @RequestParam String fileUrl) {
        if (!qiniuConfig.isEnabled()) {
            return ApiResponse.failed("七牛云存储未启用");
        }

        boolean result = qiniuStorageService.deleteFile(fileUrl);
        return ApiResponse.success(result);
    }

    /**
     * 下载文件
     */
    @GetMapping("/download")
    @Operation(summary = "下载文件", description = "从七牛云下载文件")
    public ResponseEntity<InputStreamResource> downloadFile(
            @Parameter(description = "文件URL") @RequestParam String fileUrl) {
        if (!qiniuConfig.isEnabled()) {
            return ResponseEntity.badRequest().build();
        }

        try {
            InputStream inputStream = qiniuStorageService.getFile(fileUrl);
            if (inputStream == null) {
                return ResponseEntity.notFound().build();
            }

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" +
                    fileUrl.substring(fileUrl.lastIndexOf("/") + 1));
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.add(HttpHeaders.PRAGMA, "no-cache");
            headers.add(HttpHeaders.EXPIRES, "0");

            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(new InputStreamResource(inputStream));
        } catch (Exception e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }
}
