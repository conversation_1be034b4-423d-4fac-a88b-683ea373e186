package com.phototagmoment.service;

import com.phototagmoment.dto.MentionDTO;

import java.util.List;

/**
 * 提及用户服务接口
 */
public interface MentionService {

    /**
     * 处理照片中的@用户
     *
     * @param photoId 照片ID
     * @param mentions 提及用户列表
     * @param userId 发布用户ID
     * @return 是否成功
     */
    boolean processPhotoMentions(Long photoId, List<MentionDTO> mentions, Long userId);

    /**
     * 处理评论中的@用户
     *
     * @param commentId 评论ID
     * @param content 评论内容
     * @param userId 评论用户ID
     * @return 是否成功
     */
    boolean processCommentMentions(Long commentId, String content, Long userId);

    /**
     * 从文本中提取@用户
     *
     * @param content 文本内容
     * @return 提及用户列表
     */
    List<MentionDTO> extractMentionsFromText(String content);

    /**
     * 创建提及
     *
     * @param fromUserId 发起用户ID
     * @param toUserId 被提及用户ID
     * @param type 提及类型（photo, comment等）
     * @param targetId 目标ID（照片ID、评论ID等）
     * @return 是否成功
     */
    boolean createMention(Long fromUserId, Long toUserId, String type, Long targetId);
}
