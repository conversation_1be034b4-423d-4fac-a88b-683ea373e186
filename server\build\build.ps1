param(
    [Parameter(Position=0)]
    [ValidateSet("dev", "test", "prod")]
    [string]$Environment = "dev"
)

# PhotoTagMoment PowerShell 多环境打包脚本
Write-Host "==========================================" -ForegroundColor Green
Write-Host "PhotoTagMoment 多环境打包脚本 (PowerShell)" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "目标环境: $Environment" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Green

# 设置Java 17环境
$env:JAVA_HOME = "C:\Program Files\Java\jdk-17"
Write-Host "使用Java版本: $env:JAVA_HOME" -ForegroundColor Yellow

# 检查是否在项目根目录
if (-not (Test-Path "..\pom.xml")) {
    Write-Host "错误: 请在项目根目录运行此脚本" -ForegroundColor Red
    Write-Host "当前目录: $(Get-Location)" -ForegroundColor Red
    Write-Host "应该包含: ..\pom.xml" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查环境配置文件是否存在
$configFile = "..\src\main\resources\application-$Environment.yml"
if (-not (Test-Path $configFile)) {
    Write-Host "警告: 环境配置文件不存在: application-$Environment.yml" -ForegroundColor Yellow
    Write-Host "将使用默认配置文件" -ForegroundColor Yellow
}

Write-Host "[1/4] 进入后端目录..." -ForegroundColor Yellow
Set-Location ..\

Write-Host "[2/4] 清理之前的构建..." -ForegroundColor Yellow
try {
    & mvn clean -q
    if ($LASTEXITCODE -ne 0) {
        throw "Maven清理失败"
    }
    Write-Host "清理完成" -ForegroundColor Green
} catch {
    Write-Host "错误: Maven清理失败 - $_" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "[3/4] 编译项目..." -ForegroundColor Yellow
try {
    & mvn compile -q
    if ($LASTEXITCODE -ne 0) {
        throw "Maven编译失败"
    }
    Write-Host "编译完成" -ForegroundColor Green
} catch {
    Write-Host "错误: Maven编译失败 - $_" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "[4/4] 开始打包 (环境: $Environment)..." -ForegroundColor Yellow
try {
    & mvn package -DskipTests "-Dspring.profiles.active=$Environment" -q
    if ($LASTEXITCODE -ne 0) {
        throw "Maven打包失败"
    }
    Write-Host "打包完成" -ForegroundColor Green
} catch {
    Write-Host "错误: Maven打包失败 - $_" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 获取JAR文件信息
$jarFile = "target\phototagmoment-0.0.1-SNAPSHOT.jar"
if (-not (Test-Path $jarFile)) {
    Write-Host "错误: JAR文件未生成" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

$fileInfo = Get-Item $jarFile
$fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
$buildTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

Write-Host ""
Write-Host "==========================================" -ForegroundColor Green
Write-Host "打包成功！" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host "环境配置: $Environment" -ForegroundColor Cyan
Write-Host "JAR文件位置: $(Get-Location)\$jarFile" -ForegroundColor Cyan
Write-Host "文件大小: $fileSizeMB MB" -ForegroundColor Cyan
Write-Host "生成时间: $buildTime" -ForegroundColor Cyan
Write-Host ""
Write-Host "运行命令:" -ForegroundColor Yellow
Write-Host "  java -jar $jarFile" -ForegroundColor White
Write-Host "  或者指定环境:" -ForegroundColor Yellow
Write-Host "  java -jar -Dspring.profiles.active=$Environment $jarFile" -ForegroundColor White
Write-Host ""
Write-Host "配置文件: application-$Environment.yml" -ForegroundColor Cyan

# 根据环境显示不同的端口
$port = switch ($Environment) {
    "dev" { "8081" }
    "test" { "8082" }
    "prod" { "8081" }
    default { "8081" }
}

Write-Host "API文档: http://localhost:$port/api/doc.html" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Green

Read-Host "按任意键退出"
