<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <h1 class="error-code">404</h1>
      <h2 class="error-title">页面不存在</h2>
      <p class="error-message">抱歉，您访问的页面不存在或已被删除。</p>
      <div class="action-buttons">
        <router-link to="/" class="btn-home">返回首页</router-link>
        <button class="btn-back" @click="goBack">返回上一页</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 20px;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
}

.error-code {
  font-size: 120px;
  font-weight: 700;
  color: #3498db;
  margin: 0;
  line-height: 1;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  margin: 10px 0;
  color: #333;
}

.error-message {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.btn-home,
.btn-back {
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-home {
  background-color: #3498db;
  color: white;
  text-decoration: none;
  border: none;

  &:hover {
    background-color: #2980b9;
  }
}

.btn-back {
  background-color: white;
  color: #3498db;
  border: 1px solid #3498db;

  &:hover {
    background-color: #f8f9fa;
  }
}

@media (max-width: 768px) {
  .error-code {
    font-size: 80px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-message {
    font-size: 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .btn-home,
  .btn-back {
    width: 100%;
  }
}
</style>
