/**
 * 图片处理工具函数
 * PhotoTagMoment项目图片相关的通用工具函数
 */

/**
 * 移除七牛云缩略图参数的工具函数
 * 用于在图片预览时显示原图而不是缩略图
 * 
 * @param url 原始图片URL
 * @returns 移除缩略图参数后的URL
 */
export const removeQiniuThumbnailParams = (url: string): string => {
  if (!url) return url
  
  // 移除七牛云图片处理参数（如 ?imageView2/1/w/300/h/300 等）
  const paramIndex = url.indexOf('?imageView2')
  if (paramIndex !== -1) {
    return url.substring(0, paramIndex)
  }
  
  // 移除其他可能的图片处理参数
  const otherParamIndex = url.indexOf('?imageMogr2')
  if (otherParamIndex !== -1) {
    return url.substring(0, otherParamIndex)
  }
  
  return url
}

/**
 * 为图片预览准备URL数组
 * 移除所有缩略图参数，确保预览时显示原图
 * 
 * @param images 图片对象数组
 * @param urlField URL字段名，默认为'url'
 * @returns 处理后的URL数组
 */
export const preparePreviewImages = (images: any[], urlField: string = 'url'): string[] => {
  if (!images || !Array.isArray(images)) return []
  
  return images.map(image => {
    const url = image[urlField] || image.url || image.thumbnailUrl || ''
    return removeQiniuThumbnailParams(url)
  })
}

/**
 * 获取图片预览配置
 * 返回标准的图片预览配置对象
 * 
 * @param options 自定义配置选项
 * @returns 图片预览配置对象
 */
export const getImagePreviewConfig = (options: {
  startPosition?: number
  onClose?: () => void
  onChange?: (index: number) => void
} = {}) => {
  return {
    startPosition: options.startPosition || 0,
    closeable: true,
    closeIconPosition: 'top-right' as const,
    swipeDuration: 300,
    loop: true,
    maxZoom: 3,
    minZoom: 1/3,
    onClose: options.onClose || (() => {
      console.log('图片预览已关闭')
    }),
    onChange: options.onChange || ((index: number) => {
      console.log('预览图片切换到索引:', index)
    })
  }
}

/**
 * 检查URL是否包含七牛云缩略图参数
 * 
 * @param url 图片URL
 * @returns 是否包含缩略图参数
 */
export const hasQiniuThumbnailParams = (url: string): boolean => {
  if (!url) return false
  return url.includes('?imageView2') || url.includes('?imageMogr2')
}

/**
 * 生成七牛云缩略图URL
 * 为原图URL添加缩略图参数
 * 
 * @param url 原图URL
 * @param width 缩略图宽度，默认300
 * @param height 缩略图高度，默认300
 * @param quality 图片质量，默认80
 * @returns 缩略图URL
 */
export const generateQiniuThumbnailUrl = (
  url: string, 
  width: number = 300, 
  height: number = 300, 
  quality: number = 80
): string => {
  if (!url) return url
  
  // 如果已经包含参数，先移除
  const cleanUrl = removeQiniuThumbnailParams(url)
  
  // 添加缩略图参数
  return `${cleanUrl}?imageView2/2/w/${width}/h/${height}/q/${quality}`
}

/**
 * 图片加载错误处理
 * 返回默认图片或错误占位符
 * 
 * @param error 错误对象
 * @param defaultImage 默认图片URL
 * @returns 默认图片URL
 */
export const handleImageError = (error: any, defaultImage?: string): string => {
  console.warn('图片加载失败:', error)
  return defaultImage || '/images/default-image.png'
}

/**
 * 图片尺寸验证
 * 检查图片是否符合尺寸要求
 * 
 * @param file 图片文件
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @returns Promise<boolean>
 */
export const validateImageSize = (
  file: File, 
  maxWidth: number = 2048, 
  maxHeight: number = 2048
): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      const isValid = img.width <= maxWidth && img.height <= maxHeight
      resolve(isValid)
    }
    img.onerror = () => {
      resolve(false)
    }
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 获取图片尺寸信息
 * 
 * @param url 图片URL
 * @returns Promise<{width: number, height: number}>
 */
export const getImageDimensions = (url: string): Promise<{width: number, height: number}> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      })
    }
    img.onerror = () => {
      reject(new Error('无法获取图片尺寸'))
    }
    img.src = url
  })
}

export default {
  removeQiniuThumbnailParams,
  preparePreviewImages,
  getImagePreviewConfig,
  hasQiniuThumbnailParams,
  generateQiniuThumbnailUrl,
  handleImageError,
  validateImageSize,
  getImageDimensions
}
