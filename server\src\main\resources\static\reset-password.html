<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置管理员密码</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
        }
    </style>
</head>
<body>
    <h1>重置管理员密码</h1>
    <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" value="admin" readonly>
    </div>
    <div class="form-group">
        <label for="password">新密码</label>
        <input type="password" id="password" value="123456">
    </div>
    <button onclick="resetPassword()">重置密码</button>
    <div id="result" class="result" style="display: none;"></div>

    <script>
        function resetPassword() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            if (!username || !password) {
                showResult('请填写用户名和密码', false);
                return;
            }
            
            // 构建请求URL
            const url = `/api/admin/password/reset?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;
            
            // 发送请求
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    showResult('密码重置成功！现在可以使用新密码登录了。', true);
                } else {
                    showResult(`密码重置失败: ${data.message}`, false);
                }
            })
            .catch(error => {
                showResult(`发生错误: ${error.message}`, false);
            });
        }
        
        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
