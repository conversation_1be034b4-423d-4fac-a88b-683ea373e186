<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.PhotoTagMapper">

    <!-- 批量插入照片标签 -->
    <insert id="batchInsert">
        INSERT INTO ptm_photo_tag (photo_id, tag_name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.photoId}, #{item.name})
        </foreach>
    </insert>

    <!-- 根据照片ID查询标签列表 -->
    <select id="selectTagsByPhotoId" resultType="java.lang.String">
        SELECT tag_name
        FROM ptm_photo_tag
        WHERE photo_id = #{photoId}
    </select>

    <!-- 根据照片ID删除标签 -->
    <delete id="deleteByPhotoId">
        DELETE FROM ptm_photo_tag
        WHERE photo_id = #{photoId}
    </delete>

    <!-- 搜索标签 -->
    <select id="searchTags" resultType="com.phototagmoment.entity.PhotoTag">
        SELECT id, photo_id AS photoId, tag_name AS name, created_at AS createdAt
        FROM ptm_photo_tag
        WHERE tag_name LIKE CONCAT('%', #{keyword}, '%')
        GROUP BY tag_name
        ORDER BY COUNT(photo_id) DESC
        LIMIT #{limit}
    </select>

    <!-- 获取热门标签 -->
    <select id="getPopularTags" resultType="com.phototagmoment.dto.TagDTO">
        SELECT tag_name as name, COUNT(photo_id) AS count
        FROM ptm_photo_tag
        GROUP BY tag_name
        ORDER BY count DESC
        LIMIT #{limit}
    </select>

</mapper>
