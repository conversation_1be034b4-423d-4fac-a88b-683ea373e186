package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 控制台统计数据DTO
 */
@Data
@Schema(description = "控制台统计数据")
public class DashboardStatsDTO {

    @Schema(description = "用户总数")
    private Long userCount;

    @Schema(description = "用户增长趋势百分比")
    private BigDecimal userTrend;

    @Schema(description = "照片总数")
    private Long photoCount;

    @Schema(description = "照片增长趋势百分比")
    private BigDecimal photoTrend;

    @Schema(description = "评论总数")
    private Long commentCount;

    @Schema(description = "评论增长趋势百分比")
    private BigDecimal commentTrend;

    @Schema(description = "存储使用量（字节）")
    private Long storageUsed;

    @Schema(description = "存储使用量（格式化字符串）")
    private String storageUsedFormatted;

    @Schema(description = "存储增长趋势百分比")
    private BigDecimal storageTrend;

    @Schema(description = "今日新增用户")
    private Long todayUsers;

    @Schema(description = "今日新增照片")
    private Long todayPhotos;

    @Schema(description = "今日新增评论")
    private Long todayComments;

    @Schema(description = "今日存储增长（字节）")
    private Long todayStorage;

    @Schema(description = "活跃用户数（本周）")
    private Long activeUsers;

    @Schema(description = "待审核照片数")
    private Long pendingPhotos;

    @Schema(description = "待审核评论数")
    private Long pendingComments;

    @Schema(description = "系统运行天数")
    private Long systemRunDays;
}
