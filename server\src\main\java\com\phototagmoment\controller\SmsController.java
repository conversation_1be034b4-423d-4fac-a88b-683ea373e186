package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.config.SmsConfig;
import com.phototagmoment.service.SmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 短信控制器
 */
@Slf4j
@RestController
@RequestMapping("/sms")
@Tag(name = "短信接口", description = "短信相关接口")
public class SmsController {

    @Autowired
    private SmsService smsService;

    @Autowired
    private SmsConfig smsConfig;

    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 发送验证码
     */
    @PostMapping("/send-code")
    @Operation(summary = "发送验证码", description = "发送短信验证码")
    public ApiResponse<Map<String, Object>> sendVerificationCode(
            @Parameter(description = "手机号") @RequestParam String phone) {
        if (!smsConfig.isEnabled()) {
            return ApiResponse.failed("短信服务未启用");
        }

        // 验证手机号格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return ApiResponse.failed("手机号格式不正确");
        }

        // 检查发送频率
        long cooldown = smsService.getCooldownTime(phone);
        if (cooldown > 0) {
            return ApiResponse.failed("发送过于频繁，请" + cooldown + "秒后再试");
        }

        // 检查每日发送次数
        int remaining = smsService.getRemainingAttempts(phone);
        if (remaining <= 0) {
            return ApiResponse.failed("今日发送次数已达上限，请明天再试");
        }

        // 发送验证码
        boolean success = smsService.sendVerificationCode(phone);
        if (success) {
            Map<String, Object> result = new HashMap<>();
            result.put("remaining", remaining - 1);
            result.put("expiration", smsConfig.getVerificationCode().getExpiration());
            return ApiResponse.success(result, "验证码发送成功");
        } else {
            return ApiResponse.failed("验证码发送失败，请稍后再试");
        }
    }

    /**
     * 验证验证码
     */
    @PostMapping("/verify-code")
    @Operation(summary = "验证验证码", description = "验证短信验证码")
    public ApiResponse<Boolean> verifyCode(
            @Parameter(description = "手机号") @RequestParam String phone,
            @Parameter(description = "验证码") @RequestParam String code) {
        if (!smsConfig.isEnabled()) {
            return ApiResponse.failed("短信服务未启用");
        }

        // 验证手机号格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return ApiResponse.failed("手机号格式不正确");
        }

        // 验证验证码
        boolean success = smsService.verifyCode(phone, code);
        if (success) {
            return ApiResponse.success(true, "验证码验证成功");
        } else {
            return ApiResponse.failed("验证码错误或已过期");
        }
    }

    /**
     * 获取发送状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取发送状态", description = "获取短信发送状态")
    public ApiResponse<Map<String, Object>> getSendStatus(
            @Parameter(description = "手机号") @RequestParam String phone) {
        if (!smsConfig.isEnabled()) {
            return ApiResponse.failed("短信服务未启用");
        }

        // 验证手机号格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return ApiResponse.failed("手机号格式不正确");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("remaining", smsService.getRemainingAttempts(phone));
        result.put("cooldown", smsService.getCooldownTime(phone));
        result.put("dailyLimit", smsConfig.getVerificationCode().getDailyLimit());
        result.put("interval", smsConfig.getVerificationCode().getInterval());
        result.put("expiration", smsConfig.getVerificationCode().getExpiration());
        return ApiResponse.success(result);
    }
}
