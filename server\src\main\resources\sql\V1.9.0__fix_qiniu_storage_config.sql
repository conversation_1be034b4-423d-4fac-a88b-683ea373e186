-- 修复七牛云存储配置
-- 确保数据库中有正确的七牛云配置项
-- 注意：现在七牛云配置主要从文件上传配置表（ptm_file_upload_config）读取

-- 插入或更新存储类型配置（兜底配置）
INSERT INTO `ptm_system_config` (`config_key`, `config_value`, `config_name`, `config_type`, `remark`, `is_system`, `description`)
VALUES ('storage.type', 'local', '存储类型', 'string', '存储类型（local/qiniu）', 1, '系统存储类型配置')
ON DUPLICATE KEY UPDATE
    `config_name` = VALUES(`config_name`),
    `config_type` = VALUES(`config_type`),
    `remark` = VALUES(`remark`),
    `description` = VALUES(`description`);

-- 检查并创建七牛云文件上传配置（如果不存在）
INSERT INTO `ptm_file_upload_config` (
    `config_name`,
    `storage_type`,
    `enabled`,
    `is_default`,
    `config_params`,
    `upload_limits`,
    `path_config`,
    `description`,
    `sort_order`,
    `status`,
    `created_by`,
    `updated_by`,
    `created_at`,
    `updated_at`
)
SELECT
    '七牛云存储配置',
    'QINIU',
    false,
    false,
    JSON_OBJECT(
        'qiniuAccessKey', '',
        'qiniuSecretKey', '',
        'qiniuBucket', '',
        'qiniuRegion', 'huanan',
        'domain', ''
    ),
    JSON_OBJECT(
        'maxFileSize', 10485760,
        'allowedTypes', JSON_ARRAY('jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'),
        'maxFilesPerUpload', 9
    ),
    JSON_OBJECT(
        'uploadDir', 'phototagmoment',
        'pathPattern', '{uploadDir}/{year}/{month}/{day}/{uuid}.{ext}'
    ),
    '七牛云对象存储配置，用于照片笔记上传',
    1,
    1,
    1,
    1,
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM `ptm_file_upload_config` WHERE `storage_type` = 'QINIU'
);

-- 插入或更新七牛云配置项
INSERT INTO `ptm_system_config` (`config_key`, `config_value`, `config_name`, `config_type`, `remark`, `is_system`, `description`)
VALUES
    ('storage.qiniu.enabled', 'false', '七牛云存储启用状态', 'boolean', '是否启用七牛云存储', 1, '七牛云存储启用开关'),
    ('storage.qiniu.access-key', '', '七牛云AccessKey', 'string', '七牛云AccessKey', 1, '七牛云访问密钥'),
    ('storage.qiniu.secret-key', '', '七牛云SecretKey', 'string', '七牛云SecretKey', 1, '七牛云密钥'),
    ('storage.qiniu.bucket', '', '七牛云存储空间', 'string', '七牛云存储空间名称', 1, '七牛云存储桶名称'),
    ('storage.qiniu.region', 'huanan', '七牛云存储区域', 'string', '七牛云存储区域', 1, '七牛云存储区域配置'),
    ('storage.qiniu.domain', '', '七牛云访问域名', 'string', '七牛云访问域名', 1, '七牛云CDN域名'),
    ('storage.qiniu.upload-dir', 'phototagmoment', '七牛云上传目录', 'string', '七牛云上传目录前缀', 1, '七牛云文件上传目录'),
    ('storage.qiniu.is-private', 'true', '七牛云私有空间', 'boolean', '是否为私有空间', 1, '七牛云空间访问权限'),
    ('storage.qiniu.download-expires', '3600', '七牛云下载凭证有效期', 'number', '下载凭证有效期（秒）', 1, '七牛云私有空间下载凭证有效期')
ON DUPLICATE KEY UPDATE
    `config_name` = VALUES(`config_name`),
    `config_type` = VALUES(`config_type`),
    `remark` = VALUES(`remark`),
    `description` = VALUES(`description`);

-- 更新现有配置的默认值（如果配置值为空）
UPDATE `ptm_system_config`
SET `config_value` = 'huanan'
WHERE `config_key` = 'storage.qiniu.region' AND (`config_value` = '' OR `config_value` IS NULL);

UPDATE `ptm_system_config`
SET `config_value` = 'phototagmoment'
WHERE `config_key` = 'storage.qiniu.upload-dir' AND (`config_value` = '' OR `config_value` IS NULL);

UPDATE `ptm_system_config`
SET `config_value` = 'true'
WHERE `config_key` = 'storage.qiniu.is-private' AND (`config_value` = '' OR `config_value` IS NULL);

UPDATE `ptm_system_config`
SET `config_value` = '3600'
WHERE `config_key` = 'storage.qiniu.download-expires' AND (`config_value` = '' OR `config_value` IS NULL);

-- 添加配置说明注释
UPDATE `ptm_system_config`
SET `description` = '存储类型配置，可选值：local（本地存储）、qiniu（七牛云存储）'
WHERE `config_key` = 'storage.type';

-- 确保配置状态正确
UPDATE `ptm_system_config`
SET `status` = 1, `is_system` = 1
WHERE `config_key` LIKE 'storage.%';
