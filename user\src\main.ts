import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// 引入 Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 引入样式
import './assets/styles/index.scss'
import 'vant/lib/index.css'
import './assets/styles/tailwind.css'

// 引入Vant UI
import {
  Button,
  NavBar,
  Tabbar,
  TabbarItem,
  Icon,
  Image as VanImage,
  ImagePreview,
  Cell,
  CellGroup,
  Form,
  Field,
  Toast,
  Dialog,
  Uploader,
  Swipe,
  SwipeItem,
  Lazyload,
  PullRefresh,
  List,
  Tab,
  Tabs,
  Sticky,
  Loading,
  Empty,
  Skeleton,
  Tag,
  Divider,
  Grid,
  GridItem,
  Search,
  ActionSheet,
  Popup,
  DropdownMenu,
  DropdownItem,
  Badge,
  Picker,
  DatePicker,
  Radio,
  RadioGroup,
  Checkbox,
  CheckboxGroup,
  ActionBar,
  ActionBarButton,
  ActionBarIcon
} from 'vant'

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 注册Vant组件
app.use(Button)
app.use(NavBar)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Icon)
app.use(VanImage)
app.use(ImagePreview)
app.use(Cell)
app.use(CellGroup)
app.use(Form)
app.use(Field)
app.use(Toast)
app.use(Dialog)
app.use(Uploader)
app.use(Swipe)
app.use(SwipeItem)
app.use(Lazyload)
app.use(PullRefresh)
app.use(List)
app.use(Tab)
app.use(Tabs)
app.use(Sticky)
app.use(Loading)
app.use(Empty)
app.use(Skeleton)
app.use(Tag)
app.use(Divider)
app.use(Grid)
app.use(GridItem)
app.use(Search)
app.use(ActionSheet)
app.use(Popup)
app.use(DropdownMenu)
app.use(DropdownItem)
app.use(Badge)
app.use(Picker)
app.use(DatePicker)
app.use(Radio)
app.use(RadioGroup)
app.use(Checkbox)
app.use(CheckboxGroup)
app.use(ActionBar)
app.use(ActionBarButton)
app.use(ActionBarIcon)

// 挂载应用
app.mount('#app')
