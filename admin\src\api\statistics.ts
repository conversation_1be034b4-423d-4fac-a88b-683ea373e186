import request from '@/utils/request'

// 统计查询参数接口
interface StatsParams {
  startDate?: string
  endDate?: string
  period?: string // 'day' | 'week' | 'month' | 'year'
  [key: string]: any
}

// 系统统计数据接口
interface SystemStatsData {
  totalUsers: number
  totalPhotos: number
  totalPhotoNotes: number
  totalTags: number
  todayUsers: number
  todayPhotos: number
  todayPhotoNotes: number
  activeUsers: number
  storageUsed: number
  [key: string]: any
}

// 内容审核统计数据接口
interface ContentAuditStatsData {
  pendingCount: number
  approvedCount: number
  rejectedCount: number
  todayAuditCount: number
  auditRate: number
  avgAuditTime: number
  [key: string]: any
}

// 用户活跃度统计数据接口
interface UserActivityStatsData {
  dailyActiveUsers: number
  weeklyActiveUsers: number
  monthlyActiveUsers: number
  newUsersToday: number
  newUsersThisWeek: number
  newUsersThisMonth: number
  retentionRate: number
  [key: string]: any
}

// 趋势数据接口
interface TrendData {
  date: string
  value: number
  [key: string]: any
}

/**
 * 获取系统统计信息
 * @returns Promise
 */
export function getSystemStats() {
  return request({
    url: '/admin/stats/overview',
    method: 'get'
  })
}

/**
 * 获取内容审核统计
 * @param params 查询参数
 * @returns Promise
 */
export function getContentAuditStats(params: StatsParams) {
  return request({
    url: '/admin/stats/content-audit',
    method: 'get',
    params
  })
}

/**
 * 获取用户活跃度统计
 * @param params 查询参数
 * @returns Promise
 */
export function getUserActivityStats(params: StatsParams) {
  return request({
    url: '/admin/stats/user-activity',
    method: 'get',
    params
  })
}

/**
 * 获取照片上传趋势
 * @param params 查询参数
 * @returns Promise
 */
export function getPhotoUploadTrend(params: StatsParams) {
  return request({
    url: '/admin/stats/photo-upload-trend',
    method: 'get',
    params
  })
}

/**
 * 获取用户注册趋势
 * @param params 查询参数
 * @returns Promise
 */
export function getUserRegistrationTrend(params: StatsParams) {
  return request({
    url: '/admin/stats/user-registration-trend',
    method: 'get',
    params
  })
}

/**
 * 获取标签使用统计
 * @param params 查询参数
 * @returns Promise
 */
export function getTagUsageStats(params: StatsParams) {
  return request({
    url: '/admin/stats/tag-usage',
    method: 'get',
    params
  })
}

/**
 * 获取存储使用统计
 * @returns Promise
 */
export function getStorageStats() {
  return request({
    url: '/admin/stats/storage',
    method: 'get'
  })
}

/**
 * 获取实时统计数据
 * @returns Promise
 */
export function getRealTimeStats() {
  return request({
    url: '/admin/stats/realtime',
    method: 'get'
  })
}

/**
 * 导出统计报告
 * @param params 查询参数
 * @returns Promise
 */
export function exportStatsReport(params: StatsParams) {
  return request({
    url: '/admin/stats/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出类型定义
export type {
  StatsParams,
  SystemStatsData,
  ContentAuditStatsData,
  UserActivityStatsData,
  TrendData
}
