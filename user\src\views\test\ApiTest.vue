<template>
  <div class="api-test">
    <van-nav-bar title="API测试" left-arrow @click-left="$router.back()" />

    <div class="test-section">
      <h3>照片笔记详情API测试</h3>
      <van-button type="primary" @click="testPhotoNoteDetail">测试照片笔记详情</van-button>

      <div v-if="apiResult" class="result-section">
        <h4>API响应结果：</h4>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>

      <div v-if="imageUrls.length > 0" class="image-section">
        <h4>图片URL测试：</h4>
        <div v-for="(url, index) in imageUrls" :key="index" class="image-item">
          <p>图片 {{ index + 1 }}:</p>
          <p>原始URL: {{ url.original }}</p>
          <p>处理后URL: {{ url.processed }}</p>
          <van-image
            :src="url.processed"
            width="200"
            height="200"
            fit="cover"
            :alt="`测试图片${index + 1}`"
          >
            <template #loading>
              <van-loading type="spinner" size="20" />
            </template>
            <template #error>
              <div class="error-placeholder">
                <van-icon name="photo-fail" size="24" />
                <p>加载失败</p>
              </div>
            </template>
          </van-image>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getPhotoNoteDetail } from '@/api/photo'
import { getPrivateImageUrl } from '@/api/file'

const apiResult = ref(null)
const imageUrls = ref([])

const testPhotoNoteDetail = async () => {
  try {
    console.log('开始测试照片笔记详情API')

    // 测试API调用
    const response = await getPhotoNoteDetail(31)
    console.log('API响应:', response)
    console.log('响应数据结构:', JSON.stringify(response, null, 2))
    apiResult.value = response

    // 检查响应数据结构
    if (response && response.data) {
      console.log('照片笔记数据:', response.data)
      console.log('图片数组:', response.data.images)
      console.log('图片数量:', response.data.images?.length || 0)

      // 测试图片URL处理
      if (response.data?.images?.length > 0) {
        console.log('开始测试图片URL处理')
        const urls = []

        for (let i = 0; i < response.data.images.length; i++) {
          const image = response.data.images[i]
          console.log(`第${i + 1}张图片完整信息:`, image)

          const originalUrl = image.thumbnailUrl || image.url
          console.log(`处理第${i + 1}张图片，原始URL:`, originalUrl)

          // 测试图片URL是否可以直接访问
          console.log(`测试图片URL直接访问: ${originalUrl}`)

          try {
            const processedUrl = await getPrivateImageUrl(originalUrl)
            console.log(`处理后URL:`, processedUrl)

            urls.push({
              original: originalUrl,
              processed: processedUrl,
              imageInfo: image
            })
          } catch (error) {
            console.error('处理图片URL失败:', error)
            urls.push({
              original: originalUrl,
              processed: originalUrl,
              imageInfo: image,
              error: error.message
            })
          }
        }

        imageUrls.value = urls
        console.log('所有图片URL处理完成:', urls)
      } else {
        console.warn('没有找到图片数据或图片数组为空')
        console.log('完整响应数据:', response.data)
      }
    } else {
      console.error('API响应格式异常:', response)
    }
  } catch (error) {
    console.error('测试失败:', error)
    console.error('错误详情:', error.stack)
    apiResult.value = { error: error.message, stack: error.stack }
  }
}
</script>

<style scoped>
.api-test {
  padding: 16px;
}

.test-section {
  margin-bottom: 24px;
}

.result-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.result-section pre {
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 12px;
}

.image-section {
  margin-top: 16px;
}

.image-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.image-item p {
  margin: 4px 0;
  font-size: 12px;
  word-break: break-all;
}

.error-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}
</style>
