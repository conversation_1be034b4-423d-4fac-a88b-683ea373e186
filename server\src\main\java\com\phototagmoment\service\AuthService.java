package com.phototagmoment.service;

import com.phototagmoment.dto.AuthLoginDTO;
import com.phototagmoment.dto.AuthUserInfoDTO;
import me.zhyd.oauth.model.AuthCallback;

/**
 * 第三方登录服务接口
 */
public interface AuthService {

    /**
     * 获取授权URL
     *
     * @param source 第三方平台来源(qq, wechat等)
     * @param state  状态参数，防止CSRF攻击
     * @return 授权URL和状态参数
     */
    AuthLoginDTO getAuthUrl(String source, String state);

    /**
     * 处理授权回调
     *
     * @param source   第三方平台来源(qq, wechat等)
     * @param callback 授权回调参数
     * @return 登录结果
     */
    AuthLoginDTO login(String source, AuthCallback callback);

    /**
     * 获取用户信息
     *
     * @param source      第三方平台来源(qq, wechat等)
     * @param accessToken 访问令牌
     * @param openId      用户OpenID
     * @return 用户信息
     */
    AuthUserInfoDTO getUserInfo(String source, String accessToken, String openId);
}
