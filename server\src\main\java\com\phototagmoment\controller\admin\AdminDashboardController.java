package com.phototagmoment.controller.admin;

import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.DashboardStatsDTO;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 管理员控制台控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/dashboard")
@Tag(name = "管理员控制台", description = "管理员控制台统计数据接口")
@Validated
@PreAuthorize("hasRole('ADMIN')")
public class AdminDashboardController {

    @Autowired
    private DashboardService dashboardService;

    @GetMapping("/stats")
    @Operation(summary = "获取控制台统计数据", description = "获取用户、照片、评论、存储等统计信息")
    public ApiResponse<DashboardStatsDTO> getDashboardStats() {
        DashboardStatsDTO stats = dashboardService.getDashboardStats();
        return ApiResponse.success(stats);
    }

    @GetMapping("/user-growth")
    @Operation(summary = "获取用户增长趋势", description = "获取指定周期的用户增长趋势数据")
    public ApiResponse<Map<String, Object>> getUserGrowthTrend(
            @Parameter(description = "周期类型：week/month/year") @RequestParam(defaultValue = "week") String period) {
        Map<String, Object> trendData = dashboardService.getUserGrowthTrend(period);
        return ApiResponse.success(trendData);
    }

    @GetMapping("/content-distribution")
    @Operation(summary = "获取内容分布", description = "获取照片分类分布统计")
    public ApiResponse<List<Map<String, Object>>> getContentDistribution() {
        List<Map<String, Object>> distribution = dashboardService.getContentDistribution();
        return ApiResponse.success(distribution);
    }

    @GetMapping("/latest-users")
    @Operation(summary = "获取最新用户", description = "获取最新注册的用户列表")
    public ApiResponse<List<UserDTO>> getLatestUsers(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "5") Integer limit) {
        List<UserDTO> latestUsers = dashboardService.getLatestUsers(limit);
        return ApiResponse.success(latestUsers);
    }

    @GetMapping("/latest-photos")
    @Operation(summary = "获取最新照片", description = "获取最新上传的照片列表")
    public ApiResponse<List<PhotoNoteDTO>> getLatestPhotos(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "5") Integer limit) {
        List<PhotoNoteDTO> latestPhotos = dashboardService.getLatestPhotos(limit);
        return ApiResponse.success(latestPhotos);
    }

    @GetMapping("/system-info")
    @Operation(summary = "获取系统信息", description = "获取系统运行状态信息")
    public ApiResponse<Map<String, Object>> getSystemInfo() {
        Map<String, Object> systemInfo = dashboardService.getSystemInfo();
        return ApiResponse.success(systemInfo);
    }
}
