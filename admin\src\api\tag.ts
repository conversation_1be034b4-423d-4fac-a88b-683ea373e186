import request from '@/utils/request'

// 标签查询参数接口
interface TagListParams {
  page?: number
  pageSize?: number
  keyword?: string
  sortBy?: string
  sortOrder?: string
  [key: string]: any
}

// 标签数据接口
interface TagData {
  id: number
  tagName: string
  useCount: number
  hotScore: number
  createTime: string
  updateTime: string
  [key: string]: any
}

// 标签统计数据接口
interface TagStatsData {
  tagName: string
  useCount: number
  hotScore: number
  recentUseCount: number
  photoNoteCount: number
  userCount: number
  [key: string]: any
}

// 标签合并数据接口
interface TagMergeData {
  sourceTag: string
  targetTag: string
}

/**
 * 获取标签管理列表
 * @param params 查询参数
 * @returns Promise
 */
export function getTagList(params: TagListParams) {
  return request({
    url: '/admin/tags/list',
    method: 'get',
    params
  })
}

/**
 * 获取标签统计信息
 * @param tagName 标签名称
 * @returns Promise
 */
export function getTagStats(tagName: string) {
  return request({
    url: `/admin/tags/${encodeURIComponent(tagName)}/stats`,
    method: 'get'
  })
}

/**
 * 删除标签
 * @param tagName 标签名称
 * @returns Promise
 */
export function deleteTag(tagName: string) {
  return request({
    url: `/admin/tags/${encodeURIComponent(tagName)}`,
    method: 'delete'
  })
}

/**
 * 合并标签
 * @param sourceTag 源标签
 * @param targetTag 目标标签
 * @returns Promise
 */
export function mergeTags(sourceTag: string, targetTag: string) {
  return request({
    url: '/admin/tags/merge',
    method: 'post',
    data: {
      sourceTag,
      targetTag
    }
  })
}

/**
 * 获取热门标签列表
 * @param params 查询参数
 * @returns Promise
 */
export function getHotTags(params: TagListParams) {
  return request({
    url: '/admin/tags/hot',
    method: 'get',
    params
  })
}

/**
 * 批量删除标签
 * @param tagNames 标签名称列表
 * @returns Promise
 */
export function batchDeleteTags(tagNames: string[]) {
  return request({
    url: '/admin/tags/batch-delete',
    method: 'post',
    data: {
      tagNames
    }
  })
}

/**
 * 更新标签信息
 * @param tagName 原标签名称
 * @param newTagName 新标签名称
 * @returns Promise
 */
export function updateTag(tagName: string, newTagName: string) {
  return request({
    url: `/admin/tags/${encodeURIComponent(tagName)}`,
    method: 'put',
    data: {
      newTagName
    }
  })
}

// 导出类型定义
export type {
  TagListParams,
  TagData,
  TagStatsData,
  TagMergeData
}
