package com.phototagmoment.controller;

import com.phototagmoment.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询数据库表结构
     */
    @GetMapping("/table-structure")
    public ApiResponse<List<Map<String, Object>>> getTableStructure() {
        String sql = "DESCRIBE ptm_photo";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        return ApiResponse.success(result);
    }
}
