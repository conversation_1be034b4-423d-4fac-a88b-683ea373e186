package com.phototagmoment.service.impl;

import com.phototagmoment.config.QiniuConfig;
import com.phototagmoment.service.EncryptionService;
import com.phototagmoment.service.QiniuStorageService;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 七牛云存储服务实现类
 */
@Slf4j
@Service
public class QiniuStorageServiceImpl implements QiniuStorageService {

    @Autowired
    private QiniuConfig qiniuConfig;

    @Autowired
    private EncryptionService encryptionService;

    private Auth auth;
    private UploadManager uploadManager;
    private BucketManager bucketManager;
    private Configuration configuration;

    /**
     * 初始化七牛云配置
     */
    @PostConstruct
    public void init() {
        // 延迟初始化，等待配置加载完成
        initializeQiniuService();
    }

    /**
     * 初始化七牛云服务
     */
    private void initializeQiniuService() {
        try {
            // 刷新配置
            qiniuConfig.refreshConfig();

            // 检查配置完整性
            String validationError = qiniuConfig.getConfigValidationError();
            if (validationError != null) {
                log.warn("七牛云配置验证失败: {}", validationError);
                return;
            }

            // 初始化区域
            com.qiniu.storage.Region region;
            switch (qiniuConfig.getRegion().toLowerCase()) {
                case "huadong":
                case "z0":
                    region = com.qiniu.storage.Region.region0();
                    break;
                case "huabei":
                case "z1":
                    region = com.qiniu.storage.Region.region1();
                    break;
                case "huanan":
                case "z2":
                    region = com.qiniu.storage.Region.region2();
                    break;
                case "beimei":
                case "na0":
                    region = com.qiniu.storage.Region.regionNa0();
                    break;
                case "xinjiapo":
                case "as0":
                    region = com.qiniu.storage.Region.regionAs0();
                    break;
                default:
                    region = com.qiniu.storage.Region.autoRegion();
                    break;
            }

            // 初始化配置
            configuration = new Configuration(region);
            auth = Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey());
            uploadManager = new UploadManager(configuration);
            bucketManager = new BucketManager(auth, configuration);

            log.info("七牛云存储初始化成功");
        } catch (Exception e) {
            log.error("七牛云存储初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 重新初始化七牛云服务（用于配置更新后）
     */
    public void reinitialize() {
        log.info("重新初始化七牛云存储服务");
        initializeQiniuService();
    }

    @Override
    public String uploadFile(MultipartFile file, String fileName) {
        if (!qiniuConfig.isEnabled()) {
            log.warn("七牛云存储未启用");
            return null;
        }

        try {
            return uploadFile(file.getInputStream(), fileName, file.getSize());
        } catch (IOException e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String uploadFile(InputStream inputStream, String fileName, long size) {
        if (!qiniuConfig.isEnabled()) {
            log.warn("七牛云存储未启用");
            return null;
        }

        try {
            // 生成文件名
            String key = generateFileName(fileName);

            // 是否需要加密
            if (qiniuConfig.isEncryptEnabled()) {
                // 创建临时文件
                File tempFile = File.createTempFile("qiniu_", ".tmp");
                try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                    // 加密文件内容
                    encryptionService.encryptPhoto(inputStream, outputStream);
                }

                // 上传加密后的文件
                Response response = uploadManager.put(tempFile, key, getUploadToken(key));

                // 删除临时文件
                tempFile.delete();

                // 处理上传结果
                DefaultPutRet putRet = response.jsonToObject(DefaultPutRet.class);
                return qiniuConfig.getDomain() + "/" + putRet.key;
            } else {
                // 直接上传原始文件
                Response response = uploadManager.put(inputStream, key, getUploadToken(key), null, null);
                DefaultPutRet putRet = response.jsonToObject(DefaultPutRet.class);
                return qiniuConfig.getDomain() + "/" + putRet.key;
            }
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        if (!qiniuConfig.isEnabled()) {
            log.warn("七牛云存储未启用");
            return false;
        }

        try {
            // 从URL中提取文件名
            String key = fileUrl.replace(qiniuConfig.getDomain() + "/", "");

            // 删除文件
            Response response = bucketManager.delete(qiniuConfig.getBucket(), key);
            return response.isOK();
        } catch (QiniuException e) {
            log.error("删除文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getFileUrl(String fileName) {
        if (!qiniuConfig.isEnabled()) {
            log.warn("七牛云存储未启用");
            return null;
        }

        // 如果是私有空间，需要生成带有下载凭证的URL
        if (qiniuConfig.isPrivate()) {
            try {
                // 构建baseUrl
                String baseUrl = qiniuConfig.getDomain() + "/" + fileName;

                // 生成下载凭证
                String downloadUrl = auth.privateDownloadUrl(baseUrl, qiniuConfig.getDownloadExpires());

                log.info("生成私有空间文件访问URL成功，文件名: {}", fileName);
                return downloadUrl;
            } catch (Exception e) {
                log.error("生成私有空间文件访问URL失败: {}", e.getMessage(), e);
                return null;
            }
        } else {
            // 公有空间直接返回URL
            return qiniuConfig.getDomain() + "/" + fileName;
        }
    }

    @Override
    public String getUploadToken(String fileName) {
        // 刷新配置确保最新状态
        qiniuConfig.refreshConfig();

        // 检查配置完整性
        String validationError = qiniuConfig.getConfigValidationError();
        if (validationError != null) {
            log.warn("生成上传凭证失败: {}", validationError);
            return null;
        }

        // 检查auth是否已初始化
        if (auth == null) {
            log.warn("七牛云Auth未初始化，尝试重新初始化");
            initializeQiniuService();
            if (auth == null) {
                log.error("七牛云Auth初始化失败");
                return null;
            }
        }

        try {
            // 上传策略
            StringMap putPolicy = new StringMap();
            putPolicy.put("returnBody", "{\"key\":\"$(key)\",\"hash\":\"$(etag)\",\"bucket\":\"$(bucket)\",\"fsize\":$(fsize)}");

            // 检查文件名参数
            if (fileName == null || fileName.trim().isEmpty()) {
                log.error("文件名为空");
                return null;
            }

            // 生成上传凭证，有效期设置为1小时
            String token = auth.uploadToken(qiniuConfig.getBucket(), fileName, 3600, putPolicy);

            // 记录日志
            log.info("生成上传凭证成功，文件名: {}", fileName);

            return token;
        } catch (Exception e) {
            log.error("生成上传凭证失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public InputStream getFile(String fileUrl) {
        if (!qiniuConfig.isEnabled()) {
            log.warn("七牛云存储未启用");
            return null;
        }

        try {
            // 如果是私有空间，需要生成带有下载凭证的URL
            String accessUrl = fileUrl;
            if (qiniuConfig.isPrivate() && !fileUrl.contains("token=")) {
                // 从URL中提取文件名
                String fileName = fileUrl.replace(qiniuConfig.getDomain() + "/", "");
                // 生成带有下载凭证的URL
                accessUrl = getFileUrl(fileName);
            }

            // 获取文件
            URL url = new URL(accessUrl);
            URLConnection conn = url.openConnection();
            InputStream inputStream = conn.getInputStream();

            // 如果启用了加密，需要解密
            if (qiniuConfig.isEncryptEnabled()) {
                // 创建临时文件
                File tempFile = File.createTempFile("qiniu_", ".tmp");
                try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                    // 解密文件内容
                    encryptionService.decryptPhoto(inputStream, outputStream);
                }

                // 返回解密后的文件流
                return new FileInputStream(tempFile);
            } else {
                // 直接返回原始文件流
                return inputStream;
            }
        } catch (Exception e) {
            log.error("获取文件失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成文件名
     *
     * @param originalFilename 原始文件名
     * @return 生成的文件名
     */
    private String generateFileName(String originalFilename) {
        // 获取文件后缀
        String suffix = "";
        if (originalFilename.contains(".")) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成日期路径
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

        // 生成UUID
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        // 拼接文件名
        return qiniuConfig.getUploadDir() + "/" + datePath + "/" + uuid + suffix;
    }
}
