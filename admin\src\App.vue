<template>
  <div class="app-container">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from './store/user'

const userStore = useUserStore()

onMounted(async () => {
  // 如果有token，则获取用户信息
  if (userStore.token) {
    try {
      await userStore.fetchUserInfo()
      console.log('用户信息加载成功')
    } catch (error) {
      console.error('用户信息加载失败', error)
    }
  }
})
</script>

<style lang="scss">
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
}

#app {
  height: 100%;
}

.app-container {
  height: 100%;
}
</style>
