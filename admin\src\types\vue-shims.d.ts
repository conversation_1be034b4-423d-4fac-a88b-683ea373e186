declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<any, any, any>
  export default component
}

declare module 'path-browserify' {
  const path: {
    resolve: (...paths: string[]) => string
    join: (...paths: string[]) => string
    dirname: (path: string) => string
    basename: (path: string, ext?: string) => string
    extname: (path: string) => string
    normalize: (path: string) => string
    relative: (from: string, to: string) => string
    isAbsolute: (path: string) => boolean
    sep: string
    delimiter: string
  }
  export = path
}
