package com.phototagmoment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.phototagmoment.common.ResultCode;
import com.phototagmoment.dto.LoginDTO;
import com.phototagmoment.dto.RegisterDTO;
import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.phototagmoment.service.QiniuStorageService;
import com.phototagmoment.service.SmsService;
import com.phototagmoment.service.UserService;

import com.phototagmoment.vo.TokenVO;
import com.phototagmoment.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;

import java.util.ArrayList;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Value("${jwt.secret}")
    private String jwtSecret;

    private final UserMapper userMapper;
    private final UserAuthMapper userAuthMapper;
    private final PasswordEncoder passwordEncoder;
    // private final RedisUtil redisUtil;
    private final HttpServletRequest request;
    private final SmsService smsService;
    private final QiniuStorageService qiniuStorageService;

    @Value("${qiniu.is-private:false}")
    private boolean qiniuIsPrivate;

    public UserServiceImpl(UserMapper userMapper, UserAuthMapper userAuthMapper, PasswordEncoder passwordEncoder,
                          HttpServletRequest request, SmsService smsService, QiniuStorageService qiniuStorageService) {
        this.userMapper = userMapper;
        this.userAuthMapper = userAuthMapper;
        this.passwordEncoder = passwordEncoder;
        // this.redisUtil = redisUtil;
        this.request = request;
        this.smsService = smsService;
        this.qiniuStorageService = qiniuStorageService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long register(RegisterDTO registerDTO) {
        // 验证密码是否一致
        if (!Objects.equals(registerDTO.getPassword(), registerDTO.getConfirmPassword())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "两次输入的密码不一致");
        }

        // 验证用户协议
        if (registerDTO.getAgreement() == null || !registerDTO.getAgreement()) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "请同意用户协议");
        }

        // 验证用户名是否存在
        if (checkUsernameExists(registerDTO.getUsername())) {
            throw new BusinessException(ResultCode.DUPLICATE_USERNAME);
        }

        // 验证邮箱是否存在
        if (StrUtil.isNotBlank(registerDTO.getEmail()) && checkEmailExists(registerDTO.getEmail())) {
            throw new BusinessException(ResultCode.DUPLICATE_EMAIL);
        }

        // 验证手机号是否存在
        if (StrUtil.isNotBlank(registerDTO.getPhone()) && checkPhoneExists(registerDTO.getPhone())) {
            throw new BusinessException(ResultCode.DUPLICATE_PHONE);
        }

        // 创建用户
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword())); // 设置加密后的密码
        user.setNickname(StrUtil.isNotBlank(registerDTO.getNickname()) ? registerDTO.getNickname() : registerDTO.getUsername());
        user.setEmail(registerDTO.getEmail());

        // 手机号特殊处理，避免加密后超出数据库字段长度
        String phone = registerDTO.getPhone();
        if (StrUtil.isNotBlank(phone)) {
            // 只保留手机号的后4位，其余部分用*替代，避免加密后长度过长
            if (phone.length() > 4) {
                StringBuilder maskedPhone = new StringBuilder();
                for (int i = 0; i < phone.length() - 4; i++) {
                    maskedPhone.append("*");
                }
                maskedPhone.append(phone.substring(phone.length() - 4));
                user.setPhone(maskedPhone.toString());
            } else {
                user.setPhone(phone);
            }
        }

        user.setStatus(1);
        user.setIsVerified(0);
        user.setIsAdmin(0);
        user.setRegisterIp(getClientIp());
        user.setRegisterSource("web");

        // 插入用户
        userMapper.insert(user);

        // 创建用户认证信息
        UserAuth userAuth = new UserAuth();
        userAuth.setUserId(user.getId());
        userAuth.setIdentityType("username");
        userAuth.setIdentifier(registerDTO.getUsername());
        userAuth.setCredential(passwordEncoder.encode(registerDTO.getPassword()));
        userAuth.setVerified(1);

        // 插入用户认证信息
        userAuthMapper.insert(userAuth);

        // 如果有邮箱，创建邮箱认证信息
        if (StrUtil.isNotBlank(registerDTO.getEmail())) {
            UserAuth emailAuth = new UserAuth();
            emailAuth.setUserId(user.getId());
            emailAuth.setIdentityType("email");
            emailAuth.setIdentifier(registerDTO.getEmail());
            emailAuth.setVerified(0);
            userAuthMapper.insert(emailAuth);
        }

        // 如果有手机号，创建手机认证信息
        if (StrUtil.isNotBlank(registerDTO.getPhone())) {
            UserAuth phoneAuth = new UserAuth();
            phoneAuth.setUserId(user.getId());
            phoneAuth.setIdentityType("phone");
            phoneAuth.setIdentifier(registerDTO.getPhone());
            phoneAuth.setVerified(0);
            userAuthMapper.insert(phoneAuth);
        }

        return user.getId();
    }

    @Override
    public TokenVO login(LoginDTO loginDTO) {
        try {
            log.info("用户登录: {}", loginDTO.getUsername());

            // 查找用户 - 直接使用Mapper查询，避免触发解密操作
            User user = null;
            if (StrUtil.isNotBlank(loginDTO.getUsername())) {
                // 尝试用户名登录
                user = userMapper.selectByUsername(loginDTO.getUsername());
                if (user == null) {
                    // 尝试邮箱登录
                    user = userMapper.selectByEmail(loginDTO.getUsername());
                    if (user == null) {
                        // 尝试手机号登录
                        user = userMapper.selectByPhone(loginDTO.getUsername());
                    }
                }
            }

            if (user == null) {
                log.warn("登录失败: 用户不存在 - {}", loginDTO.getUsername());
                throw new BusinessException(ResultCode.USERNAME_OR_PASSWORD_ERROR);
            }

            // 检查用户状态
            if (user.getStatus() == 0) {
                log.warn("登录失败: 用户已禁用 - {}", loginDTO.getUsername());
                throw new BusinessException(ResultCode.USER_ACCOUNT_DISABLED);
            }

            log.info("用户存在，开始验证密码: {}", user.getUsername());

            // 查询用户认证信息
            UserAuth userAuth = userAuthMapper.selectByUserIdAndType(user.getId(), "username");
            if (userAuth == null) {
                log.error("用户认证信息不存在: {}", user.getUsername());
                throw new BusinessException(ResultCode.SYSTEM_ERROR, "用户认证信息不存在");
            }

            // 直接验证密码，不使用Spring Security的认证机制
            log.info("验证密码: 用户={}, 输入密码长度={}, 存储凭证前10位={}",
                user.getUsername(),
                loginDTO.getPassword().length(),  // 不显示明文密码，只显示长度
                userAuth.getCredential().substring(0, Math.min(10, userAuth.getCredential().length())) + "..." // 只显示部分哈希
            );

            boolean passwordMatches = passwordEncoder.matches(loginDTO.getPassword(), userAuth.getCredential());
            if (!passwordMatches) {
                log.warn("密码验证失败: {}", user.getUsername());
                throw new BusinessException(ResultCode.USERNAME_OR_PASSWORD_ERROR);
            }

            log.info("密码验证成功: {}", user.getUsername());

            // 生成JWT
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", user.getId());

            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + jwtExpiration);

            SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));

            String token = Jwts.builder()
                    .setClaims(claims)
                    .setSubject(user.getUsername())
                    .setIssuedAt(now)
                    .setExpiration(expiryDate)
                    .signWith(key, SignatureAlgorithm.HS512)
                    .compact();

            log.info("生成JWT成功: {}", user.getUsername());

            // 完全跳过创建认证对象和设置SecurityContext的步骤
            // 这样可以避免触发Spring Security的认证机制，防止循环依赖
            // 认证信息将由JwtAuthenticationFilter在处理后续请求时设置
            log.info("登录过程中完全跳过创建认证对象和设置SecurityContext，避免循环依赖: {}", user.getUsername());

            // 更新用户最后登录时间和IP
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(getClientIp());
            userMapper.updateById(user);
            log.info("更新用户登录信息成功: {}", user.getUsername());

            // 构建返回对象 - 手动创建UserVO，避免触发解密操作
            UserVO userVO = new UserVO();
            userVO.setId(user.getId());
            userVO.setUsername(user.getUsername());
            userVO.setNickname(user.getNickname());
            userVO.setAvatar(user.getAvatar());

            // 对于加密字段，使用掩码处理
            if (user.getEmail() != null) {
                if (user.getEmail().contains("@")) {
                    userVO.setEmail(user.getEmail()); // 如果已经是明文，直接使用
                } else {
                    userVO.setEmail("***@***.com"); // 使用掩码
                }
            }

            if (user.getPhone() != null) {
                if (user.getPhone().contains("*")) {
                    userVO.setPhone(user.getPhone()); // 如果已经是掩码，直接使用
                } else {
                    userVO.setPhone("*******" + user.getPhone().substring(Math.max(0, user.getPhone().length() - 4))); // 使用掩码
                }
            }

            userVO.setGender(user.getGender());
            userVO.setBirthday(user.getBirthday());
            userVO.setBio(user.getBio());
            // UserVO 类中没有 status 字段，所以不设置
            userVO.setIsVerified(user.getIsVerified());
            userVO.setIsAdmin(user.getIsAdmin());
            userVO.setFollowingCount(user.getFollowingCount());
            userVO.setFollowerCount(user.getFollowerCount());
            userVO.setPhotoCount(user.getPhotoCount());
            userVO.setCreatedAt(user.getCreatedAt());

            TokenVO tokenVO = new TokenVO();
            tokenVO.setToken(token);
            tokenVO.setTokenType(tokenPrefix);
            tokenVO.setExpiresIn(jwtExpiration / 1000);
            tokenVO.setUser(userVO);

            log.info("登录成功: {}", user.getUsername());
            return tokenVO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("登录过程中发生异常: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "系统错误，请稍后再试");
        }
    }

    @Override
    public User getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public User getUserByEmail(String email) {
        return userMapper.selectByEmail(email);
    }

    @Override
    public User getUserByPhone(String phone) {
        return userMapper.selectByPhone(phone);
    }

    @Override
    public User getUserByNickname(String nickname) {
        return userMapper.selectByNickname(nickname);
    }

    @Override
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public UserVO getCurrentUser() {
        try {
            // 直接从SecurityContext获取当前用户名
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated() ||
                    "anonymousUser".equals(authentication.getPrincipal())) {
                log.debug("当前用户未认证或是匿名用户");
                return null;
            }

            String username = null;
            Object principal = authentication.getPrincipal();
            if (principal instanceof UserDetails) {
                username = ((UserDetails) principal).getUsername();
                log.debug("从UserDetails获取用户名: {}", username);
            } else if (principal instanceof String) {
                username = (String) principal;
                log.debug("从String获取用户名: {}", username);
            } else {
                log.debug("无法获取用户名，principal类型: {}", principal.getClass().getName());
                return null;
            }

            if (username == null) {
                log.debug("用户名为空");
                return null;
            }

            // 直接从数据库查询用户
            User user = userMapper.selectByUsername(username);
            if (user == null) {
                log.warn("用户不存在: {}", username);
                return null;
            }

            log.debug("成功获取当前用户: {}", username);
            return convertToUserVO(user);
        } catch (Exception e) {
            log.error("获取当前用户失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean updateUser(User user) {
        return userMapper.updateById(user) > 0;
    }

    @Override
    public boolean updatePassword(Long userId, String oldPassword, String newPassword) {
        // 查询用户
        User user = getUserById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 直接查询用户认证信息
        UserAuth userAuth = userAuthMapper.selectByUserIdAndType(userId, "username");
        if (userAuth == null) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "用户认证信息不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, userAuth.getCredential())) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "旧密码不正确");
        }

        // 更新密码
        userAuth.setCredential(passwordEncoder.encode(newPassword));
        return userAuthMapper.updateById(userAuth) > 0;
    }

    @Override
    public boolean resetPassword(String email, String password) {
        // 查询用户
        User user = getUserByEmail(email);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 直接查询用户认证信息
        UserAuth userAuth = userAuthMapper.selectByUserIdAndType(user.getId(), "username");
        if (userAuth == null) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "用户认证信息不存在");
        }

        // 更新密码
        userAuth.setCredential(passwordEncoder.encode(password));
        return userAuthMapper.updateById(userAuth) > 0;
    }

    @Override
    public boolean checkUsernameExists(String username) {
        return getUserByUsername(username) != null;
    }

    @Override
    public boolean checkEmailExists(String email) {
        return getUserByEmail(email) != null;
    }

    @Override
    public boolean checkPhoneExists(String phone) {
        return getUserByPhone(phone) != null;
    }

    @Override
    public boolean logout() {
        try {
            // 直接清除认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated() ||
                    "anonymousUser".equals(authentication.getPrincipal())) {
                log.debug("当前用户未认证或是匿名用户，无需登出");
                return false;
            }

            // 获取用户名，用于日志记录
            String username = null;
            Object principal = authentication.getPrincipal();
            if (principal instanceof UserDetails) {
                username = ((UserDetails) principal).getUsername();
            } else if (principal instanceof String) {
                username = (String) principal;
            }

            SecurityContextHolder.clearContext();
            log.info("用户 {} 已登出，认证信息已清除", username != null ? username : "未知用户");
            return true;
        } catch (Exception e) {
            log.error("登出过程中发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public UserVO convertToUserVO(User user) {
        if (user == null) {
            return null;
        }

        UserVO userVO = new UserVO();
        BeanUtil.copyProperties(user, userVO);

        // 如果是七牛云私有空间，生成带下载凭证的URL
        if (qiniuIsPrivate && userVO.getAvatar() != null && !userVO.getAvatar().isEmpty()) {
            try {
                String fileName = extractFileNameFromUrl(userVO.getAvatar());
                String privateUrl = qiniuStorageService.getFileUrl(fileName);
                if (privateUrl != null && !privateUrl.isEmpty()) {
                    userVO.setAvatar(privateUrl);
                }
            } catch (Exception e) {
                log.error("处理用户头像URL失败", e);
                // 不影响业务，继续执行
            }
        }

        return userVO;
    }

    /**
     * 从URL中提取文件名
     * @param url URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        // 移除域名部分
        int domainEndIndex = url.indexOf("/", 8); // 跳过 "https://"
        if (domainEndIndex != -1) {
            return url.substring(domainEndIndex + 1);
        }

        return url;
    }

    @Override
    public User findByWechatOpenId(String openId) {
        if (StrUtil.isBlank(openId)) {
            return null;
        }
        return userMapper.selectByWechatOpenId(openId);
    }

    @Override
    public User findByWechatUnionId(String unionId) {
        if (StrUtil.isBlank(unionId)) {
            return null;
        }
        return userMapper.selectByWechatUnionId(unionId);
    }

    @Override
    public TokenVO loginByPhone(String phone, String code) {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(code)) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "手机号或验证码不能为空");
        }

        // 验证验证码
        boolean verified = smsService.verifyCode(phone, code);
        if (!verified) {
            throw new BusinessException(ResultCode.VALIDATE_FAILED, "验证码错误或已过期");
        }

        // 查询用户
        User user = getUserByPhone(phone);
        if (user == null) {
            // 用户不存在，创建新用户
            user = new User();
            user.setUsername("phone_" + phone);
            user.setNickname("用户" + phone.substring(phone.length() - 4));
            user.setPhone(phone);
            user.setStatus(1);
            user.setIsVerified(0);
            user.setIsAdmin(0);
            user.setFollowingCount(0);
            user.setFollowerCount(0);
            user.setPhotoCount(0);
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(getClientIp());
            user.setRegisterIp(getClientIp());
            user.setRegisterSource("phone");

            // 插入用户
            userMapper.insert(user);

            // 创建手机认证信息
            UserAuth phoneAuth = new UserAuth();
            phoneAuth.setUserId(user.getId());
            phoneAuth.setIdentityType("phone");
            phoneAuth.setIdentifier(phone);
            phoneAuth.setVerified(1);
            userAuthMapper.insert(phoneAuth);
        } else {
            // 更新用户最后登录时间和IP
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(getClientIp());
            userMapper.updateById(user);
        }

        // 直接生成JWT，不创建认证对象，不设置SecurityContext
        // 这样可以避免触发过滤器的递归调用和循环依赖
        // 使用内联方式生成JWT，不依赖JwtTokenProvider
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());

        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpiration);

        SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));

        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(user.getUsername())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
        log.info("生成JWT成功: {}", user.getUsername());

        // 构建返回对象
        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(token);
        tokenVO.setTokenType(tokenPrefix);
        tokenVO.setExpiresIn(jwtExpiration / 1000);
        tokenVO.setUser(convertToUserVO(user));

        return tokenVO;
    }

    @Override
    public boolean save(User user) {
        if (user == null) {
            return false;
        }

        if (user.getId() == null) {
            return userMapper.insert(user) > 0;
        } else {
            return userMapper.updateById(user) > 0;
        }
    }

    @Override
    public boolean updateById(User user) {
        if (user == null || user.getId() == null) {
            return false;
        }

        return userMapper.updateById(user) > 0;
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
