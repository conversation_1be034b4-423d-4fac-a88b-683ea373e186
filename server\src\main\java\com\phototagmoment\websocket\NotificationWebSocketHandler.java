package com.phototagmoment.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phototagmoment.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知WebSocket处理器
 */
@Component
public class NotificationWebSocketHandler extends TextWebSocketHandler {

    // 手动添加日志对象
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(NotificationWebSocketHandler.class);

    private final ObjectMapper objectMapper;
    private final JwtTokenProvider jwtTokenProvider;

    // 用户ID -> WebSocketSession映射
    private final Map<Long, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    // WebSocketSession ID -> 用户ID映射
    private final Map<String, Long> sessionUsers = new ConcurrentHashMap<>();

    @Autowired
    public NotificationWebSocketHandler(ObjectMapper objectMapper, JwtTokenProvider jwtTokenProvider) {
        this.objectMapper = objectMapper;
        this.jwtTokenProvider = jwtTokenProvider;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("WebSocket连接已建立: {}", session.getId());

        // 从URL参数中获取token
        String token = extractToken(session);
        if (token != null && jwtTokenProvider.validateToken(token)) {
            Long userId = jwtTokenProvider.getUserIdFromToken(token);
            if (userId != null) {
                // 保存用户会话
                userSessions.put(userId, session);
                sessionUsers.put(session.getId(), userId);
                log.info("用户 {} 已连接WebSocket", userId);

                // 发送连接成功消息
                sendMessage(session, WebSocketMessage.system("连接成功"));
            } else {
                log.warn("无法从token中获取用户ID");
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("无效的用户ID"));
            }
        } else {
            log.warn("无效的token");
            session.close(CloseStatus.NOT_ACCEPTABLE.withReason("无效的token"));
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        log.debug("收到消息: {}", payload);

        // 处理心跳消息
        if ("ping".equals(payload)) {
            sendMessage(session, WebSocketMessage.heartbeat());
            return;
        }

        // 其他消息处理逻辑可以在这里添加
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        log.info("WebSocket连接已关闭: {}, 状态: {}", session.getId(), status);

        // 清理会话
        Long userId = sessionUsers.remove(session.getId());
        if (userId != null) {
            userSessions.remove(userId);
            log.info("用户 {} 已断开WebSocket连接", userId);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误: {}", exception.getMessage(), exception);

        // 清理会话
        if (session.isOpen()) {
            session.close(CloseStatus.SERVER_ERROR.withReason("传输错误"));
        }

        Long userId = sessionUsers.remove(session.getId());
        if (userId != null) {
            userSessions.remove(userId);
            log.info("用户 {} 的WebSocket连接因错误已关闭", userId);
        }
    }

    /**
     * 向指定用户发送消息
     *
     * @param userId 用户ID
     * @param message 消息对象
     * @return 是否发送成功
     */
    public boolean sendMessageToUser(Long userId, WebSocketMessage message) {
        WebSocketSession session = userSessions.get(userId);
        if (session != null && session.isOpen()) {
            try {
                sendMessage(session, message);
                return true;
            } catch (Exception e) {
                log.error("向用户 {} 发送消息失败: {}", userId, e.getMessage(), e);
            }
        }
        return false;
    }

    /**
     * 向所有在线用户广播消息
     *
     * @param message 消息对象
     */
    public void broadcastMessage(WebSocketMessage message) {
        userSessions.forEach((userId, session) -> {
            if (session.isOpen()) {
                try {
                    sendMessage(session, message);
                } catch (Exception e) {
                    log.error("向用户 {} 广播消息失败: {}", userId, e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 发送消息
     *
     * @param session WebSocket会话
     * @param message 消息对象
     * @throws IOException 发送异常
     */
    private void sendMessage(WebSocketSession session, WebSocketMessage message) throws IOException {
        String json = objectMapper.writeValueAsString(message);
        session.sendMessage(new TextMessage(json));
    }

    /**
     * 从WebSocket会话中提取token
     *
     * @param session WebSocket会话
     * @return token字符串
     */
    private String extractToken(WebSocketSession session) {
        String query = session.getUri().getQuery();
        if (query != null) {
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && "token".equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        }
        return null;
    }

    /**
     * 获取当前在线用户数
     *
     * @return 在线用户数
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }
}
