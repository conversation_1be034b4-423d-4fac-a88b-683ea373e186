<template>
  <div :class="{'hidden': hidden}" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page="currentPage"
      :page-size="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      }
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val })
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize })
      if (this.autoScroll) {
        this.scrollTo(0, 800)
      }
    },
    scrollTo(to, duration) {
      const start = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
      const change = to - start
      const increment = 20
      let currentTime = 0

      const animateScroll = function() {
        currentTime += increment
        const val = Math.easeInOutQuad(currentTime, start, change, duration)
        document.documentElement.scrollTop = val
        document.body.scrollTop = val
        if (currentTime < duration) {
          setTimeout(animateScroll, increment)
        }
      }

      Math.easeInOutQuad = function(t, b, c, d) {
        t /= d / 2
        if (t < 1) {
          return c / 2 * t * t + b
        }
        t--
        return -c / 2 * (t * (t - 2) - 1) + b
      }

      animateScroll()
    }
  }
}
</script>

<style scoped>
.pagination-container {
  background: #fff;
  padding: 32px 16px;
}
.pagination-container.hidden {
  display: none;
}
</style>
