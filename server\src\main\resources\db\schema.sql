-- 创建数据库
CREATE DATABASE IF NOT EXISTS phototag_moment DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE phototag_moment;

-- 用户表
CREATE TABLE IF NOT EXISTS `ptm_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `bio` varchar(255) DEFAULT NULL COMMENT '个人简介',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0禁用，1正常',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否实名认证：0否，1是',
  `is_admin` tinyint(1) DEFAULT 0 COMMENT '是否管理员：0否，1是',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `register_ip` varchar(50) DEFAULT NULL COMMENT '注册IP',
  `register_source` varchar(20) DEFAULT NULL COMMENT '注册来源',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`),
  UNIQUE KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户认证信息表
CREATE TABLE IF NOT EXISTS `ptm_user_auth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `identity_type` varchar(20) NOT NULL COMMENT '认证类型：wechat微信，alipay支付宝，email邮箱，phone手机',
  `identifier` varchar(100) NOT NULL COMMENT '认证标识（微信openid、邮箱、手机号）',
  `credential` varchar(100) DEFAULT NULL COMMENT '凭证（密码、token）',
  `verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_identity` (`user_id`, `identity_type`),
  UNIQUE KEY `idx_identity` (`identity_type`, `identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证信息表';

-- 用户实名认证表
CREATE TABLE IF NOT EXISTS `ptm_user_verification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card` varchar(20) NOT NULL COMMENT '身份证号',
  `front_image` varchar(255) DEFAULT NULL COMMENT '身份证正面照片',
  `back_image` varchar(255) DEFAULT NULL COMMENT '身份证背面照片',
  `face_image` varchar(255) DEFAULT NULL COMMENT '人脸照片',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0待审核，1已通过，2已拒绝',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '拒绝原因',
  `verified_at` datetime DEFAULT NULL COMMENT '认证通过时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`),
  UNIQUE KEY `idx_id_card` (`id_card`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户实名认证表';

-- 照片表
CREATE TABLE IF NOT EXISTS `ptm_photo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `title` varchar(100) DEFAULT NULL COMMENT '标题',
  `description` text DEFAULT NULL COMMENT '描述',
  `storage_path` varchar(255) NOT NULL COMMENT '存储路径',
  `url` varchar(255) NOT NULL COMMENT '访问URL',
  `thumbnail_url` varchar(255) DEFAULT NULL COMMENT '缩略图URL',
  `width` int(11) DEFAULT NULL COMMENT '宽度',
  `height` int(11) DEFAULT NULL COMMENT '高度',
  `size` bigint(20) DEFAULT NULL COMMENT '文件大小',
  `format` varchar(10) DEFAULT NULL COMMENT '文件格式',
  `location` varchar(100) DEFAULT NULL COMMENT '地理位置',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `visibility` tinyint(1) DEFAULT 1 COMMENT '可见性：0私密，1公开，2好友可见',
  `allow_comment` tinyint(1) DEFAULT 1 COMMENT '是否允许评论',
  `allow_download` tinyint(1) DEFAULT 1 COMMENT '是否允许下载',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论次数',
  `share_count` int(11) DEFAULT 0 COMMENT '分享次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0待审核，1正常，2已拒绝',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_visibility` (`visibility`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片表';

-- 照片标签表
CREATE TABLE IF NOT EXISTS `ptm_photo_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `photo_id` bigint(20) NOT NULL COMMENT '照片ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_tag_name` (`tag_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片标签表';

-- 照片人物标记表
CREATE TABLE IF NOT EXISTS `ptm_photo_person` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `photo_id` bigint(20) NOT NULL COMMENT '照片ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（如果标记的是用户）',
  `name` varchar(50) DEFAULT NULL COMMENT '人物名称（如果不是用户）',
  `position_x` decimal(5,2) DEFAULT NULL COMMENT 'X坐标（百分比）',
  `position_y` decimal(5,2) DEFAULT NULL COMMENT 'Y坐标（百分比）',
  `width` decimal(5,2) DEFAULT NULL COMMENT '宽度（百分比）',
  `height` decimal(5,2) DEFAULT NULL COMMENT '高度（百分比）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='照片人物标记表';



-- 用户关注表
CREATE TABLE IF NOT EXISTS `ptm_user_follow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `follow_user_id` bigint(20) NOT NULL COMMENT '被关注用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_follow` (`user_id`, `follow_user_id`),
  KEY `idx_follow_user_id` (`follow_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';



-- 评论表
CREATE TABLE IF NOT EXISTS `ptm_comment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `photo_id` bigint(20) NOT NULL COMMENT '照片ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID',
  `reply_to_user_id` bigint(20) DEFAULT NULL COMMENT '回复用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0待审核，1正常，2已拒绝',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_photo_id` (`photo_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';





-- 通知表
CREATE TABLE IF NOT EXISTS `ptm_notification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` bigint(20) NOT NULL COMMENT '接收用户ID',
  `sender_id` bigint(20) DEFAULT NULL COMMENT '发送用户ID',
  `type` tinyint(1) NOT NULL COMMENT '通知类型：1关注，2点赞，3评论，4回复，5系统',
  `target_id` bigint(20) DEFAULT NULL COMMENT '目标ID',
  `target_type` tinyint(1) DEFAULT NULL COMMENT '目标类型：1照片，2评论，3用户',
  `content` varchar(255) DEFAULT NULL COMMENT '通知内容',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';



-- 系统配置表
CREATE TABLE IF NOT EXISTS `ptm_system_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `ptm_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `operation` varchar(50) NOT NULL COMMENT '操作类型',
  `method` varchar(100) NOT NULL COMMENT '请求方法',
  `params` text DEFAULT NULL COMMENT '请求参数',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `status` tinyint(1) DEFAULT 1 COMMENT '操作状态：0失败，1成功',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间(ms)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation` (`operation`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
