package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 标签搜索结果DTO
 */
@Data
@Schema(description = "标签搜索结果")
public class TagSearchResultDTO {

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "相关照片笔记总数")
    private Long totalCount;

    @Schema(description = "标签热度分数")
    private BigDecimal hotScore;

    @Schema(description = "照片笔记列表")
    private List<PhotoNoteDTO> notes;

    @Schema(description = "相关标签推荐")
    private List<RelatedTagDTO> relatedTags;

    /**
     * 相关标签DTO
     */
    @Data
    @Schema(description = "相关标签信息")
    public static class RelatedTagDTO {

        @Schema(description = "标签名称")
        private String tagName;

        @Schema(description = "使用次数")
        private Integer useCount;

        @Schema(description = "热度分数")
        private BigDecimal hotScore;
    }
}
