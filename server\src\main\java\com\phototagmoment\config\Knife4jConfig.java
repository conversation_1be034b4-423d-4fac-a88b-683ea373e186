package com.phototagmoment.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.ExternalDocumentation;
import org.springframework.beans.factory.annotation.Value;

/**
 * Knife4j配置类
 */
@Configuration
public class Knife4jConfig {

    @Value("${knife4j.openapi.title:PhotoTagMoment API}")
    private String title;

    @Value("${knife4j.openapi.description:PhotoTagMoment照片社交网站API文档}")
    private String description;

    @Value("${knife4j.openapi.version:v1.0.0}")
    private String version;

    @Value("${knife4j.openapi.concat:PhotoTagMoment Team}")
    private String contactName;

    @Value("${knife4j.openapi.email:<EMAIL>}")
    private String contactEmail;

    @Value("${knife4j.openapi.url:https://www.phototagmoment.com}")
    private String contactUrl;

    @Value("${knife4j.openapi.license:Apache 2.0}")
    private String licenseName;

    @Value("${knife4j.openapi.license-url:https://www.apache.org/licenses/LICENSE-2.0.html}")
    private String licenseUrl;

    /**
     * 配置OpenAPI
     */
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(title)
                        .description(description)
                        .version(version)
                        .contact(new Contact()
                                .name(contactName)
                                .email(contactEmail)
                                .url(contactUrl))
                        .license(new License()
                                .name(licenseName)
                                .url(licenseUrl)))
                .externalDocs(new ExternalDocumentation()
                        .description("PhotoTagMoment项目文档")
                        .url("https://www.phototagmoment.com/docs"))
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", createAPIKeyScheme()));
    }

    /**
     * 创建API密钥方案
     */
    private SecurityScheme createAPIKeyScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .bearerFormat("JWT")
                .scheme("bearer")
                .in(SecurityScheme.In.HEADER)
                .name(HttpHeaders.AUTHORIZATION);
    }
}
