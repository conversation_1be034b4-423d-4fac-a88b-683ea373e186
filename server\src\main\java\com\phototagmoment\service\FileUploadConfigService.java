package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.FileUploadConfigDTO;
import com.phototagmoment.entity.FileUploadConfig;

import java.util.List;
import java.util.Map;

/**
 * 文件上传配置服务接口
 */
public interface FileUploadConfigService {

    /**
     * 分页查询配置列表
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param storageType 存储类型
     * @param enabled 启用状态
     * @param status 配置状态
     * @return 配置列表
     */
    IPage<FileUploadConfigDTO> getConfigList(Page<FileUploadConfigDTO> page, String keyword, 
                                             String storageType, Boolean enabled, Integer status);

    /**
     * 获取配置详情
     * @param configId 配置ID
     * @return 配置详情
     */
    FileUploadConfigDTO getConfigDetail(Long configId);

    /**
     * 创建配置
     * @param configDTO 配置信息
     * @return 配置ID
     */
    Long createConfig(FileUploadConfigDTO configDTO);

    /**
     * 更新配置
     * @param configDTO 配置信息
     * @return 是否成功
     */
    boolean updateConfig(FileUploadConfigDTO configDTO);

    /**
     * 删除配置
     * @param configId 配置ID
     * @return 是否成功
     */
    boolean deleteConfig(Long configId);

    /**
     * 批量删除配置
     * @param configIds 配置ID列表
     * @return 删除结果
     */
    Map<String, Object> batchDeleteConfigs(List<Long> configIds);

    /**
     * 启用/禁用配置
     * @param configId 配置ID
     * @param enabled 启用状态
     * @return 是否成功
     */
    boolean toggleConfig(Long configId, Boolean enabled);

    /**
     * 设置默认配置
     * @param configId 配置ID
     * @return 是否成功
     */
    boolean setDefaultConfig(Long configId);

    /**
     * 获取默认配置
     * @return 默认配置
     */
    FileUploadConfigDTO getDefaultConfig();

    /**
     * 获取启用的配置列表
     * @return 启用的配置列表
     */
    List<FileUploadConfigDTO> getEnabledConfigs();

    /**
     * 根据存储类型获取配置
     * @param storageType 存储类型
     * @return 配置信息
     */
    FileUploadConfigDTO getConfigByStorageType(String storageType);

    /**
     * 测试配置连接
     * @param configId 配置ID
     * @return 测试结果
     */
    FileUploadConfigDTO.TestResult testConfig(Long configId);

    /**
     * 测试配置连接（不保存）
     * @param configDTO 配置信息
     * @return 测试结果
     */
    FileUploadConfigDTO.TestResult testConfigWithoutSave(FileUploadConfigDTO configDTO);

    /**
     * 批量测试配置
     * @param configIds 配置ID列表
     * @return 测试结果
     */
    Map<String, Object> batchTestConfigs(List<Long> configIds);

    /**
     * 复制配置
     * @param configId 源配置ID
     * @param newConfigName 新配置名称
     * @return 新配置ID
     */
    Long copyConfig(Long configId, String newConfigName);

    /**
     * 导入配置
     * @param configData 配置数据
     * @return 导入结果
     */
    Map<String, Object> importConfigs(String configData);

    /**
     * 导出配置
     * @param configIds 配置ID列表
     * @return 配置数据
     */
    String exportConfigs(List<Long> configIds);

    /**
     * 获取配置统计信息
     * @return 统计信息
     */
    Map<String, Object> getConfigStatistics();

    /**
     * 获取存储类型统计
     * @return 存储类型统计
     */
    List<Map<String, Object>> getStorageTypeStatistics();

    /**
     * 获取配置健康状态
     * @return 健康状态信息
     */
    Map<String, Object> getConfigHealthStatus();

    /**
     * 刷新配置缓存
     * @return 是否成功
     */
    boolean refreshConfigCache();

    /**
     * 验证配置参数
     * @param configDTO 配置信息
     * @return 验证结果
     */
    Map<String, Object> validateConfig(FileUploadConfigDTO configDTO);

    /**
     * 获取支持的存储类型列表
     * @return 存储类型列表
     */
    List<Map<String, Object>> getSupportedStorageTypes();

    /**
     * 获取配置模板
     * @param storageType 存储类型
     * @return 配置模板
     */
    FileUploadConfigDTO getConfigTemplate(String storageType);

    /**
     * 检查配置名称是否可用
     * @param configName 配置名称
     * @param excludeId 排除的配置ID
     * @return 是否可用
     */
    boolean isConfigNameAvailable(String configName, Long excludeId);

    /**
     * 获取最近测试失败的配置
     * @param limit 数量限制
     * @return 失败的配置列表
     */
    List<FileUploadConfigDTO> getRecentFailedConfigs(Integer limit);

    /**
     * 获取长时间未测试的配置
     * @param days 天数
     * @param limit 数量限制
     * @return 未测试的配置列表
     */
    List<FileUploadConfigDTO> getUntestedConfigs(Integer days, Integer limit);

    /**
     * 自动修复配置
     * @param configId 配置ID
     * @return 修复结果
     */
    Map<String, Object> autoRepairConfig(Long configId);
}
