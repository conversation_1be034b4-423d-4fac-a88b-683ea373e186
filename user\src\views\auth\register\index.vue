<template>
  <div class="register-container">
    <h2>注册</h2>
    <form @submit.prevent="handleRegister">
      <div class="form-item">
        <label for="username">用户名</label>
        <input
          type="text"
          id="username"
          v-model="registerForm.username"
          placeholder="请输入用户名"
          required
        />
        <small v-if="usernameError" class="error-message">{{ usernameError }}</small>
      </div>
      <div class="form-item">
        <label for="password">密码</label>
        <input
          type="password"
          id="password"
          v-model="registerForm.password"
          placeholder="请输入密码"
          required
        />
        <small v-if="passwordError" class="error-message">{{ passwordError }}</small>
      </div>
      <div class="form-item">
        <label for="confirmPassword">确认密码</label>
        <input
          type="password"
          id="confirmPassword"
          v-model="registerForm.confirmPassword"
          placeholder="请再次输入密码"
          required
        />
        <small v-if="confirmPasswordError" class="error-message">{{ confirmPasswordError }}</small>
      </div>
      <div class="form-item">
        <label for="nickname">昵称</label>
        <input
          type="text"
          id="nickname"
          v-model="registerForm.nickname"
          placeholder="请输入昵称"
        />
      </div>
      <div class="form-item">
        <label for="email">邮箱</label>
        <input
          type="email"
          id="email"
          v-model="registerForm.email"
          placeholder="请输入邮箱"
        />
        <small v-if="emailError" class="error-message">{{ emailError }}</small>
      </div>
      <div class="form-item">
        <label for="phone">手机号</label>
        <input
          type="tel"
          id="phone"
          v-model="registerForm.phone"
          placeholder="请输入手机号"
        />
        <small v-if="phoneError" class="error-message">{{ phoneError }}</small>
      </div>
      <div class="form-item agreement">
        <input type="checkbox" id="agreement" v-model="registerForm.agreement" required />
        <label for="agreement">我已阅读并同意<a href="#" @click.prevent="showAgreement">用户协议</a>和<a href="#" @click.prevent="showPrivacy">隐私政策</a></label>
      </div>
      <div class="form-actions">
        <button type="submit" class="btn-register" :disabled="isSubmitting">
          {{ isSubmitting ? '注册中...' : '注册' }}
        </button>
      </div>
      <div class="form-links">
        <router-link to="/auth/login">已有账号？去登录</router-link>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { register } from '@/api/user'

const router = useRouter()

// 注册表单数据
const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  email: '',
  phone: '',
  agreement: false
})

// 错误信息
const usernameError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')
const emailError = ref('')
const phoneError = ref('')
const isSubmitting = ref(false)

// 处理注册
const handleRegister = async () => {
  // 重置错误信息
  usernameError.value = ''
  passwordError.value = ''
  confirmPasswordError.value = ''
  emailError.value = ''
  phoneError.value = ''

  // 表单验证
  let isValid = true

  // 用户名验证
  if (registerForm.username.length < 3 || registerForm.username.length > 20) {
    usernameError.value = '用户名长度必须在3-20个字符之间'
    isValid = false
  }

  // 密码验证
  if (registerForm.password.length < 6 || registerForm.password.length > 20) {
    passwordError.value = '密码长度必须在6-20个字符之间'
    isValid = false
  }

  // 确认密码验证
  if (registerForm.password !== registerForm.confirmPassword) {
    confirmPasswordError.value = '两次输入的密码不一致'
    isValid = false
  }

  // 邮箱验证
  if (registerForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerForm.email)) {
    emailError.value = '邮箱格式不正确'
    isValid = false
  }

  // 手机号验证
  if (registerForm.phone && !/^1[3-9]\d{9}$/.test(registerForm.phone)) {
    phoneError.value = '手机号格式不正确'
    isValid = false
  }

  // 协议验证
  if (!registerForm.agreement) {
    showToast('请同意用户协议和隐私政策')
    isValid = false
  }

  if (!isValid) {
    return
  }

  // 提交注册
  try {
    isSubmitting.value = true

    // 调用注册API
    await register({
      username: registerForm.username,
      password: registerForm.password,
      confirmPassword: registerForm.confirmPassword,
      nickname: registerForm.nickname || registerForm.username,
      email: registerForm.email,
      phone: registerForm.phone,
      agreement: registerForm.agreement
    })

    isSubmitting.value = false
    showToast('注册成功，请登录')
    router.push('/auth/login')
  } catch (error: any) {
    isSubmitting.value = false
    console.error('注册失败', error)

    // 处理错误
    if (error.response) {
      const { data } = error.response
      if (data.code === 1006) {
        usernameError.value = '用户名已存在'
      } else if (data.code === 1007) {
        phoneError.value = '手机号已存在'
      } else if (data.code === 1008) {
        emailError.value = '邮箱已存在'
      } else {
        showToast({
          message: data.message || '注册失败，请稍后重试',
          type: 'fail'
        })
      }
    } else {
      showToast({
        message: '注册失败，请稍后重试',
        type: 'fail'
      })
    }
  }
}

// 显示用户协议
const showAgreement = () => {
  showDialog({
    title: '用户协议',
    message: '欢迎使用PhotoTagMoment！本协议包含您使用我们服务的条款和条件。使用我们的服务，即表示您同意本协议的所有条款。',
    confirmButtonText: '我知道了'
  })
}

// 显示隐私政策
const showPrivacy = () => {
  showDialog({
    title: '隐私政策',
    message: 'PhotoTagMoment尊重并保护用户隐私。我们收集的信息将用于提供、改进和开发服务。我们不会未经您的许可向第三方出售或出租您的个人信息。',
    confirmButtonText: '我知道了'
  })
}
</script>

<style lang="scss" scoped>
.register-container {
  max-width: 500px;
  margin: 40px auto;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  background-color: white;

  h2 {
    text-align: center;
    margin-bottom: 24px;
  }

  .form-item {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="tel"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }

    &.agreement {
      display: flex;
      align-items: center;

      input {
        margin-right: 8px;
      }

      a {
        color: #3498db;
      }
    }

    .error-message {
      color: #e74c3c;
      font-size: 12px;
      margin-top: 4px;
      display: block;
    }
  }

  .form-actions {
    margin-top: 24px;

    .btn-register {
      width: 100%;
      padding: 12px;
      background-color: #3498db;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;

      &:hover {
        background-color: #2980b9;
      }

      &:disabled {
        background-color: #95a5a6;
        cursor: not-allowed;
      }
    }
  }

  .form-links {
    text-align: center;
    margin-top: 16px;

    a {
      color: #3498db;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
