<template>
  <div class="search-container">
    <div class="search-header">
      <van-search
        v-model="searchQuery"
        placeholder="搜索照片、用户或标签"
        show-action
        @search="onSearch"
        @cancel="onCancel"
      />
      <div class="search-tabs">
        <van-tabs v-model:active="activeTab" animated>
          <van-tab title="全部" name="all"></van-tab>
          <van-tab title="照片" name="photos"></van-tab>
          <van-tab title="用户" name="users"></van-tab>
          <van-tab title="标签" name="tags"></van-tab>
        </van-tabs>
      </div>
    </div>

    <div class="search-content">
      <!-- 搜索历史 -->
      <div v-if="!searchQuery && !hasSearched" class="search-history">
        <div class="history-header">
          <h3>搜索历史</h3>
          <van-button type="text" size="small" @click="clearHistory">清空</van-button>
        </div>
        <div class="history-list">
          <div
            v-for="(item, index) in searchHistory"
            :key="index"
            class="history-item"
            @click="searchWithHistory(item)"
          >
            <van-icon name="clock-o" />
            <span>{{ item }}</span>
          </div>
        </div>
      </div>

      <!-- 热门搜索 -->
      <div v-if="!searchQuery && !hasSearched" class="hot-search">
        <h3>热门搜索</h3>
        <div class="tag-list">
          <van-tag
            v-for="(tag, index) in hotTags"
            :key="index"
            class="tag-item"
            @click="searchWithTag(tag)"
          >
            {{ tag }}
          </van-tag>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="hasSearched" class="search-results">
        <!-- 加载中 -->
        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#1989fa" />
        </div>

        <!-- 无结果 -->
        <div v-else-if="activeResults.length === 0" class="empty-results">
          <van-empty description="暂无搜索结果" />
        </div>

        <!-- 照片结果 -->
        <div v-else-if="activeTab === 'all' || activeTab === 'photos'" class="photo-results">
          <div class="photo-grid">
            <div
              v-for="photo in photoResults"
              :key="photo.id"
              class="photo-item"
              @click="viewPhoto(photo.id)"
            >
              <van-image :src="photo.thumbnailUrl" fit="cover" lazy-load />
              <div class="photo-info">
                <div class="photo-title">{{ photo.title }}</div>
                <div class="photo-user">
                  <van-image
                    round
                    width="20"
                    height="20"
                    :src="photo.user.avatar"
                    fit="cover"
                  />
                  <span>{{ photo.user.nickname }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户结果 -->
        <div v-else-if="activeTab === 'users'" class="user-results">
          <div
            v-for="user in userResults"
            :key="user.id"
            class="user-item"
            @click="viewUser(user.id)"
          >
            <van-image
              round
              width="50"
              height="50"
              :src="user.avatar"
              fit="cover"
            />
            <div class="user-info">
              <div class="user-name">{{ user.nickname }}</div>
              <div class="user-bio">{{ user.bio }}</div>
            </div>
            <van-button
              size="small"
              :type="user.isFollowing ? 'default' : 'primary'"
              @click.stop="toggleFollow(user)"
            >
              {{ user.isFollowing ? '已关注' : '关注' }}
            </van-button>
          </div>
        </div>

        <!-- 标签结果 -->
        <div v-else-if="activeTab === 'tags'" class="tag-results">
          <div
            v-for="tag in tagResults"
            :key="tag.id"
            class="tag-item"
            @click="searchWithTag(tag.name)"
          >
            <div class="tag-name"># {{ tag.name }}</div>
            <div class="tag-count">{{ tag.count }} 张照片</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();

// 搜索状态
const searchQuery = ref('');
const activeTab = ref('all');
const loading = ref(false);
const hasSearched = ref(false);

// 搜索历史
const searchHistory = ref<string[]>([
  '风景',
  '美食',
  '旅行',
  '人像',
  '城市'
]);

// 热门标签
const hotTags = ref<string[]>([
  '风景',
  '美食',
  '旅行',
  '人像',
  '城市',
  '建筑',
  '动物',
  '植物',
  '黑白',
  '夜景'
]);

// 搜索结果
const photoResults = ref<any[]>([]);
const userResults = ref<any[]>([]);
const tagResults = ref<any[]>([]);

// 当前标签页的结果
const activeResults = computed(() => {
  switch (activeTab.value) {
    case 'photos':
      return photoResults.value;
    case 'users':
      return userResults.value;
    case 'tags':
      return tagResults.value;
    default:
      return [...photoResults.value, ...userResults.value, ...tagResults.value];
  }
});

// 监听标签页变化
watch(activeTab, () => {
  if (hasSearched.value) {
    search();
  }
});

// 搜索
const onSearch = () => {
  if (!searchQuery.value.trim()) {
    showToast('请输入搜索内容');
    return;
  }

  // 添加到搜索历史
  if (!searchHistory.value.includes(searchQuery.value)) {
    searchHistory.value.unshift(searchQuery.value);
    if (searchHistory.value.length > 10) {
      searchHistory.value.pop();
    }
  }

  search();
};

// 执行搜索
const search = async () => {
  loading.value = true;
  hasSearched.value = true;

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 根据标签页类型获取不同结果
    if (activeTab.value === 'all' || activeTab.value === 'photos') {
      photoResults.value = Array.from({ length: 9 }, (_, i) => ({
        id: i + 1,
        title: `${searchQuery.value} 相关照片 ${i + 1}`,
        thumbnailUrl: `https://picsum.photos/300/300?random=${i + 1}`,
        user: {
          id: i + 1,
          nickname: `用户${i + 1}`,
          avatar: `https://randomuser.me/api/portraits/men/${i + 1}.jpg`
        }
      }));
    }

    if (activeTab.value === 'all' || activeTab.value === 'users') {
      userResults.value = Array.from({ length: 5 }, (_, i) => ({
        id: i + 1,
        nickname: `${searchQuery.value}用户${i + 1}`,
        avatar: `https://randomuser.me/api/portraits/men/${i + 10}.jpg`,
        bio: '热爱摄影，记录生活中的美好瞬间',
        isFollowing: Math.random() > 0.5
      }));
    }

    if (activeTab.value === 'all' || activeTab.value === 'tags') {
      tagResults.value = Array.from({ length: 3 }, (_, i) => ({
        id: i + 1,
        name: `${searchQuery.value}${i + 1}`,
        count: Math.floor(Math.random() * 1000)
      }));
    }
  } catch (error) {
    console.error('搜索失败', error);
    showToast('搜索失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 取消搜索
const onCancel = () => {
  searchQuery.value = '';
  hasSearched.value = false;
};

// 使用历史记录搜索
const searchWithHistory = (query: string) => {
  searchQuery.value = query;
  onSearch();
};

// 使用标签搜索
const searchWithTag = (tag: string) => {
  searchQuery.value = tag;
  onSearch();
};

// 清空搜索历史
const clearHistory = () => {
  searchHistory.value = [];
};

// 查看照片详情
const viewPhoto = (id: number) => {
  router.push(`/photo/${id}`);
};

// 查看用户详情
const viewUser = (id: number) => {
  router.push(`/user/${id}`);
};

// 关注/取消关注用户
const toggleFollow = (user: any) => {
  if (!userStore.isLoggedIn) {
    router.push('/auth/login');
    return;
  }

  user.isFollowing = !user.isFollowing;
  showToast(user.isFollowing ? '已关注' : '已取消关注');
};
</script>

<style lang="scss" scoped>
.search-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: white;
}

.search-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.search-history,
.hot-search {
  margin-bottom: 24px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 16px;
  font-size: 14px;
  cursor: pointer;

  .van-icon {
    margin-right: 4px;
    font-size: 14px;
    color: #999;
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  padding: 6px 12px;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.empty-results {
  padding: 40px 0;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.photo-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.photo-info {
  padding: 8px;
}

.photo-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-user {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;

  span {
    margin-left: 4px;
  }
}

.user-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.user-info {
  flex: 1;
  margin: 0 12px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.user-bio {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.tag-name {
  font-size: 16px;
  font-weight: 500;
}

.tag-count {
  font-size: 14px;
  color: #666;
}

@media (min-width: 768px) {
  .photo-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
