package com.phototagmoment.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
// 使用完全限定名称避免命名冲突
// import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import com.phototagmoment.service.SystemConfigService;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 第三方登录配置类
 * 使用JustAuth实现统一的第三方登录功能
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "auth")
public class AuthConfig {

    /**
     * 是否启用第三方登录
     */
    private boolean enabled = true;

    /**
     * QQ登录配置
     */
    private QQProperties qq = new QQProperties();

    /**
     * 微信登录配置
     */
    private WechatProperties wechat = new WechatProperties();

    /**
     * 状态缓存
     */
    private final AuthStateCache stateCache;

    /**
     * 系统配置服务
     */
    @Autowired
    private SystemConfigService configService;

    /**
     * 构造函数，使用内存缓存
     */
    public AuthConfig() {
        // 使用内存缓存实现状态缓存
        this.stateCache = new AuthStateCache() {
            private final Map<String, String> cache = new ConcurrentHashMap<>();
            private final Map<String, Long> expireMap = new ConcurrentHashMap<>();
            private final long DEFAULT_TIMEOUT = 3 * 60; // 3分钟超时

            @Override
            public void cache(String key, String value) {
                cache(key, value, DEFAULT_TIMEOUT);
            }

            @Override
            public void cache(String key, String value, long timeout) {
                cache.put(key, value);
                expireMap.put(key, System.currentTimeMillis() + timeout * 1000);
            }

            @Override
            public String get(String key) {
                if (!containsKey(key)) {
                    return null;
                }
                return cache.get(key);
            }

            @Override
            public boolean containsKey(String key) {
                if (!cache.containsKey(key)) {
                    return false;
                }

                Long expireTime = expireMap.get(key);
                if (expireTime == null) {
                    return true;
                }

                if (System.currentTimeMillis() > expireTime) {
                    cache.remove(key);
                    expireMap.remove(key);
                    return false;
                }

                return true;
            }
        };
    }

    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        log.info("初始化第三方登录配置");

        // 从数据库读取配置
        try {
            // 读取基本配置
            this.enabled = configService.getBooleanValue("auth.enabled", true);

            // 读取QQ登录配置
            this.qq.setEnabled(configService.getBooleanValue("auth.qq.enabled", false));
            this.qq.setClientId(configService.getConfigValue("auth.qq.client-id", ""));
            this.qq.setClientSecret(configService.getConfigValue("auth.qq.client-secret", ""));
            this.qq.setRedirectUri(configService.getConfigValue("auth.qq.redirect-uri", ""));

            // 读取微信登录配置
            this.wechat.setEnabled(configService.getBooleanValue("auth.wechat.enabled", false));
            this.wechat.setClientId(configService.getConfigValue("auth.wechat.client-id", ""));
            this.wechat.setClientSecret(configService.getConfigValue("auth.wechat.client-secret", ""));
            this.wechat.setRedirectUri(configService.getConfigValue("auth.wechat.redirect-uri", ""));

            log.info("从数据库读取第三方登录配置成功");
        } catch (Exception e) {
            log.error("从数据库读取第三方登录配置失败，将使用默认配置: {}", e.getMessage());
        }

        log.info("QQ登录配置: enabled={}, clientId={}, redirectUri={}",
                qq.isEnabled(), qq.getClientId(), qq.getRedirectUri());
        log.info("微信登录配置: enabled={}, clientId={}, redirectUri={}",
                wechat.isEnabled(), wechat.getClientId(), wechat.getRedirectUri());
    }

    /**
     * 创建QQ登录请求对象
     */
    @Bean
    public AuthRequest qqAuthRequest() {
        // 从数据库重新读取配置，确保最新
        boolean qqEnabled = configService.getBooleanValue("auth.qq.enabled", false);
        String qqClientId = configService.getConfigValue("auth.qq.client-id", "");
        String qqClientSecret = configService.getConfigValue("auth.qq.client-secret", "");
        String qqRedirectUri = configService.getConfigValue("auth.qq.redirect-uri", "");

        boolean authEnabled = configService.getBooleanValue("auth.enabled", true);

        if (!authEnabled || !qqEnabled || !StringUtils.hasText(qqClientId) || !StringUtils.hasText(qqClientSecret)) {
            log.warn("QQ登录未启用或配置不完整");
            return null;
        }

        return new AuthQqRequest(me.zhyd.oauth.config.AuthConfig.builder()
                .clientId(qqClientId)
                .clientSecret(qqClientSecret)
                .redirectUri(qqRedirectUri)
                .build(), stateCache);
    }

    /**
     * 创建微信登录请求对象
     */
    @Bean
    public AuthRequest wechatAuthRequest() {
        // 从数据库重新读取配置，确保最新
        boolean wechatEnabled = configService.getBooleanValue("auth.wechat.enabled", false);
        String wechatClientId = configService.getConfigValue("auth.wechat.client-id", "");
        String wechatClientSecret = configService.getConfigValue("auth.wechat.client-secret", "");
        String wechatRedirectUri = configService.getConfigValue("auth.wechat.redirect-uri", "");

        boolean authEnabled = configService.getBooleanValue("auth.enabled", true);

        if (!authEnabled || !wechatEnabled || !StringUtils.hasText(wechatClientId) || !StringUtils.hasText(wechatClientSecret)) {
            log.warn("微信登录未启用或配置不完整");
            return null;
        }

        return new AuthWeChatOpenRequest(me.zhyd.oauth.config.AuthConfig.builder()
                .clientId(wechatClientId)
                .clientSecret(wechatClientSecret)
                .redirectUri(wechatRedirectUri)
                .build(), stateCache);
    }

    /**
     * QQ登录配置属性
     */
    @Data
    public static class QQProperties {
        /**
         * 是否启用QQ登录
         */
        private boolean enabled = false;

        /**
         * 应用ID
         */
        private String clientId;

        /**
         * 应用密钥
         */
        private String clientSecret;

        /**
         * 回调地址
         */
        private String redirectUri;
    }

    /**
     * 微信登录配置属性
     */
    @Data
    public static class WechatProperties {
        /**
         * 是否启用微信登录
         */
        private boolean enabled = false;

        /**
         * 应用ID
         */
        private String clientId;

        /**
         * 应用密钥
         */
        private String clientSecret;

        /**
         * 回调地址
         */
        private String redirectUri;
    }
}
