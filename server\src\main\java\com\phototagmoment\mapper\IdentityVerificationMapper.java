package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.IdentityVerification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 实名认证Mapper接口
 */
@Mapper
public interface IdentityVerificationMapper extends BaseMapper<IdentityVerification> {

    /**
     * 根据用户ID查询最新的实名认证记录
     *
     * @param userId 用户ID
     * @return 实名认证记录
     */
    @Select("SELECT * FROM ptm_identity_verification WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC LIMIT 1")
    IdentityVerification selectLatestByUserId(@Param("userId") Long userId);

    /**
     * 更新实名认证状态
     *
     * @param id     认证记录ID
     * @param status 认证状态
     * @return 影响行数
     */
    @Update("UPDATE ptm_identity_verification SET status = #{status}, updated_at = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新实名认证状态和失败原因
     *
     * @param id         认证记录ID
     * @param status     认证状态
     * @param failReason 失败原因
     * @return 影响行数
     */
    @Update("UPDATE ptm_identity_verification SET status = #{status}, fail_reason = #{failReason}, updated_at = NOW() WHERE id = #{id}")
    int updateStatusAndFailReason(@Param("id") Long id, @Param("status") Integer status, @Param("failReason") String failReason);

    /**
     * 更新实名认证状态和认证时间
     *
     * @param id     认证记录ID
     * @param status 认证状态
     * @return 影响行数
     */
    @Update("UPDATE ptm_identity_verification SET status = #{status}, verified_at = NOW(), updated_at = NOW() WHERE id = #{id}")
    int updateStatusAndVerifiedAt(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 检查用户是否已实名认证
     *
     * @param userId 用户ID
     * @return 认证记录数
     */
    @Select("SELECT COUNT(*) FROM ptm_identity_verification WHERE user_id = #{userId} AND status = 1 AND is_deleted = 0")
    int countVerifiedByUserId(@Param("userId") Long userId);
}
