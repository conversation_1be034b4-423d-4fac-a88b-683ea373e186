@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-primary text-white hover:bg-opacity-90;
  }
  
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-opacity-90;
  }
  
  .btn-danger {
    @apply bg-danger text-white hover:bg-opacity-90;
  }
  
  .btn-outline {
    @apply border border-gray-300 text-gray-700 hover:bg-gray-100;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-card p-4;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent;
  }
  
  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary bg-opacity-10 text-primary;
  }
  
  .badge-secondary {
    @apply bg-secondary bg-opacity-10 text-secondary;
  }
  
  .badge-danger {
    @apply bg-danger bg-opacity-10 text-danger;
  }
  
  .badge-warning {
    @apply bg-warning bg-opacity-10 text-warning;
  }
  
  .badge-info {
    @apply bg-info bg-opacity-10 text-info;
  }
}
