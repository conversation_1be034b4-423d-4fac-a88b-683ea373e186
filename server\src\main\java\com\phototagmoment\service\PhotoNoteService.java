package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.PhotoNotePublishDTO;
import com.phototagmoment.dto.TagSearchResultDTO;
import com.phototagmoment.entity.PhotoNote;
import com.phototagmoment.controller.admin.AdminPhotoNoteController.PhotoNoteStatsDTO;

import java.util.List;

/**
 * 照片笔记服务接口
 */
public interface PhotoNoteService extends IService<PhotoNote> {

    /**
     * 发布照片笔记
     *
     * @param publishDTO 发布请求DTO
     * @param userId 用户ID
     * @return 照片笔记ID
     */
    Long publishPhotoNote(PhotoNotePublishDTO publishDTO, Long userId);

    /**
     * 分页查询照片笔记列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID（可选，查询指定用户的笔记）
     * @param currentUserId 当前用户ID
     * @param status 状态筛选
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> getPhotoNoteList(Integer page, Integer size, Long userId, Long currentUserId, Integer status);

    /**
     * 管理端分页查询照片笔记列表（无可见性限制）
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID（可选，查询指定用户的笔记）
     * @param status 状态筛选
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> getAdminPhotoNoteList(Integer page, Integer size, Long userId, Integer status);

    /**
     * 查询照片笔记详情
     *
     * @param noteId 照片笔记ID
     * @param currentUserId 当前用户ID
     * @return 照片笔记详情
     */
    PhotoNoteDTO getPhotoNoteDetail(Long noteId, Long currentUserId);

    /**
     * 根据标签搜索照片笔记
     *
     * @param tagName 标签名称
     * @param page 页码
     * @param size 每页大小
     * @param sortType 排序类型：hot-热度排序，time-时间排序
     * @param currentUserId 当前用户ID
     * @return 标签搜索结果
     */
    TagSearchResultDTO searchPhotoNotesByTag(String tagName, Integer page, Integer size, String sortType, Long currentUserId);

    /**
     * 查询用户的照片笔记
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID
     * @param visibility 可见性筛选
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> getUserPhotoNotes(Long userId, Integer page, Integer size, Long currentUserId, Integer visibility);

    /**
     * 点赞照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean likePhotoNote(Long noteId, Long userId);

    /**
     * 取消点赞照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unlikePhotoNote(Long noteId, Long userId);

    /**
     * 收藏照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean collectPhotoNote(Long noteId, Long userId);

    /**
     * 取消收藏照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean uncollectPhotoNote(Long noteId, Long userId);

    /**
     * 增加照片笔记浏览量
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID（可选，用于去重）
     * @return 是否成功
     */
    boolean incrementViewCount(Long noteId, Long userId);

    /**
     * 删除照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deletePhotoNote(Long noteId, Long userId);

    /**
     * 查询热门照片笔记
     *
     * @param page 页码
     * @param size 每页大小
     * @param days 最近天数
     * @param currentUserId 当前用户ID
     * @return 热门照片笔记列表
     */
    IPage<PhotoNoteDTO> getHotPhotoNotes(Integer page, Integer size, Integer days, Long currentUserId);

    /**
     * 查询推荐照片笔记
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @return 推荐照片笔记列表
     */
    IPage<PhotoNoteDTO> getRecommendedPhotoNotes(Integer page, Integer size, Long userId);

    /**
     * 搜索照片笔记
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID
     * @return 搜索结果
     */
    IPage<PhotoNoteDTO> searchPhotoNotes(String keyword, Integer page, Integer size, Long currentUserId);

    /**
     * 审核照片笔记
     *
     * @param noteId 照片笔记ID
     * @param status 审核状态：1-通过，2-拒绝
     * @param rejectReason 拒绝原因（状态为拒绝时必填）
     * @return 是否成功
     */
    boolean auditPhotoNote(Long noteId, Integer status, String rejectReason);

    /**
     * 获取热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<String> getHotTags(Integer limit);

    /**
     * 根据关键词搜索标签
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 标签列表
     */
    List<String> searchTags(String keyword, Integer limit);

    /**
     * 处理正文内容中的Tag和@用户
     *
     * @param content 原始内容
     * @return 处理后的内容
     */
    String processContent(String content);

    /**
     * 从内容中提取Tag列表
     *
     * @param content 内容
     * @return Tag列表
     */
    List<String> extractTags(String content);

    /**
     * 从内容中提取@用户列表
     *
     * @param content 内容
     * @return @用户昵称列表
     */
    List<String> extractMentions(String content);

    /**
     * 获取关注用户的照片笔记
     *
     * @param page 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> getFollowingPhotoNotes(Integer page, Integer size, Long currentUserId, Long lastId);

    /**
     * 获取最新照片笔记
     *
     * @param page 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> getLatestPhotoNotes(Integer page, Integer size, Long currentUserId, Long lastId);

    /**
     * 获取热门照片笔记
     *
     * @param page 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> getHotPhotoNotes(Integer page, Integer size, Long currentUserId, Long lastId);



    /**
     * 举报照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 举报用户ID
     * @param reason 举报原因
     * @param description 详细描述
     * @return 是否成功
     */
    boolean reportPhotoNote(Long noteId, Long userId, String reason, String description);

    /**
     * 获取照片笔记统计信息
     *
     * @return 统计信息
     */
    PhotoNoteStatsDTO getPhotoNoteStats();
}
