<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '210px'" class="sidebar-container">
        <div class="logo-container">
          <div class="logo-text" v-if="!isCollapse">PhotoTagMoment</div>
          <div class="logo-text-mini" v-else>PTM</div>
        </div>
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            :collapse="isCollapse"
            :unique-opened="true"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
            router
          >
            <sidebar-item
              v-for="route in routes"
              :key="route.path"
              :item="route"
              :base-path="route.path"
            />
          </el-menu>
        </el-scrollbar>
      </el-aside>

      <el-container>
        <!-- 头部 -->
        <el-header class="header-container">
          <div class="header-left">
            <el-icon class="collapse-btn" @click="toggleSidebar">
              <component :is="isCollapse ? 'Expand' : 'Fold'" />
            </el-icon>
            <breadcrumb />
          </div>
          <div class="header-right">
            <el-dropdown trigger="click">
              <div class="avatar-container">
                <el-avatar :size="32" :src="userInfo?.avatar || ''" />
                <span class="username">{{ userInfo?.nickname || userInfo?.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleProfile">个人信息</el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主要内容区 -->
        <el-main class="main-container">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '../store/user'
import SidebarItem from '../components/SidebarItem.vue'
import Breadcrumb from '../components/Breadcrumb.vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 侧边栏折叠状态
const isCollapse = ref(false)

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 路由菜单
const routes = computed(() => {
  return router.options.routes.filter(route => {
    return !route.meta?.hidden
  })
})

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 处理查看个人信息
const handleProfile = () => {
  // 跳转到个人信息页面
  router.push('/profile')
}

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await userStore.logoutAction()
    router.push('/login')
  }).catch(() => {})
}

// 组件挂载时获取用户信息
onMounted(async () => {
  if (!userStore.userInfo) {
    await userStore.fetchUserInfo()
  }
})
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100%;

  .el-container {
    min-height: 100vh;
  }

  .sidebar-container {
    transition: width 0.3s;
    background-color: #304156;
    overflow: hidden;

    .logo-container {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #263445;

      .logo-text {
        color: #fff;
        font-size: 18px;
        font-weight: bold;
      }

      .logo-text-mini {
        color: #fff;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .header-left {
      display: flex;
      align-items: center;

      .collapse-btn {
        font-size: 20px;
        cursor: pointer;
        margin-right: 20px;
      }
    }

    .header-right {
      .avatar-container {
        display: flex;
        align-items: center;
        cursor: pointer;

        .username {
          margin: 0 8px;
        }
      }
    }
  }

  .main-container {
    padding: 20px;
    background-color: #f0f2f5;
    overflow-y: auto;
  }
}
</style>
