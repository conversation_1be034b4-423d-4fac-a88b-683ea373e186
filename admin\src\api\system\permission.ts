import request from '@/utils/request'

/**
 * 获取权限列表
 */
export function getPermissionList() {
  return request({
    url: '/admin/system/permission/list',
    method: 'get'
  })
}

/**
 * 获取权限树
 */
export function getPermissionTree() {
  return request({
    url: '/admin/system/permission/tree',
    method: 'get'
  })
}

/**
 * 获取权限详情
 * @param id 权限ID
 */
export function getPermissionDetail(id: number) {
  return request({
    url: `/admin/system/permission/${id}`,
    method: 'get'
  })
}

/**
 * 创建权限
 * @param data 权限信息
 */
export function createPermission(data: any) {
  return request({
    url: '/admin/system/permission',
    method: 'post',
    data
  })
}

/**
 * 更新权限
 * @param id 权限ID
 * @param data 权限信息
 */
export function updatePermission(id: number, data: any) {
  return request({
    url: `/admin/system/permission/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除权限
 * @param id 权限ID
 */
export function deletePermission(id: number) {
  return request({
    url: `/admin/system/permission/${id}`,
    method: 'delete'
  })
}

/**
 * 更新权限状态
 * @param id 权限ID
 * @param status 状态：0-禁用，1-启用
 */
export function updatePermissionStatus(id: number, status: number) {
  return request({
    url: `/admin/system/permission/${id}/status`,
    method: 'put',
    params: { status }
  })
}
