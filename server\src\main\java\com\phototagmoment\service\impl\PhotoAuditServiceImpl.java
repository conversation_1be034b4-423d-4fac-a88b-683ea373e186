package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.entity.PhotoAudit;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.PhotoAuditMapper;
import com.phototagmoment.mapper.PhotoMapper;
import com.phototagmoment.service.ContentModerationService;
import com.phototagmoment.service.NotificationService;
import com.phototagmoment.service.PhotoAuditService;
import com.phototagmoment.service.PhotoService;
import com.phototagmoment.task.PhotoAuditTask;
import com.phototagmoment.vo.PhotoAuditVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 照片审核服务实现类
 */
@Slf4j
@Service
public class PhotoAuditServiceImpl extends ServiceImpl<PhotoAuditMapper, PhotoAudit> implements PhotoAuditService {

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private PhotoService photoService;

    @Autowired
    private ContentModerationService contentModerationService;

    @Autowired
    private PhotoAuditTask photoAuditTask;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private com.phototagmoment.service.SystemConfigService configService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAuditRecord(Long photoId, Integer auditType, Integer auditResult, String rejectReason, Long auditorId) {
        // 检查照片是否存在
        Photo photo = photoMapper.selectById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 创建审核记录
        PhotoAudit photoAudit = new PhotoAudit();
        photoAudit.setPhotoId(photoId);
        photoAudit.setAuditType(auditType);
        photoAudit.setAuditResult(auditResult);
        photoAudit.setRejectReason(rejectReason);
        photoAudit.setAuditorId(auditorId);
        photoAudit.setAuditTime(LocalDateTime.now());

        // 保存审核记录
        this.save(photoAudit);

        // 更新照片状态
        if (auditResult == 1) { // 通过
            photo.setStatus(1); // 正常
            photo.setRejectReason(null);
        } else if (auditResult == 2) { // 拒绝
            photo.setStatus(2); // 审核拒绝
            photo.setRejectReason(rejectReason);
        }
        photoMapper.updateById(photo);

        return photoAudit.getId();
    }

    @Override
    public boolean submitForAutoAudit(Long photoId) {
        // 异步提交自动审核任务
        photoAuditTask.submitAutoAuditTask(photoId);
        return true;
    }

    @Override
    public boolean submitForManualAudit(Long photoId) {
        // 检查照片是否存在
        // 使用 LambdaQueryWrapper 指定要查询的字段，避免查询 taken_time 字段
        LambdaQueryWrapper<Photo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Photo::getId, photoId)
                .select(Photo::getId, Photo::getUserId, Photo::getTitle, Photo::getDescription,
                        Photo::getUrl, Photo::getThumbnailUrl, Photo::getStoragePath,
                        Photo::getFileType, Photo::getWidth, Photo::getHeight, Photo::getLocation,
                        Photo::getVisibility, Photo::getAllowComment, Photo::getAllowDownload,
                        Photo::getLikeCount, Photo::getCommentCount,
                        Photo::getViewCount, Photo::getStatus, Photo::getRejectReason,
                        Photo::getIsDeleted, Photo::getCreatedAt, Photo::getUpdatedAt);
        Photo photo = photoMapper.selectOne(queryWrapper);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 更新照片状态为待审核
        photo.setStatus(0); // 待审核
        photoMapper.updateById(photo);

        // 创建待审核记录
        PhotoAudit photoAudit = new PhotoAudit();
        photoAudit.setPhotoId(photoId);
        photoAudit.setAuditType(1); // 人工审核
        photoAudit.setAuditResult(0); // 待审核
        photoAudit.setAuditTime(LocalDateTime.now());
        this.save(photoAudit);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditPhoto(Long photoId, boolean approved, String rejectReason, Long auditorId) {
        // 检查照片是否存在
        Photo photo = photoMapper.selectById(photoId);
        if (photo == null) {
            throw new BusinessException("照片不存在或已删除");
        }

        // 更新照片状态
        if (approved) {
            photo.setStatus(1); // 正常
            photo.setRejectReason(null);
        } else {
            photo.setStatus(2); // 审核拒绝
            photo.setRejectReason(rejectReason);
        }
        photoMapper.updateById(photo);

        // 创建审核记录
        PhotoAudit photoAudit = new PhotoAudit();
        photoAudit.setPhotoId(photoId);
        photoAudit.setAuditType(1); // 人工审核
        photoAudit.setAuditResult(approved ? 1 : 2); // 1-通过, 2-拒绝
        photoAudit.setRejectReason(approved ? null : rejectReason);
        photoAudit.setAuditorId(auditorId);
        photoAudit.setAuditTime(LocalDateTime.now());
        this.save(photoAudit);

        // 发送通知给用户
        String notificationContent;
        if (approved) {
            notificationContent = "您的照片《" + photo.getTitle() + "》已通过审核，现已公开显示。";
        } else {
            notificationContent = "您的照片《" + photo.getTitle() + "》未通过审核，原因：" + rejectReason;
        }

        notificationService.createSystemNotification(photo.getUserId(), notificationContent);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAuditPhotos(List<Long> photoIds, boolean approved, String rejectReason, Long auditorId) {
        for (Long photoId : photoIds) {
            try {
                auditPhoto(photoId, approved, rejectReason, auditorId);
            } catch (Exception e) {
                log.error("批量审核照片失败，照片ID: " + photoId, e);
                // 继续处理其他照片
            }
        }
        return true;
    }

    @Override
    public IPage<PhotoAuditVO> getPendingAuditPhotos(int page, int size) {
        // 创建分页对象
        Page<Photo> photoPage = new Page<>(page, size);

        // 查询待审核照片
        LambdaQueryWrapper<Photo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Photo::getStatus, 0); // 待审核
        queryWrapper.orderByDesc(Photo::getCreatedAt);
        IPage<Photo> photoIPage = photoMapper.selectPage(photoPage, queryWrapper);

        // 转换为VO
        @SuppressWarnings("unchecked")
        IPage<PhotoAuditVO> voPage = photoIPage.convert(photo -> {
            PhotoAuditVO vo = new PhotoAuditVO();
            vo.setPhotoId(photo.getId());
            vo.setTitle(photo.getTitle());
            vo.setDescription(photo.getDescription());
            vo.setUrl(photo.getUrl());
            vo.setThumbnailUrl(photo.getThumbnailUrl());
            vo.setUserId(photo.getUserId());
            vo.setStatus(photo.getStatus());
            vo.setRejectReason(photo.getRejectReason());
            vo.setCreatedAt(photo.getCreatedAt());

            // 查询最近一次审核记录
            LambdaQueryWrapper<PhotoAudit> auditWrapper = new LambdaQueryWrapper<>();
            auditWrapper.eq(PhotoAudit::getPhotoId, photo.getId());
            auditWrapper.orderByDesc(PhotoAudit::getAuditTime);
            auditWrapper.last("LIMIT 1");
            PhotoAudit lastAudit = this.getOne(auditWrapper);
            if (lastAudit != null) {
                vo.setLastAuditTime(lastAudit.getAuditTime());
                vo.setLastAuditType(lastAudit.getAuditType());
                vo.setLastAuditResult(lastAudit.getAuditResult());
            }

            return vo;
        });

        return voPage;
    }

    @Override
    public List<PhotoAudit> getPhotoAuditHistory(Long photoId) {
        // 查询照片审核历史
        LambdaQueryWrapper<PhotoAudit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhotoAudit::getPhotoId, photoId);
        queryWrapper.orderByDesc(PhotoAudit::getAuditTime);
        return this.list(queryWrapper);
    }
}
