package com.phototagmoment.config;

import com.phototagmoment.entity.Admin;
import com.phototagmoment.entity.AdminRole;
import com.phototagmoment.mapper.AdminMapper;
import com.phototagmoment.mapper.AdminRoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.DependsOn;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 管理员初始化器
 * 用于在应用启动时创建默认管理员账户
 */
@Slf4j
@Component
@DependsOn("flyway")
public class AdminInitializer implements CommandLineRunner {

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private AdminRoleMapper adminRoleMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private Flyway flyway;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(String... args) {
        try {
            // 确保 Flyway 迁移已完成
            log.info("确保 Flyway 迁移已完成");

            // 检查数据库表是否存在
            boolean tableExists = isTableExists("ptm_admin");
            if (!tableExists) {
                log.warn("管理员表不存在，请先执行数据库迁移脚本");
                return;
            }

            log.info("管理员表已存在，继续初始化");

            // 检查是否已存在管理员
            Admin existingAdmin = adminMapper.selectByUsername("admin");
            if (existingAdmin != null) {
                log.info("默认管理员账户已存在，跳过初始化");
                return;
            }

            // 创建超级管理员角色
            AdminRole superAdminRole = adminRoleMapper.selectByCode("super_admin");
            if (superAdminRole == null) {
                superAdminRole = new AdminRole();
                superAdminRole.setName("超级管理员");
                superAdminRole.setCode("super_admin");
                superAdminRole.setDescription("系统超级管理员，拥有所有权限");
                superAdminRole.setStatus(1);
                superAdminRole.setIsSystem(1);
                superAdminRole.setCreatedAt(LocalDateTime.now());
                superAdminRole.setUpdatedAt(LocalDateTime.now());
                adminRoleMapper.insert(superAdminRole);
                log.info("创建超级管理员角色成功");
            }

            // 创建默认管理员账户
            Admin admin = new Admin();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("123456"));
            admin.setName("系统管理员");
            admin.setEmail("<EMAIL>");
            admin.setPhone("13800138000");
            admin.setStatus(1);
            admin.setRoleId(superAdminRole.getId());
            admin.setCreatedAt(LocalDateTime.now());
            admin.setUpdatedAt(LocalDateTime.now());
            adminMapper.insert(admin);

            log.info("创建默认管理员账户成功，用户名：admin，密码：123456");
        } catch (Exception e) {
            log.error("初始化管理员账户失败", e);
        }
    }

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 是否存在
     */
    private boolean isTableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表是否存在失败", e);
            return false;
        }
    }
}
