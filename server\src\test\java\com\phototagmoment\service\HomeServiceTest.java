package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.service.impl.PhotoNoteServiceImpl;
import com.phototagmoment.service.impl.RecommendationServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * 首页服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class HomeServiceTest {

    @Autowired
    private PhotoNoteService photoNoteService;

    @Autowired
    private RecommendationService recommendationService;

    @Test
    public void testGetLatestPhotoNotes() {
        System.out.println("=== 测试获取最新照片笔记 ===");

        try {
            IPage<PhotoNoteDTO> result = photoNoteService.getLatestPhotoNotes(1, 10, 1L, null);

            System.out.println("总记录数: " + result.getTotal());
            System.out.println("当前页记录数: " + result.getRecords().size());

            result.getRecords().forEach(note -> {
                System.out.println("ID: " + note.getId() + ", 标题: " + note.getTitle() +
                                 ", 用户: " + note.getNickname() + ", 创建时间: " + note.getCreatedAt());
            });

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetHotPhotoNotes() {
        System.out.println("=== 测试获取热门照片笔记 ===");

        try {
            IPage<PhotoNoteDTO> result = photoNoteService.getHotPhotoNotes(1, 10, 1L, null);

            System.out.println("总记录数: " + result.getTotal());
            System.out.println("当前页记录数: " + result.getRecords().size());

            result.getRecords().forEach(note -> {
                System.out.println("ID: " + note.getId() + ", 标题: " + note.getTitle() +
                                 ", 点赞数: " + note.getLikeCount() + ", 浏览数: " + note.getViewCount());
            });

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetRecommendedPhotoNotes() {
        System.out.println("=== 测试获取推荐照片笔记 ===");

        try {
            IPage<PhotoNoteDTO> result = recommendationService.getRecommendedPhotoNotes(1, 10, 1L, null);

            System.out.println("总记录数: " + result.getTotal());
            System.out.println("当前页记录数: " + result.getRecords().size());

            result.getRecords().forEach(note -> {
                System.out.println("ID: " + note.getId() + ", 标题: " + note.getTitle() +
                                 ", 用户: " + note.getNickname());
            });

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testUpdateUserInterestTags() {
        System.out.println("=== 测试更新用户兴趣标签 ===");

        try {
            List<String> tags = Arrays.asList("风景", "人像", "美食", "旅行", "摄影");
            boolean result = recommendationService.updateUserInterestTags(1L, tags);

            System.out.println("更新结果: " + result);

            // 验证更新结果
            List<String> userTags = recommendationService.getUserInterestTags(1L, 10);
            System.out.println("用户兴趣标签: " + userTags);

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testRecordUserBehavior() {
        System.out.println("=== 测试记录用户行为 ===");

        try {
            // 记录浏览行为
            recommendationService.recordUserBehavior(1L, 1L, "view", 0.1);
            System.out.println("记录浏览行为成功");

            // 记录点赞行为
            recommendationService.recordUserBehavior(1L, 1L, "like", 0.5);
            System.out.println("记录点赞行为成功");

            // 记录评论行为
            recommendationService.recordUserBehavior(1L, 1L, "comment", 0.8);
            System.out.println("记录评论行为成功");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testIncrementViewCount() {
        System.out.println("=== 测试增加浏览量 ===");

        try {
            boolean result = photoNoteService.incrementViewCount(1L, 1L);
            System.out.println("增加浏览量结果: " + result);

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testReportPhotoNote() {
        System.out.println("=== 测试举报照片笔记 ===");

        try {
            boolean result = photoNoteService.reportPhotoNote(1L, 2L, "不当内容", "包含不适宜内容");
            System.out.println("举报结果: " + result);

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetRecommendConfig() {
        System.out.println("=== 测试获取推荐配置 ===");

        try {
            RecommendationService.RecommendConfig config = recommendationService.getRecommendConfig();

            System.out.println("用户行为权重: " + config.getUserBehaviorWeight());
            System.out.println("关注关系权重: " + config.getFollowingWeight());
            System.out.println("标签兴趣权重: " + config.getTagInterestWeight());
            System.out.println("时间衰减权重: " + config.getTimeDecayWeight());
            System.out.println("多样性因子: " + config.getDiversityFactor());

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
