<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 引入Spring Boot默认的日志配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 定义日志文件的存储地址 -->
    <property name="LOG_HOME" value="logs"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器，用于过滤掉MyBatis的SQL结果集日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <marker>SQL_RESULT</marker>
            </evaluator>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
    </appender>
    
    <!-- 按照每天生成日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/server.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/server.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器，用于过滤掉MyBatis的SQL结果集日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <marker>SQL_RESULT</marker>
            </evaluator>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
    </appender>
    
    <!-- 专门用于记录敏感词相关的日志 -->
    <appender name="SENSITIVE_WORD_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/sensitive-word.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/sensitive-word.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 设置敏感词服务的日志级别和输出目标 -->
    <logger name="com.phototagmoment.service.impl.SensitiveWordServiceImpl" level="INFO" additivity="false">
        <appender-ref ref="SENSITIVE_WORD_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- 设置MyBatis的日志级别 -->
    <logger name="org.apache.ibatis" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="WARN"/>
    
    <!-- 设置SQL语句的日志级别 -->
    <logger name="java.sql.Connection" level="WARN"/>
    <logger name="java.sql.Statement" level="WARN"/>
    <logger name="java.sql.PreparedStatement" level="WARN"/>
    <logger name="java.sql.ResultSet" level="WARN"/>
    
    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
