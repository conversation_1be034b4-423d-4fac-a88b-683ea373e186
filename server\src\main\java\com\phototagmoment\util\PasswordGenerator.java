package com.phototagmoment.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码生成器
 * 用于生成BCrypt密码哈希
 */
public class PasswordGenerator {

    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "123456";
        String encodedPassword = encoder.encode(password);
        System.out.println("原始密码: " + password);
        System.out.println("加密后的密码: " + encodedPassword);
        System.out.println("验证密码: " + encoder.matches(password, encodedPassword));

        // 验证当前数据库中的密码是否匹配123456
        String dbPassword = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iAt2s7JS";
        boolean matches = encoder.matches(password, dbPassword);
        System.out.println("数据库密码是否匹配123456: " + matches);

        // 生成新的加密密码用于更新数据库
        String newEncodedPassword = encoder.encode("123456");
        System.out.println("新的加密密码: " + newEncodedPassword);
    }
}
