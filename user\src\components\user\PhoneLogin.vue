<template>
  <div class="phone-login">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          v-model="phone"
          name="phone"
          label="手机号"
          placeholder="请输入手机号"
          :rules="[{ required: true, message: '请输入手机号' }, { pattern: phonePattern, message: '手机号格式不正确' }]"
        />

        <van-field
          v-model="code"
          name="code"
          label="验证码"
          placeholder="请输入验证码"
          :rules="[{ required: true, message: '请输入验证码' }]"
        >
          <template #button>
            <van-button
              size="small"
              type="primary"
              :disabled="cooldown > 0"
              :loading="sending"
              @click="sendCode"
            >
              {{ cooldown > 0 ? `${cooldown}秒后重发` : '发送验证码' }}
            </van-button>
          </template>
        </van-field>
      </van-cell-group>

      <div class="mt-6">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="loading"
          size="large"
        >
          登录/注册
        </van-button>
        <div class="text-center text-xs text-gray-500 mt-2">
          未注册手机号将自动创建账号
        </div>
      </div>
    </van-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue';
import { showToast, showSuccessToast, showFailToast } from 'vant';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();

// 手机号正则表达式
const phonePattern = /^1[3-9]\d{9}$/;

// 表单数据
const phone = ref('');
const code = ref('');

// 状态
const loading = ref(false);
const sending = ref(false);
const cooldown = ref(0);
let timer: NodeJS.Timeout | null = null;

// 发送验证码
const sendCode = async () => {
  if (!phone.value) {
    showToast('请输入手机号');
    return;
  }

  if (!phonePattern.test(phone.value)) {
    showToast('手机号格式不正确');
    return;
  }

  if (cooldown.value > 0) {
    return;
  }

  try {
    sending.value = true;

    // 发送验证码请求
    const response = await fetch('/api/auth/phone/send-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: phone.value,
        type: 'login' // 登录/注册类型
      }),
    });

    const result = await response.json();

    if (result.code === 200) {
      showSuccessToast('验证码发送成功');
      startCooldown();
    } else {
      showFailToast(result.message || '验证码发送失败');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    showFailToast('发送验证码失败，请稍后再试');
  } finally {
    sending.value = false;
  }
};

// 开始倒计时
const startCooldown = () => {
  cooldown.value = 60;
  if (timer) clearInterval(timer);

  timer = setInterval(() => {
    cooldown.value--;
    if (cooldown.value <= 0 && timer) {
      clearInterval(timer);
    }
  }, 1000);
};

// 提交表单
const onSubmit = async () => {
  try {
    loading.value = true;

    // 登录/注册请求
    const response = await fetch('/api/auth/phone/login-register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: phone.value,
        code: code.value,
      }),
    });

    const result = await response.json();

    if (result.code === 200) {
      // 登录成功
      const { token, user } = result.data;

      // 保存登录信息
      userStore.setToken(token);
      userStore.setUser(user);

      showSuccessToast('登录成功');

      // 跳转到首页
      router.push('/');
    } else {
      showFailToast(result.message || '登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
    showFailToast('登录失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) clearInterval(timer);
});
</script>

<style scoped>
.phone-login {
  padding: 1rem;
}
</style>
