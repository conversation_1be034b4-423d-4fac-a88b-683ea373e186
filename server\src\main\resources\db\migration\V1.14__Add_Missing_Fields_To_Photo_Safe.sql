-- 添加所有缺少的字段到 ptm_photo 表（安全版本）
-- 检查 original_filename 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'original_filename';

-- 如果 original_filename 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN original_filename VARCHAR(255) COMMENT \'原始文件名\' AFTER storage_path',
    'SELECT \'original_filename column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 file_size 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'file_size';

-- 如果 file_size 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN file_size BIGINT COMMENT \'文件大小(字节)\' AFTER original_filename',
    'SELECT \'file_size column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 file_type 字段是否存在
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'ptm_photo' AND column_name = 'file_type';

-- 如果 file_type 字段不存在，则添加
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE ptm_photo ADD COLUMN file_type VARCHAR(50) COMMENT \'文件类型\' AFTER file_size',
    'SELECT \'file_type column already exists\' AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
