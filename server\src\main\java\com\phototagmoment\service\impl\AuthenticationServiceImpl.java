package com.phototagmoment.service.impl;

import com.phototagmoment.entity.User;
import com.phototagmoment.entity.UserAuth;
import com.phototagmoment.mapper.UserAuthMapper;
import com.phototagmoment.mapper.UserMapper;
import com.phototagmoment.service.AuthenticationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
public class AuthenticationServiceImpl implements AuthenticationService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserAuthMapper userAuthMapper;

    @Override
    public Authentication getCurrentAuthentication() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() ||
                "anonymousUser".equals(authentication.getPrincipal())) {
            log.debug("当前用户未认证或是匿名用户");
            return null;
        }
        return authentication;
    }

    @Override
    public User getCurrentUser() {
        String username = getCurrentUsername();
        if (username == null) {
            return null;
        }

        User user = userMapper.selectByUsername(username);
        if (user == null) {
            log.warn("用户不存在: {}", username);
            return null;
        }

        log.debug("成功获取当前用户: {}", username);
        return user;
    }

    @Override
    public Long getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getId() : null;
    }

    @Override
    public String getCurrentUsername() {
        Authentication authentication = getCurrentAuthentication();
        if (authentication == null) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            log.debug("从UserDetails获取用户名: {}", username);
            return username;
        } else if (principal instanceof String) {
            String username = (String) principal;
            log.debug("从String获取用户名: {}", username);
            return username;
        } else {
            log.debug("无法获取用户名，principal类型: {}", principal.getClass().getName());
            return null;
        }
    }

    @Override
    public boolean isAuthenticated() {
        return getCurrentAuthentication() != null;
    }

    @Override
    public boolean hasRole(String role) {
        Authentication authentication = getCurrentAuthentication();
        if (authentication == null) {
            return false;
        }

        return authentication.getAuthorities().stream()
                .anyMatch(authority -> authority.getAuthority().equals("ROLE_" + role));
    }

    @Override
    public UserAuth getUserAuth(Long userId, String identityType) {
        if (userId == null || identityType == null) {
            return null;
        }

        UserAuth userAuth = userAuthMapper.selectByUserIdAndType(userId, identityType);
        if (userAuth == null) {
            log.warn("用户认证信息不存在: userId={}, identityType={}", userId, identityType);
        }

        return userAuth;
    }

    @Override
    public Authentication setAuthentication(User user, String password) {
        if (user == null) {
            log.warn("无法设置认证信息: 用户为空");
            return null;
        }

        try {
            // 检查是否已经有认证信息
            Authentication existingAuth = SecurityContextHolder.getContext().getAuthentication();
            if (existingAuth != null && existingAuth.isAuthenticated() &&
                !(existingAuth.getPrincipal() instanceof String && "anonymousUser".equals(existingAuth.getPrincipal()))) {
                log.info("已存在认证信息，不再重复设置: {}", user.getUsername());
                return existingAuth;
            }

            // 检查当前线程是否正在处理登录请求
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String requestURI = request.getRequestURI();
                if (requestURI != null && requestURI.endsWith("/auth/login")) {
                    log.info("当前是登录请求，跳过设置认证信息到SecurityContext，避免循环依赖: {}", user.getUsername());
                    return null;
                }
            }

            // 创建认证对象 - 直接创建，不使用认证管理器
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            if (user.getIsAdmin() != null && user.getIsAdmin() == 1) {
                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            }

            // 创建用户详情
            org.springframework.security.core.userdetails.User userDetails = new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                "", // 密码为空，因为我们使用JWT认证
                user.getStatus() == 1, // 启用状态
                true, // 账号未过期
                true, // 凭证未过期
                true, // 账号未锁定
                authorities
            );

            // 创建认证令牌
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                    userDetails, null, authorities);

            // 设置请求详情
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            }

            // 设置认证信息到SecurityContext
            SecurityContextHolder.getContext().setAuthentication(authentication);
            log.info("设置认证信息到SecurityContext成功: {}", user.getUsername());

            return authentication;
        } catch (Exception e) {
            log.error("设置认证信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void clearAuthentication() {
        SecurityContextHolder.clearContext();
        log.info("已清除认证信息");
    }

    @Override
    public UserDetails createUserDetails(User user) {
        if (user == null) {
            return null;
        }

        try {
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            if (user.getIsAdmin() != null && user.getIsAdmin() == 1) {
                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            }

            return new org.springframework.security.core.userdetails.User(
                    user.getUsername(),
                    "", // 密码为空，因为我们使用JWT认证
                    user.getStatus() == 1, // 启用状态
                    true, // 账号未过期
                    true, // 凭证未过期
                    true, // 账号未锁定
                    authorities
            );
        } catch (Exception e) {
            log.error("创建用户详情失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
