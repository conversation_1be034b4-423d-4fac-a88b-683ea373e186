<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="form-inline">
        <el-form-item label="用户名">
          <el-input
            v-model="listQuery.username"
            placeholder="用户名"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="listQuery.phone"
            placeholder="手机号"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="状态" clearable>
            <el-option label="正常" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="实名认证">
          <el-select v-model="listQuery.isVerified" placeholder="实名认证" clearable>
            <el-option label="已认证" value="1" />
            <el-option label="未认证" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="头像" width="80">
        <template #default="scope">
          <el-avatar :size="40" :src="scope.row.avatar" />
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="用户名" width="120">
        <template #default="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="昵称" width="120">
        <template #default="scope">
          <span>{{ scope.row.nickname }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="手机号" width="120">
        <template #default="scope">
          <span>{{ scope.row.phone || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="邮箱" width="180">
        <template #default="scope">
          <span>{{ scope.row.email || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="实名认证" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isVerified ? 'success' : 'info'">
            {{ scope.row.isVerified ? '已认证' : '未认证' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'">
            {{ scope.row.status ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="注册时间" width="160">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作" fixed="right" width="200">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            size="small"
            :type="scope.row.status ? 'danger' : 'success'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="listQuery.page"
        v-model:page-size="listQuery.limit"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="用户详情"
      width="600px"
    >
      <div v-if="currentUser" class="user-detail">
        <div class="user-header">
          <el-avatar :size="80" :src="currentUser.avatar" />
          <div class="user-basic-info">
            <h3>{{ currentUser.nickname || currentUser.username }}</h3>
            <p>ID: {{ currentUser.id }}</p>
            <p>注册时间: {{ formatDateTime(currentUser.createdAt) }}</p>
          </div>
        </div>
        
        <el-divider />
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickname }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentUser.phone || '-' }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentUser.email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ currentUser.gender === 1 ? '男' : currentUser.gender === 2 ? '女' : '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="生日">{{ currentUser.birthday || '-' }}</el-descriptions-item>
          <el-descriptions-item label="实名认证" :span="2">
            <el-tag :type="currentUser.isVerified ? 'success' : 'info'">
              {{ currentUser.isVerified ? '已认证' : '未认证' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态" :span="2">
            <el-tag :type="currentUser.status ? 'success' : 'danger'">
              {{ currentUser.status ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="个人简介" :span="2">
            {{ currentUser.bio || '暂无简介' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录时间">
            {{ formatDateTime(currentUser.lastLoginTime) || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录IP">
            {{ currentUser.lastLoginIp || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册IP">
            {{ currentUser.registerIp || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册来源">
            {{ currentUser.registerSource || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="user-stats">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ currentUser.photoCount || 0 }}</div>
                <div class="stat-label">照片数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ currentUser.followerCount || 0 }}</div>
                <div class="stat-label">粉丝数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ currentUser.followingCount || 0 }}</div>
                <div class="stat-label">关注数</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            :type="currentUser?.status ? 'danger' : 'success'"
            @click="handleStatusChange(currentUser)"
          >
            {{ currentUser?.status ? '禁用用户' : '启用用户' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 列表数据
const list = ref<any[]>([])
const total = ref(0)
const listLoading = ref(false)
const dialogVisible = ref(false)
const currentUser = ref<any>(null)

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 10,
  username: '',
  phone: '',
  status: '',
  isVerified: ''
})

// 获取用户列表
const getList = () => {
  listLoading.value = true
  
  // 这里应该调用实际的API
  console.log('获取用户列表', listQuery)
  
  // 模拟API请求
  setTimeout(() => {
    // 生成模拟数据
    list.value = Array(listQuery.limit).fill(0).map((_, index) => {
      const id = (listQuery.page - 1) * listQuery.limit + index + 1
      return {
        id,
        username: `user${id}`,
        nickname: `用户${id}`,
        avatar: `https://randomuser.me/api/portraits/${id % 2 ? 'men' : 'women'}/${(id % 100) + 1}.jpg`,
        phone: id % 3 === 0 ? null : `1381234${String(id).padStart(4, '0')}`,
        email: id % 4 === 0 ? null : `user${id}@example.com`,
        gender: id % 3,
        birthday: id % 5 === 0 ? null : `1990-01-${String(id % 28 + 1).padStart(2, '0')}`,
        bio: id % 2 === 0 ? '这是用户的个人简介，介绍自己的摄影风格和兴趣爱好。' : null,
        status: id % 10 !== 0, // 90%的用户状态正常
        isVerified: id % 3 === 0, // 33%的用户已实名认证
        lastLoginTime: new Date(Date.now() - id * 86400000).toISOString(),
        lastLoginIp: `192.168.1.${id % 255}`,
        registerIp: `192.168.1.${id % 255}`,
        registerSource: id % 2 === 0 ? 'web' : 'app',
        createdAt: new Date(Date.now() - id * 86400000 * 7).toISOString(),
        photoCount: Math.floor(Math.random() * 100),
        followerCount: Math.floor(Math.random() * 500),
        followingCount: Math.floor(Math.random() * 200)
      }
    })
    
    // 设置总数
    total.value = 100
    
    // 关闭加载状态
    listLoading.value = false
  }, 500)
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 处理查询
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 重置查询条件
const resetFilter = () => {
  listQuery.username = ''
  listQuery.phone = ''
  listQuery.status = ''
  listQuery.isVerified = ''
  handleFilter()
}

// 处理每页数量变化
const handleSizeChange = (val: number) => {
  listQuery.limit = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  listQuery.page = val
  getList()
}

// 查看用户详情
const handleDetail = (row: any) => {
  currentUser.value = { ...row }
  dialogVisible.value = true
}

// 修改用户状态
const handleStatusChange = (row: any) => {
  if (!row) return
  
  const statusText = row.status ? '禁用' : '启用'
  const messageText = `确定要${statusText}用户 "${row.nickname || row.username}" 吗？`
  
  ElMessageBox.confirm(messageText, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里应该调用实际的API
    console.log(`${statusText}用户`, row.id)
    
    // 模拟API请求
    setTimeout(() => {
      // 更新用户状态
      const user = list.value.find(item => item.id === row.id)
      if (user) {
        user.status = !user.status
      }
      
      // 如果是在详情对话框中操作，也更新当前用户状态
      if (currentUser.value && currentUser.value.id === row.id) {
        currentUser.value.status = !currentUser.value.status
      }
      
      ElMessage({
        type: 'success',
        message: `${statusText}成功！`
      })
    }, 300)
  }).catch(() => {
    // 取消操作
  })
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0px;
  
  .filter-container {
    margin-bottom: 20px;
    padding: 18px 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .user-detail {
    .user-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      
      .user-basic-info {
        margin-left: 20px;
        
        h3 {
          margin: 0 0 5px;
        }
        
        p {
          margin: 0 0 5px;
          color: #666;
        }
      }
    }
    
    .user-stats {
      margin-top: 20px;
      
      .stat-item {
        text-align: center;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .stat-label {
          color: #666;
        }
      }
    }
  }
}
</style>
