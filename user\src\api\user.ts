import request from '../utils/request'
import { mockUserProfile } from '@/utils/mockApi'

// 是否使用模拟数据
const USE_MOCK = false

// 用户登录
export function login(data: any) {
  if (USE_MOCK) {
    // 模拟登录
    const mockUser = {
      id: 1,
      username: data.username,
      nickname: `用户${Math.floor(Math.random() * 1000)}`,
      avatar: `https://randomuser.me/api/portraits/men/${Math.floor(Math.random() * 100)}.jpg`,
      bio: '这是一个简短的个人介绍',
      gender: Math.floor(Math.random() * 3),
      birthday: '1990-01-01',
      location: '北京',
      website: 'https://example.com',
      email: '<EMAIL>',
      phone: '13800138000',
      followingCount: Math.floor(Math.random() * 200),
      followerCount: Math.floor(Math.random() * 300),
      photoCount: Math.floor(Math.random() * 100)
    };

    const mockResponse = {
      code: 200,
      message: 'success',
      data: {
        token: 'mock_token_' + Date.now(),
        user: mockUser
      }
    };

    return Promise.resolve(mockResponse);
  }

  console.log('发送登录请求，数据:', data);

  // 确保请求头中包含正确的Content-Type
  return request({
    url: '/auth/login',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: {
      username: data.username,
      password: data.password,
      remember: data.remember || false
    }
  })
}

// 用户注册
export function register(data: any) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  if (USE_MOCK) {
    // 从localStorage获取用户信息
    try {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        return Promise.resolve({
          code: 200,
          message: 'success',
          data: JSON.parse(userInfo)
        });
      }
    } catch (e) {
      console.error('解析用户信息失败', e);
    }

    // 模拟用户信息
    const mockUser = {
      id: 1,
      username: 'user1',
      nickname: '用户1',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      bio: '这是一个简短的个人介绍',
      gender: 1,
      birthday: '1990-01-01',
      location: '北京',
      website: 'https://example.com',
      email: '<EMAIL>',
      phone: '13800138000',
      followingCount: Math.floor(Math.random() * 200),
      followerCount: Math.floor(Math.random() * 300),
      photoCount: Math.floor(Math.random() * 100)
    };

    return Promise.resolve({
      code: 200,
      message: 'success',
      data: mockUser
    });
  }

  // 从localStorage和sessionStorage获取token
  let token = localStorage.getItem('token')
  if (!token || token.trim() === '') {
    token = sessionStorage.getItem('token')
  }
  console.log('getUserInfo API调用，当前token:', token)

  if (!token || token.trim() === '') {
    console.warn('没有token，无法获取用户信息')

    // 尝试从localStorage获取用户信息
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      try {
        const userInfo = JSON.parse(storedUserInfo)
        console.log('从localStorage获取到用户信息:', userInfo)
        return Promise.resolve({
          code: 200,
          message: 'success',
          data: userInfo
        })
      } catch (e) {
        console.error('解析localStorage中的用户信息失败', e)
      }
    }

    return Promise.reject(new Error('暂未登录或token已经过期'))
  }

  // 创建自定义请求配置
  const config = {
    url: '/user/info',
    method: 'get',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  }

  console.log('getUserInfo 请求配置:', config)
  console.log('getUserInfo 请求头:', config.headers)

  return request(config).then(response => {
    console.log('getUserInfo API响应:', response)
    return response
  }).catch(error => {
    console.error('getUserInfo API错误:', error)

    // 尝试从localStorage获取用户信息
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      try {
        const userInfo = JSON.parse(storedUserInfo)
        console.log('API请求失败，从localStorage获取到用户信息:', userInfo)
        return {
          code: 200,
          message: 'success',
          data: userInfo
        }
      } catch (e) {
        console.error('解析localStorage中的用户信息失败', e)
      }
    }

    throw error
  })
}

// 更新用户信息
export function updateUserInfo(data: any) {
  return request({
    url: '/user/info',
    method: 'put',
    data
  })
}

// 用户登出
export function logout() {
  // 获取token
  const token = localStorage.getItem('token')
  console.log('logout API调用，当前token:', token)

  if (!token) {
    console.warn('没有token，无法调用登出接口')
    // 即使没有token，也清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    return Promise.resolve({ code: 200, message: 'success', data: null })
  }

  return request({
    url: '/auth/logout',
    method: 'post',
    headers: { 'Authorization': `Bearer ${token}` }
  }).finally(() => {
    // 无论成功失败，都清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  })
}

// 获取用户照片列表
export function getUserPhotos(userId: number, params: any) {
  if (USE_MOCK) {
    return mockUserProfile(userId).then(res => {
      return {
        code: 200,
        message: 'success',
        data: res.data.photos
      };
    });
  }

  return request({
    url: `/user/${userId}/photos`,
    method: 'get',
    params
  })
}

// 获取用户关注列表
export function getUserFollowing(userId: number, params: any) {
  if (USE_MOCK) {
    // 模拟关注列表
    const { page = 1, size = 10 } = params || {};
    const mockUsers = Array.from({ length: size }, (_, i) => ({
      id: i + 1,
      username: `user${i + 1}`,
      nickname: `用户${i + 1}`,
      avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
      bio: '这是一个简短的个人介绍',
      gender: Math.floor(Math.random() * 3),
      birthday: '1990-01-01',
      location: '北京',
      website: 'https://example.com',
      email: '<EMAIL>',
      phone: '13800138000',
      followingCount: Math.floor(Math.random() * 200),
      followerCount: Math.floor(Math.random() * 300),
      photoCount: Math.floor(Math.random() * 100),
      isFollowing: Math.random() > 0.5
    }));

    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        records: mockUsers,
        total: 100,
        size,
        current: page,
        pages: Math.ceil(100 / size)
      }
    });
  }

  return request({
    url: `/user/${userId}/following`,
    method: 'get',
    params
  })
}

// 获取用户粉丝列表
export function getUserFollowers(userId: number, params: any) {
  if (USE_MOCK) {
    // 模拟粉丝列表
    const { page = 1, size = 10 } = params || {};
    const mockUsers = Array.from({ length: size }, (_, i) => ({
      id: i + 1,
      username: `user${i + 1}`,
      nickname: `用户${i + 1}`,
      avatar: `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${Math.floor(Math.random() * 100)}.jpg`,
      bio: '这是一个简短的个人介绍',
      gender: Math.floor(Math.random() * 3),
      birthday: '1990-01-01',
      location: '北京',
      website: 'https://example.com',
      email: '<EMAIL>',
      phone: '13800138000',
      followingCount: Math.floor(Math.random() * 200),
      followerCount: Math.floor(Math.random() * 300),
      photoCount: Math.floor(Math.random() * 100),
      isFollowing: Math.random() > 0.5
    }));

    return Promise.resolve({
      code: 200,
      message: 'success',
      data: {
        records: mockUsers,
        total: 100,
        size,
        current: page,
        pages: Math.ceil(100 / size)
      }
    });
  }

  return request({
    url: `/user/${userId}/followers`,
    method: 'get',
    params
  })
}

// 关注用户
export function followUser(userId: number) {
  if (USE_MOCK) {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: true
    });
  }

  return request({
    url: `/user/follow/${userId}`,
    method: 'post'
  })
}

// 取消关注用户
export function unfollowUser(userId: number) {
  if (USE_MOCK) {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: true
    });
  }

  return request({
    url: `/user/unfollow/${userId}`,
    method: 'post'
  })
}

// 检查是否关注了用户
export function checkFollowing(userId: number) {
  if (USE_MOCK) {
    return Promise.resolve({
      code: 200,
      message: 'success',
      data: Math.random() > 0.5
    });
  }

  return request({
    url: `/user/following/${userId}`,
    method: 'get'
  })
}
