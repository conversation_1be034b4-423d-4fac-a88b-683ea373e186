package com.phototagmoment.util;

import com.phototagmoment.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片工具类
 */
@Slf4j
public class ImageUtil {

    /**
     * 获取图片尺寸
     *
     * @param inputStream 图片输入流
     * @return 图片尺寸数组 [宽度, 高度]
     */
    public static int[] getImageDimensions(InputStream inputStream) {
        try {
            BufferedImage image = ImageIO.read(inputStream);
            return new int[]{image.getWidth(), image.getHeight()};
        } catch (IOException e) {
            log.error("获取图片尺寸失败", e);
            throw new BusinessException("获取图片尺寸失败");
        }
    }

    /**
     * 生成缩略图
     *
     * @param inputStream 图片输入流
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 缩略图字节数组
     */
    public static byte[] generateThumbnail(InputStream inputStream, int maxWidth, int maxHeight) {
        try {
            // 读取原图
            BufferedImage originalImage = ImageIO.read(inputStream);
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();

            // 计算缩放比例
            double widthRatio = (double) maxWidth / originalWidth;
            double heightRatio = (double) maxHeight / originalHeight;
            double ratio = Math.min(widthRatio, heightRatio);

            // 计算缩略图尺寸
            int thumbnailWidth = (int) (originalWidth * ratio);
            int thumbnailHeight = (int) (originalHeight * ratio);

            // 创建缩略图
            BufferedImage thumbnailImage = new BufferedImage(thumbnailWidth, thumbnailHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = thumbnailImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(originalImage, 0, 0, thumbnailWidth, thumbnailHeight, null);
            g2d.dispose();

            // 输出缩略图
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(thumbnailImage, "jpg", outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("生成缩略图失败", e);
            throw new BusinessException("生成缩略图失败");
        }
    }

    /**
     * 获取图片格式
     *
     * @param contentType 内容类型
     * @return 图片格式
     */
    public static String getImageFormat(String contentType) {
        if (contentType == null) {
            return "jpg";
        }
        switch (contentType.toLowerCase()) {
            case "image/jpeg":
            case "image/jpg":
                return "jpg";
            case "image/png":
                return "png";
            case "image/gif":
                return "gif";
            case "image/bmp":
                return "bmp";
            case "image/webp":
                return "webp";
            default:
                return "jpg";
        }
    }
}
