<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.CommentMapper">

    <!-- 分页查询照片评论列表 -->
    <select id="selectPhotoComments" resultType="com.phototagmoment.dto.CommentDTO">
        SELECT
            c.id, c.user_id, c.photo_id, c.parent_id, c.content, c.like_count, c.reply_count,
            c.is_deleted, c.created_at, c.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN cl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked
        FROM
            ptm_comment c
        LEFT JOIN
            ptm_user u ON c.user_id = u.id
        LEFT JOIN
            ptm_comment_like cl ON c.id = cl.comment_id AND cl.user_id = #{currentUserId} AND #{currentUserId} IS NOT NULL
        WHERE
            c.photo_id = #{photoId}
            AND c.parent_id IS NULL
            AND c.is_deleted = 0
        ORDER BY
            c.created_at DESC
    </select>

    <!-- 查询评论回复列表 -->
    <select id="selectCommentReplies" resultType="com.phototagmoment.dto.CommentDTO">
        SELECT
            c.id, c.user_id, c.photo_id, c.parent_id, c.content, c.like_count, c.reply_count,
            c.is_deleted, c.created_at, c.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN cl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked
        FROM
            ptm_comment c
        LEFT JOIN
            ptm_user u ON c.user_id = u.id
        LEFT JOIN
            ptm_comment_like cl ON c.id = cl.comment_id AND cl.user_id = #{currentUserId} AND #{currentUserId} IS NOT NULL
        WHERE
            c.parent_id = #{commentId}
            AND c.is_deleted = 0
        ORDER BY
            c.created_at ASC
    </select>

    <!-- 查询评论详情 -->
    <select id="selectCommentDetail" resultType="com.phototagmoment.dto.CommentDTO">
        SELECT
            c.id, c.user_id, c.photo_id, c.parent_id, c.content, c.like_count, c.reply_count,
            c.is_deleted, c.created_at, c.updated_at,
            u.username AS userName,
            u.nickname AS userNickname,
            u.avatar AS userAvatar,
            CASE WHEN cl.id IS NOT NULL THEN 1 ELSE 0 END AS isLiked
        FROM
            ptm_comment c
        LEFT JOIN
            ptm_user u ON c.user_id = u.id
        LEFT JOIN
            ptm_comment_like cl ON c.id = cl.comment_id AND cl.user_id = #{currentUserId} AND #{currentUserId} IS NOT NULL
        WHERE
            c.id = #{commentId}
    </select>

    <!-- 增加评论点赞数 -->
    <update id="incrementLikeCount">
        UPDATE ptm_comment
        SET like_count = like_count + 1
        WHERE id = #{commentId}
    </update>

    <!-- 减少评论点赞数 -->
    <update id="decrementLikeCount">
        UPDATE ptm_comment
        SET like_count = GREATEST(like_count - 1, 0)
        WHERE id = #{commentId}
    </update>

    <!-- 增加评论回复数 -->
    <update id="incrementReplyCount">
        UPDATE ptm_comment
        SET reply_count = reply_count + 1
        WHERE id = #{commentId}
    </update>

    <!-- 减少评论回复数 -->
    <update id="decrementReplyCount">
        UPDATE ptm_comment
        SET reply_count = GREATEST(reply_count - 1, 0)
        WHERE id = #{commentId}
    </update>

</mapper>
