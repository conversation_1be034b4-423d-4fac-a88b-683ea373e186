<template>
  <div class="photo-note-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>照片笔记管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户ID">
            <el-input
              v-model="searchForm.userId"
              placeholder="请输入用户ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="待审核" :value="0" />
              <el-option label="正常" :value="1" />
              <el-option label="审核拒绝" :value="2" />
              <el-option label="已删除" :value="3" />
            </el-select>
          </el-form-item>

          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column label="用户信息" width="200">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :src="scope.row.avatar" :size="40" />
              <div class="user-details">
                <div class="user-name">{{ scope.row.nickname }}</div>
                <div class="user-id">ID: {{ scope.row.userId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="照片预览" width="120">
          <template #default="scope">
            <div class="photo-preview">
              <el-image
                v-if="scope.row.images && scope.row.images.length > 0"
                :src="scope.row.images[0].thumbnailUrl"
                fit="cover"
                style="width: 60px; height: 60px; border-radius: 4px"
                :preview-src-list="scope.row.images.map(img => img.url)"
              />
              <span v-if="scope.row.photoCount > 1" class="photo-count">
                +{{ scope.row.photoCount - 1 }}
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="文件信息" width="150">
          <template #default="scope">
            <div class="file-info">
              <div class="file-size">{{ formatFileSize(scope.row.totalFileSize) }}</div>
              <div class="file-type">{{ scope.row.fileTypes?.join(', ') || 'JPEG' }}</div>
              <div v-if="scope.row.hasExif" class="exif-indicator">
                <el-tag size="small" type="info">EXIF</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="内容" min-width="300">
          <template #default="scope">
            <div class="content-preview">
              <div v-if="scope.row.title" class="note-title">{{ scope.row.title }}</div>
              <div class="note-content">{{ scope.row.content }}</div>
              <div v-if="scope.row.tags && scope.row.tags.length > 0" class="note-tags">
                <el-tag
                  v-for="tag in scope.row.tags.slice(0, 3)"
                  :key="tag"
                  size="small"
                  type="info"
                >
                  #{{ tag }}#
                </el-tag>
                <span v-if="scope.row.tags.length > 3">...</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="互动数据" width="120">
          <template #default="scope">
            <div class="stats-info">
              <div><el-icon><View /></el-icon> {{ scope.row.viewCount }}</div>
              <div><el-icon><Star /></el-icon> {{ scope.row.likeCount }}</div>
              <div><el-icon><ChatDotRound /></el-icon> {{ scope.row.commentCount }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDetail(scope.row)"
            >
              查看
            </el-button>

            <el-button
              v-if="scope.row.status === 0"
              type="success"
              size="small"
              @click="auditNote(scope.row, 1)"
            >
              通过
            </el-button>

            <el-button
              v-if="scope.row.status === 0"
              type="danger"
              size="small"
              @click="showRejectDialog(scope.row)"
            >
              拒绝
            </el-button>

            <el-button
              v-if="scope.row.status !== 3"
              type="danger"
              size="small"
              @click="deleteNote(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 照片笔记详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="照片笔记详情"
      width="800px"
      :before-close="handleDetailClose"
    >
      <div v-if="currentNote" class="note-detail">
        <!-- 用户信息 -->
        <div class="detail-user">
          <el-avatar :src="currentNote.avatar" :size="50" />
          <div class="user-info">
            <div class="user-name">{{ currentNote.nickname }}</div>
            <div class="user-id">用户ID: {{ currentNote.userId }}</div>
            <div class="publish-time">发布时间: {{ formatDateTime(currentNote.createdAt) }}</div>
          </div>
        </div>

        <!-- 照片展示 -->
        <div v-if="currentNote.images && currentNote.images.length > 0" class="detail-photos">
          <h4>照片 ({{ currentNote.images.length }}张)</h4>
          <div class="photos-grid">
            <el-image
              v-for="(image, index) in currentNote.images"
              :key="index"
              :src="image.thumbnailUrl"
              fit="cover"
              style="width: 120px; height: 120px; margin: 4px; border-radius: 4px"
              :preview-src-list="currentNote.images.map(img => img.url)"
            />
          </div>
        </div>

        <!-- 内容详情 -->
        <div class="detail-content">
          <h4 v-if="currentNote.title">标题</h4>
          <p v-if="currentNote.title" class="note-title">{{ currentNote.title }}</p>

          <h4>正文内容</h4>
          <div class="note-content" v-html="currentNote.processedContent"></div>

          <div v-if="currentNote.tags && currentNote.tags.length > 0" class="note-tags">
            <h4>标签</h4>
            <el-tag
              v-for="tag in currentNote.tags"
              :key="tag"
              type="info"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              #{{ tag }}#
            </el-tag>
          </div>

          <div v-if="currentNote.mentions && currentNote.mentions.length > 0" class="note-mentions">
            <h4>@用户</h4>
            <el-tag
              v-for="mention in currentNote.mentions"
              :key="mention.mentionedUserId"
              type="warning"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              @{{ mention.mentionedUserNickname }}
            </el-tag>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="detail-stats">
          <h4>统计信息</h4>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="浏览量" :value="currentNote.viewCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="点赞数" :value="currentNote.likeCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="评论数" :value="currentNote.commentCount" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="分享数" :value="currentNote.shareCount" />
            </el-col>
          </el-row>
        </div>

        <!-- 审核信息 -->
        <div v-if="currentNote.status === 2 && currentNote.rejectReason" class="reject-info">
          <h4>拒绝原因</h4>
          <p>{{ currentNote.rejectReason }}</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentNote && currentNote.status === 0"
            type="success"
            @click="auditNote(currentNote, 1)"
          >
            审核通过
          </el-button>
          <el-button
            v-if="currentNote && currentNote.status === 0"
            type="danger"
            @click="showRejectDialog(currentNote)"
          >
            审核拒绝
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 拒绝审核弹窗 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="审核拒绝"
      width="500px"
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝原因" required>
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button
            type="danger"
            @click="confirmReject"
            :disabled="!rejectForm.reason.trim()"
          >
            确认拒绝
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search, View, Star, ChatDotRound } from '@element-plus/icons-vue'
import { getPhotoNoteList, getPhotoNoteDetail, auditPhotoNote, deletePhotoNote } from '@/api/photoNote'
import { formatDateTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const detailDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const currentNote = ref(null)
const currentRejectNote = ref(null)

// 搜索表单
const searchForm = reactive({
  userId: '',
  status: null,
  dateRange: null
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 拒绝表单
const rejectForm = reactive({
  reason: ''
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      userId: searchForm.userId || undefined,
      status: searchForm.status,
      startDate: searchForm.dateRange?.[0],
      endDate: searchForm.dateRange?.[1]
    }

    const response = await getPhotoNoteList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    userId: '',
    status: null,
    dateRange: null
  })
  pagination.page = 1
  loadData()
}

const refreshData = () => {
  loadData()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const viewDetail = async (row) => {
  try {
    const response = await getPhotoNoteDetail(row.id)
    currentNote.value = response.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const handleDetailClose = () => {
  detailDialogVisible.value = false
  currentNote.value = null
}

const auditNote = async (note, status) => {
  try {
    await auditPhotoNote(note.id, status)
    ElMessage.success('审核成功')

    // 更新表格数据
    const index = tableData.value.findIndex(item => item.id === note.id)
    if (index !== -1) {
      tableData.value[index].status = status
    }

    // 关闭弹窗
    detailDialogVisible.value = false
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  }
}

const showRejectDialog = (note) => {
  currentRejectNote.value = note
  rejectForm.reason = ''
  rejectDialogVisible.value = true
  detailDialogVisible.value = false
}

const confirmReject = async () => {
  try {
    await auditPhotoNote(currentRejectNote.value.id, 2, rejectForm.reason)
    ElMessage.success('审核拒绝成功')

    // 更新表格数据
    const index = tableData.value.findIndex(item => item.id === currentRejectNote.value.id)
    if (index !== -1) {
      tableData.value[index].status = 2
      tableData.value[index].rejectReason = rejectForm.reason
    }

    rejectDialogVisible.value = false
    currentRejectNote.value = null
  } catch (error) {
    console.error('审核拒绝失败:', error)
    ElMessage.error('审核拒绝失败')
  }
}

const deleteNote = async (note) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这篇照片笔记吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deletePhotoNote(note.id)
    ElMessage.success('删除成功')

    // 重新加载数据
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const getStatusType = (status) => {
  const statusMap = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    0: '待审核',
    1: '正常',
    2: '审核拒绝',
    3: '已删除'
  }
  return statusMap[status] || '未知'
}

const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.photo-note-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 10px;
}

.user-name {
  font-weight: 600;
  color: #333;
}

.user-id {
  font-size: 12px;
  color: #999;
}

.photo-preview {
  position: relative;
}

.photo-count {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
}

.file-info {
  font-size: 12px;
  color: #666;
}

.file-info > div {
  margin-bottom: 4px;
}

.file-size {
  font-weight: 600;
  color: #333;
}

.file-type {
  color: #999;
}

.exif-indicator {
  margin-top: 4px;
}

.content-preview {
  max-width: 300px;
}

.note-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.note-content {
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 8px;
}

.note-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.stats-info {
  font-size: 12px;
  color: #666;
}

.stats-info > div {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.stats-info .el-icon {
  margin-right: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.note-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-user {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.detail-user .user-info {
  margin-left: 15px;
}

.detail-user .user-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.detail-user .user-id,
.detail-user .publish-time {
  font-size: 14px;
  color: #999;
  margin-bottom: 3px;
}

.detail-photos {
  margin-bottom: 20px;
}

.photos-grid {
  display: flex;
  flex-wrap: wrap;
}

.detail-content h4 {
  margin: 15px 0 10px 0;
  color: #333;
}

.detail-content .note-content {
  line-height: 1.6;
  color: #333;
  max-height: none;
  display: block;
  -webkit-line-clamp: none;
}

.detail-stats {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.reject-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #fef0f0;
  border-radius: 4px;
  border-left: 4px solid #f56c6c;
}

.reject-info h4 {
  color: #f56c6c;
  margin-bottom: 10px;
}

.reject-info p {
  color: #666;
  margin: 0;
}
</style>
