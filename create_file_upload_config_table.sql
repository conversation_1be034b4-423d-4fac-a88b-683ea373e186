USE phototag_moment;

CREATE TABLE IF NOT EXISTS ptm_file_upload_config (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    storage_type VARCHAR(50) NOT NULL COMMENT '存储类型',
    config_params TEXT COMMENT '配置参数',
    upload_limits TEXT COMMENT '上传限制配置',
    path_config TEXT COMMENT '路径配置',
    enabled TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    is_default TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否为默认配置',
    status TINYINT(1) NOT NULL DEFAULT 0 COMMENT '配置状态',
    sort_order INT(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
    description VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    last_test_time DATETIME DEFAULT NULL COMMENT '最后测试时间',
    last_test_result TEXT DEFAULT NULL COMMENT '最后测试结果',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at DATETIME DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_config_name (config_name, is_deleted),
    KEY idx_storage_type (storage_type),
    KEY idx_enabled (enabled),
    KEY idx_is_default (is_default),
    KEY idx_status (status),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传配置表';

INSERT INTO ptm_file_upload_config (
    config_name,
    storage_type,
    config_params,
    upload_limits,
    path_config,
    enabled,
    is_default,
    status,
    sort_order,
    description
) VALUES (
    '默认本地存储',
    'LOCAL',
    '{"localPath":"uploads","domain":"http://localhost:8081","useHttps":false,"connectTimeout":30,"readTimeout":60}',
    '{"maxFileSize":50,"maxFileCount":10,"allowedFileTypes":["jpg","jpeg","png","gif","pdf","doc","docx"],"forbiddenFileTypes":["exe","bat","sh","cmd"],"enableFileTypeCheck":true,"enableContentCheck":false,"enableVirusScan":false}',
    '{"rootPath":"uploads","fileNamingRule":"UUID","directoryStructure":"DATE_USER_TYPE","enableDateDirectory":true,"enableUserDirectory":true,"enableTypeDirectory":true,"thumbnailDirectory":"thumbnails","tempDirectory":"temp"}',
    1,
    1,
    0,
    1,
    '系统默认的本地文件存储配置'
);
