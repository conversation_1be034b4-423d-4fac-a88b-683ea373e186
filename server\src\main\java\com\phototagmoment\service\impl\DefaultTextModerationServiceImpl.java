package com.phototagmoment.service.impl;

import com.phototagmoment.service.TextModerationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

/**
 * 默认文本审核服务实现类
 * 当没有其他TextModerationService实现时使用
 * 默认通过所有审核
 */
@Slf4j
@Service
@ConditionalOnMissingBean(name = "localTextModerationServiceImpl")
public class DefaultTextModerationServiceImpl implements TextModerationService {

    private String failReason;

    @Override
    public boolean moderateText(String text) {
        log.info("使用默认文本审核服务，自动通过审核");
        return true;
    }

    @Override
    public String getFailReason() {
        return failReason;
    }
}
