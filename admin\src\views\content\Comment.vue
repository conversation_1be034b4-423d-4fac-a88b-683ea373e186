<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="form-inline">
        <el-form-item label="内容">
          <el-input
            v-model="listQuery.content"
            placeholder="评论内容"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input
            v-model="listQuery.username"
            placeholder="用户名"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="状态" clearable>
            <el-option label="正常" value="1" />
            <el-option label="已删除" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="用户" width="150">
        <template #default="scope">
          <div class="user-info">
            <el-avatar :size="24" :src="scope.row.user.avatar" />
            <span>{{ scope.row.user.nickname }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="照片" width="120">
        <template #default="scope">
          <el-image
            :src="scope.row.photo.thumbnailUrl"
            :preview-src-list="[scope.row.photo.url]"
            fit="cover"
            style="width: 60px; height: 60px; border-radius: 4px;"
          />
        </template>
      </el-table-column>

      <el-table-column align="center" label="照片标题" width="150">
        <template #default="scope">
          <span>{{ scope.row.photo.title }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="评论内容" min-width="200">
        <template #default="scope">
          <span>{{ scope.row.content }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="点赞数" width="100">
        <template #default="scope">
          <span>{{ scope.row.likeCount }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'">
            {{ scope.row.status ? '正常' : '已删除' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="评论时间" width="160">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" fixed="right" width="200">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            size="small"
            :type="scope.row.status ? 'danger' : 'success'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status ? '删除' : '恢复' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="listQuery.page"
        v-model:page-size="listQuery.size"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 评论详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="评论详情"
      width="600px"
    >
      <div v-if="currentComment" class="comment-detail">
        <div class="photo-info">
          <h3>照片信息</h3>
          <div class="photo-container">
            <el-image
              :src="currentComment.photo.thumbnailUrl"
              :preview-src-list="[currentComment.photo.url]"
              fit="cover"
              class="photo-thumbnail"
            />
            <div class="photo-meta">
              <p><strong>标题：</strong>{{ currentComment.photo.title }}</p>
              <p><strong>上传者：</strong>{{ currentComment.photo.user.nickname }}</p>
              <p><strong>上传时间：</strong>{{ formatDateTime(currentComment.photo.createdAt) }}</p>
              <el-button size="small" type="primary" @click="viewPhotoDetail(currentComment.photo.id)">
                查看照片详情
              </el-button>
            </div>
          </div>
        </div>

        <el-divider />

        <div class="comment-info">
          <h3>评论信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="ID">{{ currentComment.id }}</el-descriptions-item>
            <el-descriptions-item label="评论者">
              <div class="user-info">
                <el-avatar :size="24" :src="currentComment.user.avatar" />
                <span>{{ currentComment.user.nickname }}</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ currentComment.user.id }}</el-descriptions-item>
            <el-descriptions-item label="IP地址">{{ currentComment.ip || '-' }}</el-descriptions-item>
            <el-descriptions-item label="评论内容" :span="2">
              {{ currentComment.content }}
            </el-descriptions-item>
            <el-descriptions-item label="点赞数">{{ currentComment.likeCount }}</el-descriptions-item>
            <el-descriptions-item label="回复数">{{ currentComment.replyCount }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="currentComment.status ? 'success' : 'danger'">
                {{ currentComment.status ? '正常' : '已删除' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="评论时间">
              {{ formatDateTime(currentComment.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div v-if="currentComment.replies && currentComment.replies.length" class="replies-info">
          <h3>回复列表</h3>
          <el-table :data="currentComment.replies" border>
            <el-table-column align="center" label="ID" width="80">
              <template #default="scope">
                <span>{{ scope.row.id }}</span>
              </template>
            </el-table-column>

            <el-table-column align="center" label="用户" width="150">
              <template #default="scope">
                <div class="user-info">
                  <el-avatar :size="24" :src="scope.row.user.avatar" />
                  <span>{{ scope.row.user.nickname }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="center" label="回复内容" min-width="200">
              <template #default="scope">
                <span>{{ scope.row.content }}</span>
              </template>
            </el-table-column>

            <el-table-column align="center" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'">
                  {{ scope.row.status ? '正常' : '已删除' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column align="center" label="回复时间" width="160">
              <template #default="scope">
                <span>{{ formatDateTime(scope.row.createdAt) }}</span>
              </template>
            </el-table-column>

            <el-table-column align="center" label="操作" width="120">
              <template #default="scope">
                <el-button
                  size="small"
                  :type="scope.row.status ? 'danger' : 'success'"
                  @click="handleReplyStatusChange(scope.row)"
                >
                  {{ scope.row.status ? '删除' : '恢复' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            :type="currentComment?.status ? 'danger' : 'success'"
            @click="handleStatusChange(currentComment)"
          >
            {{ currentComment?.status ? '删除评论' : '恢复评论' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getCommentList,
  getCommentDetail,
  updateCommentStatus,
  updateReplyStatus,
  type CommentData,
  type CommentQueryParams
} from '@/api/comment'
import { formatDateTime } from '@/utils/date'

// 列表数据
const list = ref<CommentData[]>([])
const total = ref(0)
const listLoading = ref(false)
const dialogVisible = ref(false)
const currentComment = ref<CommentData | null>(null)

// 查询参数
const listQuery = reactive<CommentQueryParams>({
  page: 1,
  size: 10,
  content: '',
  username: '',
  status: undefined
})

// 获取评论列表
const getList = async () => {
  try {
    listLoading.value = true

    const response = await getCommentList(listQuery)
    list.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取评论列表失败:', error)
    ElMessage.error('获取评论列表失败')
  } finally {
    listLoading.value = false
  }
}



// 处理查询
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 重置查询条件
const resetFilter = () => {
  listQuery.content = ''
  listQuery.username = ''
  listQuery.status = undefined
  handleFilter()
}

// 处理每页数量变化
const handleSizeChange = (val: number) => {
  listQuery.size = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  listQuery.page = val
  getList()
}

// 查看评论详情
const handleDetail = async (row: CommentData) => {
  try {
    const response = await getCommentDetail(row.id)
    currentComment.value = response.data
    dialogVisible.value = true
  } catch (error) {
    console.error('获取评论详情失败:', error)
    ElMessage.error('获取评论详情失败')
  }
}

// 查看照片详情
const viewPhotoDetail = (photoId: number) => {
  // 这里应该跳转到照片详情页面
  console.log('查看照片详情', photoId)

  // 模拟跳转
  ElMessage({
    type: 'info',
    message: `正在跳转到照片ID为${photoId}的详情页...`
  })
}

// 修改评论状态
const handleStatusChange = async (row: CommentData) => {
  if (!row) return

  const statusText = row.status === 1 ? '删除' : '恢复'
  const newStatus = row.status === 1 ? 0 : 1
  const messageText = `确定要${statusText}该评论吗？`

  try {
    await ElMessageBox.confirm(messageText, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateCommentStatus(row.id, newStatus)

    // 更新评论状态
    const comment = list.value.find(item => item.id === row.id)
    if (comment) {
      comment.status = newStatus
    }

    // 如果是在详情对话框中操作，也更新当前评论状态
    if (currentComment.value && currentComment.value.id === row.id) {
      currentComment.value.status = newStatus
    }

    ElMessage.success(`${statusText}成功！`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${statusText}评论失败:`, error)
      ElMessage.error(`${statusText}失败`)
    }
  }
}

// 修改回复状态
const handleReplyStatusChange = async (reply: CommentData) => {
  if (!reply || !currentComment.value) return

  const statusText = reply.status === 1 ? '删除' : '恢复'
  const newStatus = reply.status === 1 ? 0 : 1
  const messageText = `确定要${statusText}该回复吗？`

  try {
    await ElMessageBox.confirm(messageText, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await updateReplyStatus(currentComment.value.id, reply.id, newStatus)

    // 更新回复状态
    const replyIndex = currentComment.value.replies?.findIndex(item => item.id === reply.id)
    if (replyIndex !== undefined && replyIndex !== -1 && currentComment.value.replies) {
      currentComment.value.replies[replyIndex].status = newStatus
    }

    ElMessage.success(`${statusText}成功！`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${statusText}回复失败:`, error)
      ElMessage.error(`${statusText}失败`)
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0px;

  .filter-container {
    margin-bottom: 20px;
    padding: 18px 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .user-info {
    display: flex;
    align-items: center;

    .el-avatar {
      margin-right: 8px;
    }
  }

  .comment-detail {
    .photo-info, .comment-info, .replies-info {
      margin-bottom: 20px;

      h3 {
        margin-bottom: 15px;
      }
    }

    .photo-container {
      display: flex;

      .photo-thumbnail {
        width: 120px;
        height: 120px;
        border-radius: 4px;
        margin-right: 20px;
      }

      .photo-meta {
        flex: 1;

        p {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
