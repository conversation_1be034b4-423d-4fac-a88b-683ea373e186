<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="form-inline">
        <el-form-item label="用户名">
          <el-input
            v-model="listQuery.username"
            placeholder="用户名"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input
            v-model="listQuery.realName"
            placeholder="真实姓名"
            clearable
            @keyup.enter="handleFilter"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="状态" clearable>
            <el-option label="待审核" value="0" />
            <el-option label="已通过" value="1" />
            <el-option label="已拒绝" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="用户ID" width="80">
        <template #default="scope">
          <span>{{ scope.row.userId }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="用户名" width="120">
        <template #default="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="真实姓名" width="120">
        <template #default="scope">
          <span>{{ scope.row.realName }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="身份证号" width="180">
        <template #default="scope">
          <span>{{ maskIdCard(scope.row.idCard) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="证件照片" width="100">
        <template #default="scope">
          <el-button size="small" type="primary" @click="previewIdCardImages(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="申请时间" width="160">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="审核时间" width="160">
        <template #default="scope">
          <span>{{ scope.row.verifiedAt ? formatDateTime(scope.row.verifiedAt) : '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="审核备注" min-width="180">
        <template #default="scope">
          <span>{{ scope.row.remark || '-' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column align="center" label="操作" fixed="right" width="200">
        <template #default="scope">
          <el-button
            v-if="scope.row.status === 0"
            size="small"
            type="success"
            @click="handleVerify(scope.row, 1)"
          >
            通过
          </el-button>
          <el-button
            v-if="scope.row.status === 0"
            size="small"
            type="danger"
            @click="handleVerify(scope.row, 2)"
          >
            拒绝
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="listQuery.page"
        v-model:page-size="listQuery.limit"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 审核对话框 -->
    <el-dialog
      v-model="verifyDialogVisible"
      :title="verifyType === 1 ? '通过实名认证' : '拒绝实名认证'"
      width="500px"
    >
      <el-form :model="verifyForm" label-width="100px">
        <el-form-item label="用户名">
          <span>{{ currentVerification?.username }}</span>
        </el-form-item>
        <el-form-item label="真实姓名">
          <span>{{ currentVerification?.realName }}</span>
        </el-form-item>
        <el-form-item label="身份证号">
          <span>{{ maskIdCard(currentVerification?.idCard) }}</span>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input
            v-model="verifyForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="verifyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitVerify">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="实名认证详情"
      width="600px"
    >
      <div v-if="currentVerification" class="verification-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentVerification.id }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ currentVerification.userId }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentVerification.username }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ currentVerification.realName }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ maskIdCard(currentVerification.idCard) }}</el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ currentVerification.gender === 1 ? '男' : '女' }}
          </el-descriptions-item>
          <el-descriptions-item label="出生日期">{{ currentVerification.birthday }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ currentVerification.address }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentVerification.status)">
              {{ getStatusText(currentVerification.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ formatDateTime(currentVerification.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="审核时间" :span="2">
            {{ currentVerification.verifiedAt ? formatDateTime(currentVerification.verifiedAt) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="审核备注" :span="2">
            {{ currentVerification.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="id-card-images">
          <h3>证件照片</h3>
          <div class="image-container">
            <div class="image-item">
              <p>身份证正面</p>
              <el-image
                :src="currentVerification.idCardFront"
                :preview-src-list="[currentVerification.idCardFront]"
                fit="cover"
              />
            </div>
            <div class="image-item">
              <p>身份证反面</p>
              <el-image
                :src="currentVerification.idCardBack"
                :preview-src-list="[currentVerification.idCardBack]"
                fit="cover"
              />
            </div>
            <div class="image-item">
              <p>手持身份证</p>
              <el-image
                :src="currentVerification.idCardHand"
                :preview-src-list="[currentVerification.idCardHand]"
                fit="cover"
              />
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentVerification?.status === 0"
            type="success"
            @click="handleVerify(currentVerification, 1)"
          >
            通过
          </el-button>
          <el-button
            v-if="currentVerification?.status === 0"
            type="danger"
            @click="handleVerify(currentVerification, 2)"
          >
            拒绝
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="证件照片"
      width="800px"
    >
      <div class="id-card-preview">
        <div class="image-container">
          <div class="image-item">
            <p>身份证正面</p>
            <el-image
              :src="previewImages.front"
              :preview-src-list="[previewImages.front]"
              fit="cover"
            />
          </div>
          <div class="image-item">
            <p>身份证反面</p>
            <el-image
              :src="previewImages.back"
              :preview-src-list="[previewImages.back]"
              fit="cover"
            />
          </div>
          <div class="image-item">
            <p>手持身份证</p>
            <el-image
              :src="previewImages.hand"
              :preview-src-list="[previewImages.hand]"
              fit="cover"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 列表数据
const list = ref<any[]>([])
const total = ref(0)
const listLoading = ref(false)
const verifyDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const currentVerification = ref<any>(null)
const verifyType = ref(0) // 1: 通过, 2: 拒绝
const previewImages = reactive({
  front: '',
  back: '',
  hand: ''
})

// 查询参数
const listQuery = reactive({
  page: 1,
  limit: 10,
  username: '',
  realName: '',
  status: ''
})

// 审核表单
const verifyForm = reactive({
  remark: ''
})

// 获取实名认证列表
const getList = () => {
  listLoading.value = true
  
  // 这里应该调用实际的API
  console.log('获取实名认证列表', listQuery)
  
  // 模拟API请求
  setTimeout(() => {
    // 生成模拟数据
    list.value = Array(listQuery.limit).fill(0).map((_, index) => {
      const id = (listQuery.page - 1) * listQuery.limit + index + 1
      const status = id % 3 // 0: 待审核, 1: 已通过, 2: 已拒绝
      
      return {
        id,
        userId: id + 100,
        username: `user${id + 100}`,
        realName: `张${String.fromCharCode(97 + (id % 26))}`,
        idCard: `11010119${90 + (id % 10)}0101${String(1000 + id).slice(1)}`,
        gender: id % 2 + 1,
        birthday: `19${90 + (id % 10)}-01-01`,
        address: '北京市朝阳区xxx街道xxx小区',
        idCardFront: `https://picsum.photos/id/${(id * 3) % 100}/400/300`,
        idCardBack: `https://picsum.photos/id/${(id * 3 + 1) % 100}/400/300`,
        idCardHand: `https://picsum.photos/id/${(id * 3 + 2) % 100}/400/300`,
        status,
        remark: status === 1 ? '资料审核通过' : status === 2 ? '身份证照片不清晰，请重新上传' : '',
        createdAt: new Date(Date.now() - id * 86400000).toISOString(),
        verifiedAt: status === 0 ? null : new Date(Date.now() - id * 86400000 + 3600000).toISOString()
      }
    })
    
    // 设置总数
    total.value = 100
    
    // 关闭加载状态
    listLoading.value = false
  }, 500)
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 处理查询
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 重置查询条件
const resetFilter = () => {
  listQuery.username = ''
  listQuery.realName = ''
  listQuery.status = ''
  handleFilter()
}

// 处理每页数量变化
const handleSizeChange = (val: number) => {
  listQuery.limit = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  listQuery.page = val
  getList()
}

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0:
      return 'info'
    case 1:
      return 'success'
    case 2:
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '待审核'
    case 1:
      return '已通过'
    case 2:
      return '已拒绝'
    default:
      return '未知'
  }
}

// 掩码身份证号
const maskIdCard = (idCard: string) => {
  if (!idCard) return ''
  return idCard.replace(/^(.{6})(.*)(.{4})$/, '$1********$3')
}

// 查看证件照片
const previewIdCardImages = (row: any) => {
  previewImages.front = row.idCardFront
  previewImages.back = row.idCardBack
  previewImages.hand = row.idCardHand
  previewDialogVisible.value = true
}

// 查看详情
const handleDetail = (row: any) => {
  currentVerification.value = { ...row }
  detailDialogVisible.value = true
}

// 处理审核
const handleVerify = (row: any, type: number) => {
  currentVerification.value = { ...row }
  verifyType.value = type
  verifyForm.remark = ''
  verifyDialogVisible.value = true
}

// 提交审核
const submitVerify = () => {
  if (!currentVerification.value) return
  
  // 这里应该调用实际的API
  console.log('提交审核', {
    id: currentVerification.value.id,
    status: verifyType.value,
    remark: verifyForm.remark
  })
  
  // 模拟API请求
  setTimeout(() => {
    // 更新认证状态
    const verification = list.value.find(item => item.id === currentVerification.value.id)
    if (verification) {
      verification.status = verifyType.value
      verification.remark = verifyForm.remark
      verification.verifiedAt = new Date().toISOString()
    }
    
    // 更新当前认证状态
    if (currentVerification.value) {
      currentVerification.value.status = verifyType.value
      currentVerification.value.remark = verifyForm.remark
      currentVerification.value.verifiedAt = new Date().toISOString()
    }
    
    // 关闭对话框
    verifyDialogVisible.value = false
    
    ElMessage({
      type: 'success',
      message: verifyType.value === 1 ? '审核通过成功！' : '审核拒绝成功！'
    })
  }, 300)
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0px;
  
  .filter-container {
    margin-bottom: 20px;
    padding: 18px 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .verification-detail {
    .id-card-images {
      margin-top: 20px;
      
      h3 {
        margin-bottom: 15px;
      }
      
      .image-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        
        .image-item {
          flex: 1;
          min-width: 150px;
          
          p {
            margin-bottom: 10px;
            font-weight: 500;
          }
          
          .el-image {
            width: 100%;
            height: 150px;
            border-radius: 4px;
          }
        }
      }
    }
  }
  
  .id-card-preview {
    .image-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .image-item {
        flex: 1;
        min-width: 200px;
        
        p {
          margin-bottom: 10px;
          font-weight: 500;
          text-align: center;
        }
        
        .el-image {
          width: 100%;
          height: 200px;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
