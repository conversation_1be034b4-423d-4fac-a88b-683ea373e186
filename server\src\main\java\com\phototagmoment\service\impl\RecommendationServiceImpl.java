package com.phototagmoment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.dto.TagDTO;
import com.phototagmoment.entity.Photo;
import com.phototagmoment.entity.PhotoNoteImage;
import com.phototagmoment.entity.PhotoTag;
import com.phototagmoment.entity.UserBehavior;
import com.phototagmoment.entity.UserFollow;
import com.phototagmoment.entity.UserInterestTag;
import com.phototagmoment.mapper.*;
import com.phototagmoment.service.PhotoService;
import com.phototagmoment.service.QiniuStorageService;
import com.phototagmoment.service.RecommendationService;
import com.phototagmoment.service.SystemConfigService;
import com.phototagmoment.service.UserService;
import com.phototagmoment.util.UserUtil;
import com.phototagmoment.vo.PhotoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 推荐服务实现类
 */
@Slf4j
@Service
public class RecommendationServiceImpl implements RecommendationService {

    @Autowired
    private PhotoMapper photoMapper;

    @Autowired
    private PhotoTagMapper photoTagMapper;

    @Autowired
    private UserFollowMapper userFollowMapper;

    @Autowired
    private UserBehaviorMapper userBehaviorMapper;

    @Autowired
    private PhotoLikeMapper photoLikeMapper;

    @Autowired
    private PhotoCommentMapper photoCommentMapper;

    @Autowired
    private PhotoCollectMapper photoCollectMapper;

    @Autowired
    private PhotoNoteMapper photoNoteMapper;

    @Autowired
    private PhotoNoteImageMapper photoNoteImageMapper;

    @Autowired
    private UserInterestTagMapper userInterestTagMapper;

    @Autowired
    private PhotoService photoService;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private QiniuStorageService qiniuStorageService;

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    private static final String HOT_PHOTOS_CACHE_KEY = "hot_photos";
    private static final String USER_INTEREST_KEY_PREFIX = "user_interest:";
    private static final String USER_BEHAVIOR_KEY_PREFIX = "user_behavior:";
    private static final int CACHE_DAYS = 7; // 缓存7天

    @Override
    @Cacheable(value = "following_photos", key = "#userId + '_' + #page + '_' + #size", unless = "#result == null")
    public IPage<PhotoVO> getFollowingPhotos(Long userId, int page, int size) {
        log.info("获取用户{}关注的人发布的照片，页码：{}，每页大小：{}", userId, page, size);

        // 获取用户关注的人
        List<Long> followingIds = userFollowMapper.getFollowingIds(userId);
        if (followingIds.isEmpty()) {
            return new Page<>(page, size);
        }

        // 获取关注的人发布的照片（包含用户信息）
        Page<PhotoDTO> photoPage = new Page<>(page, size);
        IPage<PhotoDTO> photoDTOPage = photoMapper.selectFollowingPhotosWithUserInfo(photoPage, followingIds, userId);

        // 转换为PhotoVO
        return convertToPhotoVOPage(photoDTOPage);
    }

    @Override
    @Cacheable(value = "hot_photos", key = "#page + '_' + #size", unless = "#result == null")
    public IPage<PhotoVO> getHotPhotos(int page, int size) {
        return getHotPhotos(30, page, size); // 默认获取30天内的热门照片
    }

    @Override
    @Cacheable(value = "hot_photos", key = "#days + '_' + #page + '_' + #size", unless = "#result == null")
    public IPage<PhotoVO> getHotPhotos(int days, int page, int size) {
        log.info("获取最近{}天的热门照片，页码：{}，每页大小：{}", days, page, size);

        try {
            // 计算热门照片的分数
            String cacheKey = HOT_PHOTOS_CACHE_KEY + ":" + days;

            // 尝试使用Redis缓存
            if (redisTemplate != null) {
                try {
                    // 如果缓存中没有热门照片的排序，则计算并缓存
                    if (Boolean.FALSE.equals(redisTemplate.hasKey(cacheKey))) {
                        calculateHotPhotos(days, cacheKey);
                    }

                    // 从缓存中获取热门照片ID
                    Set<ZSetOperations.TypedTuple<Object>> hotPhotoTuples = redisTemplate.opsForZSet()
                            .reverseRangeWithScores(cacheKey, (page - 1) * size, page * size - 1);

                    if (hotPhotoTuples != null && !hotPhotoTuples.isEmpty()) {
                        // 获取照片ID列表
                        List<Long> photoIds = hotPhotoTuples.stream()
                                .map(tuple -> Long.parseLong(tuple.getValue().toString()))
                                .collect(Collectors.toList());

                        // 查询照片详情
                        List<Photo> photos = photoMapper.selectBatchIds(photoIds);

                        // 按照热门排序重新排序
                        Map<Long, Photo> photoMap = photos.stream()
                                .collect(Collectors.toMap(Photo::getId, photo -> photo));

                        List<Photo> orderedPhotos = photoIds.stream()
                                .map(photoMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());

                        // 创建分页对象
                        Page<Photo> photoPage = new Page<>(page, size);
                        photoPage.setRecords(orderedPhotos);
                        photoPage.setTotal(redisTemplate.opsForZSet().size(cacheKey));

                        // 转换为PhotoVO
                        return photoService.convertToPhotoVOPage(photoPage);
                    }
                } catch (Exception e) {
                    log.warn("Redis缓存访问失败，使用数据库查询: {}", e.getMessage());
                }
            } else {
                log.warn("Redis不可用，直接使用数据库查询");
            }

            // 如果Redis不可用或缓存为空，直接从数据库查询
            return getHotPhotosFromDatabase(days, page, size);
        } catch (Exception e) {
            log.error("获取热门照片失败: {}", e.getMessage(), e);
            // 返回空结果
            return new Page<>(page, size);
        }
    }

    /**
     * 从数据库获取热门照片（Redis不可用时的备选方案）
     */
    private IPage<PhotoVO> getHotPhotosFromDatabase(int days, int page, int size) {
        log.info("从数据库获取最近{}天的热门照片", days);

        // 获取最近days天的照片
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        Page<PhotoDTO> photoPage = new Page<>(page, size);

        // 使用关联查询获取带用户信息的照片列表
        Long currentUserId = UserUtil.getCurrentUserId();
        IPage<PhotoDTO> photoDTOPage = photoMapper.selectHotPhotosWithUserInfo(photoPage, startTime, currentUserId);

        // 转换为PhotoVO
        return convertToPhotoVOPage(photoDTOPage);
    }

    /**
     * 将PhotoDTO分页对象转换为PhotoVO分页对象
     */
    private IPage<PhotoVO> convertToPhotoVOPage(IPage<PhotoDTO> photoDTOPage) {
        if (photoDTOPage == null) {
            return new Page<>();
        }

        // 创建新的分页对象
        Page<PhotoVO> voPage = new Page<>();
        voPage.setCurrent(photoDTOPage.getCurrent());
        voPage.setSize(photoDTOPage.getSize());
        voPage.setTotal(photoDTOPage.getTotal());

        // 转换记录
        List<PhotoVO> voList = photoDTOPage.getRecords().stream()
                .map(this::convertToPhotoVO)
                .collect(Collectors.toList());

        voPage.setRecords(voList);
        return voPage;
    }

    /**
     * 将PhotoDTO转换为PhotoVO
     */
    private PhotoVO convertToPhotoVO(PhotoDTO dto) {
        if (dto == null) {
            return null;
        }

        PhotoVO vo = new PhotoVO();
        vo.setId(dto.getId());
        vo.setUserId(dto.getUserId());
        vo.setTitle(dto.getTitle());
        vo.setDescription(dto.getDescription());
        vo.setUrl(dto.getUrl());
        vo.setThumbnailUrl(dto.getThumbnailUrl());
        vo.setLocation(dto.getLocation());

        // 设置状态，默认为已发布(1)
        // PhotoDTO中没有status字段，使用默认值
        vo.setStatus(1);

        vo.setViewCount(dto.getViewCount());
        vo.setLikeCount(dto.getLikeCount());
        vo.setCommentCount(dto.getCommentCount());
        vo.setCollectCount(dto.getCollectCount());
        vo.setCreatedAt(dto.getCreatedAt());

        // 设置更新时间，如果不存在则使用创建时间
        // PhotoDTO中没有updatedAt字段，使用createdAt
        vo.setUpdatedAt(dto.getCreatedAt());

        vo.setGroupId(dto.getGroupId());

        // 设置用户信息
        // PhotoDTO中只有username, nickname, avatar字段
        vo.setUserName(dto.getUsername() != null ? dto.getUsername() :
                      (dto.getNickname() != null ? dto.getNickname() : "未知用户"));
        vo.setUserAvatar(dto.getAvatar());

        // 设置是否点赞、收藏
        // PhotoDTO中isLiked和isCollected是Boolean类型
        vo.setIsLiked(dto.getIsLiked() != null ? dto.getIsLiked() : false);
        vo.setIsCollected(dto.getIsCollected() != null ? dto.getIsCollected() : false);

        // 设置照片组信息
        vo.setIsGrouped(dto.getIsGrouped() != null ? dto.getIsGrouped() : false);
        vo.setGroupPhotoCount(dto.getGroupPhotoCount() != null ? dto.getGroupPhotoCount() : 1);

        // 设置同组照片ID列表
        if (dto.getGroupPhotoIds() != null && !dto.getGroupPhotoIds().isEmpty()) {
            vo.setGroupPhotoIds(dto.getGroupPhotoIds());
        } else {
            vo.setGroupPhotoIds(new ArrayList<>());
        }

        // 设置同组照片列表
        if (dto.getGroupPhotos() != null && !dto.getGroupPhotos().isEmpty()) {
            List<PhotoVO> groupPhotoVOs = dto.getGroupPhotos().stream()
                    .map(this::convertToPhotoVO)
                    .collect(Collectors.toList());
            vo.setGroupPhotos(groupPhotoVOs);
        } else {
            vo.setGroupPhotos(new ArrayList<>());
        }

        return vo;
    }

    /**
     * 计算热门照片并缓存
     */
    private void calculateHotPhotos(int days, String cacheKey) {
        if (redisTemplate == null) {
            log.warn("Redis不可用，无法缓存热门照片");
            return;
        }

        log.info("计算最近{}天的热门照片", days);

        // 获取最近days天的照片
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        LambdaQueryWrapper<Photo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(Photo::getCreatedAt, startTime)
                .eq(Photo::getStatus, 1)
                .eq(Photo::getIsDeleted, 0);

        List<Photo> photos = photoMapper.selectList(queryWrapper);

        // 计算每张照片的热门分数
        for (Photo photo : photos) {
            double score = calculatePhotoHotScore(photo.getId());
            redisTemplate.opsForZSet().add(cacheKey, photo.getId(), score);
        }

        // 设置缓存过期时间
        redisTemplate.expire(cacheKey, 1, TimeUnit.HOURS);
    }

    /**
     * 计算照片的热门分数
     * 分数 = 点赞数 * 1 + 评论数 * 2 + 收藏数 * 3 + 浏览数 * 0.1
     * 并根据时间衰减
     */
    private double calculatePhotoHotScore(Long photoId) {
        // 获取照片的点赞数、评论数、收藏数、浏览数
        int likeCount = photoLikeMapper.countByPhotoId(photoId);
        int commentCount = photoCommentMapper.countByPhotoId(photoId);
        int collectCount = photoCollectMapper.countByPhotoId(photoId);
        int viewCount = photoMapper.getViewCount(photoId);

        // 计算基础分数
        double score = likeCount * 1.0 + commentCount * 2.0 + collectCount * 3.0 + viewCount * 0.1;

        // 获取照片发布时间
        Photo photo = photoMapper.selectById(photoId);
        if (photo != null) {
            // 根据时间计算衰减因子，越新的照片分数越高
            long photoTime = photo.getCreatedAt().toEpochSecond(ZoneOffset.UTC);
            long now = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC);
            long diff = now - photoTime;
            double decay = Math.exp(-diff / (7 * 24 * 3600.0)); // 一周的衰减因子

            // 应用衰减因子
            score = score * decay;
        }

        return score;
    }

    @Override
    public IPage<PhotoVO> getRecommendedPhotos(Long userId, int page, int size) {
        log.info("为用户{}推荐照片，页码：{}，每页大小：{}", userId, page, size);

        // 获取用户兴趣标签
        List<String> interestTags = getUserInterestTags(userId, 10);

        if (interestTags.isEmpty()) {
            // 如果没有兴趣标签，返回热门照片
            return getHotPhotos(page, size);
        }

        // 根据兴趣标签查询照片（包含用户信息）
        int offset = (page - 1) * size;
        List<PhotoDTO> photoDTOs = photoMapper.getPhotosByTagsWithUserInfo(interestTags, userId, offset, size);

        // 创建分页对象
        Page<PhotoVO> photoPage = new Page<>(page, size);
        List<PhotoVO> photoVOs = photoDTOs.stream()
                .map(this::convertToPhotoVO)
                .collect(Collectors.toList());

        photoPage.setRecords(photoVOs);
        photoPage.setTotal(photoMapper.countPhotosByTags(interestTags));

        return photoPage;
    }

    @Override
    public IPage<PhotoVO> getHomeRecommendations(Long userId, int page, int size) {
        log.info("为用户{}获取首页推荐，页码：{}，每页大小：{}", userId, page, size);

        try {
            // 混合推荐策略：30%关注的人的照片，30%兴趣推荐，40%热门照片
            int followingSize = (int) (size * 0.3);
            int interestSize = (int) (size * 0.3);
            int hotSize = size - followingSize - interestSize;

            List<PhotoVO> mixedPhotos = new ArrayList<>();

            try {
                // 获取关注的人的照片
                IPage<PhotoVO> followingPhotos = getFollowingPhotos(userId, 1, followingSize);
                mixedPhotos.addAll(followingPhotos.getRecords());
            } catch (Exception e) {
                log.warn("获取关注的人的照片失败: {}", e.getMessage());
            }

            try {
                // 获取兴趣推荐的照片
                IPage<PhotoVO> interestPhotos = getRecommendedPhotos(userId, 1, interestSize);
                mixedPhotos.addAll(interestPhotos.getRecords());
            } catch (Exception e) {
                log.warn("获取兴趣推荐的照片失败: {}", e.getMessage());
            }

            try {
                // 获取热门照片
                IPage<PhotoVO> hotPhotos = getHotPhotos(1, hotSize);
                mixedPhotos.addAll(hotPhotos.getRecords());
            } catch (Exception e) {
                log.warn("获取热门照片失败: {}", e.getMessage());
            }

            // 如果没有获取到任何照片，则直接从数据库获取最新照片
            if (mixedPhotos.isEmpty()) {
                log.info("没有获取到任何推荐照片，使用最新照片");
                Page<PhotoDTO> photoPage = new Page<>(page, size);
                // 使用关联查询获取带用户信息的照片列表
                IPage<PhotoDTO> photoDTOPage = photoMapper.selectPhotoPageWithUserInfo(photoPage, userId);
                return convertToPhotoVOPage(photoDTOPage);
            }

            // 按照分组ID组织照片
            Map<String, List<PhotoVO>> groupedPhotos = new HashMap<>();
            List<PhotoVO> ungroupedPhotos = new ArrayList<>();

            // 将照片按照分组ID分类
            for (PhotoVO photo : mixedPhotos) {
                if (photo.getGroupId() != null && !photo.getGroupId().isEmpty()) {
                    // 有分组ID的照片
                    if (!groupedPhotos.containsKey(photo.getGroupId())) {
                        groupedPhotos.put(photo.getGroupId(), new ArrayList<>());
                    }
                    groupedPhotos.get(photo.getGroupId()).add(photo);
                } else {
                    // 没有分组ID的照片
                    ungroupedPhotos.add(photo);
                }
            }

            // 处理分组照片
            List<PhotoVO> resultPhotos = new ArrayList<>();

            // 处理有分组的照片
            for (Map.Entry<String, List<PhotoVO>> entry : groupedPhotos.entrySet()) {
                List<PhotoVO> group = entry.getValue();
                if (group.size() > 0) {
                    // 按照创建时间排序
                    group.sort(Comparator.comparing(PhotoVO::getCreatedAt));

                    // 使用第一张照片作为组的代表
                    PhotoVO groupRepresentative = group.get(0);
                    groupRepresentative.setIsGrouped(true);
                    groupRepresentative.setGroupPhotoCount(group.size());

                    // 设置同组照片
                    groupRepresentative.setGroupPhotos(group);

                    // 设置同组照片ID列表
                    List<Long> groupPhotoIds = group.stream()
                            .map(PhotoVO::getId)
                            .collect(Collectors.toList());
                    groupRepresentative.setGroupPhotoIds(groupPhotoIds);

                    // 添加到结果列表
                    resultPhotos.add(groupRepresentative);
                }
            }

            // 添加未分组的照片
            resultPhotos.addAll(ungroupedPhotos);

            // 随机打乱顺序
            Collections.shuffle(resultPhotos);

            // 创建分页对象
            Page<PhotoVO> resultPage = new Page<>(page, size);

            // 如果结果数量超过了请求的大小，截取前size个
            if (resultPhotos.size() > size) {
                resultPhotos = resultPhotos.subList(0, size);
            }

            resultPage.setRecords(resultPhotos);
            resultPage.setTotal(resultPhotos.size());

            return resultPage;
        } catch (Exception e) {
            log.error("获取首页推荐失败: {}", e.getMessage(), e);
            // 返回空结果
            return new Page<>(page, size);
        }
    }

    @Override
    @Transactional
    public void updateUserInterestModel(Long userId) {
        log.info("更新用户{}的兴趣模型", userId);

        // 获取用户的行为数据
        List<UserBehavior> behaviors = userBehaviorMapper.getUserBehaviors(userId, 100);

        // 统计用户对各个标签的兴趣度
        Map<String, Double> tagScores = new HashMap<>();

        for (UserBehavior behavior : behaviors) {
            // 获取照片的标签
            List<PhotoTag> photoTags = photoTagMapper.getPhotoTags(behavior.getPhotoId());

            // 根据行为类型计算权重
            double weight = getWeightByBehaviorType(behavior.getBehaviorType());

            // 更新标签分数
            for (PhotoTag photoTag : photoTags) {
                String tag = photoTag.getName();
                tagScores.put(tag, tagScores.getOrDefault(tag, 0.0) + weight);
            }
        }

        // 将用户兴趣模型存入Redis
        if (redisTemplate != null) {
            try {
                String interestKey = USER_INTEREST_KEY_PREFIX + userId;
                redisTemplate.delete(interestKey);

                for (Map.Entry<String, Double> entry : tagScores.entrySet()) {
                    redisTemplate.opsForZSet().add(interestKey, entry.getKey(), entry.getValue());
                }

                // 设置过期时间
                redisTemplate.expire(interestKey, CACHE_DAYS, TimeUnit.DAYS);
            } catch (Exception e) {
                log.warn("Redis操作失败，无法更新用户兴趣模型: {}", e.getMessage());
            }
        } else {
            log.warn("Redis不可用，无法缓存用户兴趣模型");
        }
    }

    /**
     * 根据行为类型获取权重
     */
    private double getWeightByBehaviorType(String behaviorType) {
        switch (behaviorType) {
            case "view":
                return 1.0;
            case "like":
                return 2.0;
            case "comment":
                return 3.0;
            case "collect":
                return 4.0;
            default:
                return 0.5;
        }
    }

    @Override
    @Transactional
    public void recordUserBehavior(Long userId, Long photoId, String behavior) {
        log.info("记录用户{}对照片{}的{}行为", userId, photoId, behavior);

        // 检查用户ID是否为空
        if (userId == null) {
            log.warn("用户ID为空，无法记录用户行为");
            return;
        }

        try {
            // 记录到数据库
            UserBehavior userBehavior = new UserBehavior();
            userBehavior.setUserId(userId);
            userBehavior.setPhotoId(photoId);
            userBehavior.setBehaviorType(behavior);
            userBehavior.setBehaviorTime(LocalDateTime.now());
            userBehaviorMapper.insert(userBehavior);

            if (redisTemplate != null) {
                try {
                    // 尝试记录到Redis，用于实时更新用户兴趣模型
                    String behaviorKey = USER_BEHAVIOR_KEY_PREFIX + userId;
                    redisTemplate.opsForList().leftPush(behaviorKey, photoId + ":" + behavior);
                    redisTemplate.expire(behaviorKey, CACHE_DAYS, TimeUnit.DAYS);

                    // 如果行为次数达到阈值，更新用户兴趣模型
                    if (redisTemplate.opsForList().size(behaviorKey) >= 10) {
                        updateUserInterestModel(userId);
                    }
                } catch (Exception e) {
                    log.warn("Redis操作失败，无法记录用户行为到缓存: {}", e.getMessage());
                    // Redis失败不影响主要功能，继续执行
                }
            }
        } catch (Exception e) {
            log.error("记录用户行为失败: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，因为这是一个@Transactional方法
        }
    }



    @Override
    @Cacheable(value = "user_interest_tags", key = "#userId + '_' + #limit", unless = "#result == null || #result.isEmpty()")
    public List<String> getUserInterestTags(Long userId, int limit) {
        log.info("获取用户{}的兴趣标签，限制数量：{}", userId, limit);

        try {
            // 尝试从Redis获取用户兴趣标签
            if (redisTemplate != null) {
                try {
                    String interestKey = USER_INTEREST_KEY_PREFIX + userId;
                    Set<ZSetOperations.TypedTuple<Object>> tagTuples = redisTemplate.opsForZSet()
                            .reverseRangeWithScores(interestKey, 0, limit - 1);

                    if (tagTuples != null && !tagTuples.isEmpty()) {
                        // 提取标签名称
                        return tagTuples.stream()
                                .map(tuple -> tuple.getValue().toString())
                                .collect(Collectors.toList());
                    }

                    // 如果Redis中没有，则尝试从数据库中获取用户行为数据并计算
                    try {
                        updateUserInterestModel(userId);

                        // 再次尝试从Redis获取
                        tagTuples = redisTemplate.opsForZSet().reverseRangeWithScores(interestKey, 0, limit - 1);
                        if (tagTuples != null && !tagTuples.isEmpty()) {
                            // 提取标签名称
                            return tagTuples.stream()
                                    .map(tuple -> tuple.getValue().toString())
                                    .collect(Collectors.toList());
                        }
                    } catch (Exception e) {
                        log.warn("更新用户兴趣模型失败: {}", e.getMessage());
                    }
                } catch (Exception e) {
                    log.warn("Redis操作失败，无法获取用户兴趣标签: {}", e.getMessage());
                }
            }

            // 如果Redis操作失败或没有数据，返回热门标签
            return photoTagMapper.getPopularTags(limit).stream()
                    .map(tag -> tag.getName())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户兴趣标签失败: {}", e.getMessage(), e);
            // 返回空列表
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getUserInterestTags(Long userId) {
        return getUserInterestTags(userId, 20);
    }

    @Override
    public IPage<PhotoNoteDTO> getRecommendedPhotoNotes(Integer page, Integer size, Long userId, Long lastId) {
        log.info("为用户{}推荐照片笔记，页码：{}，大小：{}，lastId：{}", userId, page, size, lastId);

        try {
            if (userId == null) {
                // 未登录用户，返回热门内容
                return getHotPhotoNotesForGuest(page, size, lastId);
            }

            // 获取用户兴趣标签
            List<String> interestTags = getUserInterestTags(userId, 10);

            // 获取用户关注的用户ID列表
            List<Long> followingIds = userFollowMapper.getFollowingIds(userId);

            // 混合推荐策略
            Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
            return photoNoteMapper.selectRecommendedPhotoNotes(pageParam, userId, interestTags, followingIds, lastId);

        } catch (Exception e) {
            log.error("获取推荐照片笔记失败", e);
            // 返回热门内容作为备选
            return getHotPhotoNotesForGuest(page, size, lastId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserInterestTags(Long userId, List<String> tags) {
        log.info("更新用户{}的兴趣标签：{}", userId, tags);

        try {
            // 删除用户现有的兴趣标签
            userInterestTagMapper.deleteByUserId(userId);

            if (tags != null && !tags.isEmpty()) {
                // 插入新的兴趣标签
                List<UserInterestTag> userInterestTags = new ArrayList<>();
                for (int i = 0; i < tags.size(); i++) {
                    UserInterestTag userInterestTag = new UserInterestTag();
                    userInterestTag.setUserId(userId);
                    userInterestTag.setTagName(tags.get(i));
                    userInterestTag.setInterestScore(1.0 - i * 0.1); // 按顺序递减分数
                    userInterestTag.setCreatedAt(LocalDateTime.now());
                    userInterestTag.setUpdatedAt(LocalDateTime.now());
                    userInterestTags.add(userInterestTag);
                }

                // 批量插入
                userInterestTagMapper.batchInsertOrUpdate(userInterestTags);
            }

            return true;
        } catch (Exception e) {
            log.error("更新用户兴趣标签失败", e);
            throw e;
        }
    }



    @Override
    public RecommendConfig getRecommendConfig() {
        RecommendConfig config = new RecommendConfig();
        config.setUserBehaviorWeight(0.4);
        config.setFollowingWeight(0.3);
        config.setTagInterestWeight(0.2);
        config.setTimeDecayWeight(0.1);
        config.setDiversityFactor(0.3);
        return config;
    }

    /**
     * 为游客获取热门照片笔记
     */
    private IPage<PhotoNoteDTO> getHotPhotoNotesForGuest(Integer page, Integer size, Long lastId) {
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        return photoNoteMapper.selectHotPhotoNotes(pageParam, 7, null, lastId);
    }

    /**
     * 异步更新用户兴趣模型
     */
    private void updateUserInterestModelAsync(Long userId) {
        // 这里可以使用线程池或消息队列来异步处理
        // 为了简化，这里直接调用同步方法
        try {
            updateUserInterestModelForPhotoNotes(userId);
        } catch (Exception e) {
            log.error("异步更新用户兴趣模型失败", e);
        }
    }

    /**
     * 更新用户兴趣模型（针对照片笔记）
     */
    private void updateUserInterestModelForPhotoNotes(Long userId) {
        log.info("更新用户{}的照片笔记兴趣模型", userId);

        try {
            // 获取用户最近的行为数据
            List<UserBehavior> behaviors = userBehaviorMapper.getUserBehaviors(userId, 100);

            // 统计用户对各个标签的兴趣度
            Map<String, Double> tagScores = new HashMap<>();

            for (UserBehavior behavior : behaviors) {
                // 获取照片笔记的标签
                List<String> tags = photoNoteMapper.getPhotoNoteTags(behavior.getPhotoId());

                // 根据行为类型计算权重
                double weight = getWeightByBehaviorType(behavior.getBehaviorType());

                // 更新标签分数
                for (String tag : tags) {
                    tagScores.put(tag, tagScores.getOrDefault(tag, 0.0) + weight);
                }
            }

            // 转换为用户兴趣标签列表
            List<String> topTags = tagScores.entrySet().stream()
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .limit(20)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            // 更新用户兴趣标签
            if (!topTags.isEmpty()) {
                updateUserInterestTags(userId, topTags);
            }

        } catch (Exception e) {
            log.error("更新用户兴趣模型失败", e);
        }
    }

    @Override
    public IPage<PhotoNoteDTO> getHotPhotoNotes(int page, int size) {
        log.info("获取热门照片笔记，页码：{}，每页大小：{}", page, size);
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        // 默认获取最近7天的热门照片笔记
        IPage<PhotoNoteDTO> result = photoNoteMapper.selectHotPhotoNotes(pageParam, 7, null);

        // 填充照片数据
        fillPhotoNoteImages(result.getRecords());

        return result;
    }

    @Override
    public IPage<PhotoNoteDTO> getHotPhotoNotes(int days, int page, int size) {
        log.info("获取最近{}天的热门照片笔记，页码：{}，每页大小：{}", days, page, size);
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        IPage<PhotoNoteDTO> result = photoNoteMapper.selectHotPhotoNotes(pageParam, days, null);

        // 填充照片数据
        fillPhotoNoteImages(result.getRecords());

        return result;
    }

    @Override
    public IPage<PhotoNoteDTO> getFollowingPhotoNotes(Long userId, int page, int size) {
        log.info("获取用户{}关注的人发布的照片笔记，页码：{}，每页大小：{}", userId, page, size);

        if (userId == null) {
            log.warn("用户未登录，无法获取关注的照片笔记");
            return new Page<>(page, size);
        }

        // 获取用户关注的用户ID列表
        List<Long> followingIds = userFollowMapper.getFollowingIds(userId);
        if (followingIds.isEmpty()) {
            log.info("用户{}没有关注任何人", userId);
            return new Page<>(page, size);
        }

        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        IPage<PhotoNoteDTO> result = photoNoteMapper.selectFollowingPhotoNotes(pageParam, followingIds, userId, null);

        // 填充照片数据
        fillPhotoNoteImages(result.getRecords());

        return result;
    }

    @Override
    public IPage<PhotoNoteDTO> getHomePhotoNoteRecommendations(Long userId, int page, int size) {
        log.info("获取用户{}的首页推荐照片笔记，页码：{}，每页大小：{}", userId, page, size);

        try {
            // 混合推荐策略：30%关注的人的照片笔记，30%兴趣推荐，40%热门照片笔记
            int followingSize = (int) (size * 0.3);
            int interestSize = (int) (size * 0.3);
            int hotSize = size - followingSize - interestSize;

            List<PhotoNoteDTO> mixedPhotoNotes = new ArrayList<>();

            try {
                // 获取关注的人的照片笔记
                IPage<PhotoNoteDTO> followingPhotoNotes = getFollowingPhotoNotes(userId, 1, followingSize);
                mixedPhotoNotes.addAll(followingPhotoNotes.getRecords());
            } catch (Exception e) {
                log.warn("获取关注的人的照片笔记失败: {}", e.getMessage());
            }

            try {
                // 获取兴趣推荐的照片笔记
                IPage<PhotoNoteDTO> interestPhotoNotes = getRecommendedPhotoNotes(userId, 1, interestSize);
                mixedPhotoNotes.addAll(interestPhotoNotes.getRecords());
            } catch (Exception e) {
                log.warn("获取兴趣推荐的照片笔记失败: {}", e.getMessage());
            }

            try {
                // 获取热门照片笔记
                IPage<PhotoNoteDTO> hotPhotoNotes = getHotPhotoNotes(1, hotSize);
                mixedPhotoNotes.addAll(hotPhotoNotes.getRecords());
            } catch (Exception e) {
                log.warn("获取热门照片笔记失败: {}", e.getMessage());
            }

            // 如果没有获取到任何照片笔记，则直接从数据库获取最新照片笔记
            if (mixedPhotoNotes.isEmpty()) {
                log.info("没有获取到任何推荐照片笔记，使用最新照片笔记");
                Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
                return photoNoteMapper.selectPhotoNotePage(pageParam, null, userId, 1);
            }

            // 去重并随机打乱
            List<PhotoNoteDTO> uniquePhotoNotes = mixedPhotoNotes.stream()
                    .collect(Collectors.toMap(
                            PhotoNoteDTO::getId,
                            Function.identity(),
                            (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());

            Collections.shuffle(uniquePhotoNotes);

            // 创建分页结果
            Page<PhotoNoteDTO> resultPage = new Page<>(page, size);
            resultPage.setRecords(uniquePhotoNotes.stream().limit(size).collect(Collectors.toList()));
            resultPage.setTotal(uniquePhotoNotes.size());

            return resultPage;

        } catch (Exception e) {
            log.error("获取首页推荐照片笔记失败: {}", e.getMessage(), e);
            // 返回热门照片笔记作为备选
            return getHotPhotoNotes(page, size);
        }
    }

    @Override
    public IPage<PhotoNoteDTO> getRecommendedPhotoNotes(Long userId, int page, int size) {
        log.info("为用户{}推荐照片笔记，页码：{}，每页大小：{}", userId, page, size);

        // 获取用户兴趣标签
        List<String> interestTags = getUserInterestTags(userId, 10);

        if (interestTags.isEmpty()) {
            // 如果没有兴趣标签，返回热门照片笔记
            return getHotPhotoNotes(page, size);
        }

        // 根据兴趣标签查询照片笔记
        Page<PhotoNoteDTO> pageParam = new Page<>(page, size);
        IPage<PhotoNoteDTO> result = photoNoteMapper.selectPhotoNotesByTags(pageParam, interestTags, userId);

        // 填充照片数据
        fillPhotoNoteImages(result.getRecords());

        return result;
    }

    @Override
    public void recordUserBehavior(Long userId, Long noteId, String behaviorType, Double weight) {
        log.info("记录用户{}对照片笔记{}的行为：{}，权重：{}", userId, noteId, behaviorType, weight);

        try {
            // 记录用户行为到数据库
            UserBehavior behavior = new UserBehavior();
            behavior.setUserId(userId);
            behavior.setPhotoId(noteId); // 这里复用photoId字段存储noteId
            behavior.setBehaviorType(behaviorType);
            behavior.setBehaviorTime(LocalDateTime.now());
            userBehaviorMapper.insert(behavior);

            // 异步更新用户兴趣模型
            updateUserInterestModelAsync(userId);

        } catch (Exception e) {
            log.error("记录用户行为失败", e);
        }
    }

    /**
     * 填充照片笔记的图片数据
     */
    private void fillPhotoNoteImages(List<PhotoNoteDTO> photoNotes) {
        if (CollectionUtils.isEmpty(photoNotes)) {
            return;
        }

        for (PhotoNoteDTO photoNote : photoNotes) {
            try {
                // 查询照片列表
                List<PhotoNoteImage> images = photoNoteImageMapper.selectImagesByNoteId(photoNote.getId());
                if (!CollectionUtils.isEmpty(images)) {
                    List<PhotoNoteDTO.PhotoNoteImageDTO> imageDTOs = images.stream()
                            .map(this::convertToImageDTO)
                            .collect(Collectors.toList());
                    photoNote.setImages(imageDTOs);
                }
            } catch (Exception e) {
                log.error("填充照片笔记{}的图片数据失败", photoNote.getId(), e);
                // 设置空列表，避免null
                photoNote.setImages(new ArrayList<>());
            }
        }
    }

    /**
     * 转换图片实体为DTO
     */
    private PhotoNoteDTO.PhotoNoteImageDTO convertToImageDTO(PhotoNoteImage image) {
        PhotoNoteDTO.PhotoNoteImageDTO dto = new PhotoNoteDTO.PhotoNoteImageDTO();
        dto.setPhotoId(image.getPhotoId());
        dto.setSortOrder(image.getSortOrder());

        // 这里需要从Photo表查询图片信息
        Photo photo = photoMapper.selectById(image.getPhotoId());
        if (photo != null) {
            String url = photo.getUrl();
            String thumbnailUrl = photo.getThumbnailUrl();

            // 如果是七牛云私有空间，生成带下载凭证的URL
            try {
                boolean qiniuIsPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", false);
                if (qiniuIsPrivate && qiniuStorageService != null) {
                    // 处理照片URL
                    if (url != null && !url.isEmpty()) {
                        String storagePath = extractStoragePathFromUrl(url, photo.getStoragePath());
                        log.info("推荐服务 - 照片ID: {}, 原始URL: {}, 存储路径: {}, 提取的路径: {}",
                                photo.getId(), url, photo.getStoragePath(), storagePath);
                        String privateUrl = qiniuStorageService.getFileUrl(storagePath);
                        if (privateUrl != null && !privateUrl.isEmpty()) {
                            log.info("推荐服务 - 生成的私有URL: {}", privateUrl);
                            url = privateUrl;
                        }
                    }

                    // 处理缩略图URL
                    if (thumbnailUrl != null && !thumbnailUrl.isEmpty()) {
                        // 缩略图使用原图的存储路径，但需要去掉图片处理参数
                        String storagePath = extractStoragePathFromUrl(thumbnailUrl, photo.getStoragePath());
                        String privateUrl = qiniuStorageService.getFileUrl(storagePath);
                        if (privateUrl != null && !privateUrl.isEmpty()) {
                            // 重新添加缩略图处理参数
                            thumbnailUrl = privateUrl + "?imageView2/2/w/300/h/300/q/80";
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("处理私有空间URL失败: {}", e.getMessage());
                // 不影响业务，继续使用原URL
            }

            dto.setUrl(url);
            dto.setThumbnailUrl(thumbnailUrl);
            dto.setWidth(photo.getWidth());
            dto.setHeight(photo.getHeight());
        }

        return dto;
    }

    /**
     * 从URL中提取存储路径（优先使用数据库中的storage_path）
     */
    private String extractStoragePathFromUrl(String url, String storagePath) {
        // 优先使用数据库中存储的storage_path
        if (storagePath != null && !storagePath.isEmpty()) {
            log.debug("使用数据库存储路径: {}", storagePath);
            return storagePath;
        }

        // 如果storage_path为空，从URL中提取
        if (url == null || url.isEmpty()) {
            return null;
        }

        try {
            // 移除查询参数
            int queryIndex = url.indexOf('?');
            if (queryIndex != -1) {
                url = url.substring(0, queryIndex);
            }

            // 查找域名后的路径部分
            // 例如：http://sw5eg63qc.hn-bkt.clouddn.com/phototagmoment/photos/1/2025/05/27/90682cffacf24fee9e4e1feff76f5dc0
            // 提取：phototagmoment/photos/1/2025/05/27/90682cffacf24fee9e4e1feff76f5dc0
            int domainEndIndex = url.indexOf('/', 8); // 跳过 "http://" 或 "https://"
            if (domainEndIndex != -1 && domainEndIndex < url.length() - 1) {
                String path = url.substring(domainEndIndex + 1);
                log.debug("从URL提取存储路径: {}", path);
                return path;
            }

            // 如果无法提取路径，返回文件名
            int lastSlashIndex = url.lastIndexOf('/');
            if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
                String fileName = url.substring(lastSlashIndex + 1);
                log.debug("提取文件名: {}", fileName);
                return fileName;
            }

            return url;
        } catch (Exception e) {
            log.warn("提取存储路径失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从URL中提取文件名（保留原方法以备其他地方使用）
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }
        // 移除查询参数
        int queryIndex = url.indexOf('?');
        if (queryIndex != -1) {
            url = url.substring(0, queryIndex);
        }
        // 提取文件名
        int lastSlashIndex = url.lastIndexOf('/');
        if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
            return url.substring(lastSlashIndex + 1);
        }
        return url;
    }
}
