package com.phototagmoment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统参数配置实体类
 */
@Data
@TableName("ptm_system_config")
public class SystemConfig {

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置键
     */
    private String configKey;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置类型（string, number, boolean, json）
     */
    private String configType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否系统内置（0否 1是）
     */
    private Boolean isSystem;

    /**
     * 状态（0禁用 1启用）
     * 注意：数据库中不存在此字段，仅用于程序逻辑
     */
    @TableField(exist = false)
    private Boolean status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 注意：数据库表中可能没有is_deleted字段，所以这里注释掉
    // /**
    //  * 是否删除（0否 1是）
    //  */
    // @TableLogic
    // private Boolean isDeleted;
}
