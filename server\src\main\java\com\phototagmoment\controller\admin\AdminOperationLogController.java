package com.phototagmoment.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.entity.AdminOperationLog;
import com.phototagmoment.service.AdminOperationLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 管理员操作日志控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system/log")
@Tag(name = "管理员操作日志", description = "管理员操作日志相关接口")
public class AdminOperationLogController {

    @Autowired
    private AdminOperationLogService adminOperationLogService;

    /**
     * 获取操作日志列表
     *
     * @param page      页码
     * @param pageSize  每页条数
     * @param keyword   关键字
     * @param module    模块
     * @param operation 操作类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 操作日志列表
     */
    @GetMapping("/operation")
    @Operation(summary = "获取操作日志列表", description = "获取操作日志列表")
    public Result<IPage<AdminOperationLog>> list(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "模块") @RequestParam(required = false) String module,
            @Parameter(description = "操作类型") @RequestParam(required = false) String operation,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {
        IPage<AdminOperationLog> logs = adminOperationLogService.getOperationLogList(
                page, pageSize, keyword, module, operation, startDate, endDate);
        return Result.success(logs);
    }

    /**
     * 导出操作日志
     *
     * @param keyword   关键字
     * @param module    模块
     * @param operation 操作类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     */
    @GetMapping("/operation/export")
    @Operation(summary = "导出操作日志", description = "导出操作日志")
    public void export(
            @Parameter(description = "关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "模块") @RequestParam(required = false) String module,
            @Parameter(description = "操作类型") @RequestParam(required = false) String operation,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate,
            HttpServletResponse response) {
        adminOperationLogService.exportOperationLog(keyword, module, operation, startDate, endDate, response);
    }

    /**
     * 清空操作日志
     *
     * @return 是否成功
     */
    @DeleteMapping("/operation/clear")
    @Operation(summary = "清空操作日志", description = "清空操作日志")
    public Result<Boolean> clear() {
        boolean result = adminOperationLogService.clearOperationLog();
        return Result.success(result);
    }
}
