# PhotoTagMoment 下一阶段开发计划

## 📋 **项目现状总结**

### ✅ **已完成的核心功能**
1. **照片笔记功能** - 完整实现（1-9张照片、Tag、@用户、九宫格布局）
2. **用户认证系统** - JWT认证、第三方登录、权限控制
3. **基础社交功能** - 点赞、评论、收藏、关注系统
4. **通知系统** - WebSocket实时通知、@用户通知
5. **管理后台** - 基础架构、照片笔记管理、评论管理、文件统计
6. **搜索功能** - Tag搜索、用户搜索基础框架

### 🔄 **部分完成功能**
1. **用户端界面** - 基础框架完成，部分页面使用模拟数据
2. **搜索功能** - 后端API完成，前端界面需要完善
3. **消息系统** - 通知框架完成，私信功能未实现
4. **用户主页** - 基础结构完成，详细功能待完善

### ❌ **待开发功能**
1. **用户端完整界面** - 首页、个人中心、搜索页面等
2. **私信聊天系统** - 实时聊天功能
3. **高级搜索功能** - 综合搜索、搜索历史
4. **个性化推荐** - 首页内容推荐算法
5. **用户个人中心** - 完整的个人资料管理

## 🎯 **开发优先级确定**

### **第一优先级：用户端核心界面完善** 🔥
**原因**：照片笔记功能已完成，但用户端界面大部分使用模拟数据，影响整体用户体验

### **第二优先级：搜索功能完善** 🔥
**原因**：Tag和@用户功能已实现，搜索是内容发现的关键功能

### **第三优先级：个人中心功能完善** 🔥
**原因**：用户管理个人内容和设置的核心功能

## 📋 **阶段一：用户端核心界面完善（2-3周）**

### **1.1 首页照片流界面优化（3-4天）**

#### **技术实现方案**
```typescript
// 1. 首页API集成
// user/src/api/home.ts
export interface HomePhotoNote {
  id: number
  title?: string
  content: string
  processedContent: string
  photos: PhotoInfo[]
  user: UserInfo
  stats: {
    likeCount: number
    commentCount: number
    viewCount: number
  }
  isLiked: boolean
  isCollected: boolean
  createdAt: string
  tags: string[]
}

export function getHomePhotoNotes(params: {
  page: number
  size: number
  type?: 'recommend' | 'following' | 'latest'
}) {
  return request<{
    records: HomePhotoNote[]
    total: number
    hasMore: boolean
  }>({
    url: '/api/photo-notes/home',
    method: 'get',
    params
  })
}
```

#### **前端组件优化**
```vue
<!-- user/src/views/home/<USER>
<template>
  <div class="home-container">
    <!-- 顶部导航 -->
    <div class="home-header">
      <div class="tab-bar">
        <div 
          v-for="tab in tabs" 
          :key="tab.key"
          :class="['tab-item', { active: currentTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- 照片流 -->
    <div class="photo-stream">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <PhotoNoteCard
            v-for="note in photoNotes"
            :key="note.id"
            :note="note"
            @like="handleLike"
            @collect="handleCollect"
            @comment="handleComment"
          />
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getHomePhotoNotes, type HomePhotoNote } from '@/api/home'
import PhotoNoteCard from '@/components/photo/PhotoNoteCard.vue'

// 数据状态
const photoNotes = ref<HomePhotoNote[]>([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const currentTab = ref('recommend')
const page = ref(1)

// 标签页配置
const tabs = [
  { key: 'recommend', label: '推荐' },
  { key: 'following', label: '关注' },
  { key: 'latest', label: '最新' }
]

// 加载数据
const loadData = async (isRefresh = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    const response = await getHomePhotoNotes({
      page: isRefresh ? 1 : page.value,
      size: 10,
      type: currentTab.value as any
    })
    
    if (isRefresh) {
      photoNotes.value = response.data.records
      page.value = 1
    } else {
      photoNotes.value.push(...response.data.records)
    }
    
    finished.value = !response.data.hasMore
    page.value++
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 事件处理
const onLoad = () => loadData()
const onRefresh = () => loadData(true)
const switchTab = (tab: string) => {
  currentTab.value = tab
  page.value = 1
  finished.value = false
  loadData(true)
}

onMounted(() => {
  loadData(true)
})
</script>
```

### **1.2 照片笔记详情页优化（2-3天）**

#### **技术实现方案**
```typescript
// user/src/api/photoNote.ts
export interface PhotoNoteDetail extends HomePhotoNote {
  comments: CommentInfo[]
  relatedNotes: HomePhotoNote[]
  location?: LocationInfo
}

export function getPhotoNoteDetail(noteId: number) {
  return request<PhotoNoteDetail>({
    url: `/api/photo-notes/${noteId}`,
    method: 'get'
  })
}

export function likePhotoNote(noteId: number) {
  return request<boolean>({
    url: `/api/photo-notes/${noteId}/like`,
    method: 'post'
  })
}

export function collectPhotoNote(noteId: number) {
  return request<boolean>({
    url: `/api/photo-notes/${noteId}/collect`,
    method: 'post'
  })
}
```

### **1.3 用户主页界面完善（3-4天）**

#### **技术实现方案**
```typescript
// user/src/api/userProfile.ts
export interface UserProfile {
  id: number
  username: string
  nickname: string
  avatar: string
  bio?: string
  location?: string
  website?: string
  stats: {
    photoCount: number
    followerCount: number
    followingCount: number
    likeCount: number
  }
  isFollowing: boolean
  isBlocked: boolean
  joinDate: string
}

export interface UserPhotoNotes {
  records: HomePhotoNote[]
  total: number
  hasMore: boolean
}

export function getUserProfile(userId: number) {
  return request<UserProfile>({
    url: `/api/users/${userId}/profile`,
    method: 'get'
  })
}

export function getUserPhotoNotes(userId: number, params: {
  page: number
  size: number
  type?: 'all' | 'liked' | 'collected'
}) {
  return request<UserPhotoNotes>({
    url: `/api/users/${userId}/photo-notes`,
    method: 'get',
    params
  })
}

export function followUser(userId: number) {
  return request<boolean>({
    url: `/api/users/${userId}/follow`,
    method: 'post'
  })
}
```

## 📋 **阶段二：搜索功能完善（1-2周）**

### **2.1 综合搜索界面（3-4天）**

#### **技术实现方案**
```typescript
// user/src/api/search.ts
export interface SearchResult {
  photoNotes: HomePhotoNote[]
  users: UserProfile[]
  tags: TagInfo[]
  total: number
  hasMore: boolean
}

export interface SearchHistory {
  id: number
  keyword: string
  type: 'keyword' | 'tag' | 'user'
  searchTime: string
}

export function searchAll(params: {
  keyword: string
  page: number
  size: number
  type?: 'all' | 'photo' | 'user' | 'tag'
}) {
  return request<SearchResult>({
    url: '/api/search',
    method: 'get',
    params
  })
}

export function getSearchHistory() {
  return request<SearchHistory[]>({
    url: '/api/search/history',
    method: 'get'
  })
}

export function getHotSearches() {
  return request<string[]>({
    url: '/api/search/hot',
    method: 'get'
  })
}
```

### **2.2 Tag搜索页面优化（2-3天）**

#### **现有Tag搜索功能增强**
- 添加相关Tag推荐
- 优化搜索结果展示
- 添加Tag关注功能
- 实现Tag热度趋势图

## 📋 **阶段三：个人中心功能完善（1-2周）**

### **3.1 个人设置页面（2-3天）**

#### **技术实现方案**
```typescript
// user/src/api/settings.ts
export interface UserSettings {
  privacy: {
    profileVisibility: 'public' | 'friends' | 'private'
    photoVisibility: 'public' | 'friends' | 'private'
    allowComments: boolean
    allowMentions: boolean
  }
  notifications: {
    likeNotification: boolean
    commentNotification: boolean
    followNotification: boolean
    mentionNotification: boolean
    emailNotification: boolean
  }
  account: {
    phone?: string
    email?: string
    isPhoneVerified: boolean
    isEmailVerified: boolean
  }
}

export function getUserSettings() {
  return request<UserSettings>({
    url: '/api/user/settings',
    method: 'get'
  })
}

export function updateUserSettings(settings: Partial<UserSettings>) {
  return request<boolean>({
    url: '/api/user/settings',
    method: 'put',
    data: settings
  })
}
```

### **3.2 个人资料编辑（2-3天）**

#### **功能包含**
- 头像上传和裁剪
- 个人信息编辑
- 社交链接管理
- 隐私设置

## 📋 **阶段四：高级功能开发（2-3周）**

### **4.1 私信聊天系统（1周）**

#### **技术实现方案**
```typescript
// user/src/api/message.ts
export interface ChatMessage {
  id: number
  senderId: number
  receiverId: number
  content: string
  type: 'text' | 'image' | 'photo_note'
  isRead: boolean
  createdAt: string
}

export interface ChatConversation {
  id: number
  user: UserProfile
  lastMessage: ChatMessage
  unreadCount: number
  updatedAt: string
}

export function getChatConversations() {
  return request<ChatConversation[]>({
    url: '/api/messages/conversations',
    method: 'get'
  })
}

export function getChatMessages(userId: number, params: {
  page: number
  size: number
}) {
  return request<{
    records: ChatMessage[]
    hasMore: boolean
  }>({
    url: `/api/messages/chat/${userId}`,
    method: 'get',
    params
  })
}

export function sendMessage(data: {
  receiverId: number
  content: string
  type: 'text' | 'image' | 'photo_note'
}) {
  return request<ChatMessage>({
    url: '/api/messages/send',
    method: 'post',
    data
  })
}
```

### **4.2 个性化推荐算法（1周）**

#### **推荐策略**
1. **基于用户行为** - 点赞、评论、收藏历史
2. **基于关注关系** - 关注用户的内容
3. **基于Tag兴趣** - 用户常用Tag
4. **基于时间热度** - 最新热门内容

### **4.3 高级搜索功能（3-4天）**

#### **功能包含**
- 搜索过滤器（时间、用户、Tag）
- 搜索历史管理
- 搜索建议和自动补全
- 保存搜索条件

## 🛠️ **技术要求**

### **代码规范**
1. **TypeScript严格模式** - 所有新代码必须有完整类型定义
2. **组件化开发** - 可复用组件抽取
3. **API统一管理** - 统一的请求封装和错误处理
4. **响应式设计** - 移动端优先，PC端适配

### **性能优化**
1. **图片懒加载** - 照片流性能优化
2. **虚拟滚动** - 大列表性能优化
3. **缓存策略** - 合理的数据缓存
4. **代码分割** - 路由级别的代码分割

### **用户体验**
1. **加载状态** - 友好的加载提示
2. **错误处理** - 完善的错误提示和重试机制
3. **离线支持** - 基础的离线功能
4. **无障碍访问** - 基础的无障碍支持

## 📅 **时间安排**

### **总体时间：6-8周**

| 阶段 | 功能 | 时间 | 优先级 |
|------|------|------|--------|
| 阶段一 | 用户端核心界面完善 | 2-3周 | 🔥 高 |
| 阶段二 | 搜索功能完善 | 1-2周 | 🔥 高 |
| 阶段三 | 个人中心功能完善 | 1-2周 | 🔥 高 |
| 阶段四 | 高级功能开发 | 2-3周 | 🔶 中 |

## 🎯 **预期成果**

### **用户体验提升**
1. **完整的用户端体验** - 从注册到使用的完整流程
2. **流畅的内容发现** - 高效的搜索和推荐
3. **丰富的社交互动** - 完善的用户交互功能

### **技术架构完善**
1. **前后端完全分离** - 标准化的API接口
2. **高性能前端应用** - 优化的加载和渲染性能
3. **可扩展的架构** - 便于后续功能扩展

### **商业化准备**
1. **生产就绪状态** - 可直接部署的完整应用
2. **用户增长基础** - 完善的用户体验和功能
3. **数据分析支持** - 完整的用户行为数据收集

---

**制定时间**: 2025-05-23  
**版本**: V4.0.0 开发计划  
**状态**: 📋 待执行  
**负责人**: 开发团队
