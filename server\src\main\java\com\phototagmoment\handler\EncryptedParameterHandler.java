package com.phototagmoment.handler;

import com.phototagmoment.annotation.Encrypted;
import com.phototagmoment.service.EncryptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.Collection;
import java.util.Map;

/**
 * 加密参数处理器
 * 拦截MyBatis参数设置，对标记为@Encrypted的字段进行加密
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = ParameterHandler.class, method = "setParameters", args = {PreparedStatement.class})
})
public class EncryptedParameter<PERSON>andler implements Interceptor {

    @Autowired
    private EncryptionService encryptionService;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();

        // 获取参数对象
        Object parameterObject = getParameterObject(parameterHandler);
        if (parameterObject == null) {
            return invocation.proceed();
        }

        // 获取SQL命令类型
        SqlCommandType sqlCommandType = getSqlCommandType(parameterHandler);
        if (sqlCommandType == SqlCommandType.INSERT || sqlCommandType == SqlCommandType.UPDATE) {
            encryptObject(parameterObject);
        }

        return invocation.proceed();
    }

    /**
     * 获取参数对象
     *
     * @param parameterHandler 参数处理器
     * @return 参数对象
     */
    private Object getParameterObject(ParameterHandler parameterHandler) {
        try {
            Field parameterObjectField = parameterHandler.getClass().getDeclaredField("parameterObject");
            parameterObjectField.setAccessible(true);
            return parameterObjectField.get(parameterHandler);
        } catch (Exception e) {
            log.error("获取参数对象失败", e);
            return null;
        }
    }

    /**
     * 获取SQL命令类型
     *
     * @param parameterHandler 参数处理器
     * @return SQL命令类型
     */
    private SqlCommandType getSqlCommandType(ParameterHandler parameterHandler) {
        try {
            Field mappedStatementField = parameterHandler.getClass().getDeclaredField("mappedStatement");
            mappedStatementField.setAccessible(true);
            MappedStatement mappedStatement = (MappedStatement) mappedStatementField.get(parameterHandler);
            return mappedStatement.getSqlCommandType();
        } catch (Exception e) {
            log.error("获取SQL命令类型失败", e);
            return null;
        }
    }

    /**
     * 加密对象中的加密字段
     *
     * @param obj 对象
     */
    private void encryptObject(Object obj) {
        if (obj == null) {
            return;
        }

        try {
            Class<?> clazz = obj.getClass();

            // 跳过基本类型、字符串和集合类型
            if (clazz.isPrimitive() || clazz == String.class ||
                Collection.class.isAssignableFrom(clazz) ||
                Map.class.isAssignableFrom(clazz)) {
                return;
            }

            List<Field> fields = getAllFields(clazz);

            for (Field field : fields) {
                if (field.isAnnotationPresent(Encrypted.class) && field.getType() == String.class) {
                    field.setAccessible(true);
                    try {
                        String value = (String) field.get(obj);
                        if (StringUtils.hasText(value)) {
                            String encryptedValue = encryptionService.encryptUserData(field.getName(), value);
                            field.set(obj, encryptedValue);
                        }
                    } catch (Exception e) {
                        log.error("加密字段失败: {}.{}", clazz.getSimpleName(), field.getName(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("加密对象失败: {}", obj.getClass().getSimpleName(), e);
        }
    }

    /**
     * 获取类的所有字段，包括父类字段
     *
     * @param clazz 类
     * @return 字段列表
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                fields.add(field);
            }
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 不需要额外配置
    }
}
