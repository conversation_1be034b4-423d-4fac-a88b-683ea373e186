import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, logout, getUserInfo } from '../api/user'

interface LoginData {
  username: string
  password: string
  remember?: boolean
}

interface SmsLoginData {
  phone: string
  code: string
}

interface UserInfo {
  id: number
  username: string
  avatar: string
  email?: string
  phone?: string
  nickname?: string
  [key: string]: any
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref<boolean>(!!token.value)
  const unreadCount = ref<number>(0)

  // 计算属性
  const userId = ref<number | null>(null)
  const username = ref<string>('')
  const avatar = ref<string>('')

  // 获取用户信息
  const fetchUserInfo = async (): Promise<void> => {
    // 先从localStorage获取token，确保最新
    const localToken = localStorage.getItem('token')
    console.log('从localStorage获取token:', localToken)

    // 如果localStorage中有token但store中没有，先同步
    if (localToken && !token.value) {
      console.log('从localStorage同步token到store')
      token.value = localToken
      isLoggedIn.value = true
    }

    if (!token.value) {
      console.log('没有token，无法获取用户信息')

      // 尝试从localStorage恢复用户信息
      const storedUserInfo = localStorage.getItem('userInfo')
      if (storedUserInfo) {
        try {
          const parsedUserInfo = JSON.parse(storedUserInfo)
          console.log('从localStorage恢复用户信息:', parsedUserInfo)
          userInfo.value = parsedUserInfo
          userId.value = parsedUserInfo.id
          username.value = parsedUserInfo.username
          avatar.value = parsedUserInfo.avatar || ''
          console.log('从localStorage恢复用户信息成功')
        } catch (e) {
          console.error('解析localStorage中的用户信息失败', e)
        }
      }

      return
    }

    try {
      console.log('开始获取用户信息，当前token:', token.value)

      // 添加延迟，确保token已被设置
      console.log('等待300ms确保token已被设置')
      await new Promise(resolve => setTimeout(resolve, 300))

      // 手动设置请求头，确保token被正确传递
      console.log('准备发送获取用户信息请求，手动设置Authorization头')
      const headers = {
        'Authorization': `Bearer ${token.value}`,
        'Content-Type': 'application/json'
      }
      console.log('请求头:', headers)

      // 创建自定义请求配置，确保携带token
      const res = await getUserInfo()
      console.log('获取用户信息响应:', res)

      if (res.code === 200) {
        // 更新用户信息
        userInfo.value = res.data
        userId.value = res.data.id
        username.value = res.data.username
        avatar.value = res.data.avatar || ''

        // 确保登录状态为true
        isLoggedIn.value = true

        // 保存用户信息到localStorage，用于页面刷新后恢复
        localStorage.setItem('userInfo', JSON.stringify(res.data))
        console.log('用户信息已保存到localStorage')

        // 确保token也保存到localStorage
        if (token.value && (!localToken || localToken !== token.value)) {
          localStorage.setItem('token', token.value)
          console.log('token已更新到localStorage')
        }
      } else {
        console.warn('获取用户信息失败:', res.message)

        // 如果是401错误，尝试从localStorage恢复用户信息
        if (res.code === 401) {
          console.log('服务器返回401，尝试从localStorage恢复用户信息')

          const storedUserInfo = localStorage.getItem('userInfo')
          if (storedUserInfo) {
            try {
              const parsedUserInfo = JSON.parse(storedUserInfo)
              console.log('从localStorage恢复用户信息:', parsedUserInfo)
              userInfo.value = parsedUserInfo
              userId.value = parsedUserInfo.id
              username.value = parsedUserInfo.username
              avatar.value = parsedUserInfo.avatar || ''
              console.log('从localStorage恢复用户信息成功')

              // 保持登录状态
              isLoggedIn.value = true
              return
            } catch (e) {
              console.error('解析localStorage中的用户信息失败', e)
            }
          }

          // 如果无法恢复，则清除登录状态
          console.log('无法从localStorage恢复用户信息，清除登录状态')
          resetState()
        }
      }
    } catch (error) {
      console.error('获取用户信息失败', error)

      // 尝试从localStorage恢复用户信息
      const storedUserInfo = localStorage.getItem('userInfo')
      if (storedUserInfo) {
        try {
          const parsedUserInfo = JSON.parse(storedUserInfo)
          console.log('从localStorage恢复用户信息:', parsedUserInfo)
          userInfo.value = parsedUserInfo
          userId.value = parsedUserInfo.id
          username.value = parsedUserInfo.username
          avatar.value = parsedUserInfo.avatar || ''
          console.log('从localStorage恢复用户信息成功')

          // 保持登录状态
          isLoggedIn.value = true
          return
        } catch (e) {
          console.error('解析localStorage中的用户信息失败', e)
        }
      }

      // 如果是401错误，清除登录状态
      if (error instanceof Error &&
          (error.message.includes('暂未登录或token已经过期') ||
           error.message.includes('401'))) {
        console.log('清除无效token和登录状态')
        resetState()
      }
    }
  }

  // 登录
  const loginAction = async (loginData: LoginData): Promise<boolean> => {
    try {
      console.log('开始登录，登录数据:', loginData)

      // 使用从auth.ts导入的login函数，确保使用统一的登录路径
      // 从api/auth.ts导入的login函数使用/auth/login路径
      const res = await import('../api/auth').then(module => {
        console.log('使用auth.ts中的login函数，路径为/auth/login');
        return module.login({
          username: loginData.username,
          password: loginData.password
        });
      });

      console.log('登录响应:', res)

      if (res.code === 200) {
        // 先设置token
        const newToken = res.data.token
        console.log('登录成功，获取到token:', newToken)

        // 确保token被正确设置到localStorage、sessionStorage和Cookie
        localStorage.setItem('token', newToken)
        sessionStorage.setItem('token', newToken)

        // 设置Cookie，过期时间为24小时
        const expirationDate = new Date()
        expirationDate.setTime(expirationDate.getTime() + 24 * 60 * 60 * 1000)
        document.cookie = `token=${newToken}; expires=${expirationDate.toUTCString()}; path=/`
        document.cookie = `Authorization=Bearer ${newToken}; expires=${expirationDate.toUTCString()}; path=/`

        console.log('token已保存到localStorage、sessionStorage和Cookie:', newToken)

        // 然后更新状态
        token.value = newToken
        isLoggedIn.value = true

        // 直接设置用户信息，避免额外的请求
        if (res.data.user) {
          console.log('从登录响应中获取用户信息:', res.data.user)
          userInfo.value = res.data.user
          userId.value = res.data.user.id
          username.value = res.data.user.username
          avatar.value = res.data.user.avatar || ''

          // 保存用户信息到localStorage
          localStorage.setItem('userInfo', JSON.stringify(res.data.user))

          // 已经有用户信息，不需要再次请求
          console.log('已从登录响应获取用户信息，不再请求/user/info')
          return true
        }

        // 添加延迟，确保token已被设置到请求拦截器
        console.log('等待300ms确保token被设置到请求拦截器')
        await new Promise(resolve => setTimeout(resolve, 300))

        // 最后获取用户信息
        console.log('开始获取用户信息')
        try {
          await fetchUserInfo()
          return true
        } catch (error) {
          console.error('获取用户信息失败，但登录已成功，使用本地存储的信息', error)

          // 尝试从localStorage恢复用户信息
          const storedUserInfo = localStorage.getItem('userInfo')
          if (storedUserInfo) {
            try {
              const parsedUserInfo = JSON.parse(storedUserInfo)
              userInfo.value = parsedUserInfo
              userId.value = parsedUserInfo.id
              username.value = parsedUserInfo.username
              avatar.value = parsedUserInfo.avatar || ''
              console.log('从localStorage恢复用户信息成功')
              return true
            } catch (e) {
              console.error('解析localStorage中的用户信息失败', e)
            }
          }

          // 即使获取用户信息失败，登录也是成功的
          return true
        }
      } else {
        console.error('登录失败，服务器返回错误:', res.message)
        return false
      }
    } catch (error) {
      console.error('登录失败，发生异常:', error)
      return false
    }
  }

  // 短信登录
  const smsLoginAction = async (phoneData: SmsLoginData): Promise<boolean> => {
    try {
      // 这里应该调用短信登录API
      // const res = await smsLogin(phoneData)
      // 模拟登录成功
      console.log('短信登录数据:', phoneData)
      const res = { code: 200, data: { token: 'mock-token-' + Date.now() } }

      if (res.code === 200) {
        // 先设置token
        const newToken = res.data.token
        console.log('短信登录成功，获取到token:', newToken)

        // 确保token被正确设置到localStorage
        localStorage.setItem('token', newToken)

        // 然后更新状态
        token.value = newToken
        isLoggedIn.value = true

        // 添加延迟，确保token已被设置到请求拦截器
        console.log('等待100ms确保token被设置到请求拦截器')
        await new Promise(resolve => setTimeout(resolve, 100))

        // 最后获取用户信息
        console.log('开始获取用户信息')
        await fetchUserInfo()
        return true
      }
      return false
    } catch (error) {
      console.error('短信登录失败', error)
      return false
    }
  }

  // 登出
  const logoutAction = async (): Promise<boolean> => {
    try {
      console.log('执行登出操作')
      await logout()
      resetState()
      return true
    } catch (error) {
      console.error('登出失败', error)
      // 即使API调用失败，也重置状态
      resetState()
      return true
    }
  }

  // 设置 Token
  const setToken = (newToken: string): void => {
    if (!newToken) {
      console.warn('尝试设置空token')
      return
    }

    console.log('设置token:', newToken)
    token.value = newToken

    // 保存到localStorage和sessionStorage
    localStorage.setItem('token', newToken)
    sessionStorage.setItem('token', newToken)

    // 设置Cookie，过期时间为24小时
    const expirationDate = new Date()
    expirationDate.setTime(expirationDate.getTime() + 24 * 60 * 60 * 1000)
    document.cookie = `token=${newToken}; expires=${expirationDate.toUTCString()}; path=/`
    document.cookie = `Authorization=Bearer ${newToken}; expires=${expirationDate.toUTCString()}; path=/`

    console.log('token已保存到localStorage、sessionStorage和Cookie:', newToken)
    isLoggedIn.value = true
  }

  // 设置用户信息
  const setUser = (user: UserInfo): void => {
    if (!user) {
      console.warn('尝试设置空用户信息')
      return
    }

    console.log('设置用户信息:', user)
    userInfo.value = user
    userId.value = user.id
    username.value = user.username
    avatar.value = user.avatar

    // 保存用户信息到localStorage，用于页面刷新后恢复
    localStorage.setItem('userInfo', JSON.stringify(user))
  }

  // 设置登录状态
  const setLoggedIn = (status: boolean): void => {
    console.log('设置登录状态:', status)
    isLoggedIn.value = status
  }

  // 重置状态
  const resetState = (): void => {
    console.log('重置用户状态')

    // 清除内存中的状态
    token.value = ''
    userInfo.value = null
    isLoggedIn.value = false
    unreadCount.value = 0
    userId.value = null
    username.value = ''
    avatar.value = ''

    // 清除localStorage和sessionStorage中的数据
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    sessionStorage.removeItem('token')
    sessionStorage.removeItem('userInfo')

    // 清除Cookie中的token
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
    document.cookie = 'Authorization=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

    console.log('已清除localStorage、sessionStorage和Cookie中的token和用户信息')

    console.log('用户状态已重置')
  }

  // 更新未读通知数量
  const updateUnreadCount = (count: number): void => {
    unreadCount.value = count
  }

  // 获取未读通知数量
  const fetchUnreadCount = async (): Promise<void> => {
    if (!token.value) {
      console.log('没有token，跳过获取未读通知数量')
      return
    }

    try {
      console.log('开始获取未读通知数量，当前token:', token.value)

      // 使用axios请求，确保请求拦截器能添加token
      // 这里应该导入并使用notification API
      // const res = await getUnreadCount()

      // 暂时模拟响应
      const mockResult = { code: 200, data: Math.floor(Math.random() * 10) }
      console.log('获取未读通知数量响应:', mockResult)

      if (mockResult.code === 200) {
        unreadCount.value = mockResult.data
      }
    } catch (error) {
      console.error('获取未读通知数量失败', error)
    }
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    unreadCount,
    userId,
    username,
    avatar,
    loginAction,
    smsLoginAction,
    logoutAction,
    fetchUserInfo,
    resetState,
    updateUnreadCount,
    fetchUnreadCount,
    setToken,
    setUser,
    setLoggedIn
  }
})
