import request from '@/utils/request'
import type { AxiosProgressEvent } from 'axios'
import { PhotoUploadParams } from './photo'

/**
 * 草稿详情
 */
export interface PhotoDraft {
  id: number
  title: string
  description?: string
  location?: string
  tags?: string[]
  mentions?: {
    userId: number
    username: string
    nickname?: string
  }[]
  visibility: number
  allowComment: boolean
  allowDownload: boolean
  tempFilePaths?: string[]
  createdAt: string
  updatedAt: string
}

/**
 * 草稿列表响应
 */
export interface DraftListResponse {
  records: PhotoDraft[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 保存草稿
 * @param files 照片文件列表
 * @param params 草稿参数
 * @param onUploadProgress 上传进度回调
 * @returns 草稿ID
 */
export function saveDraft(
  files: File[],
  params: PhotoUploadParams,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<number> {
  const formData = new FormData()
  
  files.forEach(file => {
    formData.append('files', file)
  })
  
  formData.append('data', JSON.stringify(params))
  
  return request({
    url: '/photo/draft/save',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

/**
 * 更新草稿
 * @param draftId 草稿ID
 * @param files 新增照片文件列表
 * @param params 草稿参数
 * @param onUploadProgress 上传进度回调
 * @returns 是否成功
 */
export function updateDraft(
  draftId: number,
  files: File[],
  params: PhotoUploadParams,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<boolean> {
  const formData = new FormData()
  
  files.forEach(file => {
    formData.append('files', file)
  })
  
  formData.append('data', JSON.stringify(params))
  
  return request({
    url: `/photo/draft/${draftId}`,
    method: 'put',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

/**
 * 获取草稿详情
 * @param draftId 草稿ID
 * @returns 草稿详情
 */
export function getDraftDetail(draftId: number): Promise<PhotoDraft> {
  return request({
    url: `/photo/draft/${draftId}`,
    method: 'get'
  })
}

/**
 * 获取草稿列表
 * @param page 页码
 * @param size 每页大小
 * @returns 草稿列表
 */
export function getDraftList(page: number = 1, size: number = 10): Promise<DraftListResponse> {
  return request({
    url: '/photo/draft/list',
    method: 'get',
    params: { page, size }
  })
}

/**
 * 删除草稿
 * @param draftId 草稿ID
 * @returns 是否成功
 */
export function deleteDraft(draftId: number): Promise<boolean> {
  return request({
    url: `/photo/draft/${draftId}`,
    method: 'delete'
  })
}

/**
 * 发布草稿
 * @param draftId 草稿ID
 * @returns 照片ID列表
 */
export function publishDraft(draftId: number): Promise<number[]> {
  return request({
    url: `/photo/draft/publish/${draftId}`,
    method: 'post'
  })
}
