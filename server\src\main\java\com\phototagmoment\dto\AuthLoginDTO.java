package com.phototagmoment.dto;

import lombok.Data;

/**
 * 第三方登录DTO
 */
@Data
public class AuthLoginDTO {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 授权URL
     */
    private String authUrl;

    /**
     * 状态参数
     */
    private String state;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 用户OpenID
     */
    private String openId;

    /**
     * 用户UnionID
     */
    private String unionId;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 会话密钥（微信小程序）
     */
    private String sessionKey;

    /**
     * 过期时间(秒)
     */
    private int expiresIn;

    /**
     * 第三方平台来源
     */
    private String source;

    /**
     * 用户信息
     */
    private AuthUserInfoDTO userInfo;

    /**
     * 创建成功结果
     */
    public static AuthLoginDTO success(String openId, String unionId, String sessionKey, String accessToken, int expiresIn) {
        AuthLoginDTO dto = new AuthLoginDTO();
        dto.setSuccess(true);
        dto.setOpenId(openId);
        dto.setUnionId(unionId);
        dto.setSessionKey(sessionKey);
        dto.setAccessToken(accessToken);
        dto.setExpiresIn(expiresIn);
        return dto;
    }

    /**
     * 创建失败结果
     */
    public static AuthLoginDTO fail(String errorMsg) {
        AuthLoginDTO dto = new AuthLoginDTO();
        dto.setSuccess(false);
        dto.setErrorMsg(errorMsg);
        return dto;
    }

    /**
     * 创建授权URL结果
     */
    public static AuthLoginDTO authUrl(String authUrl, String state, String source) {
        AuthLoginDTO dto = new AuthLoginDTO();
        dto.setSuccess(true);
        dto.setAuthUrl(authUrl);
        dto.setState(state);
        dto.setSource(source);
        return dto;
    }
}
