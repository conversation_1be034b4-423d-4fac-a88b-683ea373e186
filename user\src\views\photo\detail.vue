<template>
  <div class="photo-detail-container">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
    <div v-else-if="!photo.id" class="empty-container">
      <div class="empty-icon">
        <van-icon name="photo-o" />
      </div>
      <div class="empty-text">照片不存在或已被删除</div>
      <el-button type="primary" @click="goBack">返回</el-button>
    </div>
    <div v-else class="photo-detail">
      <div class="photo-main">
        <div class="photo-image">
          <!-- 移动端使用Vant的Swipe组件实现照片轮播 -->
          <div class="mobile-swipe-container" v-if="isMobileView">
            <van-swipe
              class="photo-swiper"
              :loop="true"
              :touchable="true"
              :stop-propagation="false"
              :show-indicators="true"
              :initial-swipe="currentPhotoIndex"
              @change="handleSwipeChange"
            >
              <van-swipe-item v-for="groupPhoto in groupPhotosWithUrls" :key="groupPhoto.id">
                <div class="swipe-item-container" @click="previewImage">
                  <img :src="groupPhoto.privateUrl" :alt="groupPhoto.title" />
                </div>
              </van-swipe-item>

              <!-- 添加左右滑动指示器 -->
              <template #prev>
                <div class="swipe-nav-button left" @click.stop="prevPhoto">
                  <van-icon name="arrow-left" />
                </div>
              </template>
              <template #next>
                <div class="swipe-nav-button right" @click.stop="nextPhoto">
                  <van-icon name="arrow" />
                </div>
              </template>
            </van-swipe>
          </div>

          <!-- PC端使用静态图片展示 -->
          <div class="pc-image-container" v-else>
            <div class="main-image" @click="previewImage">
              <img :src="currentPhotoUrl" :alt="currentPhotoTitle" />

              <!-- PC端导航按钮 -->
              <div v-if="hasMultiplePhotos" class="pc-nav-buttons">
                <div class="nav-button prev" @click.stop="prevPhoto">
                  <van-icon name="arrow-left" />
                </div>
                <div class="nav-button next" @click.stop="nextPhoto">
                  <van-icon name="arrow" />
                </div>
              </div>
            </div>
          </div>

          <!-- 照片计数指示器 -->
          <div v-if="hasMultiplePhotos" class="photo-counter">
            {{ currentPhotoIndex + 1 }} / {{ groupPhotosWithUrls.length }}
          </div>

          <!-- PC端缩略图导航 -->
          <div v-if="hasMultiplePhotos && !isMobileView" class="thumbnail-navigation">
            <div
              v-for="(photo, index) in groupPhotosWithUrls"
              :key="photo.id"
              class="thumbnail-item"
              :class="{ active: index === currentPhotoIndex }"
              @click="selectThumbnail(index)"
            >
              <img :src="photo.privateUrl" :alt="photo.title" />
            </div>
          </div>
        </div>
        <div class="photo-actions">
          <div class="action-item" @click="handleLike">
            <van-icon :name="photo.isLiked ? 'like' : 'like-o'" :class="{ active: photo.isLiked }" />
            <span>{{ photo.likeCount }}</span>
          </div>
          <div class="action-item" @click="handleCollect">
            <van-icon :name="photo.isCollected ? 'star' : 'star-o'" :class="{ active: photo.isCollected }" />
            <span>{{ photo.collectCount }}</span>
          </div>
          <div class="action-item" @click="focusComment">
            <van-icon name="chat-o" />
            <span>{{ photo.commentCount }}</span>
          </div>
          <div class="action-item" @click="handleShare">
            <van-icon name="share-o" />
            <span>分享</span>
          </div>
        </div>
      </div>
      <div class="photo-info">
        <div class="photo-header">
          <div class="photo-user">
            <img
              :src="privateUserAvatarUrl ||
                    (photo.userAvatar ? getPrivateImageUrl(photo.userAvatar) :
                    (photo.avatar ? getPrivateImageUrl(photo.avatar) :
                    'https://randomuser.me/api/portraits/men/1.jpg'))"
              :alt="getUserNickname"
              class="user-avatar"
              @click="goToUserProfile(getUserId)"
            />
            <div class="user-info">
              <div class="user-name" @click="goToUserProfile(getUserId)">{{ getUserNickname }}</div>
              <div class="photo-time">{{ formatTime(photo.createdAt) }}</div>
            </div>
          </div>
          <div class="follow-btn" v-if="getUserId !== currentUserId">
            <el-button
              :type="photo.user?.isFollowed ? 'info' : 'primary'"
              size="small"
              @click="handleFollow"
            >
              {{ photo.user?.isFollowed ? '已关注' : '关注' }}
            </el-button>
          </div>
        </div>
        <div class="photo-header-info">
          <div class="photo-title">{{ photo.title }}</div>
          <PhotoStatus v-if="photo.status !== 1" :status="photo.status" />
        </div>
        <div class="photo-description" v-if="photo.description" v-html="formatDescription(photo.description)"></div>
        <!-- 显示拒绝原因 -->
        <div v-if="photo.status === 2 && photo.rejectReason" class="photo-reject-reason">
          <div class="reject-title">审核未通过原因：</div>
          <div class="reject-content">{{ photo.rejectReason }}</div>
        </div>
        <div class="photo-tags" v-if="photo.tags && photo.tags.length > 0">
          <span
            v-for="tag in photo.tags"
            :key="tag"
            class="tag-item"
            @click="handleTagClick(tag)"
          >
            #{{ tag }}
          </span>
        </div>
        <div class="photo-location" v-if="photo.location">
          <van-icon name="location-o" />
          <span>{{ photo.location }}</span>
        </div>
        <div class="photo-stats">
          <div class="stat-item">
            <van-icon name="eye-o" />
            <span>{{ photo.viewCount }} 次浏览</span>
          </div>
          <div class="stat-item">
            <van-icon name="like-o" />
            <span>{{ photo.likeCount }} 次点赞</span>
          </div>
          <div class="stat-item">
            <van-icon name="star-o" />
            <span>{{ photo.collectCount }} 次收藏</span>
          </div>
        </div>
        <div class="comment-section">
          <div class="comment-header">
            <h3>评论 ({{ photo.commentCount }})</h3>
          </div>
          <div class="comment-form">
            <img :src="privateCurrentUserAvatarUrl || currentUserAvatar" :alt="currentUserName" class="user-avatar" />
            <div class="comment-input">
              <el-input
                v-model="commentContent"
                type="textarea"
                :rows="2"
                placeholder="写下你的评论..."
                ref="commentInput"
              ></el-input>
              <div class="comment-actions">
                <el-button type="primary" size="small" @click="submitComment" :disabled="!commentContent.trim()">
                  发表评论
                </el-button>
              </div>
            </div>
          </div>
          <div class="comment-list">
            <div v-if="comments.length === 0" class="empty-comment">
              <div class="empty-icon">
                <van-icon name="chat-o" />
              </div>
              <div class="empty-text">暂无评论，快来发表第一条评论吧！</div>
            </div>
            <div v-else class="comment-items">
              <div
                v-for="comment in comments"
                :key="comment.id"
                class="comment-item"
              >
                <img :src="comment.userAvatar" :alt="comment.userName" class="user-avatar" @click="goToUserProfile(comment.userId)" />
                <div class="comment-content">
                  <div class="comment-user">
                    <span class="user-name" @click="goToUserProfile(comment.userId)">{{ comment.userName }}</span>
                    <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                  </div>
                  <div class="comment-text">{{ comment.content }}</div>
                  <div class="comment-actions">
                    <span class="action-item" @click="handleReply(comment)">回复</span>
                    <span class="action-item" @click="handleLikeComment(comment)">
                      <van-icon :name="comment.isLiked ? 'like' : 'like-o'" :class="{ active: comment.isLiked }" />
                      {{ comment.likeCount }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="comments.length > 0 && hasMoreComments" class="load-more">
              <el-button type="text" :loading="loadingMoreComments" @click="loadMoreComments">
                加载更多评论
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 相关推荐 -->
    <div class="related-photos" v-if="relatedPhotos.length > 0">
      <h3>相关推荐</h3>
      <div class="photo-grid">
        <div
          v-for="relatedPhoto in relatedPhotos"
          :key="relatedPhoto.id"
          class="photo-item"
          @click="goToPhotoDetail(relatedPhoto.id)"
        >
          <div class="photo-card">
            <div class="photo-image">
              <img :src="relatedPhoto.url" :alt="relatedPhoto.title" />
            </div>
            <div class="photo-info">
              <div class="photo-title">{{ relatedPhoto.title }}</div>
              <div class="photo-user">
                <img
                  :src="relatedPhoto.userAvatar || (relatedPhoto.user && relatedPhoto.user.avatar) || 'https://randomuser.me/api/portraits/men/1.jpg'"
                  :alt="relatedPhoto.userNickname || relatedPhoto.userName || (relatedPhoto.user && (relatedPhoto.user.nickname || relatedPhoto.user.username)) || '未知用户'"
                  class="user-avatar"
                />
                <span class="user-name">{{ relatedPhoto.userNickname || relatedPhoto.userName || (relatedPhoto.user && (relatedPhoto.user.nickname || relatedPhoto.user.username)) || '未知用户' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分享对话框 -->
    <el-dialog
      title="分享照片"
      v-model="shareDialogVisible"
      width="400px"
    >
      <div class="share-content">
        <div class="share-image">
          <img :src="privatePhotoUrl || photo.url" :alt="photo.title" />
        </div>
        <div class="share-title">{{ photo.title }}</div>
        <div class="share-link">
          <el-input v-model="shareLink" readonly>
            <template #append>
              <el-button @click="copyShareLink">复制</el-button>
            </template>
          </el-input>
        </div>
        <div class="share-platforms">
          <div class="platform-item" @click="shareToWeChat">
            <van-icon name="wechat" />
            <span>微信</span>
          </div>
          <div class="platform-item" @click="shareToWeibo">
            <van-icon name="weibo" />
            <span>微博</span>
          </div>
          <div class="platform-item" @click="shareToQQ">
            <van-icon name="qq" />
            <span>QQ</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getPhotoDetail, likePhoto, collectPhoto, getPhotoComments, addPhotoComment, likeComment, getPhotosByGroupId } from '@/api/photo'
import { followUser, unfollowUser } from '@/api/user'
import { getRecommendedPhotos, recordUserBehavior } from '@/api/recommendation'
import { useUserStore } from '@/stores/user'
import PhotoStatus from '@/components/photo/PhotoStatus.vue'
import { getPrivateImageUrl } from '@/api/file'
import { Swipe, SwipeItem, showImagePreview, Icon as VanIcon } from 'vant'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 照片详情
const photo = ref({})
const loading = ref(true)
const photoId = computed(() => {
  // 确保photoId是数字类型
  const id = route.params.id;
  return typeof id === 'object' ? Number(id.id || 0) : Number(id);
})

// 同组照片列表
const groupPhotos = ref([])
const groupPhotosWithUrls = ref([]) // 带有私有URL的照片数组
const hasMultiplePhotos = computed(() => groupPhotos.value.length > 1)
const currentPhotoIndex = ref(0)

// 当前照片URL和标题
const currentPhotoUrl = computed(() => {
  if (groupPhotosWithUrls.value.length > 0 && currentPhotoIndex.value < groupPhotosWithUrls.value.length) {
    const photo = groupPhotosWithUrls.value[currentPhotoIndex.value] as any;
    return photo.privateUrl || '';
  }
  return '';
});

const currentPhotoTitle = computed(() => {
  if (groupPhotosWithUrls.value.length > 0 && currentPhotoIndex.value < groupPhotosWithUrls.value.length) {
    const photo = groupPhotosWithUrls.value[currentPhotoIndex.value] as any;
    return photo.title || '';
  }
  return '';
});

// 判断是否为移动端视图
const isMobileView = ref(false);

// 监听窗口大小变化
const updateViewMode = () => {
  isMobileView.value = window.innerWidth <= 768;
};

// 私有图片URL
const privatePhotoUrl = ref('')
const privateUserAvatarUrl = ref('')
const privateCurrentUserAvatarUrl = ref('')

// 当前用户信息
const isLoggedIn = computed(() => userStore.isLoggedIn)
const currentUserId = computed(() => userStore.userId)
const currentUserAvatar = computed(() => userStore.avatar || '/default-avatar.png')
const currentUserName = computed(() => userStore.username)

// 获取用户昵称，兼容不同的数据结构
const getUserNickname = computed(() => {
  const photoData = photo.value as any;

  // 检查嵌套的user对象
  if (photoData.user) {
    if (photoData.user.nickname) return photoData.user.nickname;
    if (photoData.user.username) return photoData.user.username;
  }

  // 检查SQL别名字段
  if (photoData.userNickname) return photoData.userNickname;
  if (photoData.userName) return photoData.userName;

  // 检查原始字段
  if (photoData.nickname) return photoData.nickname;
  if (photoData.username) return photoData.username;

  // 默认值
  return '未知用户';
})

// 获取用户ID，兼容不同的数据结构
const getUserId = computed(() => {
  const photoData = photo.value as any;

  // 检查嵌套的user对象
  if (photoData.user && photoData.user.id) {
    return photoData.user.id;
  }

  // 检查直接的userId字段
  if (photoData.userId) {
    return photoData.userId;
  }

  // 默认值
  return 0;
})

// 评论相关
const commentContent = ref('')
const comments = ref([])
const commentPage = ref(1)
const commentSize = ref(10)
const commentTotal = ref(0)
const hasMoreComments = ref(false)
const loadingMoreComments = ref(false)
const commentInput = ref<HTMLInputElement | null>(null)

// 相关推荐
const relatedPhotos = ref([])

// 分享对话框
const shareDialogVisible = ref(false)
const shareLink = ref('')

// 加载同组照片
const loadGroupPhotos = async () => {
  const photoData = photo.value as any;

  // 如果照片数据中已经包含了同组照片信息，直接使用
  if (photoData.groupPhotos && photoData.groupPhotos.length > 0) {
    groupPhotos.value = photoData.groupPhotos;

    // 找到当前照片在组中的索引
    const index = (groupPhotos.value as any[]).findIndex(p => p.id === photoId.value);
    if (index !== -1) {
      currentPhotoIndex.value = index;
    }

    // 获取所有照片的私有URL
    await loadGroupPhotosPrivateUrls();
    return;
  }

  // 如果照片数据中没有同组照片信息，但有分组ID，则通过API获取
  if (photoData.groupId) {
    try {
      const res = await getPhotosByGroupId(photoData.groupId);
      if (res.code === 200) {
        groupPhotos.value = res.data;

        // 找到当前照片在组中的索引
        const index = (groupPhotos.value as any[]).findIndex(p => p.id === photoId.value);
        if (index !== -1) {
          currentPhotoIndex.value = index;
        }

        // 获取所有照片的私有URL
        await loadGroupPhotosPrivateUrls();
      }
    } catch (error) {
      console.error('加载同组照片失败', error);
    }
  } else {
    // 如果没有分组信息，则只显示当前照片
    groupPhotos.value = [photoData];
    currentPhotoIndex.value = 0;
    await loadGroupPhotosPrivateUrls();
  }
}

// 获取同组照片的私有URL
const loadGroupPhotosPrivateUrls = async () => {
  try {
    const photosWithUrls: any[] = [];

    for (const photo of groupPhotos.value as any[]) {
      const privateUrl = await getPrivateImageUrl(photo.url);
      photosWithUrls.push({
        ...photo,
        privateUrl
      });
    }

    groupPhotosWithUrls.value = photosWithUrls;
  } catch (error) {
    console.error('获取照片私有URL失败', error);
  }
}

// 切换到组内的其他照片
const switchToPhoto = (index: number) => {
  if (index >= 0 && index < groupPhotos.value.length) {
    const targetPhoto = (groupPhotos.value as any[])[index];
    router.push(`/photo/detail/${targetPhoto.id}`);
  }
}

// 处理Swipe组件变化
const handleSwipeChange = (index: number) => {
  currentPhotoIndex.value = index;

  // 如果需要更新URL或其他状态，可以在这里处理
  if (groupPhotosWithUrls.value[index]) {
    const photo = groupPhotosWithUrls.value[index] as any;
    privatePhotoUrl.value = photo.privateUrl || '';
  }
}

// 上一张照片
const prevPhoto = () => {
  const swipeRef = document.querySelector('.photo-swiper') as any;
  if (swipeRef && swipeRef.__vue__) {
    swipeRef.__vue__.prev();
  } else {
    // 如果无法直接访问Swipe组件实例，手动更新索引
    if (currentPhotoIndex.value > 0) {
      currentPhotoIndex.value--;
    } else if (groupPhotosWithUrls.value.length > 0) {
      // 循环到最后一张
      currentPhotoIndex.value = groupPhotosWithUrls.value.length - 1;
    }
  }
}

// 下一张照片
const nextPhoto = () => {
  const swipeRef = document.querySelector('.photo-swiper') as any;
  if (swipeRef && swipeRef.__vue__) {
    swipeRef.__vue__.next();
  } else {
    // 如果无法直接访问Swipe组件实例，手动更新索引
    if (currentPhotoIndex.value < groupPhotosWithUrls.value.length - 1) {
      currentPhotoIndex.value++;
    } else {
      // 循环到第一张
      currentPhotoIndex.value = 0;
    }
  }
}

// 移除七牛云缩略图参数的工具函数
const removeQiniuThumbnailParams = (url: string): string => {
  if (!url) return url

  // 移除七牛云图片处理参数（如 ?imageView2/1/w/300/h/300 等）
  const paramIndex = url.indexOf('?imageView2')
  if (paramIndex !== -1) {
    return url.substring(0, paramIndex)
  }

  // 移除其他可能的图片处理参数
  const otherParamIndex = url.indexOf('?imageMogr2')
  if (otherParamIndex !== -1) {
    return url.substring(0, otherParamIndex)
  }

  return url
}

// 预览照片
const previewImage = () => {
  // 如果有多张照片
  if (hasMultiplePhotos.value && groupPhotosWithUrls.value.length > 0) {
    // 从groupPhotosWithUrls中提取所有图片URL，并移除缩略图参数
    const images = groupPhotosWithUrls.value.map((photo: any) => {
      const originalUrl = photo.privateUrl || ''
      return removeQiniuThumbnailParams(originalUrl)
    });

    // 使用Vant的图片预览组件
    showImagePreview({
      images,
      startPosition: currentPhotoIndex.value,
      closeable: true,
      closeIconPosition: 'top-right',
      swipeDuration: 300,  // 设置滑动动画时长
      loop: true,          // 允许循环滑动
      maxZoom: 3,          // 最大缩放比例
      minZoom: 1/3,        // 最小缩放比例
      onClose: () => {
        // 关闭预览时可以执行的操作
        console.log('图片预览已关闭');
      },
      onChange: (index: number) => {
        // 当预览的图片变化时，同步更新当前照片索引
        currentPhotoIndex.value = index;
      }
    });
  } else {
    // 单张照片，移除缩略图参数
    const originalUrl = removeQiniuThumbnailParams(privatePhotoUrl.value)
    showImagePreview({
      images: [originalUrl],
      closeable: true,
      closeIconPosition: 'top-right',
      swipeDuration: 300,
      maxZoom: 3,
      minZoom: 1/3
    });
  }
}

// 选择缩略图
const selectThumbnail = (index: number) => {
  if (index >= 0 && index < groupPhotosWithUrls.value.length) {
    currentPhotoIndex.value = index;
    const photo = groupPhotosWithUrls.value[index] as any;
    privatePhotoUrl.value = photo.privateUrl || '';
  }
}

// 初始化
onMounted(async () => {
  // 初始化视图模式
  updateViewMode();

  // 添加窗口大小变化监听
  window.addEventListener('resize', updateViewMode);

  // 加载数据
  await loadPhotoDetail();
  await loadComments();
  await loadRelatedPhotos();

  // 记录浏览行为
  recordUserBehavior({
    photoId: photoId.value,
    behavior: 'view'
  });
})

// 加载照片详情
const loadPhotoDetail = async () => {
  try {
    loading.value = true
    const res = await getPhotoDetail(photoId.value)
    if (res && res.code === 200) {
      console.log('照片详情数据:', res.data)
      photo.value = res.data

      // 使用类型断言处理照片数据
      const photoData = photo.value as any;

      // 获取带下载凭证的图片URL
      if (photoData.url) {
        privatePhotoUrl.value = await getPrivateImageUrl(photoData.url)
      }

      // 获取带下载凭证的用户头像URL
      if (photoData.user?.avatar) {
        privateUserAvatarUrl.value = await getPrivateImageUrl(photoData.user.avatar)
      } else if (photoData.avatar) {
        privateUserAvatarUrl.value = await getPrivateImageUrl(photoData.avatar)
      } else if (photoData.userAvatar) {
        privateUserAvatarUrl.value = await getPrivateImageUrl(photoData.userAvatar)
      }

      // 获取带下载凭证的当前用户头像URL
      const userAvatar = currentUserAvatar.value
      if (userAvatar && !userAvatar.startsWith('/')) {
        privateCurrentUserAvatarUrl.value = await getPrivateImageUrl(userAvatar)
      } else {
        privateCurrentUserAvatarUrl.value = userAvatar
      }

      // 设置分享链接
      shareLink.value = `${window.location.origin}/photo/detail/${photoId.value}`

      // 检查是否有照片组信息
      if (photoData.isGrouped && photoData.groupPhotoCount > 1) {
        console.log('照片组信息:', photoData.isGrouped, photoData.groupPhotoCount, photoData.groupPhotoIds);
      }

      // 加载同组照片
      await loadGroupPhotos();

      // 记录用户行为
      if (isLoggedIn.value) {
        recordUserBehavior({
          photoId: photoId.value,
          behavior: 'view'
        }).catch(error => {
          console.error('记录用户行为失败', error)
        })
      }

      // 加载评论
      loadComments();

      // 加载相关推荐
      loadRelatedPhotos();
    } else {
      ElMessage.error((res && res.message) || '获取照片详情失败')
    }
  } catch (error) {
    console.error('加载照片详情失败', error)
    ElMessage.error('加载照片详情失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 加载评论
const loadComments = async () => {
  try {
    const res = await getPhotoComments({
      photoId: photoId.value,
      page: commentPage.value,
      size: commentSize.value
    })
    if (res.code === 200) {
      comments.value = res.data.records
      commentTotal.value = res.data.total
      hasMoreComments.value = commentPage.value * commentSize.value < commentTotal.value
    }
  } catch (error) {
    console.error('加载评论失败', error)
  }
}

// 加载更多评论
const loadMoreComments = async () => {
  try {
    loadingMoreComments.value = true
    commentPage.value++
    const res = await getPhotoComments({
      photoId: photoId.value,
      page: commentPage.value,
      size: commentSize.value
    })
    if (res.code === 200) {
      comments.value = [...comments.value, ...res.data.records]
      commentTotal.value = res.data.total
      hasMoreComments.value = commentPage.value * commentSize.value < commentTotal.value
    }
  } catch (error) {
    console.error('加载更多评论失败', error)
  } finally {
    loadingMoreComments.value = false
  }
}

// 加载相关推荐
const loadRelatedPhotos = async () => {
  try {
    const res = await getRecommendedPhotos({
      page: 1,
      size: 6
    })
    // 使用类型断言处理响应数据
    const response = res as any;
    if (response.code === 200) {
      // 过滤掉当前照片
      relatedPhotos.value = response.data.records.filter((item: any) => item.id !== photoId.value)
    }
  } catch (error) {
    console.error('加载相关推荐失败', error)
  }
}

// 处理点赞
const handleLike = async () => {
  // 检查用户是否已登录
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再进行点赞')
    router.push(`/login?redirect=${encodeURIComponent(router.currentRoute.value.fullPath)}`)
    return
  }

  try {
    const res = await likePhoto(photoId.value)
    if (res.code === 200) {
      // 使用类型断言处理照片数据
      const photoData = photo.value as any;

      photoData.isLiked = !photoData.isLiked;
      photoData.likeCount = photoData.isLiked
        ? (photoData.likeCount || 0) + 1
        : Math.max((photoData.likeCount || 0) - 1, 0);

      // 记录点赞行为
      if (photoData.isLiked) {
        recordUserBehavior({
          photoId: photoId.value,
          behavior: 'like'
        });
      }
    }
  } catch (error) {
    console.error('点赞失败', error)
  }
}

// 处理收藏
const handleCollect = async () => {
  // 检查用户是否已登录
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再进行收藏')
    router.push(`/login?redirect=${encodeURIComponent(router.currentRoute.value.fullPath)}`)
    return
  }

  try {
    const res = await collectPhoto(photoId.value)
    if (res.code === 200) {
      // 使用类型断言处理照片数据
      const photoData = photo.value as any;

      photoData.isCollected = !photoData.isCollected;
      photoData.collectCount = photoData.isCollected
        ? (photoData.collectCount || 0) + 1
        : Math.max((photoData.collectCount || 0) - 1, 0);

      // 记录收藏行为
      if (photoData.isCollected) {
        recordUserBehavior({
          photoId: photoId.value,
          behavior: 'collect'
        });
      }
    }
  } catch (error) {
    console.error('收藏失败', error)
  }
}

// 处理关注
const handleFollow = async () => {
  // 检查用户是否已登录
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再进行关注')
    router.push(`/login?redirect=${encodeURIComponent(router.currentRoute.value.fullPath)}`)
    return
  }

  try {
    // 使用类型断言处理照片数据
    const photoData = photo.value as any;

    if (photoData.user?.isFollowed) {
      const res = await unfollowUser(photoData.user.id)
      if (res.code === 200) {
        if (photoData.user) {
          photoData.user.isFollowed = false
        }
        ElMessage.success('已取消关注')
      }
    } else {
      const userId = photoData.user?.id || getUserId.value;
      if (!userId) {
        ElMessage.warning('无法获取用户ID');
        return;
      }

      const res = await followUser(userId)
      if (res.code === 200) {
        if (photoData.user) {
          photoData.user.isFollowed = true
        } else {
          // 如果没有user对象，创建一个
          photoData.user = {
            id: userId,
            isFollowed: true
          };
        }
        ElMessage.success('关注成功')
      }
    }
  } catch (error) {
    console.error('关注操作失败', error)
  }
}

// 提交评论
const submitComment = async () => {
  // 检查用户是否已登录
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再进行评论')
    router.push(`/login?redirect=${encodeURIComponent(router.currentRoute.value.fullPath)}`)
    return
  }

  if (!commentContent.value.trim()) {
    ElMessage.warning('评论内容不能为空')
    return
  }

  try {
    const res = await addPhotoComment({
      photoId: photoId.value,
      content: commentContent.value
    })
    if (res.code === 200) {
      // 添加新评论到列表
      const newComment = {
        id: res.data.id,
        userId: currentUserId.value,
        userName: currentUserName.value,
        userAvatar: currentUserAvatar.value,
        content: commentContent.value,
        likeCount: 0,
        isLiked: false,
        createdAt: new Date().toISOString()
      };

      // 使用类型断言处理评论数组
      (comments.value as any[]).unshift(newComment);

      // 更新评论数
      // 使用类型断言处理照片数据
      const photoData = photo.value as any;
      if (photoData.commentCount !== undefined) {
        photoData.commentCount++;
      }
      commentTotal.value++;

      // 清空评论内容
      commentContent.value = '';

      // 记录评论行为
      recordUserBehavior({
        photoId: photoId.value,
        behavior: 'comment'
      });

      ElMessage.success('评论成功');
    }
  } catch (error) {
    console.error('提交评论失败', error);
  }
}

// 处理评论点赞
const handleLikeComment = async (comment: any) => {
  try {
    const res = await likeComment(comment.id)
    // 使用类型断言处理响应数据
    const response = res as any;
    if (response.code === 200) {
      comment.isLiked = !comment.isLiked
      comment.likeCount = comment.isLiked
        ? comment.likeCount + 1
        : comment.likeCount - 1
    }
  } catch (error) {
    console.error('评论点赞失败', error)
  }
}

// 处理回复
const handleReply = (comment: any) => {
  commentContent.value = `@${comment.userName} `
  focusComment()
}

// 聚焦评论输入框
const focusComment = () => {
  if (commentInput.value) {
    commentInput.value.focus()
  }
}

// 处理分享
const handleShare = () => {
  shareLink.value = `${window.location.origin}/photo/${photoId.value}`
  shareDialogVisible.value = true
}

// 复制分享链接
const copyShareLink = () => {
  navigator.clipboard.writeText(shareLink.value)
    .then(() => {
      ElMessage.success('链接已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
}

// 分享到微信
const shareToWeChat = () => {
  ElMessage.info('请使用微信扫描二维码分享')
}

// 分享到微博
const shareToWeibo = () => {
  // 使用类型断言处理照片数据
  const photoData = photo.value as any;
  const title = photoData.title || '分享一张照片'
  const url = `http://service.weibo.com/share/share.php?url=${encodeURIComponent(shareLink.value)}&title=${encodeURIComponent(title)}`
  window.open(url, '_blank')
}

// 分享到QQ
const shareToQQ = () => {
  // 使用类型断言处理照片数据
  const photoData = photo.value as any;
  const title = photoData.title || '分享一张照片'
  const url = `http://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareLink.value)}&title=${encodeURIComponent(title)}`
  window.open(url, '_blank')
}

// 跳转到用户主页
const goToUserProfile = (userId: number | string | null) => {
  if (!userId) {
    userId = getUserId.value;
  }
  if (userId) {
    router.push(`/user/${userId}`);
  }
}

// 跳转到照片详情
const goToPhotoDetail = (id: number | string) => {
  router.push(`/photo/detail/${id}`)
}

// 处理标签点击
const handleTagClick = (tag: string) => {
  router.push(`/search?keyword=${encodeURIComponent(tag)}&type=tag`)
}

// 格式化描述，将@用户和#标签转换为链接
const formatDescription = (description: string) => {
  if (!description) return '';

  // 替换@用户为链接
  let formattedText = description.replace(/@(\w+)/g, '<a class="mention" href="/user/$1">@$1</a>');

  // 替换#标签为链接
  formattedText = formattedText.replace(/#(\w+)/g, '<a class="hashtag" href="/search?keyword=$1&type=tag">#$1</a>');

  // 替换换行符为<br>
  formattedText = formattedText.replace(/\n/g, '<br>');

  return formattedText;
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化时间
const formatTime = (time: string | number | Date) => {
  if (!time) return ''

  const date = new Date(time)
  const now = new Date()
  // 使用getTime()方法获取时间戳进行计算
  const diff = now.getTime() - date.getTime()

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }

  // 小于1周
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }

  // 格式化日期
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}
</script>

<style scoped>
.photo-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 60px;
  color: #ccc;
  margin-bottom: 10px;
}

.photo-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.photo-main {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.photo-image {
  width: 100%;
  position: relative;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

/* 移动端轮播样式 */
.mobile-swipe-container {
  width: 100%;
  height: 100%;
}

.photo-swiper {
  width: 100%;
  height: 100%;
  position: relative;
  touch-action: pan-y; /* 允许垂直滚动，但水平滑动会被捕获为滑动事件 */
  user-select: none; /* 防止文本选择干扰滑动 */
}

.swipe-item-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  cursor: pointer;
}

/* PC端主图样式 */
.pc-image-container {
  width: 100%;
  position: relative;
}

.main-image {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.main-image img {
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
}

/* PC端导航按钮 */
.pc-nav-buttons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.main-image:hover .pc-nav-buttons {
  opacity: 1;
}

.nav-button {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  margin: 0 10px;
  z-index: 10;
  transition: background-color 0.3s;
}

.nav-button:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 缩略图导航 */
.thumbnail-navigation {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  margin-top: 15px;
  overflow-x: auto;
  padding-bottom: 10px;
  scrollbar-width: thin;
}

.thumbnail-item {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s;
  border: 2px solid transparent;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  opacity: 0.9;
}

.thumbnail-item.active {
  opacity: 1;
  border-color: #409EFF;
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 滑动导航按钮 */
.swipe-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s;
}

.swipe-nav-button.left {
  left: 10px;
}

.swipe-nav-button.right {
  right: 10px;
}

.photo-image:hover .swipe-nav-button {
  opacity: 0.7;
}

.swipe-nav-button:hover {
  opacity: 1 !important;
  background: rgba(0, 0, 0, 0.5);
}

/* 移动端样式优化 */
@media (max-width: 768px) {
  .swipe-nav-button {
    width: 36px;
    height: 36px;
    opacity: 0.5; /* 在移动端默认显示导航按钮 */
  }
}

.photo-image img {
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
}

/* 照片计数指示器 */
.photo-counter {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  z-index: 10;
}

/* 同组照片缩略图 */
.photo-group-thumbnails {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 5px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.5);
  overflow-x: auto;
}

.thumbnail-item {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.thumbnail-item:hover {
  opacity: 0.9;
}

.thumbnail-item.active {
  opacity: 1;
  border-color: #409EFF;
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-actions {
  display: flex;
  justify-content: space-around;
  padding: 10px;
  border-top: 1px solid #eee;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
}

.action-item:hover {
  background-color: #f5f5f5;
}

.action-item i.active {
  color: #f14049;
}

.photo-info {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.photo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.photo-user {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: bold;
  cursor: pointer;
}

.user-name:hover {
  color: #409eff;
}

.photo-time {
  font-size: 12px;
  color: #999;
}

.photo-header-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.photo-title {
  font-size: 18px;
  font-weight: bold;
}

.photo-description {
  margin-bottom: 15px;
  line-height: 1.5;
}

.photo-description :deep(a) {
  color: #3b82f6;
  text-decoration: none;
}

.photo-description :deep(a:hover) {
  text-decoration: underline;
}

.photo-description :deep(.mention) {
  color: #3b82f6;
}

.photo-description :deep(.hashtag) {
  color: #8b5cf6;
}

.photo-reject-reason {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}

.reject-title {
  font-weight: bold;
  color: #ff4d4f;
  margin-bottom: 5px;
}

.reject-content {
  color: #666;
}

.photo-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.tag-item {
  color: #409eff;
  cursor: pointer;
}

.tag-item:hover {
  text-decoration: underline;
}

.photo-location {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  margin-bottom: 15px;
}

.photo-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  color: #666;
}

.comment-section {
  margin-top: 20px;
}

.comment-header {
  margin-bottom: 15px;
}

.comment-form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.comment-input {
  flex: 1;
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.comment-list {
  margin-top: 20px;
}

.empty-comment {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
  color: #999;
}

.comment-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.comment-item {
  display: flex;
  gap: 10px;
}

.comment-content {
  flex: 1;
}

.comment-user {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-text {
  line-height: 1.5;
  margin-bottom: 5px;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

.action-item {
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.action-item:hover {
  color: #409eff;
}

.load-more {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.related-photos {
  margin-top: 30px;
}

.related-photos h3 {
  margin-bottom: 15px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 15px;
}

.photo-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s;
}

.photo-card:hover {
  transform: translateY(-5px);
}

.photo-card .photo-image {
  height: 120px;
}

.photo-card .photo-info {
  padding: 10px;
  box-shadow: none;
}

.photo-card .photo-title {
  font-size: 14px;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-card .photo-user {
  font-size: 12px;
}

.photo-card .user-avatar {
  width: 20px;
  height: 20px;
}

.share-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.share-image {
  width: 200px;
  height: 200px;
  overflow: hidden;
  margin-bottom: 15px;
}

.share-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.share-title {
  font-weight: bold;
  margin-bottom: 15px;
}

.share-link {
  width: 100%;
  margin-bottom: 20px;
}

.share-platforms {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.platform-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.platform-item i {
  font-size: 24px;
  margin-bottom: 5px;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .photo-detail {
    flex-direction: row;
  }

  .photo-main {
    flex: 3;
  }

  .photo-info {
    flex: 2;
  }
}

@media (max-width: 767px) {
  .photo-image {
    max-height: 400px;
  }

  .photo-image img {
    max-height: 400px;
  }

  .photo-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}

/* 照片滑动相关样式 */
.photo-image {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 10px;
  touch-action: pan-y;
}

.photo-swiper {
  width: 100%;
  transition: transform 0.3s ease;
}

.photo-image img {
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
  display: block;
}

/* 滑动指示器 */
.swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.swipe-indicator.left {
  left: 10px;
}

.swipe-indicator.right {
  right: 10px;
}

.photo-image:hover .swipe-indicator {
  opacity: 1;
}
</style>
