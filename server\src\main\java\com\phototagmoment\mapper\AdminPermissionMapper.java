package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.AdminPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 管理员权限Mapper接口
 */
@Mapper
public interface AdminPermissionMapper extends BaseMapper<AdminPermission> {

    /**
     * 根据权限编码查询权限
     *
     * @param code 权限编码
     * @return 权限信息
     */
    @Select("SELECT * FROM ptm_admin_permission WHERE code = #{code} AND is_deleted = 0")
    AdminPermission selectByCode(@Param("code") String code);

    /**
     * 更新权限状态
     *
     * @param id     权限ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("UPDATE ptm_admin_permission SET status = #{status}, updated_at = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Select("SELECT p.* FROM ptm_admin_permission p " +
            "INNER JOIN ptm_admin_role_permission rp ON p.id = rp.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 AND p.is_deleted = 0")
    List<AdminPermission> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色ID查询权限编码列表
     *
     * @param roleId 角色ID
     * @return 权限编码列表
     */
    @Select("SELECT p.code FROM ptm_admin_permission p " +
            "INNER JOIN ptm_admin_role_permission rp ON p.id = rp.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 AND p.is_deleted = 0")
    List<String> selectCodesByRoleId(@Param("roleId") Long roleId);
}
