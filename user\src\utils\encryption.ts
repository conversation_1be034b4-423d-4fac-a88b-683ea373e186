/**
 * 加密工具类
 */

// 导入 JSEncrypt 库
import JSEncrypt from 'jsencrypt'

/**
 * RSA 加密
 * @param publicKey RSA 公钥
 * @param data 待加密数据
 * @returns 加密后的数据
 */
export const encryptWithRSA = (publicKey: string, data: string): string => {
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(publicKey)
  return encrypt.encrypt(data) || ''
}

/**
 * 获取加密公钥
 * @returns 加密公钥
 */
export const getPublicKey = async (): Promise<string> => {
  try {
    const response = await fetch('/api/encryption/public-key')
    const result = await response.json()
    
    if (result.code === 200 && result.data && result.data.publicKey) {
      return result.data.publicKey
    }
    
    throw new Error('获取加密公钥失败')
  } catch (error) {
    console.error('获取加密公钥失败:', error)
    throw error
  }
}

/**
 * 加密敏感数据
 * @param data 敏感数据
 * @returns 加密后的数据
 */
export const encryptSensitiveData = async (data: string): Promise<string> => {
  try {
    const publicKey = await getPublicKey()
    return encryptWithRSA(publicKey, data)
  } catch (error) {
    console.error('加密敏感数据失败:', error)
    return data
  }
}

/**
 * 加密对象中的敏感字段
 * @param obj 对象
 * @param sensitiveFields 敏感字段列表
 * @returns 加密后的对象
 */
export const encryptSensitiveFields = async (
  obj: Record<string, any>,
  sensitiveFields: string[]
): Promise<Record<string, any>> => {
  try {
    const publicKey = await getPublicKey()
    const result = { ...obj }
    
    for (const field of sensitiveFields) {
      if (result[field] && typeof result[field] === 'string') {
        result[field] = encryptWithRSA(publicKey, result[field])
      }
    }
    
    return result
  } catch (error) {
    console.error('加密敏感字段失败:', error)
    return obj
  }
}

/**
 * 加密文件
 * @param file 文件
 * @returns 加密后的文件
 */
export const encryptFile = async (file: File): Promise<File> => {
  // 这里只是一个示例，实际上前端不应该直接加密文件
  // 文件加密应该在服务端进行
  return file
}

/**
 * 上传加密文件到七牛云
 * @param file 文件
 * @returns 上传结果
 */
export const uploadEncryptedFile = async (file: File): Promise<any> => {
  try {
    // 创建 FormData
    const formData = new FormData()
    formData.append('file', file)
    
    // 上传文件
    const response = await fetch('/api/qiniu/upload', {
      method: 'POST',
      body: formData
    })
    
    return await response.json()
  } catch (error) {
    console.error('上传加密文件失败:', error)
    throw error
  }
}

/**
 * 获取七牛云上传凭证
 * @param fileName 文件名
 * @returns 上传凭证
 */
export const getQiniuUploadToken = async (fileName?: string): Promise<any> => {
  try {
    const url = fileName ? 
      `/api/qiniu/upload-token?fileName=${encodeURIComponent(fileName)}` : 
      '/api/qiniu/upload-token'
    
    const response = await fetch(url)
    return await response.json()
  } catch (error) {
    console.error('获取七牛云上传凭证失败:', error)
    throw error
  }
}

export default {
  encryptWithRSA,
  getPublicKey,
  encryptSensitiveData,
  encryptSensitiveFields,
  encryptFile,
  uploadEncryptedFile,
  getQiniuUploadToken
}
