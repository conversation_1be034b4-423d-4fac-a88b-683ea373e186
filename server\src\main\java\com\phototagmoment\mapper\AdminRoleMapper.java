package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.phototagmoment.entity.AdminRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 管理员角色Mapper接口
 */
@Mapper
public interface AdminRoleMapper extends BaseMapper<AdminRole> {

    /**
     * 根据角色编码查询角色
     *
     * @param code 角色编码
     * @return 角色信息
     */
    @Select("SELECT * FROM ptm_admin_role WHERE code = #{code} AND is_deleted = 0")
    AdminRole selectByCode(@Param("code") String code);

    /**
     * 更新角色状态
     *
     * @param id     角色ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("UPDATE ptm_admin_role SET status = #{status}, updated_at = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
}
