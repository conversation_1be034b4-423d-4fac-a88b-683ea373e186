<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>系统配置管理</span>
          <el-button type="primary" @click="refreshCache">刷新缓存</el-button>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="内容审核配置" name="content-moderation">
          <ContentModerationConfig />
        </el-tab-pane>
        <el-tab-pane label="第三方登录配置" name="auth">
          <AuthConfig />
        </el-tab-pane>
        <el-tab-pane label="实名认证配置" name="identity-verification">
          <IdentityVerificationConfig />
        </el-tab-pane>
        <el-tab-pane label="短信配置" name="sms">
          <SmsConfig />
        </el-tab-pane>
        <el-tab-pane label="所有配置" name="all">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-input v-model="searchKeyword" placeholder="请输入关键字" clearable @keyup.enter="handleSearch" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>

          <el-table :data="configList" border style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="configKey" label="配置键" width="250" />
            <el-table-column prop="configName" label="配置名称" width="150" />
            <el-table-column prop="configValue" label="配置值" show-overflow-tooltip />
            <el-table-column prop="configType" label="类型" width="100" />
            <el-table-column prop="isSystem" label="系统内置" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isSystem ? 'success' : 'info'">
                  {{ scope.row.isSystem ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status ? 'success' : 'danger'">
                  {{ scope.row.status ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button
                  v-if="!scope.row.isSystem"
                  type="danger"
                  size="small"
                  @click="handleDelete(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="total > 0"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="pagination"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 编辑配置对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
      <el-form :model="configForm" :rules="rules" ref="configFormRef" label-width="100px">
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="configForm.configKey" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="configForm.configName" />
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="configForm.configValue" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="配置类型" prop="configType">
          <el-select v-model="configForm.configType" placeholder="请选择配置类型">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="布尔值" value="boolean" />
            <el-option label="JSON" value="json" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="configForm.remark" type="textarea" :rows="2" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="configForm.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAllSystemConfig, getConfigDetail, updateSystemConfig } from '@/api/system/config'
import ContentModerationConfig from './ContentModerationConfig.vue'
import AuthConfig from './AuthConfig.vue'
import IdentityVerificationConfig from './IdentityVerificationConfig.vue'
import SmsConfig from './SmsConfig.vue'

export default {
  name: 'ConfigManager',
  components: {
    ContentModerationConfig,
    AuthConfig,
    IdentityVerificationConfig,
    SmsConfig
  },
  setup() {
    const activeTab = ref('content-moderation')
    const configList = ref([])
    const loading = ref(false)
    const searchKeyword = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    const dialogVisible = ref(false)
    const dialogTitle = ref('添加配置')
    const isEdit = ref(false)
    const configFormRef = ref(null)

    const configForm = reactive({
      id: null,
      configKey: '',
      configName: '',
      configValue: '',
      configType: 'string',
      remark: '',
      isSystem: false,
      status: true
    })

    const rules = {
      configKey: [{ required: true, message: '请输入配置键', trigger: 'blur' }],
      configName: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
      configValue: [{ required: true, message: '请输入配置值', trigger: 'blur' }],
      configType: [{ required: true, message: '请选择配置类型', trigger: 'change' }]
    }

    // 获取配置列表
    const fetchConfigList = async () => {
      loading.value = true
      try {
        const res = await getAllSystemConfig()
        if (res.code === 200) {
          configList.value = res.data
          total.value = res.data.length
        } else {
          ElMessage.error(res.message || '获取配置列表失败')
        }
      } catch (error) {
        console.error('获取配置列表失败', error)
        ElMessage.error('获取配置列表失败')
      } finally {
        loading.value = false
      }
    }

    // 搜索
    const handleSearch = () => {
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        const filteredList = configList.value.filter(item => {
          return (
            item.configKey.toLowerCase().includes(keyword) ||
            item.configName.toLowerCase().includes(keyword) ||
            item.configValue.toLowerCase().includes(keyword) ||
            (item.remark && item.remark.toLowerCase().includes(keyword))
          )
        })
        configList.value = filteredList
        total.value = filteredList.length
      } else {
        fetchConfigList()
      }
    }

    // 重置搜索
    const resetSearch = () => {
      searchKeyword.value = ''
      fetchConfigList()
    }

    // 编辑配置
    const handleEdit = (row) => {
      dialogTitle.value = '编辑配置'
      isEdit.value = true
      Object.assign(configForm, row)
      dialogVisible.value = true
    }

    // 删除配置
    const handleDelete = (row) => {
      ElMessageBox.confirm('确定要删除该配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteConfig(row.id)
          if (res.code === 200) {
            ElMessage.success('删除成功')
            fetchConfigList()
          } else {
            ElMessage.error(res.message || '删除失败')
          }
        } catch (error) {
          console.error('删除配置失败', error)
          ElMessage.error('删除配置失败')
        }
      }).catch(() => {})
    }

    // 提交表单
    const submitForm = async () => {
      if (!configFormRef.value) return
      
      await configFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const res = await updateSystemConfig({
              [configForm.configKey]: configForm.configValue
            })
            
            if (res.code === 200) {
              ElMessage.success('保存成功')
              dialogVisible.value = false
              fetchConfigList()
            } else {
              ElMessage.error(res.message || '保存失败')
            }
          } catch (error) {
            console.error('保存配置失败', error)
            ElMessage.error('保存配置失败')
          }
        }
      })
    }

    // 刷新缓存
    const refreshCache = async () => {
      try {
        const res = await refreshConfigCache()
        if (res.code === 200) {
          ElMessage.success('刷新缓存成功')
        } else {
          ElMessage.error(res.message || '刷新缓存失败')
        }
      } catch (error) {
        console.error('刷新缓存失败', error)
        ElMessage.error('刷新缓存失败')
      }
    }

    // 分页相关
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchConfigList()
    }

    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchConfigList()
    }

    onMounted(() => {
      fetchConfigList()
    })

    return {
      activeTab,
      configList,
      loading,
      searchKeyword,
      currentPage,
      pageSize,
      total,
      dialogVisible,
      dialogTitle,
      isEdit,
      configForm,
      configFormRef,
      rules,
      handleSearch,
      resetSearch,
      handleEdit,
      handleDelete,
      submitForm,
      refreshCache,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
.pagination {
  margin-top: 20px;
  text-align: right;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
