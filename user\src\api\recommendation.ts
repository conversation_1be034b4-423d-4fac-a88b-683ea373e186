import request from '@/utils/request'
import {
  mockHomeRecommendations,
  mockFollowingPhotos,
  mockHotPhotos,
  mockRecommendedPhotos,
  mockUserInterestTags,
  mockRecordUserBehavior,
  mockUpdateUserInterestModel
} from '@/utils/mockApi'

// 是否使用模拟数据
const USE_MOCK = false

interface RecommendationParams {
  page?: number
  size?: number
  category?: string
  [key: string]: any
}

interface BehaviorData {
  photoId?: number  // 兼容旧版本
  noteId?: number   // 新版本推荐使用
  behavior: string
  [key: string]: any
}

/**
 * 获取首页推荐
 * @param params 查询参数
 * @returns 请求结果
 */
export function getHomeRecommendations(params: RecommendationParams) {
  if (USE_MOCK) {
    return mockHomeRecommendations(params)
  }
  return request({
    url: '/recommendation/home',
    method: 'get',
    params
  })
}

/**
 * 获取关注的人发布的照片
 * @param params 查询参数
 * @returns 请求结果
 */
export function getFollowingPhotos(params: RecommendationParams) {
  if (USE_MOCK) {
    return mockFollowingPhotos(params)
  }
  return request({
    url: '/recommendation/following',
    method: 'get',
    params
  })
}

/**
 * 获取热门照片
 * @param params 查询参数
 * @returns 请求结果
 */
export function getHotPhotos(params: RecommendationParams) {
  if (USE_MOCK) {
    return mockHotPhotos(params)
  }
  return request({
    url: '/recommendation/hot',
    method: 'get',
    params
  })
}

/**
 * 获取推荐照片
 * @param params 查询参数
 * @returns 请求结果
 */
export function getRecommendedPhotos(params: RecommendationParams) {
  if (USE_MOCK) {
    return mockRecommendedPhotos(params)
  }
  return request({
    url: '/recommendation/recommended',
    method: 'get',
    params
  })
}

/**
 * 获取用户兴趣标签
 * @param params 查询参数
 * @returns 请求结果
 */
export function getUserInterestTags(params?: RecommendationParams) {
  if (USE_MOCK) {
    return mockUserInterestTags(params)
  }
  return request({
    url: '/recommendation/interest-tags',
    method: 'get',
    params
  })
}

/**
 * 记录用户行为
 * @param data 请求数据
 * @returns 请求结果
 */
export function recordUserBehavior(data: BehaviorData) {
  if (USE_MOCK) {
    return mockRecordUserBehavior(data)
  }

  // 确保ID是数字类型，支持photoId和noteId两种字段
  let id: number;
  if (data.noteId !== undefined) {
    id = typeof data.noteId === 'object' ? Number((data.noteId as any).id || 0) : Number(data.noteId);
  } else if (data.photoId !== undefined) {
    id = typeof data.photoId === 'object' ? Number((data.photoId as any).id || 0) : Number(data.photoId);
  } else {
    console.error('记录用户行为失败：缺少photoId或noteId参数');
    return Promise.reject(new Error('缺少photoId或noteId参数'));
  }

  const behavior = data.behavior;

  // 打印请求信息，用于调试
  console.log('记录用户行为:', { id, behavior, originalData: data });

  // 使用请求体发送数据，同时发送两个字段以确保兼容性
  return request({
    url: '/recommendation/record-behavior',
    method: 'post',
    data: {
      noteId: id,    // 新版本字段
      photoId: id,   // 兼容旧版本字段
      behavior: behavior
    }
  })
}

/**
 * 更新用户兴趣模型
 * @returns 请求结果
 */
export function updateUserInterestModel() {
  if (USE_MOCK) {
    return mockUpdateUserInterestModel()
  }
  return request({
    url: '/recommendation/update-interest-model',
    method: 'post'
  })
}
