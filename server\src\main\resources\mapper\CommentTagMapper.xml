<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.phototagmoment.mapper.CommentTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.phototagmoment.entity.CommentTag">
        <id column="id" property="id" />
        <result column="comment_id" property="commentId" />
        <result column="tag_name" property="tagName" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, comment_id, tag_name, created_at
    </sql>

    <!-- 批量插入评论标签 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ptm_comment_tag (comment_id, tag_name, created_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.commentId}, #{item.tagName}, #{item.createdAt})
        </foreach>
    </insert>

    <!-- 根据评论ID删除标签 -->
    <delete id="deleteByCommentId" parameterType="java.lang.Long">
        DELETE FROM ptm_comment_tag WHERE comment_id = #{commentId}
    </delete>

    <!-- 根据标签名称统计使用次数 -->
    <select id="countByTagName" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ptm_comment_tag WHERE tag_name = #{tagName}
    </select>

    <!-- 获取热门标签（按使用次数排序） -->
    <select id="selectHotTags" resultType="java.util.Map">
        SELECT 
            tag_name as tagName,
            COUNT(*) as useCount
        FROM ptm_comment_tag 
        GROUP BY tag_name 
        ORDER BY useCount DESC 
        LIMIT #{limit}
    </select>

</mapper>
