package com.phototagmoment.service.impl;

import com.phototagmoment.service.QiniuStorageService;
import com.phototagmoment.service.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 七牛云存储服务适配器
 * 将 QiniuStorageService 适配为 StorageService
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "storage.qiniu.enabled", havingValue = "true")
public class QiniuStorageServiceAdapter implements StorageService {

    @Autowired
    private QiniuStorageService qiniuStorageService;

    @Override
    public String uploadFile(InputStream inputStream, String path, String contentType) {
        return qiniuStorageService.uploadFile(inputStream, path, 0);
    }

    @Override
    public String uploadFile(byte[] bytes, String path, String contentType) {
        return qiniuStorageService.uploadFile(new ByteArrayInputStream(bytes), path, bytes.length);
    }

    @Override
    public boolean deleteFile(String path) {
        return qiniuStorageService.deleteFile(path);
    }

    @Override
    public String getFileUrl(String path) {
        return qiniuStorageService.getFileUrl(path);
    }
}
